/**
 * 全局监控包装器模块
 * 负责为工厂函数添加监控功能
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 监控包装器类
     * 用于包装函数并添加监控功能
     */
    class MonitoringWrapper {
        constructor() {
            this.wrappedFunctions = new Set();
            this.performanceMarks = new Map();
        }

        /**
         * 包装工厂函数添加监控
         * @param {string} functionName - 函数名称
         * @param {function} originalFunction - 原始函数
         * @returns {function} 包装后的函数
         */
        wrapFactoryFunction(functionName, originalFunction) {
            if (this.wrappedFunctions.has(functionName)) {
                return originalFunction;
            }

            this.wrappedFunctions.add(functionName);

            return (...args) => {
                const startTime = performance.now();
                const markStart = `${functionName}_start`;
                const markEnd = `${functionName}_end`;
                const measureName = `${functionName}_duration`;

                try {
                    // 性能标记开始
                    performance.mark(markStart);

                    // 调用原函数
                    const result = originalFunction.apply(this, args);

                    // 性能标记结束
                    performance.mark(markEnd);
                    performance.measure(measureName, markStart, markEnd);

                    const endTime = performance.now();
                    const duration = endTime - startTime;

                    // 记录监控数据
                    this.logFactoryFunctionCall(functionName, duration, result, {
                        args: args.length,
                        hasResult: Boolean(result),
                        resultType: typeof result
                    });

                    return result;
                } catch (error) {
                    const endTime = performance.now();
                    const duration = endTime - startTime;

                    // 记录错误
                    this.logFactoryFunctionCall(functionName, duration, null, {
                        error: true,
                        errorMessage: error.message,
                        args: args.length
                    });

                    throw error;
                } finally {
                    // 清理性能标记
                    try {
                        performance.clearMarks(markStart);
                        performance.clearMarks(markEnd);
                        performance.clearMeasures(measureName);
                    } catch (e) {
                        // 忽略清理错误
                    }
                }
            };
        }

        /**
         * 包装异步函数添加监控
         * @param {string} functionName - 函数名称
         * @param {function} originalFunction - 原始异步函数
         * @returns {function} 包装后的异步函数
         */
        wrapAsyncFunction(functionName, originalFunction) {
            if (this.wrappedFunctions.has(functionName)) {
                return originalFunction;
            }

            this.wrappedFunctions.add(functionName);

            return async (...args) => {
                const startTime = performance.now();
                const markStart = `${functionName}_async_start`;
                const markEnd = `${functionName}_async_end`;
                const measureName = `${functionName}_async_duration`;

                try {
                    performance.mark(markStart);

                    const result = await originalFunction.apply(this, args);

                    performance.mark(markEnd);
                    performance.measure(measureName, markStart, markEnd);

                    const endTime = performance.now();
                    const duration = endTime - startTime;

                    this.logFactoryFunctionCall(functionName, duration, result, {
                        async: true,
                        args: args.length,
                        hasResult: Boolean(result),
                        resultType: typeof result
                    });

                    return result;
                } catch (error) {
                    const endTime = performance.now();
                    const duration = endTime - startTime;

                    this.logFactoryFunctionCall(functionName, duration, null, {
                        async: true,
                        error: true,
                        errorMessage: error.message,
                        args: args.length
                    });

                    throw error;
                } finally {
                    try {
                        performance.clearMarks(markStart);
                        performance.clearMarks(markEnd);
                        performance.clearMeasures(measureName);
                    } catch (e) {
                        // 忽略清理错误
                    }
                }
            };
        }

        /**
         * 记录工厂函数调用
         * @param {string} functionName - 函数名称
         * @param {number} duration - 执行时间
         * @param {any} result - 返回结果
         * @param {object} context - 上下文信息
         */
        logFactoryFunctionCall(functionName, duration, result, context) {
            const logger = this.getLogger();
            if (logger && logger.logFactoryFunctionCall) {
                logger.logFactoryFunctionCall(functionName, duration, result, context);
            }
        }

        /**
         * 获取Logger实例
         * @returns {object} Logger实例
         */
        getLogger() {
            return window.OTA?.logger || window.logger;
        }

        /**
         * 自动包装所有已知的工厂函数
         */
        wrapAllFactoryFunctions() {
            const logger = this.getLogger();
            if (logger) {
                logger.log('🔧 开始包装工厂函数...', 'info', {
                    type: 'monitoring_wrapper_init'
                });
            }

            // 需要包装的工厂函数列表
            const factoryFunctions = [
                'getAppState',
                'getGeminiService', 
                'getAPIService',
                'getImageUploadManager',
                'getCurrencyConverter',
                'getMultiOrderManager',
                'getPagingServiceManager',
                'getOrderHistoryManager',
                'getI18nManager'
            ];

            let wrappedCount = 0;

            factoryFunctions.forEach(functionName => {
                if (window[functionName] && typeof window[functionName] === 'function') {
                    const originalFunction = window[functionName];
                    
                    // 创建包装函数
                    const wrappedFunction = this.wrapFactoryFunction(functionName, originalFunction);
                    
                    // 替换全局函数
                    window[functionName] = wrappedFunction;
                    
                    // 保持OTA命名空间同步
                    if (window.OTA && window.OTA[functionName]) {
                        window.OTA[functionName] = wrappedFunction;
                    }
                    
                    wrappedCount++;
                }
            });

            // 包装已经初始化的服务实例的关键方法
            this.wrapExistingServices();

            if (logger) {
                logger.log(`✅ 工厂函数包装完成`, 'success', {
                    type: 'monitoring_wrapper_complete',
                    wrappedCount,
                    totalFunctions: factoryFunctions.length
                });
            }

            return wrappedCount;
        }

        /**
         * 包装已存在的服务实例方法
         */
        wrapExistingServices() {
            // 包装API服务
            if (window.OTA && window.OTA.apiService) {
                this.wrapApiService(window.OTA.apiService);
            } else if (window.apiService) {
                this.wrapApiService(window.apiService);
            }

            // 包装Gemini服务
            if (window.OTA && window.OTA.geminiService) {
                this.wrapGeminiService(window.OTA.geminiService);
            } else if (window.geminiService) {
                this.wrapGeminiService(window.geminiService);
            }

            // 包装其他重要服务
            this.wrapOtherServices();
        }

        /**
         * 包装其他重要服务
         */
        wrapOtherServices() {
            const logger = this.getLogger();

            // 包装图片上传管理器
            try {
                const imageManager = window.getImageUploadManager && window.getImageUploadManager();
                if (imageManager && imageManager.uploadAndAnalyze) {
                    const originalMethod = imageManager.uploadAndAnalyze;
                    imageManager.uploadAndAnalyze = this.wrapAsyncFunction(
                        'ImageUploadManager.uploadAndAnalyze',
                        originalMethod.bind(imageManager)
                    );
                }
            } catch (error) {
                // 服务可能还未初始化，忽略错误
            }

            // 包装多订单管理器
            try {
                const multiOrderManager = window.getMultiOrderManager && window.getMultiOrderManager();
                if (multiOrderManager && multiOrderManager.processMultiOrderData) {
                    const originalMethod = multiOrderManager.processMultiOrderData;
                    multiOrderManager.processMultiOrderData = this.wrapFactoryFunction(
                        'MultiOrderManager.processMultiOrderData',
                        originalMethod.bind(multiOrderManager)
                    );
                }
            } catch (error) {
                // 服务可能还未初始化，忽略错误
            }

            if (logger) {
                logger.log('其他服务方法包装完成', 'info', {
                    type: 'service_wrapping_complete'
                });
            }
        }

        /**
         * 包装API调用函数
         * @param {object} apiService - API服务实例
         */
        wrapApiService(apiService) {
            if (!apiService) return;

            const logger = this.getLogger();
            
            // 包装主要的API方法
            const apiMethods = ['login', 'fetchSystemData', 'createOrder', 'fetchOrderHistory'];
            
            apiMethods.forEach(methodName => {
                if (apiService[methodName] && typeof apiService[methodName] === 'function') {
                    const originalMethod = apiService[methodName];
                    
                    apiService[methodName] = this.wrapAsyncFunction(
                        `APIService.${methodName}`, 
                        originalMethod.bind(apiService)
                    );
                }
            });

            if (logger) {
                logger.log('API服务监控已启用', 'info', {
                    type: 'api_monitoring_enabled',
                    methods: apiMethods
                });
            }
        }

        /**
         * 包装Gemini服务函数
         * @param {object} geminiService - Gemini服务实例
         */
        wrapGeminiService(geminiService) {
            if (!geminiService) return;

            const logger = this.getLogger();
            
            // 包装主要的Gemini方法（更新为新架构的方法名）
            const geminiMethods = ['parseOrder', 'parseMultipleOrders', 'analyzeImage'];
            
            geminiMethods.forEach(methodName => {
                if (geminiService[methodName] && typeof geminiService[methodName] === 'function') {
                    const originalMethod = geminiService[methodName];
                    
                    geminiService[methodName] = this.wrapAsyncFunction(
                        `GeminiService.${methodName}`, 
                        originalMethod.bind(geminiService)
                    );
                }
            });

            if (logger) {
                logger.log('Gemini服务监控已启用', 'info', {
                    type: 'gemini_monitoring_enabled',
                    methods: geminiMethods
                });
            }
        }

        /**
         * 获取包装统计
         * @returns {object} 统计信息
         */
        getWrapperStats() {
            return {
                wrappedFunctionsCount: this.wrappedFunctions.size,
                wrappedFunctions: Array.from(this.wrappedFunctions)
            };
        }
    }

    // 创建全局监控包装器实例
    const monitoringWrapper = new MonitoringWrapper();

    // 暴露到OTA命名空间
    window.OTA.monitoringWrapper = monitoringWrapper;

    // 向后兼容：暴露到全局window对象
    window.monitoringWrapper = monitoringWrapper;

    // 等待DOM加载完成后自动包装工厂函数
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                monitoringWrapper.wrapAllFactoryFunctions();
            }, 100); // 延迟确保所有工厂函数都已定义
        });
    } else {
        // 如果DOM已经加载完成，立即执行
        setTimeout(() => {
            monitoringWrapper.wrapAllFactoryFunctions();
        }, 100);
    }

})();
