/**
 * 语言数据管理器 - 统一语言数据源和访问接口
 * 解决系统中多处重复定义和数据不一致问题
 * 
 * 作者: Claude
 * 创建时间: 2025-07-20
 * 版本: 1.0.0
 */

/**
 * 语言数据管理器类
 * 提供统一的语言数据访问、验证、转换和缓存功能
 */
class LanguageManager {
    constructor() {
        // 增强的多层缓存系统
        this.cache = {
            // 主数据缓存
            data: null,
            timestamp: null,
            maxAge: 5 * 60 * 1000, // 5分钟缓存
            
            // 性能优化缓存
            enabledOnly: null,
            enabledOnlyTimestamp: null,
            
            // 默认选择缓存
            defaultSelections: new Map(),
            
            // 验证结果缓存
            validationCache: new Map(),
            validationCacheMaxSize: 100,
            
            // API转换缓存
            apiTransformCache: new Map(),
            apiTransformCacheMaxSize: 50
        };
        
        // 性能监控
        this.performance = {
            hits: 0,
            misses: 0,
            totalRequests: 0,
            averageResponseTime: 0,
            lastCleanup: Date.now()
        };
        
        // 事件监听器
        this.listeners = new Set();
        
        // 标准语言数据 - 唯一数据源
        this.standardLanguages = [
            { id: 2, name: 'English (EN)', code: 'en', enabled: true, priority: 1, default: true },
            { id: 3, name: 'Malay (MY)', code: 'ms', enabled: true, priority: 2, default: false },
            { id: 4, name: 'Chinese (CN)', code: 'zh', enabled: true, priority: 3, default: false },
            { id: 5, name: 'Paging (PG)', code: 'pg', enabled: true, priority: 4, default: false },
            { id: 6, name: 'Charter (CHARTER)', code: 'charter', enabled: true, priority: 5, default: false },
            { id: 8, name: '携程司导 (IM)', code: 'im', enabled: true, priority: 6, default: false },
            { id: 9, name: 'PSV (PSV)', code: 'psv', enabled: true, priority: 7, default: false },
            { id: 10, name: 'EVP (EVP)', code: 'evp', enabled: true, priority: 8, default: false },
            { id: 11, name: 'Car Type Reverify (CAR)', code: 'car', enabled: true, priority: 9, default: false },
            { id: 12, name: 'Jetty (JETTY)', code: 'jetty', enabled: true, priority: 10, default: false },
            { id: 13, name: 'PhotoSkill Proof (PHOTO)', code: 'photo', enabled: true, priority: 11, default: false }
        ];
        
        // AI语言映射配置
        this.aiMapping = {
            'english': { id: 2, confidence: 0.9 },
            'en': { id: 2, confidence: 0.9 },
            'malay': { id: 3, confidence: 0.9 },
            'ms': { id: 3, confidence: 0.9 },
            'chinese': { id: 4, confidence: 0.9 },
            'zh': { id: 4, confidence: 0.9 },
            'mandarin': { id: 4, confidence: 0.8 }
        };
        
        // 默认选择策略
        this.defaultStrategy = {
            mode: 'smart', // 'smart', 'fixed', 'user-preference'
            fallback: [2, 3, 4], // 默认选择的语言ID
            maxSelection: 5 // 最大选择数量
        };
    }
    
    /**
     * 同步获取所有可用语言 - 主要接口（性能优化版）
     * @param {Object} options - 选项
     * @param {boolean} options.enabledOnly - 只返回启用的语言
     * @returns {Array} 语言数组
     */
    getLanguagesSync(options = {}) {
        const startTime = performance.now();
        const { enabledOnly = false } = options;
        
        try {
            this.performance.totalRequests++;
            
            // 性能优化：优先检查enabledOnly专用缓存
            if (enabledOnly && this.isEnabledOnlyCacheValid()) {
                this.performance.hits++;
                this.updatePerformanceMetrics(startTime);
                return [...this.cache.enabledOnly];
            }
            
            // 主缓存检查
            if (this.isCacheValid()) {
                this.performance.hits++;
                let languages = [...this.cache.data];
                const result = enabledOnly ? languages.filter(lang => lang.enabled) : languages;
                
                // 更新enabledOnly缓存
                if (enabledOnly) {
                    this.cache.enabledOnly = [...result];
                    this.cache.enabledOnlyTimestamp = Date.now();
                }
                
                this.updatePerformanceMetrics(startTime);
                return result;
            }
            
            this.performance.misses++;
            
            // 尝试从AppState获取
            try {
                const appState = window.OTA.getService('appState');
                const systemData = appState.get('systemData');
                if (systemData && systemData.languages && systemData.languages.length > 0) {
                    const languages = this.mergeLanguageData(systemData.languages, this.standardLanguages);
                    this.updateCache(languages);
                    return enabledOnly ? languages.filter(lang => lang.enabled) : languages;
                }
            } catch (error) {
                getLogger().logWarning('从AppState获取语言数据失败', error);
            }
            
            // 最后使用标准数据
            let fallbackLanguages = [...this.standardLanguages];
            const result = enabledOnly ? fallbackLanguages.filter(lang => lang.enabled) : fallbackLanguages;
            
            // 缓存fallback结果
            this.updateCache(fallbackLanguages);
            if (enabledOnly) {
                this.cache.enabledOnly = [...result];
                this.cache.enabledOnlyTimestamp = Date.now();
            }
            
            this.updatePerformanceMetrics(startTime);
            return result;
            
        } catch (error) {
            getLogger().logError('同步获取语言数据失败', error);
            
            // 返回标准数据作为最终fallback
            let fallbackLanguages = [...this.standardLanguages];
            const result = enabledOnly ? fallbackLanguages.filter(lang => lang.enabled) : fallbackLanguages;
            this.updatePerformanceMetrics(startTime);
            return result;
        }
    }

    /**
     * 异步获取所有可用语言 - 向后兼容接口
     * @param {Object} options - 选项
     * @param {boolean} options.enabledOnly - 只返回启用的语言
     * @param {boolean} options.useCache - 是否使用缓存
     * @returns {Promise<Array>} 语言数组
     */
    async getLanguages(options = {}) {
        const { enabledOnly = false, useCache = true } = options;
        
        try {
            // 立即返回同步结果
            const syncResult = this.getLanguagesSync({ enabledOnly });
            
            // 后台异步更新（不阻塞）
            if (!useCache || !this.isCacheValid()) {
                this.refreshCacheInBackground();
            }
            
            return syncResult;
            
        } catch (error) {
            getLogger().logError('获取语言数据失败', error);
            
            // 返回标准数据作为fallback
            let fallbackLanguages = [...this.standardLanguages];
            return enabledOnly ? fallbackLanguages.filter(lang => lang.enabled) : fallbackLanguages;
        }
    }
    
    /**
     * 根据ID获取语言
     * @param {number} id - 语言ID
     * @returns {Promise<Object|null>} 语言对象
     */
    async getLanguageById(id) {
        const languages = await this.getLanguages();
        return languages.find(lang => lang.id === id) || null;
    }
    
    /**
     * 根据代码获取语言
     * @param {string} code - 语言代码
     * @returns {Promise<Object|null>} 语言对象
     */
    async getLanguageByCode(code) {
        const languages = await this.getLanguages();
        return languages.find(lang => lang.code === code) || null;
    }
    
    /**
     * 同步验证语言ID数组 - 主要接口（带缓存优化）
     * @param {Array} languageIds - 语言ID数组
     * @returns {Object} 验证结果
     */
    validateLanguageIdsSync(languageIds) {
        // 性能优化：缓存检查
        const cacheKey = JSON.stringify(languageIds);
        if (this.cache.validationCache.has(cacheKey)) {
            return this.cache.validationCache.get(cacheKey);
        }
        
        if (!Array.isArray(languageIds)) {
            const result = {
                valid: false,
                error: '语言ID必须是数组格式',
                validIds: [],
                invalidIds: []
            };
            return result; // 不缓存错误输入
        }
        
        const languages = this.getLanguagesSync({ enabledOnly: true });
        const validIds = languages.map(lang => lang.id);
        
        const invalidIds = languageIds.filter(id => !validIds.includes(id));
        const validSelectedIds = languageIds.filter(id => validIds.includes(id));
        
        const result = {
            valid: invalidIds.length === 0,
            error: invalidIds.length > 0 ? `无效的语言ID: ${invalidIds.join(', ')}` : null,
            validIds: validSelectedIds,
            invalidIds,
            suggestions: invalidIds.length > 0 ? this.suggestAlternatives(invalidIds) : []
        };
        
        // 缓存结果
        this.cacheValidationResult(cacheKey, result);
        return result;
    }

    /**
     * 异步验证语言ID数组 - 向后兼容接口
     * @param {Array} languageIds - 语言ID数组
     * @returns {Promise<Object>} 验证结果
     */
    async validateLanguageIds(languageIds) {
        try {
            return this.validateLanguageIdsSync(languageIds);
        } catch (error) {
            getLogger().logError('语言ID验证失败', error);
            return {
                valid: false,
                error: '验证过程出错',
                validIds: [],
                invalidIds: languageIds || []
            };
        }
    }
    
    /**
     * 同步获取默认语言选择 - 主要接口（带缓存优化）
     * @param {string} context - 上下文 ('form', 'multi-order', 'ai-analysis')
     * @returns {Array} 默认语言ID数组
     */
    getDefaultSelectionSync(context = 'form') {
        // 性能优化：缓存检查
        if (this.cache.defaultSelections.has(context)) {
            return [...this.cache.defaultSelections.get(context)];
        }
        
        const languages = this.getLanguagesSync({ enabledOnly: true });
        let result;
        
        switch (this.defaultStrategy.mode) {
            case 'smart':
                result = this.getSmartDefaultSelection(languages, context);
                break;
            case 'fixed':
                result = this.defaultStrategy.fallback.filter(id => 
                    languages.some(lang => lang.id === id)
                );
                break;
            case 'user-preference':
                result = this.getUserPreferenceSelectionSync();
                break;
            default:
                result = [2]; // English as ultimate fallback
        }
        
        // 缓存结果（除了user-preference，因为它可能变化）
        if (this.defaultStrategy.mode !== 'user-preference') {
            this.cache.defaultSelections.set(context, [...result]);
        }
        
        return result;
    }

    /**
     * 异步获取默认语言选择 - 向后兼容接口
     * @param {string} context - 上下文 ('form', 'multi-order', 'ai-analysis')
     * @returns {Promise<Array>} 默认语言ID数组
     */
    async getDefaultSelection(context = 'form') {
        try {
            return this.getDefaultSelectionSync(context);
        } catch (error) {
            getLogger().logError('获取默认语言选择失败', error);
            return [2]; // English fallback
        }
    }
    
    /**
     * 转换语言数据格式
     * @param {Array} languageIds - 语言ID数组
     * @param {string} format - 目标格式 ('array', 'object', 'api')
     * @returns {Promise<any>} 转换后的数据
     */
    async transformLanguageData(languageIds, format = 'array') {
        const validation = await this.validateLanguageIds(languageIds);
        
        if (!validation.valid) {
            throw new Error(validation.error);
        }
        
        const languages = await this.getLanguages();
        const selectedLanguages = languages.filter(lang => 
            validation.validIds.includes(lang.id)
        );
        
        switch (format) {
            case 'array':
                return validation.validIds;
                
            case 'object':
                return selectedLanguages.reduce((obj, lang) => {
                    obj[lang.id] = lang;
                    return obj;
                }, {});
                
            case 'api':
                // API格式：使用后端要求的snake_case字段名
                return {
                    languages_id_array: validation.validIds
                };
                
            case 'ui':
                return selectedLanguages.map(lang => ({
                    value: lang.id,
                    label: lang.name,
                    code: lang.code,
                    enabled: lang.enabled
                }));
                
            default:
                throw new Error(`不支持的格式: ${format}`);
        }
    }
    
    /**
     * AI语言检测映射
     * @param {string} detectedLanguage - AI检测到的语言
     * @returns {Promise<Object|null>} 映射结果
     */
    async mapAIDetectedLanguage(detectedLanguage) {
        const key = detectedLanguage.toLowerCase().trim();
        const mapping = this.aiMapping[key];
        
        if (mapping) {
            const language = await this.getLanguageById(mapping.id);
            return {
                language,
                confidence: mapping.confidence,
                detected: detectedLanguage,
                mapped: true
            };
        }
        
        // 模糊匹配
        const fuzzyMatch = await this.fuzzyMatchLanguage(detectedLanguage);
        if (fuzzyMatch) {
            return {
                language: fuzzyMatch.language,
                confidence: fuzzyMatch.confidence * 0.8, // 降低模糊匹配的置信度
                detected: detectedLanguage,
                mapped: true
            };
        }
        
        // 返回默认语言
        const defaultLanguage = await this.getLanguageById(2); // English
        return {
            language: defaultLanguage,
            confidence: 0.5,
            detected: detectedLanguage,
            mapped: false
        };
    }
    
    /**
     * 同步转换语言数据为API格式 - 主要接口（带缓存优化）
     * @param {Array} languageIds - 语言ID数组
     * @returns {Object} API格式的语言对象
     */
    transformForAPISync(languageIds) {
        // 性能优化：缓存检查
        const cacheKey = JSON.stringify(languageIds);
        if (this.cache.apiTransformCache.has(cacheKey)) {
            return { ...this.cache.apiTransformCache.get(cacheKey) };
        }
        
        try {
            const validation = this.validateLanguageIdsSync(languageIds);
            
            if (!validation.valid) {
                throw new Error(validation.error);
            }
            
            // 转换为API要求的对象格式 {"0":"1","1":"2","2":"3"}
            const languageObject = {};
            validation.validIds.forEach((id, index) => {
                languageObject[index.toString()] = id.toString();
            });
            
            // 缓存结果
            this.cacheApiTransformResult(cacheKey, languageObject);
            
            return languageObject;
        } catch (error) {
            getLogger().logError('API语言数据转换失败', error);
            // 返回默认英语
            const fallback = { "0": "2" };
            this.cacheApiTransformResult(cacheKey, fallback);
            return fallback;
        }
    }

    /**
     * 异步转换语言数据为API格式 - 向后兼容接口
     * @param {Array} languageIds - 语言ID数组
     * @returns {Promise<Object>} API格式的语言对象
     */
    async transformForAPI(languageIds) {
        try {
            return this.transformForAPISync(languageIds);
        } catch (error) {
            getLogger().logError('API语言数据转换失败', error);
            // 返回默认英语
            return { "0": "2" };
        }
    }
    
    /**
     * 订阅语言数据变化
     * @param {Function} callback - 回调函数
     * @returns {Function} 取消订阅函数
     */
    subscribe(callback) {
        this.listeners.add(callback);
        return () => this.listeners.delete(callback);
    }
    
    /**
     * 清除缓存（增强版）
     * @param {string} type - 缓存类型 ('all', 'main', 'validation', 'transform', 'defaults')
     */
    clearCache(type = 'all') {
        switch (type) {
            case 'all':
                this.cache.data = null;
                this.cache.timestamp = null;
                this.cache.enabledOnly = null;
                this.cache.enabledOnlyTimestamp = null;
                this.cache.defaultSelections.clear();
                this.cache.validationCache.clear();
                this.cache.apiTransformCache.clear();
                this.performance.hits = 0;
                this.performance.misses = 0;
                this.performance.totalRequests = 0;
                break;
            case 'main':
                this.cache.data = null;
                this.cache.timestamp = null;
                this.cache.enabledOnly = null;
                this.cache.enabledOnlyTimestamp = null;
                break;
            case 'validation':
                this.cache.validationCache.clear();
                break;
            case 'transform':
                this.cache.apiTransformCache.clear();
                break;
            case 'defaults':
                this.cache.defaultSelections.clear();
                break;
        }
        
        getLogger().logInfo(`语言数据缓存已清除: ${type}`, 'info');
        
        // 定期清理过期缓存
        this.scheduleCleanup();
    }
    
    /**
     * 后台刷新缓存（不阻塞主线程）
     */
    refreshCacheInBackground() {
        setTimeout(async () => {
            try {
                const languages = await this.fetchLanguageData();
                this.updateCache(languages);
                this.notifyListeners('languages:updated', languages);
                getLogger().logInfo('后台缓存刷新完成');
            } catch (error) {
                getLogger().logWarning('后台缓存刷新失败', error);
            }
        }, 0);
    }

    /**
     * 获取管理器状态
     */
    getStatus() {
        return {
            cacheValid: this.isCacheValid(),
            cacheAge: this.cache.timestamp ? Date.now() - this.cache.timestamp : null,
            listenersCount: this.listeners.size,
            standardLanguagesCount: this.standardLanguages.length
        };
    }
    
    // ==================== 私有方法 ====================
    
    /**
     * 从API或静态数据获取语言数据
     */
    async fetchLanguageData() {
        try {
            // 尝试从API获取
            const apiService = window.OTA.getService('apiService');
            if (apiService && typeof apiService.getLanguages === 'function') {
                const apiLanguages = await apiService.getLanguages();
                if (apiLanguages && apiLanguages.length > 0) {
                    // 合并API数据与标准数据的元数据
                    return this.mergeLanguageData(apiLanguages, this.standardLanguages);
                }
            }
        } catch (error) {
            getLogger().logWarning('API获取语言数据失败，使用标准数据', error);
        }
        
        // 返回标准数据
        return [...this.standardLanguages];
    }
    
    /**
     * 合并API数据和标准数据
     */
    mergeLanguageData(apiData, standardData) {
        const merged = [];
        
        // 以API数据为主，补充标准数据的元信息
        apiData.forEach(apiLang => {
            const standardLang = standardData.find(std => std.id === apiLang.id);
            merged.push({
                ...apiLang,
                code: standardLang?.code || this.generateCodeFromName(apiLang.name),
                enabled: standardLang?.enabled ?? true,
                priority: standardLang?.priority ?? 999,
                default: standardLang?.default ?? false
            });
        });
        
        return merged;
    }
    
    /**
     * 从名称生成语言代码
     */
    generateCodeFromName(name) {
        const match = name.match(/\(([^)]+)\)$/);
        return match ? match[1].toLowerCase() : name.toLowerCase().substring(0, 3);
    }
    
    /**
     * 检查缓存是否有效
     */
    isCacheValid() {
        return this.cache.data && 
               this.cache.timestamp && 
               (Date.now() - this.cache.timestamp) < this.cache.maxAge;
    }
    
    /**
     * 更新缓存
     */
    updateCache(data) {
        this.cache.data = [...data];
        this.cache.timestamp = Date.now();
    }
    
    /**
     * 通知监听器
     */
    notifyListeners(event, data) {
        this.listeners.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                getLogger().logError('语言管理器监听器回调失败', error);
            }
        });
    }
    
    /**
     * 智能默认选择
     */
    getSmartDefaultSelection(languages, context) {
        switch (context) {
            case 'ai-analysis':
                // AI分析上下文：返回最常用的语言
                return [2, 3, 4]; // English, Malay, Chinese
                
            case 'multi-order':
                // 多订单上下文：返回所有启用的语言
                return languages.filter(lang => lang.enabled).map(lang => lang.id);
                
            case 'form':
            default:
                // 表单上下文：返回默认语言
                const defaultLangs = languages.filter(lang => lang.default);
                return defaultLangs.length > 0 ? 
                    defaultLangs.map(lang => lang.id) : 
                    [2]; // English fallback
        }
    }
    
    /**
     * 同步获取用户偏好选择
     */
    getUserPreferenceSelectionSync() {
        try {
            const appState = window.OTA.getService('appState');
            const userPreference = appState.get('user.languagePreference');
            if (userPreference && Array.isArray(userPreference)) {
                const validation = this.validateLanguageIdsSync(userPreference);
                return validation.validIds;
            }
        } catch (error) {
            getLogger().logWarning('获取用户语言偏好失败', error);
        }
        
        return [2]; // Fallback to English
    }

    /**
     * 异步获取用户偏好选择 - 向后兼容
     */
    async getUserPreferenceSelection() {
        try {
            return this.getUserPreferenceSelectionSync();
        } catch (error) {
            getLogger().logWarning('获取用户语言偏好失败', error);
            return [2]; // Fallback to English
        }
    }
    
    /**
     * 建议替代语言
     */
    suggestAlternatives(invalidIds) {
        const suggestions = [];
        
        invalidIds.forEach(id => {
            // 基于ID范围的建议
            if (id >= 1 && id <= 15) {
                const closestValid = this.standardLanguages
                    .map(lang => ({ 
                        id: lang.id, 
                        distance: Math.abs(lang.id - id),
                        name: lang.name 
                    }))
                    .sort((a, b) => a.distance - b.distance)[0];
                
                if (closestValid) {
                    suggestions.push({
                        invalidId: id,
                        suggestedId: closestValid.id,
                        suggestedName: closestValid.name,
                        reason: `最接近的有效ID`
                    });
                }
            }
        });
        
        return suggestions;
    }
    
    /**
     * 模糊匹配语言
     */
    async fuzzyMatchLanguage(input) {
        const languages = await this.getLanguages();
        const inputLower = input.toLowerCase();
        
        for (const lang of languages) {
            const nameLower = lang.name.toLowerCase();
            const codeLower = (lang.code || '').toLowerCase();
            
            // 检查是否包含输入字符串
            if (nameLower.includes(inputLower) || 
                codeLower.includes(inputLower) ||
                inputLower.includes(nameLower) ||
                inputLower.includes(codeLower)) {
                
                return {
                    language: lang,
                    confidence: this.calculateSimilarity(inputLower, nameLower)
                };
            }
        }
        
        return null;
    }
    
    /**
     * 计算字符串相似度
     */
    calculateSimilarity(str1, str2) {
        const longer = str1.length > str2.length ? str1 : str2;
        const shorter = str1.length > str2.length ? str2 : str1;
        
        if (longer.length === 0) return 1.0;
        
        const editDistance = this.levenshteinDistance(longer, shorter);
        return (longer.length - editDistance) / longer.length;
    }
    
    /**
     * 计算编辑距离
     */
    levenshteinDistance(str1, str2) {
        const matrix = [];
        
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        
        return matrix[str2.length][str1.length];
    }
    
    // ==================== 性能优化方法 ====================
    
    /**
     * 检查enabledOnly缓存是否有效
     */
    isEnabledOnlyCacheValid() {
        return this.cache.enabledOnly && 
               this.cache.enabledOnlyTimestamp && 
               (Date.now() - this.cache.enabledOnlyTimestamp) < this.cache.maxAge;
    }
    
    /**
     * 更新性能监控指标
     */
    updatePerformanceMetrics(startTime) {
        const duration = performance.now() - startTime;
        this.performance.averageResponseTime = 
            (this.performance.averageResponseTime * (this.performance.totalRequests - 1) + duration) / 
            this.performance.totalRequests;
    }
    
    /**
     * 缓存验证结果（带LRU清理）
     */
    cacheValidationResult(key, result) {
        if (this.cache.validationCache.size >= this.cache.validationCacheMaxSize) {
            // LRU清理：删除最早的条目
            const firstKey = this.cache.validationCache.keys().next().value;
            this.cache.validationCache.delete(firstKey);
        }
        this.cache.validationCache.set(key, result);
    }
    
    /**
     * 缓存API转换结果（带LRU清理）
     */
    cacheApiTransformResult(key, result) {
        if (this.cache.apiTransformCache.size >= this.cache.apiTransformCacheMaxSize) {
            // LRU清理：删除最早的条目
            const firstKey = this.cache.apiTransformCache.keys().next().value;
            this.cache.apiTransformCache.delete(firstKey);
        }
        this.cache.apiTransformCache.set(key, result);
    }
    
    /**
     * 定期清理过期缓存
     */
    scheduleCleanup() {
        const now = Date.now();
        if (now - this.performance.lastCleanup > 30 * 60 * 1000) { // 30分钟清理一次
            this.performCleanup();
            this.performance.lastCleanup = now;
        }
    }
    
    /**
     * 执行缓存清理
     */
    performCleanup() {
        // 清理过期的默认选择缓存（如果主缓存过期）
        if (!this.isCacheValid()) {
            this.cache.defaultSelections.clear();
        }
        
        // 清理enabledOnly缓存（如果过期）
        if (!this.isEnabledOnlyCacheValid()) {
            this.cache.enabledOnly = null;
            this.cache.enabledOnlyTimestamp = null;
        }
        
        getLogger().logInfo('语言管理器：定期缓存清理完成', 'info');
    }
    
    /**
     * 获取性能统计信息
     */
    getPerformanceStats() {
        const hitRate = this.performance.totalRequests > 0 ? 
            (this.performance.hits / this.performance.totalRequests * 100).toFixed(2) : 0;
            
        return {
            totalRequests: this.performance.totalRequests,
            cacheHits: this.performance.hits,
            cacheMisses: this.performance.misses,
            hitRate: `${hitRate}%`,
            averageResponseTime: `${this.performance.averageResponseTime.toFixed(2)}ms`,
            cacheStatus: {
                main: this.isCacheValid(),
                enabledOnly: this.isEnabledOnlyCacheValid(),
                defaultSelections: this.cache.defaultSelections.size,
                validationCache: this.cache.validationCache.size,
                apiTransformCache: this.cache.apiTransformCache.size
            }
        };
    }
    
    /**
     * 获取增强的管理器状态
     */
    getEnhancedStatus() {
        return {
            ...this.getStatus(),
            performance: this.getPerformanceStats(),
            memoryUsage: {
                validationCacheEntries: this.cache.validationCache.size,
                apiTransformCacheEntries: this.cache.apiTransformCache.size,
                defaultSelectionContexts: this.cache.defaultSelections.size,
                listeners: this.listeners.size
            }
        };
    }
}

// 创建单例实例
let languageManagerInstance = null;

/**
 * 获取语言管理器实例（单例模式）
 * @returns {LanguageManager} 语言管理器实例
 */
function getLanguageManager() {
    if (!languageManagerInstance) {
        languageManagerInstance = new LanguageManager();
        
        // 添加到全局命名空间
        if (typeof window !== 'undefined') {
            window.OTA = window.OTA || {};
            window.OTA.languageManager = languageManagerInstance;
        }
        
        getLogger().logInfo('语言管理器初始化完成');
    }
    
    return languageManagerInstance;
}

// 向后兼容的全局访问
if (typeof window !== 'undefined') {
    window.getLanguageManager = getLanguageManager;
}

// 导出（如果支持模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { LanguageManager, getLanguageManager };
}