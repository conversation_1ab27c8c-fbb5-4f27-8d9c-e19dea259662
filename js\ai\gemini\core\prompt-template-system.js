/**
 * @CORE_COMPONENT 提示词模板系统
 * 🏷️ 标签: @TEMPLATE_SYSTEM @PROMPT_GENERATOR
 * 📝 说明: 动态提示词模板系统，支持OTA特定的提示词组装和个性化定制
 * 🎯 目标: 为不同OTA渠道提供专业化的提示词模板，提升AI解析准确性
 */

(function() {
    'use strict';
    
    // 延迟获取依赖，确保加载顺序
    
    function getRegistry() {
        return window.OTA.Registry;
    }
    
    function getConfigManager() {
        return window.OTA.Registry?.getService('config-manager');
    }
    
    /**
     * 提示词模板系统类
     * 负责管理和生成各种OTA渠道的专用提示词模板
     */
    class PromptTemplateSystem {
        constructor() {
            this.logger = window.OTA.getService('logger');
            this.templates = new Map(); // 模板缓存
            this.variables = new Map(); // 变量定义
            this.processors = new Map(); // 模板处理器
            this.initialized = false;
            
            this.logger.log('🎯 提示词模板系统初始化开始', 'info');
        }
        
        /**
         * 初始化模板系统
         * @returns {Promise<void>}
         */
        async initialize() {
            if (this.initialized) return;
            
            try {
                // 加载基础模板
                await this.loadBaseTemplates();
                
                // 加载OTA特定模板
                await this.loadOTATemplates();
                
                // 注册模板变量
                this.registerTemplateVariables();
                
                // 注册模板处理器
                this.registerTemplateProcessors();
                
                this.initialized = true;
                this.logger.log('✅ 提示词模板系统初始化完成', 'info');
            } catch (error) {
                this.logger.logError('❌ 提示词模板系统初始化失败', error);
                throw error;
            }
        }
        
        /**
         * 加载基础模板
         * @private
         */
        async loadBaseTemplates() {
            // 基础订单解析模板
            this.templates.set('base-order-parsing', {
                name: '基础订单解析模板',
                template: `
你是一个专业的旅游订单解析助手。请仔细分析以下订单信息，并按照指定的JSON格式返回结构化数据。

订单内容：
{{ORDER_TEXT}}

请提取以下信息：
1. 客户信息：姓名、联系电话、邮箱
2. 行程信息：接送地点、目的地、日期、时间
3. 服务信息：服务类型、车型要求、乘客人数
4. 特殊要求：行李数量、特殊需求、备注信息
5. 订单信息：订单号、OTA平台、价格信息

返回格式：
{
  "customer_name": "客户姓名",
  "phone": "联系电话",
  "email": "邮箱地址",
  "pickup_location": "接送地点",
  "destination": "目的地",
  "pickup_date": "接送日期(DD-MM-YYYY)",
  "pickup_time": "接送时间(HH:MM)",
  "service_type": "服务类型",
  "car_type": "车型要求",
  "passenger_count": "乘客人数",
  "luggage_count": "行李数量",
  "special_requirements": "特殊要求",
  "order_reference": "订单参考号",
  "ota_platform": "OTA平台",
  "price": "价格信息"
}

请确保：
- 所有日期使用DD-MM-YYYY格式
- 所有时间使用24小时制HH:MM格式
- 如果某些信息不存在，请返回null
- 保持JSON格式的完整性和正确性
                `,
                variables: ['ORDER_TEXT'],
                category: 'base'
            });
            
            // 多订单解析模板
            this.templates.set('multi-order-parsing', {
                name: '多订单解析模板',
                template: `
你是一个专业的旅游订单解析助手。以下内容包含多个订单信息，请分别解析每个订单。

订单内容：
{{ORDER_TEXT}}

请识别并分离每个独立的订单，然后为每个订单提取以下信息：
{{BASE_EXTRACTION_RULES}}

返回格式：
{
  "orders": [
    {
      "order_index": 1,
      "customer_name": "客户姓名",
      "phone": "联系电话",
      // ... 其他字段
    },
    {
      "order_index": 2,
      // ... 第二个订单信息
    }
  ],
  "total_orders": "订单总数"
}

请确保：
- 正确识别订单边界
- 每个订单都有完整的信息
- 保持订单的原始顺序
                `,
                variables: ['ORDER_TEXT', 'BASE_EXTRACTION_RULES'],
                category: 'multi'
            });
            
            // 图片解析模板
            this.templates.set('image-analysis', {
                name: '图片订单解析模板',
                template: `
你是一个专业的图片内容分析助手。请仔细分析上传的图片，识别其中的文字信息，并按照旅游订单的格式进行解析。

图片分析要求：
1. 识别图片中的所有文字内容
2. 判断这是否为旅游订单相关的图片
3. 提取订单中的关键信息

{{OTA_SPECIFIC_RULES}}

请按照以下JSON格式返回：
{
  "image_type": "图片类型(订单截图/确认邮件/其他)",
  "text_content": "识别出的文字内容",
  "is_travel_order": true/false,
  "extracted_data": {
    // 标准订单字段
  },
  "confidence": "解析置信度(0-1)"
}
                `,
                variables: ['OTA_SPECIFIC_RULES'],
                category: 'image'
            });
        }
        
        /**
         * 加载OTA特定模板
         * @private
         */
        async loadOTATemplates() {
            // Fliggy飞猪模板
            this.templates.set('fliggy-specific', {
                name: 'Fliggy飞猪专用模板',
                template: `
你正在处理来自飞猪旅行(Fliggy)的订单。飞猪订单具有以下特点：

特殊识别规则：
- 订单号通常以"FL"开头，后跟数字
- 包含"飞猪旅行"、"Fliggy"等关键词
- 可能包含支付宝相关信息
- 日期格式可能为中文格式

{{BASE_TEMPLATE}}

飞猪特定字段映射：
- 预订人 → customer_name
- 手机号码 → phone  
- 出发地 → pickup_location
- 目的地 → destination
- 出发日期 → pickup_date
- 出发时间 → pickup_time
- 订单编号 → order_reference

请特别注意：
- 正确识别飞猪订单号格式
- 处理中文日期格式转换
- 识别支付宝支付信息
                `,
                variables: ['BASE_TEMPLATE'],
                category: 'ota',
                platform: 'fliggy'
            });
            
            // Agoda模板
            this.templates.set('agoda-specific', {
                name: 'Agoda专用模板',
                template: `
你正在处理来自Agoda的订单。Agoda订单具有以下特点：

特殊识别规则：
- 订单号通常以"AG"开头或包含"Agoda"
- 多语言内容（英文为主）
- 包含酒店预订信息
- 可能包含接送服务信息

{{BASE_TEMPLATE}}

Agoda特定字段映射：
- Guest Name → customer_name
- Contact Number → phone
- Email Address → email
- Pickup Location → pickup_location
- Drop-off Location → destination
- Pickup Date → pickup_date
- Pickup Time → pickup_time
- Booking Reference → order_reference

请特别注意：
- 处理英文订单内容
- 正确识别Agoda订单格式
- 区分酒店预订和接送服务
                `,
                variables: ['BASE_TEMPLATE'],
                category: 'ota',
                platform: 'agoda'
            });
            
            // Booking.com模板
            this.templates.set('booking-specific', {
                name: 'Booking.com专用模板',
                template: `
你正在处理来自Booking.com的订单。Booking.com订单具有以下特点：

特殊识别规则：
- 订单号通常包含"Booking"或特定格式
- 多语言支持
- 详细的预订确认信息
- 可能包含取消政策

{{BASE_TEMPLATE}}

Booking.com特定字段映射：
- Main Guest → customer_name
- Phone → phone
- Email → email
- Pickup Address → pickup_location
- Destination → destination
- Date → pickup_date
- Time → pickup_time
- Confirmation Number → order_reference

请特别注意：
- 识别Booking.com确认邮件格式
- 处理多语言内容
- 提取准确的预订信息
                `,
                variables: ['BASE_TEMPLATE'],
                category: 'ota',
                platform: 'booking'
            });
            
            // 通用降级模板
            this.templates.set('generic-fallback', {
                name: '通用降级模板',
                template: `
你正在处理一个未识别OTA平台的订单。请使用通用规则进行解析：

{{BASE_TEMPLATE}}

通用识别规则：
- 寻找常见的订单关键词
- 识别联系信息模式
- 提取日期时间信息
- 查找地址和位置信息

请特别注意：
- 使用最宽泛的识别规则
- 提供最大的兼容性
- 标记不确定的信息
- 建议可能的OTA平台
                `,
                variables: ['BASE_TEMPLATE'],
                category: 'fallback',
                platform: 'generic'
            });
        }
        
        /**
         * 注册模板变量
         * @private
         */
        registerTemplateVariables() {
            // 基础变量
            this.variables.set('ORDER_TEXT', {
                name: '订单文本',
                description: '原始订单文本内容',
                type: 'string',
                required: true
            });
            
            this.variables.set('BASE_TEMPLATE', {
                name: '基础模板',
                description: '基础订单解析模板内容',
                type: 'template',
                required: true,
                resolver: () => this.templates.get('base-order-parsing')?.template || ''
            });
            
            this.variables.set('BASE_EXTRACTION_RULES', {
                name: '基础提取规则',
                description: '基础字段提取规则说明',
                type: 'string',
                required: false,
                default: '客户信息、行程信息、服务信息、订单信息'
            });
            
            this.variables.set('OTA_SPECIFIC_RULES', {
                name: 'OTA特定规则',
                description: '特定OTA平台的解析规则',
                type: 'string',
                required: false,
                resolver: (context) => this.getOTASpecificRules(context.platform)
            });
            
            // 动态变量
            this.variables.set('CURRENT_DATE', {
                name: '当前日期',
                description: '当前系统日期',
                type: 'string',
                resolver: () => new Date().toLocaleDateString('en-GB')
            });
            
            this.variables.set('SUPPORTED_LANGUAGES', {
                name: '支持的语言',
                description: '系统支持的语言列表',
                type: 'array',
                default: ['中文', '英文', '马来文']
            });
        }
        
        /**
         * 注册模板处理器
         * @private
         */
        registerTemplateProcessors() {
            // 变量替换处理器
            this.processors.set('variable-replacement', {
                name: '变量替换处理器',
                process: (template, context) => {
                    let processedTemplate = template;
                    
                    // 替换所有变量
                    const variablePattern = /\{\{([^}]+)\}\}/g;
                    processedTemplate = processedTemplate.replace(variablePattern, (match, variableName) => {
                        const variable = this.variables.get(variableName.trim());
                        if (!variable) {
                            this.logger.log(`⚠️ 未找到模板变量: ${variableName}`, 'warning');
                            return match;
                        }
                        
                        // 解析变量值
                        if (variable.resolver) {
                            return variable.resolver(context);
                        } else if (context[variableName]) {
                            return context[variableName];
                        } else if (variable.default !== undefined) {
                            return variable.default;
                        } else {
                            return match;
                        }
                    });
                    
                    return processedTemplate;
                }
            });
            
            // 条件处理器
            this.processors.set('conditional', {
                name: '条件处理器',
                process: (template, context) => {
                    // 处理条件语句 {{#if condition}}...{{/if}}
                    const conditionalPattern = /\{\{#if\s+([^}]+)\}\}([\s\S]*?)\{\{\/if\}\}/g;
                    return template.replace(conditionalPattern, (match, condition, content) => {
                        // 简单的条件评估
                        if (this.evaluateCondition(condition, context)) {
                            return content;
                        }
                        return '';
                    });
                }
            });
            
            // 循环处理器
            this.processors.set('loop', {
                name: '循环处理器',
                process: (template, context) => {
                    // 处理循环语句 {{#each items}}...{{/each}}
                    const loopPattern = /\{\{#each\s+([^}]+)\}\}([\s\S]*?)\{\{\/each\}\}/g;
                    return template.replace(loopPattern, (match, arrayName, content) => {
                        const array = context[arrayName];
                        if (!Array.isArray(array)) return '';
                        
                        return array.map((item, index) => {
                            return content
                                .replace(/\{\{this\}\}/g, item)
                                .replace(/\{\{@index\}\}/g, index);
                        }).join('');
                    });
                }
            });
        }
        
        /**
         * 生成提示词
         * @param {string} templateName - 模板名称
         * @param {Object} context - 上下文数据
         * @returns {Promise<string>} 生成的提示词
         */
        async generatePrompt(templateName, context = {}) {
            if (!this.initialized) {
                await this.initialize();
            }
            
            try {
                const template = this.templates.get(templateName);
                if (!template) {
                    throw new Error(`模板不存在: ${templateName}`);
                }
                
                let prompt = template.template;
                
                // 应用所有处理器
                for (const [processorName, processor] of this.processors) {
                    try {
                        prompt = processor.process(prompt, context);
                    } catch (error) {
                        this.logger.logError(`模板处理器 ${processorName} 执行失败`, error);
                    }
                }
                
                this.logger.log(`✅ 提示词生成成功: ${templateName}`, 'info');
                return prompt;
            } catch (error) {
                this.logger.logError(`提示词生成失败: ${templateName}`, error);
                throw error;
            }
        }
        
        /**
         * 根据OTA平台获取最佳模板
         * @param {string} platform - OTA平台名称
         * @param {string} type - 模板类型 (order/multi/image)
         * @returns {string} 模板名称
         */
        getBestTemplate(platform, type = 'order') {
            // 优先使用平台特定模板
            const specificTemplate = `${platform}-specific`;
            if (this.templates.has(specificTemplate)) {
                return specificTemplate;
            }
            
            // 根据类型选择基础模板
            switch (type) {
                case 'multi':
                    return 'multi-order-parsing';
                case 'image':
                    return 'image-analysis';
                case 'order':
                default:
                    return 'base-order-parsing';
            }
        }
        
        /**
         * 获取OTA特定规则
         * @param {string} platform - OTA平台
         * @returns {string} 特定规则文本
         * @private
         */
        getOTASpecificRules(platform) {
            const rules = {
                fliggy: '注意飞猪订单的中文格式和支付宝支付信息',
                agoda: '注意Agoda的英文格式和酒店预订信息',
                booking: '注意Booking.com的多语言格式和确认邮件结构',
                generic: '使用通用识别规则，适应各种格式'
            };
            
            return rules[platform] || rules.generic;
        }
        
        /**
         * 评估条件表达式
         * @param {string} condition - 条件表达式
         * @param {Object} context - 上下文
         * @returns {boolean} 条件结果
         * @private
         */
        evaluateCondition(condition, context) {
            // 简单的条件评估实现
            try {
                // 支持简单的存在性检查
                if (condition.includes('exists')) {
                    const variable = condition.replace('exists', '').trim();
                    return context[variable] !== undefined && context[variable] !== null;
                }
                
                // 支持简单的相等性检查
                if (condition.includes('==')) {
                    const [left, right] = condition.split('==').map(s => s.trim());
                    return context[left] == right.replace(/['"]/g, '');
                }
                
                // 默认检查变量是否为真值
                return !!context[condition.trim()];
            } catch (error) {
                this.logger.logError(`条件评估失败: ${condition}`, error);
                return false;
            }
        }
        
        /**
         * 获取系统状态
         * @returns {Object} 系统状态信息
         */
        getStatus() {
            return {
                initialized: this.initialized,
                templateCount: this.templates.size,
                variableCount: this.variables.size,
                processorCount: this.processors.size,
                availableTemplates: Array.from(this.templates.keys()),
                availableVariables: Array.from(this.variables.keys())
            };
        }
    }
    
    // 创建全局实例
    const promptTemplateSystem = new PromptTemplateSystem();
    
    // 注册到服务注册中心
    if (typeof window !== 'undefined') {
        window.OTA = window.OTA || {};
        window.OTA.promptTemplateSystem = promptTemplateSystem;
        
        // 注册到服务注册中心
        if (window.OTA.Registry) {
            window.OTA.Registry.registerService('prompt-template-system', promptTemplateSystem, {
                dependencies: ['logger', 'config-manager'],
                description: '提示词模板系统，支持动态模板生成和OTA特定优化'
            });
        }
    }
    
    // Node.js环境支持
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = PromptTemplateSystem;
    }
    
})();
