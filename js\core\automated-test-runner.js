/**
 * @OTA_CORE 自动化测试执行器
 * 🏷️ 标签: @AUTOMATED_TEST_RUNNER @CI_CD_INTEGRATION
 * 📝 说明: 提供自动化测试执行、持续集成支持和测试流水线管理功能
 * ⚠️ 警告: 核心CI/CD基础设施，请勿重复开发
 */

(function() {
    'use strict';

    // 延迟获取依赖，确保加载顺序
    function getTestCoverageEngine() {
        return window.OTA?.testCoverageEngine || window.getTestCoverageEngine?.();
    }

    function getConfigCenter() {
        return window.OTA?.configCenter || window.getConfigCenter?.();
    }

    /**
     * @OTA_CORE 自动化测试执行器类
     * 提供自动化测试执行和CI/CD集成功能
     */
    class AutomatedTestRunner {
        constructor() {
            this.logger = window.OTA.getService('logger');
            this.coverageEngine = getTestCoverageEngine();
            this.configCenter = getConfigCenter();
            
            // 测试流水线配置
            this.pipelineConfig = this.loadPipelineConfig();
            
            // 测试队列
            this.testQueue = [];
            this.isRunning = false;
            this.currentExecution = null;
            
            // 执行历史
            this.executionHistory = [];
            this.maxHistorySize = 50;
            
            // 调度器
            this.scheduler = null;
            this.watchMode = false;
            
            // 通知系统
            this.notificationHandlers = new Map();
            
            this.initialize();
            
            this.logger.log('✅ 自动化测试执行器已初始化', 'info', {
                pipelineEnabled: this.pipelineConfig.enabled,
                schedulerEnabled: this.pipelineConfig.scheduler.enabled,
                watchMode: this.pipelineConfig.watchMode
            });
        }

        /**
         * 加载流水线配置
         * @returns {Object} 流水线配置
         */
        loadPipelineConfig() {
            const defaultConfig = {
                enabled: true,
                parallel: false,
                maxConcurrency: 3,
                timeout: 300000, // 5分钟
                retryAttempts: 2,
                retryDelay: 5000,
                failFast: false,
                scheduler: {
                    enabled: false,
                    interval: 3600000, // 1小时
                    cron: null
                },
                watchMode: false,
                watchPatterns: ['*.js', '*.html'],
                notifications: {
                    slack: false,
                    email: false,
                    webhook: null
                },
                reporting: {
                    junit: false,
                    html: true,
                    json: true
                },
                qualityGates: {
                    enforced: true,
                    breakOnFailure: true
                }
            };

            if (this.configCenter) {
                const testConfig = this.configCenter.getConfig('testing') || {};
                return {
                    ...defaultConfig,
                    ...testConfig.automation,
                    ...testConfig.pipeline
                };
            }

            return defaultConfig;
        }

        /**
         * 初始化执行器
         */
        initialize() {
            // 设置调度器
            if (this.pipelineConfig.scheduler.enabled) {
                this.setupScheduler();
            }
            
            // 设置文件监控
            if (this.pipelineConfig.watchMode) {
                this.setupFileWatcher();
            }
            
            // 注册默认通知处理器
            this.setupDefaultNotifications();
        }

        /**
         * 添加测试到队列
         * @param {Object} testConfig - 测试配置
         */
        queueTest(testConfig) {
            const testItem = {
                id: this.generateTestId(),
                config: testConfig,
                status: 'queued',
                queuedAt: Date.now(),
                attempts: 0,
                maxAttempts: this.pipelineConfig.retryAttempts + 1
            };
            
            this.testQueue.push(testItem);
            
            this.logger.log(`测试已加入队列: ${testItem.id}`, 'debug', testConfig);
            
            // 如果当前没有运行测试，立即开始
            if (!this.isRunning) {
                this.processQueue();
            }
        }

        /**
         * 运行完整测试套件
         * @param {Object} options - 运行选项
         * @returns {Promise<Object>} 执行结果
         */
        async runFullTestSuite(options = {}) {
            const config = {
                suites: ['unit', 'integration', 'functional', 'performance'],
                parallel: options.parallel || this.pipelineConfig.parallel,
                timeout: options.timeout || this.pipelineConfig.timeout,
                ...options
            };
            
            return this.queueTest({
                type: 'full-suite',
                config,
                priority: 'high'
            });
        }

        /**
         * 运行特定测试套件
         * @param {string} suiteName - 套件名称
         * @param {Object} options - 运行选项
         * @returns {Promise<Object>} 执行结果
         */
        async runTestSuite(suiteName, options = {}) {
            const config = {
                suite: suiteName,
                timeout: options.timeout || this.pipelineConfig.timeout,
                ...options
            };
            
            return this.queueTest({
                type: 'single-suite',
                config,
                priority: options.priority || 'normal'
            });
        }

        /**
         * 处理测试队列
         */
        async processQueue() {
            if (this.isRunning || this.testQueue.length === 0) {
                return;
            }

            this.isRunning = true;
            
            try {
                while (this.testQueue.length > 0) {
                    const testItem = this.testQueue.shift();
                    await this.executeTest(testItem);
                    
                    // 如果启用了快速失败且测试失败，停止执行
                    if (this.pipelineConfig.failFast && testItem.status === 'failed') {
                        this.logger.log('快速失败模式：停止执行剩余测试', 'warning');
                        break;
                    }
                }
            } catch (error) {
                this.logger.logError('测试队列处理失败', error);
            } finally {
                this.isRunning = false;
            }
        }

        /**
         * 执行单个测试
         * @param {Object} testItem - 测试项
         */
        async executeTest(testItem) {
            const startTime = Date.now();
            testItem.status = 'running';
            testItem.startedAt = startTime;
            testItem.attempts++;
            
            this.currentExecution = testItem;
            
            this.logger.log(`开始执行测试: ${testItem.id}`, 'info', testItem.config);
            
            try {
                // 设置超时
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('测试执行超时')), this.pipelineConfig.timeout);
                });
                
                // 执行测试
                const executionPromise = this.performTestExecution(testItem);
                
                const result = await Promise.race([executionPromise, timeoutPromise]);
                
                testItem.status = 'completed';
                testItem.result = result;
                testItem.completedAt = Date.now();
                testItem.duration = testItem.completedAt - startTime;
                
                // 检查质量门禁
                if (this.pipelineConfig.qualityGates.enforced) {
                    const qualityCheck = this.checkQualityGates(result);
                    if (!qualityCheck.passed) {
                        testItem.status = 'failed';
                        testItem.qualityGateFailures = qualityCheck.failures;
                        
                        if (this.pipelineConfig.qualityGates.breakOnFailure) {
                            throw new Error(`质量门禁失败: ${qualityCheck.failures.join(', ')}`);
                        }
                    }
                }
                
                this.logger.log(`测试执行完成: ${testItem.id} (${testItem.duration}ms)`, 'info');
                
                // 发送成功通知
                await this.sendNotification('test_success', {
                    testId: testItem.id,
                    duration: testItem.duration,
                    result
                });
                
            } catch (error) {
                testItem.status = 'failed';
                testItem.error = error.message;
                testItem.completedAt = Date.now();
                testItem.duration = testItem.completedAt - startTime;
                
                this.logger.logError(`测试执行失败: ${testItem.id}`, error);
                
                // 重试逻辑
                if (testItem.attempts < testItem.maxAttempts) {
                    this.logger.log(`准备重试测试: ${testItem.id} (${testItem.attempts}/${testItem.maxAttempts})`, 'warning');
                    
                    // 延迟后重新加入队列
                    setTimeout(() => {
                        testItem.status = 'queued';
                        this.testQueue.unshift(testItem); // 优先执行重试
                    }, this.pipelineConfig.retryDelay);
                    
                    return;
                }
                
                // 发送失败通知
                await this.sendNotification('test_failure', {
                    testId: testItem.id,
                    error: error.message,
                    attempts: testItem.attempts
                });
            } finally {
                // 添加到执行历史
                this.addToHistory(testItem);
                this.currentExecution = null;
            }
        }

        /**
         * 执行具体的测试逻辑
         * @param {Object} testItem - 测试项
         * @returns {Promise<Object>} 执行结果
         */
        async performTestExecution(testItem) {
            const { type, config } = testItem.config;
            
            if (!this.coverageEngine) {
                throw new Error('测试覆盖率引擎未初始化');
            }
            
            switch (type) {
                case 'full-suite':
                    return await this.coverageEngine.runAllTestSuites();
                    
                case 'single-suite':
                    return await this.coverageEngine.runTestSuite(config.suite);
                    
                case 'custom':
                    return await this.executeCustomTest(config);
                    
                default:
                    throw new Error(`未知的测试类型: ${type}`);
            }
        }

        /**
         * 执行自定义测试
         * @param {Object} config - 测试配置
         * @returns {Promise<Object>} 执行结果
         */
        async executeCustomTest(config) {
            if (typeof config.executor === 'function') {
                return await config.executor(config);
            }
            
            throw new Error('自定义测试缺少执行器函数');
        }

        /**
         * 检查质量门禁
         * @param {Object} result - 测试结果
         * @returns {Object} 质量检查结果
         */
        checkQualityGates(result) {
            const failures = [];
            
            // 检查测试通过率
            if (result.report && result.report.summary) {
                const { passedSuites, totalSuites } = result.report.summary;
                const passRate = totalSuites > 0 ? (passedSuites / totalSuites) * 100 : 0;
                
                if (passRate < 90) { // 90%通过率要求
                    failures.push(`测试通过率不足: ${passRate.toFixed(1)}% < 90%`);
                }
            }
            
            // 检查覆盖率
            if (result.report && result.report.summary && result.report.summary.overallCoverage) {
                const coverage = parseFloat(result.report.summary.overallCoverage);
                if (coverage < 80) { // 80%覆盖率要求
                    failures.push(`代码覆盖率不足: ${coverage}% < 80%`);
                }
            }
            
            return {
                passed: failures.length === 0,
                failures
            };
        }

        /**
         * 设置调度器
         */
        setupScheduler() {
            if (this.pipelineConfig.scheduler.cron) {
                // 简化的cron实现
                this.logger.log('Cron调度器功能需要服务器端支持', 'warning');
            } else if (this.pipelineConfig.scheduler.interval) {
                this.scheduler = setInterval(() => {
                    this.logger.log('定时执行自动化测试', 'info');
                    this.runFullTestSuite({ priority: 'scheduled' });
                }, this.pipelineConfig.scheduler.interval);
            }
        }

        /**
         * 设置文件监控
         */
        setupFileWatcher() {
            // 简化的文件监控实现
            this.watchMode = true;
            this.logger.log('文件监控模式已启用（简化实现）', 'info');
            
            // 在实际环境中，这里会使用文件系统API或服务器端支持
        }

        /**
         * 设置默认通知处理器
         */
        setupDefaultNotifications() {
            // 控制台通知
            this.notificationHandlers.set('console', (type, data) => {
                const message = `[${type.toUpperCase()}] 测试通知: ${JSON.stringify(data)}`;
                if (type.includes('failure') || type.includes('error')) {
                    this.logger.logError(message);
                } else {
                    this.logger.log(message, 'info');
                }
            });
            
            // 浏览器通知（如果支持）
            if ('Notification' in window) {
                this.notificationHandlers.set('browser', async (type, data) => {
                    if (Notification.permission === 'granted') {
                        const title = type.includes('success') ? '✅ 测试成功' : '❌ 测试失败';
                        const body = `测试ID: ${data.testId}`;
                        
                        new Notification(title, { body, icon: '/favicon.ico' });
                    }
                });
            }
        }

        /**
         * 发送通知
         * @param {string} type - 通知类型
         * @param {Object} data - 通知数据
         */
        async sendNotification(type, data) {
            for (const [name, handler] of this.notificationHandlers) {
                try {
                    await handler(type, data);
                } catch (error) {
                    this.logger.logError(`通知处理器失败: ${name}`, error);
                }
            }
        }

        /**
         * 添加到执行历史
         * @param {Object} testItem - 测试项
         */
        addToHistory(testItem) {
            this.executionHistory.unshift({
                id: testItem.id,
                type: testItem.config.type,
                status: testItem.status,
                duration: testItem.duration,
                attempts: testItem.attempts,
                timestamp: testItem.completedAt || testItem.startedAt,
                error: testItem.error
            });
            
            // 限制历史记录大小
            if (this.executionHistory.length > this.maxHistorySize) {
                this.executionHistory = this.executionHistory.slice(0, this.maxHistorySize);
            }
        }

        /**
         * 生成测试ID
         * @returns {string} 测试ID
         */
        generateTestId() {
            return `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }

        /**
         * 停止当前执行
         */
        stopExecution() {
            if (this.currentExecution) {
                this.currentExecution.status = 'cancelled';
                this.logger.log(`测试执行已取消: ${this.currentExecution.id}`, 'warning');
            }
            
            this.testQueue = [];
            this.isRunning = false;
        }

        /**
         * 获取执行状态
         * @returns {Object} 执行状态
         */
        getExecutionStatus() {
            return {
                isRunning: this.isRunning,
                currentExecution: this.currentExecution,
                queueLength: this.testQueue.length,
                historyCount: this.executionHistory.length,
                lastExecution: this.executionHistory[0] || null
            };
        }

        /**
         * 获取执行历史
         * @param {number} limit - 限制数量
         * @returns {Array} 执行历史
         */
        getExecutionHistory(limit = 10) {
            return this.executionHistory.slice(0, limit);
        }

        /**
         * 清理资源
         */
        cleanup() {
            if (this.scheduler) {
                clearInterval(this.scheduler);
                this.scheduler = null;
            }
            
            this.stopExecution();
            this.notificationHandlers.clear();
            
            this.logger.log('自动化测试执行器已清理', 'info');
        }

        /**
         * 获取执行器状态
         * @returns {Object} 状态信息
         */
        getStatus() {
            return {
                isRunning: this.isRunning,
                watchMode: this.watchMode,
                schedulerActive: !!this.scheduler,
                queueLength: this.testQueue.length,
                executionHistory: this.executionHistory.length,
                pipelineConfig: this.pipelineConfig
            };
        }
    }

    // 创建全局实例
    const automatedTestRunner = new AutomatedTestRunner();

    // 导出到全局作用域
    window.OTA = window.OTA || {};
    window.OTA.automatedTestRunner = automatedTestRunner;
    window.OTA.getAutomatedTestRunner = () => automatedTestRunner;

    // 向后兼容
    window.getAutomatedTestRunner = () => automatedTestRunner;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('automatedTestRunner', automatedTestRunner, '@OTA_AUTOMATED_TEST_RUNNER');
        window.OTA.Registry.registerFactory('getAutomatedTestRunner', () => automatedTestRunner, '@OTA_AUTOMATED_TEST_RUNNER_FACTORY');
    }

    // 页面卸载时清理资源
    window.addEventListener('beforeunload', () => {
        automatedTestRunner.cleanup();
    });

})();
