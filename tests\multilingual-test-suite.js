/**
 * @TEST 多语言测试套件
 * 🏷️ 标签: @MULTILINGUAL_TEST_SUITE
 * 📝 说明: 测试重构后Gemini系统的多语言处理能力
 * 🎯 功能: 中英文处理测试、混合语言测试、语言识别测试、本地化测试
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保测试环境
if (typeof window === 'undefined') {
    global.window = {};
}

// 多语言测试套件
describe('Gemini系统多语言测试', function() {
    let geminiService;
    let testData;

    // 测试前置设置
    beforeAll(async function() {
        geminiService = window.OTA?.geminiService;
        
        if (!geminiService) {
            throw new Error('Gemini服务未初始化');
        }

        // 初始化多语言测试数据
        testData = {
            chinese: {
                simple: `
                    客户姓名：张三
                    联系电话：+***********
                    接送地点：吉隆坡国际机场
                    目的地：双子塔
                    航班信息：马航MH123，下午3点30分抵达
                    订单号：CN2024010001
                `,
                complex: `
                    【中文复杂订单】
                    尊敬的客户：李明华先生
                    联系方式：手机+***********，微信liminghua2024
                    服务类型：机场接机服务
                    接机地点：吉隆坡国际机场第一航站楼到达大厅
                    航班详情：马来西亚航空MH123航班，预计下午15:30抵达
                    目的地：吉隆坡双子塔丽思卡尔顿酒店
                    车型要求：豪华商务车（7座）
                    特殊需求：需要提供儿童安全座椅2个，车内准备矿泉水
                    备注：客户为VIP会员，请提供优质服务
                `
            },
            english: {
                simple: `
                    Customer Name: John Smith
                    Contact Number: +***********
                    Pickup Location: Kuala Lumpur International Airport
                    Destination: Petronas Twin Towers
                    Flight Information: Malaysia Airlines MH123, arriving at 3:30 PM
                    Order Number: EN2024010001
                `,
                complex: `
                    【English Complex Order】
                    Dear Valued Customer: Mr. Robert Johnson
                    Contact Details: Mobile +***********, Email <EMAIL>
                    Service Type: Airport Pickup Service
                    Pickup Point: Kuala Lumpur International Airport Terminal 1 Arrival Hall
                    Flight Details: Malaysia Airlines Flight MH123, Expected arrival 15:30
                    Destination: The Ritz-Carlton Kuala Lumpur, Petronas Twin Towers
                    Vehicle Requirement: Luxury Business Van (7-seater)
                    Special Requirements: Child safety seats x2, Bottled water provided
                    Remarks: VIP customer, please provide premium service
                `
            },
            mixed: {
                chineseEnglish: `
                    Customer姓名：王小明 (Wang Xiaoming)
                    Phone: +***********
                    接机地点：KLIA Terminal 2
                    Destination: Pavilion KL Shopping Mall
                    Flight: AK456 arriving 18:45
                    Special request: 需要baby seat
                `,
                englishChinese: `
                    Dear Customer: 李女士 Ms. Li
                    Contact: +***********
                    Pickup: 吉隆坡机场 (Kuala Lumpur Airport)
                    Drop-off: Sunway Pyramid Shopping Mall
                    Time: 明天上午10点 (Tomorrow 10:00 AM)
                    Note: Please call客户 when arrived
                `
            },
            malay: {
                simple: `
                    Nama Pelanggan: Ahmad bin Abdullah
                    Nombor Telefon: +***********
                    Lokasi Ambil: Lapangan Terbang Antarabangsa Kuala Lumpur
                    Destinasi: Menara Berkembar Petronas
                    Maklumat Penerbangan: Malaysia Airlines MH123, tiba pada 3:30 petang
                `
            },
            other: {
                japanese: `
                    お客様名：田中太郎
                    電話番号：+***********
                    お迎え場所：クアラルンプール国際空港
                    目的地：ペトロナスツインタワー
                    フライト情報：マレーシア航空MH123便、午後3時30分到着予定
                `,
                korean: `
                    고객명: 김철수
                    연락처: +***********
                    픽업 장소: 쿠알라룸푸르 국제공항
                    목적지: 페트로나스 트윈 타워
                    항공편 정보: 말레이시아항공 MH123편, 오후 3시 30분 도착 예정
                `,
                thai: `
                    ชื่อลูกค้า: สมชาย ใจดี
                    เบอร์โทร: +***********
                    สถานที่รับ: สนามบินนานาชาติกัวลาลัมเปอร์
                    จุดหมาย: เพทโรนาส ทวิน ทาวเวอร์
                    ข้อมูลเที่ยวบิน: มาเลเซียแอร์ไลน์ MH123 มาถึงเวลา 15:30
                `
            }
        };
    });

    // 中文处理测试
    describe('中文处理测试', function() {
        
        it('应该正确解析简单中文订单', async function() {
            const result = await geminiService.parseOrder(testData.chinese.simple);
            
            assertTrue(result.success, '中文订单解析应该成功');
            assertTrue(result.data, '应该返回解析数据');
            
            if (result.data) {
                // 检查中文字段提取
                assertTrue(result.data.customer_name?.includes('张三'), '应该正确提取中文客户姓名');
                assertTrue(result.data.customer_contact?.includes('+***********'), '应该提取联系电话');
                assertTrue(result.data.pickup?.includes('吉隆坡') || result.data.pickup_location?.includes('吉隆坡'), '应该提取中文接送地点');
                
                // 检查航班信息提取
                if (result.data.flight_info) {
                    assertTrue(result.data.flight_info.includes('MH123'), '应该提取航班号');
                }
            }
        });

        it('应该正确解析复杂中文订单', async function() {
            const result = await geminiService.parseOrder(testData.chinese.complex);
            
            assertTrue(result.success, '复杂中文订单解析应该成功');
            
            if (result.data) {
                // 检查复杂中文内容提取
                assertTrue(result.data.customer_name?.includes('李明华'), '应该提取完整中文姓名');
                
                // 检查特殊需求提取
                if (result.data.special_requirements || result.data.notes) {
                    const requirements = result.data.special_requirements || result.data.notes || '';
                    assertTrue(requirements.includes('儿童') || requirements.includes('安全座椅'), '应该提取中文特殊需求');
                }
            }
        });

        it('应该正确识别中文语言类型', async function() {
            const result = await geminiService.parseOrder(testData.chinese.simple);
            
            if (result.data && result.data.languages_id_array) {
                // 检查是否识别为中文（ID通常为4）
                const languageIds = result.data.languages_id_array;
                if (typeof languageIds === 'object') {
                    const values = Object.values(languageIds);
                    assertTrue(values.includes('4') || values.includes(4), '应该识别为中文语言');
                }
            }
        });
    });

    // 英文处理测试
    describe('英文处理测试', function() {
        
        it('应该正确解析简单英文订单', async function() {
            const result = await geminiService.parseOrder(testData.english.simple);
            
            assertTrue(result.success, '英文订单解析应该成功');
            assertTrue(result.data, '应该返回解析数据');
            
            if (result.data) {
                // 检查英文字段提取
                assertTrue(result.data.customer_name?.includes('John Smith'), '应该正确提取英文客户姓名');
                assertTrue(result.data.customer_contact?.includes('+***********'), '应该提取联系电话');
                assertTrue(result.data.pickup?.includes('Airport') || result.data.pickup_location?.includes('Airport'), '应该提取英文接送地点');
            }
        });

        it('应该正确解析复杂英文订单', async function() {
            const result = await geminiService.parseOrder(testData.english.complex);
            
            assertTrue(result.success, '复杂英文订单解析应该成功');
            
            if (result.data) {
                // 检查复杂英文内容提取
                assertTrue(result.data.customer_name?.includes('Robert Johnson'), '应该提取完整英文姓名');
                
                // 检查特殊需求提取
                if (result.data.special_requirements || result.data.notes) {
                    const requirements = result.data.special_requirements || result.data.notes || '';
                    assertTrue(requirements.includes('Child') || requirements.includes('safety'), '应该提取英文特殊需求');
                }
            }
        });

        it('应该正确识别英文语言类型', async function() {
            const result = await geminiService.parseOrder(testData.english.simple);
            
            if (result.data && result.data.languages_id_array) {
                // 检查是否识别为英文（ID通常为2）
                const languageIds = result.data.languages_id_array;
                if (typeof languageIds === 'object') {
                    const values = Object.values(languageIds);
                    assertTrue(values.includes('2') || values.includes(2), '应该识别为英文语言');
                }
            }
        });
    });

    // 混合语言处理测试
    describe('混合语言处理测试', function() {
        
        it('应该正确处理中英混合订单', async function() {
            const result = await geminiService.parseOrder(testData.mixed.chineseEnglish);
            
            assertTrue(result.success, '中英混合订单解析应该成功');
            
            if (result.data) {
                // 检查混合语言字段提取
                assertTrue(result.data.customer_name?.includes('王小明') || result.data.customer_name?.includes('Wang'), '应该提取混合语言姓名');
                assertTrue(result.data.pickup?.includes('KLIA') || result.data.pickup_location?.includes('KLIA'), '应该提取混合语言地点');
            }
        });

        it('应该正确处理英中混合订单', async function() {
            const result = await geminiService.parseOrder(testData.mixed.englishChinese);
            
            assertTrue(result.success, '英中混合订单解析应该成功');
            
            if (result.data) {
                // 检查混合语言内容提取
                assertTrue(result.data.customer_name?.includes('李') || result.data.customer_name?.includes('Li'), '应该提取混合语言客户信息');
            }
        });

        it('应该为混合语言订单选择合适的语言ID', async function() {
            const result = await geminiService.parseOrder(testData.mixed.chineseEnglish);
            
            if (result.data && result.data.languages_id_array) {
                // 混合语言可能选择主要语言或包含多种语言
                const languageIds = result.data.languages_id_array;
                assertTrue(languageIds, '应该有语言ID设置');
            }
        });
    });

    // 马来语处理测试
    describe('马来语处理测试', function() {
        
        it('应该能够处理马来语订单', async function() {
            const result = await geminiService.parseOrder(testData.malay.simple);
            
            assertTrue(result.success, '马来语订单解析应该成功');
            
            if (result.data) {
                // 检查马来语字段提取
                assertTrue(result.data.customer_name?.includes('Ahmad'), '应该提取马来语客户姓名');
                assertTrue(result.data.pickup?.includes('Lapangan') || result.data.pickup_location?.includes('Lapangan'), '应该提取马来语地点信息');
            }
        });
    });

    // 其他语言处理测试
    describe('其他语言处理测试', function() {
        
        it('应该能够处理日语订单', async function() {
            const result = await geminiService.parseOrder(testData.other.japanese);
            
            assertTrue(result.success, '日语订单解析应该成功');
            
            if (result.data) {
                // 检查日语内容处理
                assertTrue(result.data.customer_name?.includes('田中'), '应该提取日语客户姓名');
            }
        });

        it('应该能够处理韩语订单', async function() {
            const result = await geminiService.parseOrder(testData.other.korean);
            
            assertTrue(result.success, '韩语订单解析应该成功');
            
            if (result.data) {
                // 检查韩语内容处理
                assertTrue(result.data.customer_name?.includes('김철수'), '应该提取韩语客户姓名');
            }
        });

        it('应该能够处理泰语订单', async function() {
            const result = await geminiService.parseOrder(testData.other.thai);
            
            assertTrue(result.success, '泰语订单解析应该成功');
            
            if (result.data) {
                // 检查泰语内容处理
                assertTrue(result.data.customer_name?.includes('สมชาย'), '应该提取泰语客户姓名');
            }
        });
    });

    // 语言识别准确性测试
    describe('语言识别准确性测试', function() {
        
        it('应该准确识别纯中文内容', async function() {
            const pureChineseOrder = '客户：张三，电话：+***********，接机：机场，送往：酒店';
            const result = await geminiService.parseOrder(pureChineseOrder);
            
            if (result.data && result.data.detected_language) {
                assertTrue(result.data.detected_language.includes('chinese') || result.data.detected_language.includes('中文'), '应该识别为中文');
            }
        });

        it('应该准确识别纯英文内容', async function() {
            const pureEnglishOrder = 'Customer: John Smith, Phone: +***********, Pickup: Airport, Destination: Hotel';
            const result = await geminiService.parseOrder(pureEnglishOrder);
            
            if (result.data && result.data.detected_language) {
                assertTrue(result.data.detected_language.includes('english') || result.data.detected_language.includes('英文'), '应该识别为英文');
            }
        });

        it('应该识别混合语言内容', async function() {
            const mixedOrder = 'Customer客户：John张三，Phone电话：+***********';
            const result = await geminiService.parseOrder(mixedOrder);
            
            if (result.data && result.data.detected_language) {
                assertTrue(result.data.detected_language.includes('mixed') || result.data.detected_language.includes('混合'), '应该识别为混合语言');
            }
        });
    });

    // 字符编码测试
    describe('字符编码测试', function() {
        
        it('应该正确处理UTF-8编码', async function() {
            const utf8Order = '客户：张三🚗，电话：+***********📞，接机：机场✈️';
            const result = await geminiService.parseOrder(utf8Order);
            
            assertTrue(result.success, 'UTF-8编码订单应该解析成功');
            
            if (result.data) {
                // 检查emoji和特殊字符是否正确处理
                assertTrue(result.data.customer_name?.includes('张三'), '应该正确处理UTF-8中文字符');
            }
        });

        it('应该处理特殊Unicode字符', async function() {
            const unicodeOrder = 'Customer: José María Ñiño, Phone: +***********, Pickup: Café';
            const result = await geminiService.parseOrder(unicodeOrder);
            
            assertTrue(result.success, 'Unicode字符订单应该解析成功');
            
            if (result.data) {
                assertTrue(result.data.customer_name?.includes('José'), '应该正确处理Unicode字符');
            }
        });
    });

    // 本地化测试
    describe('本地化测试', function() {
        
        it('应该根据语言选择合适的日期格式', async function() {
            const chineseDateOrder = '客户：张三，日期：2024年1月15日，时间：下午3点30分';
            const englishDateOrder = 'Customer: John, Date: January 15, 2024, Time: 3:30 PM';
            
            const chineseResult = await geminiService.parseOrder(chineseDateOrder);
            const englishResult = await geminiService.parseOrder(englishDateOrder);
            
            assertTrue(chineseResult.success, '中文日期格式应该解析成功');
            assertTrue(englishResult.success, '英文日期格式应该解析成功');
        });

        it('应该处理不同的电话号码格式', async function() {
            const phoneFormats = [
                '+***********',      // 国际格式
                '0123456789',        // 本地格式
                '+60 12 345 6789',   // 带空格
                '+60-12-345-6789',   // 带连字符
                '(60) 123456789'     // 带括号
            ];

            for (const phone of phoneFormats) {
                const order = `客户：测试用户，电话：${phone}`;
                const result = await geminiService.parseOrder(order);
                
                assertTrue(result.success, `电话格式${phone}应该解析成功`);
                
                if (result.data && result.data.customer_contact) {
                    assertTrue(result.data.customer_contact.includes('123456789'), `应该提取电话号码：${phone}`);
                }
            }
        });
    });
});

// 日志记录
const logger = window.getLogger?.() || console;
logger.log('多语言测试套件加载完成', 'info', {
    version: '1.0.0',
    supportedLanguages: [
        'chinese_simplified',
        'english',
        'mixed_languages',
        'malay',
        'japanese',
        'korean',
        'thai'
    ],
    testCategories: [
        'chinese_processing',
        'english_processing',
        'mixed_language_processing',
        'other_languages',
        'language_detection',
        'character_encoding',
        'localization'
    ]
});
