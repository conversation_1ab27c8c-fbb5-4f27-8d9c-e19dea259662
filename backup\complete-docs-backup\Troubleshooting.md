# OTA系统故障排查指南

## 🔧 概述

本文档提供OTA订单处理系统的全面故障排查指南，帮助用户和开发者快速识别和解决常见问题。

### 使用说明
- 🔴 **紧急问题**: 影响系统基本功能
- 🟡 **一般问题**: 影响用户体验但不危及核心功能
- 🟢 **提示问题**: 轻微问题或优化建议

## 🔴 紧急问题排查

### 系统无法加载

#### 症状
- 页面显示空白或加载指示器一直转动
- 控制台出现“Script error”或类似错误
- 网络请求失败

#### 排查步骤

**1. 检查网络连接**
```bash
# 测试网络连接
ping google.com

# 检查DNS解析
nslookup yourdomain.com
```

**2. 检查浏览器控制台**
打开开发者工具 (F12)，查看是否有：
- JavaScript 错误
- 网络请求失败 (404, 500 等)
- CSP (内容安全策略) 错误

**3. 检查缓存问题**
```javascript
// 清除浏览器缓存
// Chrome: Ctrl+Shift+Del
// 或使用硬刷新: Ctrl+F5

// 清除LocalStorage
localStorage.clear();
sessionStorage.clear();
```

**4. 检查服务器状态**
```bash
# 检查API服务器
curl -I https://yourdomain.com/api/health

# 检查静态资源
curl -I https://yourdomain.com/js/core/application-bootstrap.js
```

#### 解决方案

**方案 A: 缓存问题**
1. 在隐私浏览模式下测试
2. 清除所有缓存和Cookie
3. 禁用浏览器扩展

**方案 B: 脚本加载失败**
1. 检查 `main.js` 是否存在
2. 验证所有必需的核心文件
3. 检查CDN或静态资源服务器

**方案 C: 兼容性问题**
```javascript
// 检查浏览器版本
console.log(navigator.userAgent);

// 检查支持的特性
console.log('ES6 Support:', typeof Symbol !== 'undefined');
console.log('Fetch Support:', typeof fetch !== 'undefined');
console.log('LocalStorage Support:', typeof Storage !== 'undefined');
```

### API连接失败

#### 症状
- 订单提交失败
- AI分析不响应
- 数据加载错误

#### 排查步骤

**1. 检查API端点**
```javascript
// 在控制台中测试
fetch('https://gomyhire.com.my/api/static_data')
  .then(response => response.json())
  .then(data => console.log('API Response:', data))
  .catch(error => console.error('API Error:', error));
```

**2. 检查认证状态**
```javascript
// 检查存储的token
const token = localStorage.getItem('authToken');
console.log('Stored Token:', token);

// 测试认证
fetch('https://gomyhire.com.my/api/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'Gomyhire@123456'
  })
})
.then(response => response.json())
.then(data => console.log('Auth Result:', data));
```

**3. 检查网络配置**
- 防火墙设置
- 代理服务器配置
- CORS 策略

#### 解决方案

**方案 A: 认证问题**
1. 重新登录获取新token
2. 检查token是否过期
3. 验证API凭据是否正确

**方案 B: 网络问题**
1. 切换到移动网络测试
2. 检查公司网络限制
3. 联系网络管理员

**方案 C: 服务器问题**
1. 检查API服务器状态
2. 查看服务器日志
3. 联系技术支持

### 数据丢失或损坏

#### 症状
- 输入的订单信息突然消失
- 表单重置为空
- 历史记录无法加载

#### 排查步骤

**1. 检查本地存储**
```javascript
// 检查LocalStorage
for (let i = 0; i < localStorage.length; i++) {
  const key = localStorage.key(i);
  console.log(`${key}: ${localStorage.getItem(key)}`);
}

// 检查SessionStorage
for (let i = 0; i < sessionStorage.length; i++) {
  const key = sessionStorage.key(i);
  console.log(`${key}: ${sessionStorage.getItem(key)}`);
}

// 检查存储容量
function getStorageSize() {
  let total = 0;
  for (let key in localStorage) {
    if (localStorage.hasOwnProperty(key)) {
      total += localStorage[key].length + key.length;
    }
  }
  return total;
}
console.log('Storage Size:', getStorageSize(), 'characters');
```

**2. 检查浏览器存储限制**
```javascript
// 测试存储容量
function testStorageQuota() {
  try {
    const testData = 'x'.repeat(1024 * 1024); // 1MB
    localStorage.setItem('test', testData);
    localStorage.removeItem('test');
    console.log('Storage test passed');
  } catch (e) {
    console.error('Storage quota exceeded:', e);
  }
}
testStorageQuota();
```

**3. 检查数据完整性**
```javascript
// 检查数据格式
function validateStoredData(key) {
  try {
    const data = localStorage.getItem(key);
    if (data) {
      const parsed = JSON.parse(data);
      console.log(`${key} data is valid:`, parsed);
      return true;
    }
  } catch (e) {
    console.error(`${key} data is corrupted:`, e);
    return false;
  }
}

// 检查所有相关数据
['orderHistory', 'userSettings', 'formData'].forEach(validateStoredData);
```

#### 解决方案

**方案 A: 数据恢复**
```javascript
// 尝试从备份恢复
function recoverFromBackup() {
  const backupKeys = Object.keys(localStorage).filter(key => key.endsWith('_backup'));
  backupKeys.forEach(backupKey => {
    const originalKey = backupKey.replace('_backup', '');
    const backupData = localStorage.getItem(backupKey);
    if (backupData) {
      localStorage.setItem(originalKey, backupData);
      console.log(`Recovered ${originalKey} from backup`);
    }
  });
}
```

**方案 B: 重新初始化**
```javascript
// 安全重置
function safeReset() {
  // 备份重要数据
  const importantData = {
    userSettings: localStorage.getItem('userSettings'),
    authToken: localStorage.getItem('authToken')
  };
  
  // 清理所有数据
  localStorage.clear();
  sessionStorage.clear();
  
  // 恢复重要数据
  Object.entries(importantData).forEach(([key, value]) => {
    if (value) {
      localStorage.setItem(key, value);
    }
  });
  
  // 重新加载页面
  window.location.reload();
}
```

**方案 C: 数据导出保护**
```javascript
// 导出所有数据
function exportAllData() {
  const allData = {};
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    allData[key] = localStorage.getItem(key);
  }
  
  const dataStr = JSON.stringify(allData, null, 2);
  const dataBlob = new Blob([dataStr], { type: 'application/json' });
  const url = URL.createObjectURL(dataBlob);
  
  const a = document.createElement('a');
  a.href = url;
  a.download = `ota-data-backup-${new Date().toISOString().split('T')[0]}.json`;
  a.click();
}
```

## 🟡 一般问题排查

### AI分析问题

#### 症状
- AI分析结果不准确
- 分析速度很慢
- 某些文本无法识别

#### 排查步骤

**1. 检查AI服务状态**
```javascript
// 检查Gemini服务
fetch('https://generativelanguage.googleapis.com/v1beta/models?key=YOUR_API_KEY')
  .then(response => response.json())
  .then(data => console.log('Gemini Status:', data))
  .catch(error => console.error('Gemini Error:', error));

// 检查系统配置
const aiConfig = window.OTA.getService('learningConfig');
console.log('AI Config:', {
  enabled: aiConfig.get('learningSystem.enabled'),
  confidence: aiConfig.get('learningSystem.confidenceThreshold'),
  autoApply: aiConfig.get('learningSystem.autoApplyThreshold')
});
```

**2. 分析输入文本质量**
```javascript
// 检查文本长度和格式
function analyzeInputText(text) {
  const analysis = {
    length: text.length,
    wordCount: text.split(/\s+/).length,
    hasNumbers: /\d/.test(text),
    hasChineseChars: /[\u4e00-\u9fff]/.test(text),
    hasEnglishChars: /[a-zA-Z]/.test(text),
    lineCount: text.split('\n').length
  };
  
  console.log('Text Analysis:', analysis);
  return analysis;
}

// 检查常见问题
function checkCommonIssues(text) {
  const issues = [];
  
  if (text.length < 10) {
    issues.push('文本太短，AI分析可能不准确');
  }
  
  if (text.length > 2000) {
    issues.push('文本太长，可能影响分析效果');
  }
  
  if (!/[一-鿿]/.test(text) && !/[a-zA-Z]/.test(text)) {
    issues.push('文本不包含可识别的语言内容');
  }
  
  return issues;
}
```

**3. 检查学习数据**
```javascript
// 检查学习数据状态
const storageManager = window.OTA.learningStorageManager;
const patternAnalyzer = window.OTA.patternAnalyzer;

// 查看学习规则
function checkLearningRules() {
  const ruleEngine = window.OTA.ruleGenerationEngine;
  const allRules = ruleEngine.getAllRules();
  
  console.log(`Total Learning Rules: ${allRules.length}`);
  
  const rulesByField = {};
  allRules.forEach(rule => {
    if (!rulesByField[rule.field]) {
      rulesByField[rule.field] = [];
    }
    rulesByField[rule.field].push(rule);
  });
  
  console.log('Rules by field:', rulesByField);
}
```

#### 解决方案

**方案 A: 优化输入文本**
1. 确保文本长度适中 (20-500字)
2. 使用清晰、完整的描述
3. 包含关键信息：地点、时间、人数等

**方案 B: 重设学习数据**
```javascript
// 清除学习数据
function resetLearningData() {
  const ruleEngine = window.OTA.ruleGenerationEngine;
  const allRules = ruleEngine.getAllRules();
  
  // 删除低置信度的规则
  allRules.forEach(rule => {
    if (rule.confidence < 0.5) {
      ruleEngine.deleteRule(rule.id);
    }
  });
  
  console.log('Low confidence rules removed');
}

// 重新训练
function retrainWithGoodExamples() {
  const goodExamples = [
    {
      text: '从KLIA机场到吉隆坡市中心，明天下午2点，3个人',
      expected: {
        pickup_location: 'KLIA',
        dropoff_location: '吉隆坡市中心',
        passenger_number: 3
      }
    }
    // 添加更多好示例
  ];
  
  const operationLearner = window.OTA.userOperationLearner;
  goodExamples.forEach(example => {
    operationLearner.recordOperation({
      type: 'correction',
      field: 'all',
      originalValue: example.text,
      correctedValue: JSON.stringify(example.expected),
      confidence: 1.0
    });
  });
}
```

**方案 C: 切换AI引擎**
```javascript
// 手动切换到Kimi引擎
function switchToKimiEngine() {
  const config = window.OTA.getService('learningConfig');
  config.set('ai.primaryEngine', 'kimi');
  config.set('ai.fallbackEngine', 'gemini');
  
  console.log('Switched to Kimi as primary AI engine');
}

// 测试不同AI引擎
function testAllEngines(text) {
  const geminiService = window.OTA.getService('geminiService');
  const kimiService = window.OTA.getService('kimiService');
  
  Promise.all([
    geminiService.analyzeOrderText(text).catch(e => ({ error: e.message })),
    kimiService.analyzeOrderText(text).catch(e => ({ error: e.message }))
  ]).then(([geminiResult, kimiResult]) => {
    console.log('Gemini Result:', geminiResult);
    console.log('Kimi Result:', kimiResult);
  });
}
```

### 多订单处理问题

#### 症状
- 多订单模式未自动激活
- 订单卡片显示异常
- 批量提交失败

#### 排查步骤

**1. 检查多订单检测机制**
```javascript
// 测试检测功能
const detector = window.OTA.getService('multiOrderDetector');

function testMultiOrderDetection() {
  const testTexts = [
    '订单1: 从KLIA到KL\n订单2: 从KL到KLIA',
    '1. 从A地到B地\n2. 从B地到C地',
    '第一个订单：...。第二个订单：...'
  ];
  
  testTexts.forEach((text, index) => {
    const result = detector.detectMultipleOrders(text);
    console.log(`Test ${index + 1}:`, {
      text: text.substring(0, 50) + '...',
      isMultiOrder: result.isMultiOrder,
      orderCount: result.orderCount
    });
  });
}

testMultiOrderDetection();
```

**2. 检查UI组件状态**
```javascript
// 检查多订单容器
function checkMultiOrderUI() {
  const container = document.getElementById('multi-order-container');
  const cards = container ? container.querySelectorAll('.order-card') : [];
  
  console.log('Multi-order UI Status:', {
    containerExists: !!container,
    containerVisible: container ? container.style.display !== 'none' : false,
    cardCount: cards.length,
    containerHTML: container ? container.innerHTML.length : 0
  });
  
  // 检查每个卡片的状态
  cards.forEach((card, index) => {
    const inputs = card.querySelectorAll('input, select, textarea');
    const filledInputs = Array.from(inputs).filter(input => input.value.trim());
    
    console.log(`Card ${index + 1}:`, {
      totalInputs: inputs.length,
      filledInputs: filledInputs.length,
      completeness: filledInputs.length / inputs.length
    });
  });
}
```

**3. 检查数据处理逻辑**
```javascript
// 检查数据转换
function testDataTransformation() {
  const transformer = window.OTA.getService('multiOrderTransformer');
  const testData = {
    orders: [
      { id: 1, customer_name: 'Test 1', pickup_location: 'A' },
      { id: 2, customer_name: 'Test 2', pickup_location: 'B' }
    ]
  };
  
  try {
    const transformed = transformer.transformForSubmission(testData);
    console.log('Transformation successful:', transformed);
  } catch (error) {
    console.error('Transformation failed:', error);
  }
}
```

#### 解决方案

**方案 A: UI重新渲染**
```javascript
// 强制重新渲染多订单界面
function forceMultiOrderRerender() {
  const renderer = window.OTA.getService('multiOrderRenderer');
  const container = document.getElementById('multi-order-container');
  
  if (container) {
    container.innerHTML = '';
  }
  
  // 重新初始化
  const mockData = {
    isMultiOrder: true,
    orderCount: 2,
    orders: [
      { id: 1, customer_name: '', pickup_location: '' },
      { id: 2, customer_name: '', pickup_location: '' }
    ]
  };
  
  renderer.renderMultiOrderCards(mockData);
}
```

**方案 B: 检测参数调优**
```javascript
// 调整检测敏感度
function adjustDetectionSensitivity() {
  const detector = window.OTA.getService('multiOrderDetector');
  
  // 降低检测阈值
  detector.config = {
    minOrderCount: 2,
    confidenceThreshold: 0.6, // 从0.8降低到0.6
    keywordWeight: 0.7,
    structureWeight: 0.3
  };
  
  console.log('Detection sensitivity adjusted');
}
```

**方案 C: 手动模式**
```javascript
// 提供手动激活选项
function enableManualMultiOrderMode() {
  // 添加手动切换按钮
  const toggleButton = document.createElement('button');
  toggleButton.textContent = '手动开启多订单模式';
  toggleButton.onclick = () => {
    const manager = window.OTA.getService('multiOrderManager');
    manager.enableMultiOrderMode(true);
  };
  
  document.querySelector('.toolbar').appendChild(toggleButton);
}
```

### 数据导出问题

#### 症状
- 导出文件为空或损坏
- 中文乱码问题
- 导出格式不正确

#### 排查步骤

**1. 检查数据完整性**
```javascript
// 检查待导出数据
function checkExportData() {
  const exportManager = window.OTA.getService('dataExportManager');
  const orderHistory = JSON.parse(localStorage.getItem('orderHistory') || '[]');
  
  console.log('Export Data Check:', {
    totalOrders: orderHistory.length,
    hasValidData: orderHistory.length > 0,
    sampleOrder: orderHistory[0] || null,
    dataSize: JSON.stringify(orderHistory).length
  });
  
  // 检查数据结构
  if (orderHistory.length > 0) {
    const fields = Object.keys(orderHistory[0]);
    console.log('Available fields:', fields);
  }
}
```

**2. 测试导出功能**
```javascript
// 测试不同格式的导出
function testExportFormats() {
  const testData = [
    {
      customer_name: '测试客户',
      pickup_location: 'KLIA',
      dropoff_location: '吉隆坡市中心',
      created_at: new Date().toISOString()
    }
  ];
  
  const exportManager = window.OTA.getService('dataExportManager');
  
  // 测试CSV导出
  try {
    const csvData = exportManager.exportToCSV(testData);
    console.log('CSV Export Success:', csvData.substring(0, 100));
  } catch (error) {
    console.error('CSV Export Failed:', error);
  }
  
  // 测试Excel导出
  try {
    const excelBlob = exportManager.exportToExcel(testData);
    console.log('Excel Export Success:', excelBlob.size, 'bytes');
  } catch (error) {
    console.error('Excel Export Failed:', error);
  }
}
```

**3. 检查浏览器支持**
```javascript
// 检查浏览器导出能力
function checkBrowserCapabilities() {
  const capabilities = {
    blobSupport: typeof Blob !== 'undefined',
    urlSupport: typeof URL !== 'undefined' && typeof URL.createObjectURL === 'function',
    downloadSupport: document.createElement('a').download !== undefined,
    fileReaderSupport: typeof FileReader !== 'undefined'
  };
  
  console.log('Browser Export Capabilities:', capabilities);
  
  const unsupported = Object.entries(capabilities)
    .filter(([key, value]) => !value)
    .map(([key]) => key);
    
  if (unsupported.length > 0) {
    console.warn('Unsupported features:', unsupported);
  }
}
```

#### 解决方案

**方案 A: 修复编码问题**
```javascript
// 解决CSV中文乱码
function fixCSVEncoding(data) {
  // 添加BOM头解决Excel中文乱码
  const BOM = '\uFEFF';
  const csvContent = BOM + convertToCSV(data);
  
  return new Blob([csvContent], { 
    type: 'text/csv;charset=utf-8;' 
  });
}

// 正确的CSV转换
function convertToCSV(data) {
  if (!data || data.length === 0) return '';
  
  const headers = Object.keys(data[0]);
  const csvHeaders = headers.join(',');
  
  const csvRows = data.map(row => {
    return headers.map(header => {
      let value = row[header] || '';
      // 处理包含逗号或换行的字段
      if (typeof value === 'string' && (value.includes(',') || value.includes('\n'))) {
        value = `"${value.replace(/"/g, '""')}"`;
      }
      return value;
    }).join(',');
  });
  
  return [csvHeaders, ...csvRows].join('\n');
}
```

**方案 B: 替代导出方法**
```javascript
// 使用第三方库导出Excel
function exportWithSheetJS(data) {
  // 需要先加载SheetJS库
  if (typeof XLSX === 'undefined') {
    console.error('SheetJS library not loaded');
    return;
  }
  
  const ws = XLSX.utils.json_to_sheet(data);
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, 'Orders');
  
  XLSX.writeFile(wb, `orders-${new Date().toISOString().split('T')[0]}.xlsx`);
}

// 简单数据复制
function copyToClipboard(data) {
  const csvData = convertToCSV(data);
  
  if (navigator.clipboard) {
    navigator.clipboard.writeText(csvData).then(() => {
      alert('数据已复制到剪贴板，可粘贴到Excel中');
    });
  } else {
    // 旧浏览器兼容
    const textArea = document.createElement('textarea');
    textArea.value = csvData;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    alert('数据已复制到剪贴板');
  }
}
```

## 🟢 提示问题排查

### 性能优化建议

#### 症状
- 页面加载较慢
- 操作响应延迟
- 内存使用较高

#### 排查和优化

**1. 性能监控**
```javascript
// 检查实时性能指标
function checkPerformanceMetrics() {
  const monitor = window.OTA.performanceMonitor;
  const metrics = monitor.getRealTimeMetrics();
  
  console.log('Current Performance:', {
    fps: metrics.fps.slice(-10), // 最后10个FPS数据
    memoryUsage: performance.memory ? {
      used: performance.memory.usedJSHeapSize,
      total: performance.memory.totalJSHeapSize,
      limit: performance.memory.jsHeapSizeLimit
    } : '不支持',
    domQueries: metrics.domQueries,
    apiCalls: metrics.apiCalls
  });
  
  // 检查是否超过阈值
  const warnings = [];
  if (performance.memory && performance.memory.usedJSHeapSize > 50 * 1024 * 1024) {
    warnings.push('内存使用超过50MB');
  }
  
  const avgFPS = metrics.fps.slice(-10).reduce((a, b) => a + b, 0) / 10;
  if (avgFPS < 30) {
    warnings.push(`FPS过低: ${avgFPS.toFixed(1)}`);
  }
  
  if (warnings.length > 0) {
    console.warn('Performance Warnings:', warnings);
  }
}

// 定期检查
setInterval(checkPerformanceMetrics, 10000); // 每10秒检查一次
```

**2. 懒加载优化**
```javascript
// 检查懒加载状态
function checkLazyLoadingStatus() {
  const lazyLoader = window.OTA.lazyLoader;
  const loadedModules = lazyLoader.getLoadedModules();
  
  console.log('Lazy Loading Status:', {
    totalLoaded: loadedModules.length,
    pendingLoads: lazyLoader.getPendingLoads(),
    failedLoads: lazyLoader.getFailedLoads()
  });
  
  // 预加载常用模块
  const commonModules = [
    'js/ai/gemini-service.js',
    'js/managers/form-manager.js',
    'js/components/image-upload-manager.js'
  ];
  
  commonModules.forEach(module => {
    if (!loadedModules.includes(module)) {
      lazyLoader.preloadModule(module);
    }
  });
}
```

**3. 内存清理**
```javascript
// 手动垃圾回收
function performMemoryCleanup() {
  const lifecycleManager = window.OTA.componentLifecycleManager;
  
  // 清理组件池
  lifecycleManager.performGarbageCollection();
  
  // 强制垃圾回收（如果可用）
  if (window.gc) {
    window.gc();
  }
  
  // 清理旧缓存
  const cacheManager = window.OTA.intelligentCacheManager;
  cacheManager.clearExpiredCache();
  
  console.log('Memory cleanup completed');
}
```

### 用户体验优化

#### 目标优化点
- 提升操作流畅度
- 减少加载等待时间
- 增强直觉性

#### 优化建议

**1. 界面响应优化**
```javascript
// 添加加载指示器
function addLoadingIndicators() {
  const style = document.createElement('style');
  style.textContent = `
    .loading {
      position: relative;
      pointer-events: none;
      opacity: 0.6;
    }
    .loading::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 20px;
      height: 20px;
      margin: -10px 0 0 -10px;
      border: 2px solid #ccc;
      border-top-color: #333;
      border-radius: 50%;
      animation: spin 1s infinite linear;
    }
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  `;
  document.head.appendChild(style);
}

// 自动给按钮添加加载状态
function enhanceButtons() {
  const buttons = document.querySelectorAll('button');
  buttons.forEach(button => {
    const originalClick = button.onclick;
    button.onclick = function(e) {
      this.classList.add('loading');
      
      if (originalClick) {
        const result = originalClick.call(this, e);
        
        // 如果返回Promise，等待完成
        if (result && typeof result.then === 'function') {
          result.finally(() => {
            this.classList.remove('loading');
          });
        } else {
          // 否则简单延迟移除
          setTimeout(() => {
            this.classList.remove('loading');
          }, 1000);
        }
      }
    };
  });
}
```

**2. 输入体验优化**
```javascript
// 实时验证反馈
function addRealTimeValidation() {
  const inputs = document.querySelectorAll('input[required], select[required]');
  
  inputs.forEach(input => {
    input.addEventListener('blur', function() {
      const isValid = this.value.trim() !== '';
      
      this.classList.toggle('error', !isValid);
      
      let feedback = this.nextElementSibling;
      if (!feedback || !feedback.classList.contains('validation-feedback')) {
        feedback = document.createElement('div');
        feedback.className = 'validation-feedback';
        this.parentNode.insertBefore(feedback, this.nextSibling);
      }
      
      feedback.textContent = isValid ? '' : '请填写此字段';
      feedback.style.color = isValid ? 'green' : 'red';
    });
  });
}

// 智能输入提示
function addSmartSuggestions() {
  const locationInputs = document.querySelectorAll('input[id*="location"]');
  
  locationInputs.forEach(input => {
    input.addEventListener('input', function() {
      const query = this.value.trim();
      if (query.length > 2) {
        // 模拟智能提示
        const suggestions = ['KLIA Terminal 1', 'KLIA Terminal 2', '吉隆坡市中心', 'Sunway Pyramid']
          .filter(item => item.toLowerCase().includes(query.toLowerCase()))
          .slice(0, 5);
          
        showSuggestions(this, suggestions);
      }
    });
  });
}

function showSuggestions(input, suggestions) {
  // 移除旧的提示
  const oldSuggestions = document.querySelector('.suggestions');
  if (oldSuggestions) {
    oldSuggestions.remove();
  }
  
  if (suggestions.length === 0) return;
  
  const suggestionDiv = document.createElement('div');
  suggestionDiv.className = 'suggestions';
  suggestionDiv.style.cssText = `
    position: absolute;
    background: white;
    border: 1px solid #ccc;
    border-top: none;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    width: ${input.offsetWidth}px;
  `;
  
  suggestions.forEach(suggestion => {
    const item = document.createElement('div');
    item.textContent = suggestion;
    item.style.cssText = 'padding: 8px; cursor: pointer; border-bottom: 1px solid #eee;';
    
    item.onmouseover = () => item.style.backgroundColor = '#f0f0f0';
    item.onmouseout = () => item.style.backgroundColor = 'white';
    item.onclick = () => {
      input.value = suggestion;
      suggestionDiv.remove();
    };
    
    suggestionDiv.appendChild(item);
  });
  
  input.parentNode.style.position = 'relative';
  input.parentNode.appendChild(suggestionDiv);
}
```

**3. 数据持久化优化**
```javascript
// 自动保存功能
function enableAutoSave() {
  const form = document.getElementById('order-form');
  if (!form) return;
  
  const formData = {};
  const inputs = form.querySelectorAll('input, select, textarea');
  
  // 恢复保存的数据
  const savedData = JSON.parse(localStorage.getItem('autoSave_formData') || '{}');
  Object.entries(savedData).forEach(([key, value]) => {
    const input = form.querySelector(`[name="${key}"]`);
    if (input && !input.value) {
      input.value = value;
    }
  });
  
  // 监听输入变化
  inputs.forEach(input => {
    input.addEventListener('input', debounce(() => {
      formData[input.name] = input.value;
      localStorage.setItem('autoSave_formData', JSON.stringify(formData));
    }, 1000));
  });
  
  // 成功提交后清理
  form.addEventListener('submit', () => {
    localStorage.removeItem('autoSave_formData');
  });
}

// 防抖函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}
```

## 📄 常见问题 FAQ

### Q1: 系统支持哪些浏览器？
**A**: 系统支持以下现代浏览器：
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

不支持IE浏览器。

### Q2: AI分析不准确怎么办？
**A**: 可以尝试以下方法：
1. 使用更清晰、完整的描述
2. 包含关键信息：地点、时间、人数
3. 手动修正AI分析结果，系统会学习
4. 切换到另一个AI引擎

### Q3: 如何备份数据？
**A**: 系统支持多种备份方式：
1. 自动本地备份（默认开启）
2. 手动导出数据（Excel/CSV/JSON）
3. 浏览器书签夹备份

### Q4: 移动端体验如何？
**A**: 系统已优化移动端体验：
- 响应式设计适配各种屏幕
- 触屏优化的交互
- 支持手势操作
- 离线缓存支持

### Q5: 如何提高系统性能？
**A**: 可以通过以下方式优化：
1. 定期清理浏览器缓存
2. 关闭不必要的浏览器标签页
3. 使用较新版本的浏览器
4. 确保网络连接稳定

### Q6: 数据安全如何保障？
**A**: 系统采用多层安全保障：
- 所有数据本地存储，不上传敏感信息
- API传输加密保护
- 输入验证防止恶意输入
- 定期数据清理机制

### Q7: 可以同时处理多少个订单？
**A**: 理论上没有上限，但建议：
- 单次处理不超过50个订单
- 可分批处理大量订单
- 注意浏览器内存使用情况

### Q8: 如何获取技术支持？
**A**: 支持渠道：
1. 使用系统内置反馈功能
2. 邮件联系：<EMAIL>
3. 内部技术支持群
4. 在线文档和帮助

## 📞 紧急联系方式

### 技术支持团队
- **紧急热线**: +1-555-TECH-911
- **邮件支持**: <EMAIL>
- **在线聊天**: 系统内置聊天窗口

### 上报流程
1. 记录问题现象和错误信息
2. 收集相关截图和日志
3. 描述重现步骤
4. 通过上述渠道联系支持

### 响应时间
- **紧急问题**: 1小时内
- **一般问题**: 4小时内
- **提示建议**: 1个工作日内

## 📚 相关资源

### 项目文档
- [API参考文档](API-Reference.md)
- [用户使用指南](User-Guide.md)
- [开发者指南](Development-Guide.md)
- [架构设计指南](Architecture-Guide.md)
- [性能优化指南](Performance-Guide.md)
- [更新日志](Change-Log.md)

### 外部资源
- [Web开发者工具](https://developers.google.com/web/tools)
- [浏览器兼容性查询](https://caniuse.com/)
- [JavaScript错误处理指南](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Control_flow_and_error_handling)

---
*故障排查指南版本: v1.0 | 最后更新: 2025-07-27*