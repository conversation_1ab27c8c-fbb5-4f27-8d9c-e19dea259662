/**
 * @TEST 错误处理测试套件
 * 🏷️ 标签: @ERROR_HANDLING_TEST_SUITE
 * 📝 说明: 测试重构后Gemini系统的错误处理和容错能力
 * 🎯 功能: 异常处理测试、边界条件测试、降级处理测试、错误恢复测试
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保测试环境
if (typeof window === 'undefined') {
    global.window = {};
}

// 错误处理测试套件
describe('Gemini系统错误处理测试', function() {
    let geminiService;
    let coordinator;
    let originalConsoleError;

    // 测试前置设置
    beforeAll(async function() {
        geminiService = window.OTA?.geminiService;
        coordinator = window.OTA?.gemini?.getGeminiCoordinator?.();
        
        if (!geminiService) {
            throw new Error('Gemini服务未初始化');
        }

        // 捕获console.error以便测试
        originalConsoleError = console.error;
        console.error = function(...args) {
            // 静默错误输出，避免测试时的噪音
        };
    });

    // 测试后置清理
    afterAll(function() {
        // 恢复console.error
        if (originalConsoleError) {
            console.error = originalConsoleError;
        }
    });

    // 输入验证错误处理测试
    describe('输入验证错误处理', function() {
        
        it('应该优雅处理null输入', async function() {
            const result = await geminiService.parseOrder(null);
            
            assertTrue(result, '应该返回结果对象');
            assertTrue(typeof result.success === 'boolean', '应该有success字段');
            
            // 即使输入为null，也不应该抛出异常
            if (!result.success) {
                assertTrue(result.error, '失败时应该有错误信息');
            }
        });

        it('应该优雅处理undefined输入', async function() {
            const result = await geminiService.parseOrder(undefined);
            
            assertTrue(result, '应该返回结果对象');
            assertTrue(typeof result.success === 'boolean', '应该有success字段');
        });

        it('应该优雅处理空字符串输入', async function() {
            const result = await geminiService.parseOrder('');
            
            assertTrue(result, '应该返回结果对象');
            assertTrue(typeof result.success === 'boolean', '应该有success字段');
        });

        it('应该优雅处理只包含空白字符的输入', async function() {
            const whitespaceInputs = ['   ', '\n\n\n', '\t\t\t', '   \n  \t  '];
            
            for (const input of whitespaceInputs) {
                const result = await geminiService.parseOrder(input);
                assertTrue(result, `空白字符输入"${input}"应该返回结果`);
            }
        });

        it('应该处理超长文本输入', async function() {
            const longText = 'A'.repeat(50000); // 50KB文本
            const result = await geminiService.parseOrder(longText);
            
            assertTrue(result, '超长文本应该返回结果');
            // 不应该抛出异常或导致系统崩溃
        });

        it('应该处理特殊字符和Unicode输入', async function() {
            const specialInputs = [
                '🚗🛫✈️🏨📞💰',
                '中文English123!@#$%^&*()',
                'Ñiño José María',
                '日本語テスト',
                '한국어 테스트',
                'العربية اختبار',
                '🎉🎊🎈🎁🎂🍰'
            ];

            for (const input of specialInputs) {
                const result = await geminiService.parseOrder(input);
                assertTrue(result, `特殊字符输入应该返回结果: ${input}`);
            }
        });
    });

    // API错误处理测试
    describe('API错误处理测试', function() {
        
        it('应该处理网络连接错误', async function() {
            // 模拟网络错误 - 临时修改API密钥
            const originalApiKey = geminiService.apiKey;
            geminiService.apiKey = 'invalid_api_key_for_testing';

            try {
                const result = await geminiService.parseOrder('测试订单\n客户：测试用户');
                
                // 应该返回结果（可能是降级处理）
                assertTrue(result, '网络错误时应该返回结果');
                
                if (!result.success) {
                    assertTrue(result.error, '失败时应该有错误信息');
                }
            } finally {
                // 恢复原始API密钥
                geminiService.apiKey = originalApiKey;
            }
        });

        it('应该处理API响应超时', async function() {
            // 模拟超时情况 - 临时设置很短的超时时间
            const originalTimeout = geminiService.timeout;
            geminiService.timeout = 1; // 1ms超时

            try {
                const result = await geminiService.parseOrder('超时测试订单');
                
                // 应该返回结果（降级处理或错误信息）
                assertTrue(result, '超时时应该返回结果');
            } finally {
                // 恢复原始超时设置
                geminiService.timeout = originalTimeout;
            }
        });

        it('应该处理API返回格式错误', async function() {
            // 这个测试模拟API返回非JSON格式的情况
            // 由于我们无法直接模拟API响应，我们测试JSON解析错误处理
            
            const testOrder = '格式错误测试\n客户：测试用户';
            const result = await geminiService.parseOrder(testOrder);
            
            // 即使遇到格式错误，也应该有适当的处理
            assertTrue(result, '格式错误时应该返回结果');
        });
    });

    // 协调器错误处理测试
    describe('协调器错误处理测试', function() {
        
        it('应该在协调器不可用时使用降级处理', async function() {
            // 临时禁用协调器
            const originalGetCoordinator = geminiService.getCoordinator;
            geminiService.getCoordinator = () => null;

            try {
                const result = await geminiService.parseOrder('降级测试\n客户：降级用户');
                
                assertTrue(result, '协调器不可用时应该返回结果');
                
                // 检查是否使用了降级模式
                if (result.data && result.data._fallback_mode) {
                    assertTrue(true, '应该使用降级模式');
                } else if (result.source === 'fallback') {
                    assertTrue(true, '应该标记为降级处理');
                }
            } finally {
                // 恢复协调器获取方法
                geminiService.getCoordinator = originalGetCoordinator;
            }
        });

        it('应该处理协调器方法调用错误', async function() {
            // 模拟协调器方法抛出异常
            const originalGetCoordinator = geminiService.getCoordinator;
            geminiService.getCoordinator = () => ({
                parseOrderCompatible: () => {
                    throw new Error('协调器方法测试错误');
                }
            });

            try {
                const result = await geminiService.parseOrder('协调器错误测试');
                
                assertTrue(result, '协调器方法错误时应该返回结果');
                
                // 应该降级到fallback处理
                if (!result.success) {
                    assertTrue(result.error, '应该包含错误信息');
                }
            } finally {
                geminiService.getCoordinator = originalGetCoordinator;
            }
        });
    });

    // 数据处理错误测试
    describe('数据处理错误测试', function() {
        
        it('应该处理无效JSON数据', async function() {
            // 测试包含可能导致JSON解析错误的文本
            const invalidJsonTexts = [
                '{"invalid": json}',
                '{broken json structure',
                'not json at all',
                '{"nested": {"broken": json}}'
            ];

            for (const text of invalidJsonTexts) {
                const result = await geminiService.parseOrder(text);
                assertTrue(result, `无效JSON文本应该返回结果: ${text}`);
            }
        });

        it('应该处理循环引用数据', async function() {
            // 虽然输入是字符串，但测试系统内部处理循环引用的能力
            const result = await geminiService.parseOrder('循环引用测试\n客户：测试用户');
            
            assertTrue(result, '应该处理循环引用数据');
            
            // 确保返回的数据可以被JSON序列化
            try {
                JSON.stringify(result);
                assertTrue(true, '结果应该可以被JSON序列化');
            } catch (error) {
                assertTrue(false, '结果不应该包含循环引用');
            }
        });

        it('应该处理数据类型转换错误', async function() {
            const result = await geminiService.parseOrder('类型转换测试\n电话：not_a_number\n日期：invalid_date');
            
            assertTrue(result, '数据类型错误应该返回结果');
            
            // 检查是否有适当的错误处理
            if (result.data) {
                // 数据应该存在，即使某些字段可能无法正确解析
                assertTrue(typeof result.data === 'object', '数据应该是对象');
            }
        });
    });

    // 边界条件测试
    describe('边界条件测试', function() {
        
        it('应该处理极小输入', async function() {
            const tinyInputs = ['a', '1', '中', '🚗'];
            
            for (const input of tinyInputs) {
                const result = await geminiService.parseOrder(input);
                assertTrue(result, `极小输入应该返回结果: ${input}`);
            }
        });

        it('应该处理重复字段', async function() {
            const duplicateFieldOrder = `
                客户姓名：张三
                客户姓名：李四
                电话：+60123456789
                电话：+60198765432
                客户姓名：王五
            `;

            const result = await geminiService.parseOrder(duplicateFieldOrder);
            
            assertTrue(result, '重复字段应该返回结果');
            
            if (result.data && result.data.customer_name) {
                // 应该处理重复字段，可能取最后一个或合并
                assertTrue(result.data.customer_name.length > 0, '客户姓名应该有值');
            }
        });

        it('应该处理缺失关键字段', async function() {
            const incompleteOrder = '这是一个不完整的订单，缺少大部分关键信息';
            
            const result = await geminiService.parseOrder(incompleteOrder);
            
            assertTrue(result, '不完整订单应该返回结果');
            
            // 即使缺失关键字段，也应该尽可能提取信息
            if (result.data) {
                assertTrue(typeof result.data === 'object', '应该返回数据对象');
            }
        });
    });

    // 并发错误处理测试
    describe('并发错误处理测试', function() {
        
        it('应该处理并发请求中的部分失败', async function() {
            const requests = [
                geminiService.parseOrder('正常订单1'),
                geminiService.parseOrder(null), // 可能失败
                geminiService.parseOrder('正常订单2'),
                geminiService.parseOrder(''), // 可能失败
                geminiService.parseOrder('正常订单3')
            ];

            const results = await Promise.all(requests);
            
            assertEqual(results.length, 5, '应该返回所有结果');
            
            // 检查每个结果都是有效的
            results.forEach((result, index) => {
                assertTrue(result, `结果${index + 1}应该存在`);
                assertTrue(typeof result.success === 'boolean', `结果${index + 1}应该有success字段`);
            });
        });

        it('应该处理高并发下的资源竞争', async function() {
            const concurrentRequests = 20;
            const testOrder = '并发错误测试\n客户：并发用户';
            
            const promises = [];
            for (let i = 0; i < concurrentRequests; i++) {
                promises.push(geminiService.parseOrder(testOrder));
            }

            const results = await Promise.all(promises);
            
            assertEqual(results.length, concurrentRequests, '应该处理所有并发请求');
            
            // 检查是否有资源竞争导致的错误
            const successCount = results.filter(r => r && r.success).length;
            assertTrue(successCount > 0, '应该有成功的请求');
            
            // 允许一定的失败率，但不应该全部失败
            const successRate = successCount / concurrentRequests;
            assertTrue(successRate > 0.5, `成功率应该大于50%，实际：${(successRate * 100).toFixed(2)}%`);
        });
    });

    // 错误恢复测试
    describe('错误恢复测试', function() {
        
        it('应该能从临时错误中恢复', async function() {
            // 模拟临时错误后的恢复
            let shouldFail = true;
            const originalParseOrder = geminiService.parseOrder;
            
            // 临时修改方法以模拟错误
            geminiService.parseOrder = async function(orderText) {
                if (shouldFail) {
                    shouldFail = false; // 只失败一次
                    throw new Error('临时错误');
                }
                return originalParseOrder.call(this, orderText);
            };

            try {
                // 第一次调用应该失败
                try {
                    await geminiService.parseOrder('恢复测试1');
                    assertTrue(false, '第一次调用应该失败');
                } catch (error) {
                    assertTrue(error.message.includes('临时错误'), '应该抛出临时错误');
                }

                // 第二次调用应该成功
                const result = await geminiService.parseOrder('恢复测试2');
                assertTrue(result, '第二次调用应该成功');
                
            } finally {
                // 恢复原始方法
                geminiService.parseOrder = originalParseOrder;
            }
        });

        it('应该维护错误状态统计', function() {
            const status = geminiService.getStatus();
            
            assertTrue(status, '应该返回状态对象');
            
            // 检查状态对象是否包含错误相关信息
            if (status.errors || status.errorCount || status.lastError) {
                assertTrue(true, '状态应该包含错误统计信息');
            }
        });
    });

    // 错误日志测试
    describe('错误日志测试', function() {
        
        it('应该正确记录错误日志', async function() {
            const loggedErrors = [];
            
            // 模拟logger来捕获错误日志
            const originalLogger = geminiService.logger;
            geminiService.logger = {
                log: originalLogger?.log || (() => {}),
                logError: function(message, error) {
                    loggedErrors.push({ message, error });
                },
                logWarning: originalLogger?.logWarning || (() => {})
            };

            try {
                // 触发一个可能的错误
                await geminiService.parseOrder(null);
                
                // 检查是否记录了适当的日志
                // 注意：由于降级处理，可能不会记录错误日志
                assertTrue(true, '错误日志测试完成');
                
            } finally {
                // 恢复原始logger
                geminiService.logger = originalLogger;
            }
        });
    });
});

// 日志记录
const logger = window.getLogger?.() || console;
logger.log('错误处理测试套件加载完成', 'info', {
    version: '1.0.0',
    testCategories: [
        'input_validation_errors',
        'api_error_handling',
        'coordinator_error_handling',
        'data_processing_errors',
        'boundary_conditions',
        'concurrent_error_handling',
        'error_recovery',
        'error_logging'
    ]
});
