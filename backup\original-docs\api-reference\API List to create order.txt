﻿API List to create order

Obtain data to be passed into create order API, need to login to avoid impostor accessing system data
Data included backend users, car types, sub categories
Login
Method: POST
Live URL: https://gomyhire.com.my/api/login
Body: {
   ‘email’: ‘<EMAIL>’,
   ‘password’: ‘Gomyhire@123456’
}
Response: {
   "status": true,
   "token": "2409|F0hye4nKqL58qhcdW8knwLwLMbQltjl35uggBwGA",
}
See token, copy the text after number| , that is the token used for login

Obtain system backend users, the user.id need to be passed into create order API and the user will become the operator who incharge of the order (deduct credit, accept email…)
Bearer token: token obtain from login API, such as “F0hye4nKqL58qhcdW8knwLwLMbQltjl35uggBwGA”
Method: GET
	Live URL: https://gomyhire.com.my/api/backend_users?search=
	Parameter: {
	   ‘search’: ‘’
	}
	Response: {
	   ‘data’: [
   {
		      ‘id’: 1,
		      ‘name’: ‘super admin name’,
		      ‘phone’: ‘012345689’,
		      ‘role_id’: ‘super admin’
		   },
		   {
		      ‘id’: 2,
		      ‘name’: ‘operator 2’,
		      ‘phone’: ‘012345688’,
		      ‘role_id’: ‘operator’
		   }
		  ]
	}
	?search= can be used to filter user.id, user.name, user.phone
	user.id is required to be passed into create order API

Obtain system sub categories, the sub_category.id need to be passed into create order API. Sub category got preset order_type, ota, driving region, languages, when call create order API, no need to mention order_type, ota, region, languages, only pass in sub_category.id then system will auto fill based on what has been preset by sub category
Bearer token: token obtain from login API, such as “F0hye4nKqL58qhcdW8knwLwLMbQltjl35uggBwGA”
Method: GET
Live URL: https://gomyhire.com.my/api/sub_category?search=
API will return where sub_category.id or sub_category.name or category,name matching “search”


Obtain system car types, the car_type.id need to be passed into create order API
Bearer token: token obtain from login API, such as “F0hye4nKqL58qhcdW8knwLwLMbQltjl35uggBwGA”
Method: GET
URL: https://staging.gomyhire.com.my/api/car_types?search=

Obtain system driving regions, the region.id is optionally to be passed into create order API, this driving region id will replace sub_category preset region id
Bearer token: token obtain from login API, such as “F0hye4nKqL58qhcdW8knwLwLMbQltjl35uggBwGA”
Method: GET
Live URL: https://staging.gomyhire.com.my/api/driving_regions?search=


Obtain system languages, multiple language.id can be put into an array and optionally passed into create order API to replace sub_categort preset languages
Bearer token: token obtain from login API, such as “F0hye4nKqL58qhcdW8knwLwLMbQltjl35uggBwGA”
Method: GET
Live URL: https://staging.gomyhire.com.my/api/languages?search=

After obtaining data from backend, someone will create third party page to insert order. Create order API does not require login.
Create order API
Method: POST
Live URL: https://gomyhire.com.my/api/create_order
No need bearer token
Response: 
sub_category_id, car_type_id, incharge_by_backend_user_id are obtained from the APIs above, these three and ota_reference_number are required to be filled in, others will be optional
{
    "status": false,
    "message": "Data need to be refined",
    "data": {
        "validation_error": {
            "sub_category_id": [
                "The sub category id field is required."
            ],
            "ota_reference_number": [
                "The ota reference number field is required."
            ],
            "car_type_id": [
                "The car type id field is required."
            ],
            "incharge_by_backend_user_id": [
                "The incharge by backend user id field is required."
            ]
        },
        "available_fields_to_fill_in": [
            "sub_category_id",
            "ota",
            "ota_reference_number",
            "ota_price",
            "customer_name",
            "customer_contact",
            "customer_email",
            "flight_info",
            "pickup",
            "pickup_lat",
            "pickup_long",
            "date",
            "time",
            "destination",
            "destination_lat",
            "destination_long",
            "car_type_id",
            "passenger_number",
            "luggage_number",
            "driver_fee",
            "driver_collect",
            "tour_guide",
            "baby_chair",
            "meet_and_greet",
            "extra_requirement",
            "incharge_by_backend_user_id",
            "driving_region_id",
            "languages_id_array" // array or json object contains multiple language id 
// either like this [1,2,3]
// or like this {"0":"1","1":"2",”2”:”3”}
        ]
    }
}

可以检查 api document
https://docs.google.com/document/d/1JjuDSPxrP6U_aDmX7jLfcN1pAPPNoUnKDj0_C6GXYDg/edit?tab=t.0

新api
Need login

Create order 多了两个optional attribute
driving_region_id, example: 1
languages_id_array, example: [1,2,3] or {"0":"1","1":"2","2":"3"}
如果有pass in，会替代 sub category preset 的 region 和 languages
driving_region_id pass in 一个
languages_id_array 把一个或多个language_id 放在一个array或 json object