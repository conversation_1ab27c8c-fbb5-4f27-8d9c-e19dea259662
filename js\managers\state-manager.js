/**
 * 状态管理器模块
 * 负责UI状态管理、主题控制、连接状态和界面更新
 * 协调应用状态与UI显示的同步
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.managers = window.OTA.managers || {};

(function() {
    'use strict';

    // 获取依赖模块 - 使用统一的服务定位器

    /**
     * 状态管理器类
     * 负责UI状态管理和界面更新
     */
    class StateManager {
        constructor(elements) {
            this.elements = elements;
        }

        /**
         * 初始化状态管理器
         */
        init() {
            this.setupStateListeners();
            this.initializeTheme();
            this.updateUI();
            getLogger().log('状态管理器初始化完成', 'success');
        }

        /**
         * 设置状态监听器
         */
        setupStateListeners() {
            // 监听登录状态变化
            getAppState().on('auth.isLoggedIn', (isLoggedIn) => {
                this.updateLoginUI(isLoggedIn);
                if (isLoggedIn) {
                    this.updateDataStatus();
                }
            });

            // 监听主题变化
            getAppState().on('config.theme', (theme) => {
                this.updateThemeIcon(theme);
                document.documentElement.setAttribute('data-theme', theme);
            });

            // 监听当前订单变化
            getAppState().on('currentOrder', (orderData) => {
                this.updateOrderForm();
            });

            // 监听系统数据变化
            getAppState().on('systemData', () => {
                this.updateDataStatus();
                this.updateLastUpdateTime();
            });

            // **新增**: 监听系统数据更新，确保下拉菜单被填充
            getAppState().on('systemData.lastUpdated', (newTimestamp, oldTimestamp) => {
                if (newTimestamp && newTimestamp !== oldTimestamp) {
                    // **修复**: 使用UIManager获取唯一的FormManager实例
                    const formManager = window.OTA.uiManager?.getManager('form');
                    if (formManager) {
                        formManager.populateFormOptions();
                        getLogger().log('系统数据更新，已重新填充表单选项', 'info');
                    }
                }
            });

            // 监听连接状态变化
            getAppState().on('connection.status', (connected) => {
                this.updateConnectionStatus(connected);
            });

            getLogger().log('状态监听器设置完成', 'info');
        }

        /**
         * 初始化主题
         */
        initializeTheme() {
            const theme = getAppState().get('config.theme') || 'light';
            document.documentElement.setAttribute('data-theme', theme);
            this.updateThemeIcon(theme);
        }

        /**
         * 更新UI状态
         */
        updateUI() {
            const isLoggedIn = getAppState().get('auth.isLoggedIn');
            this.updateLoginUI(isLoggedIn);

            if (isLoggedIn) {
                this.updateDataStatus();
                this.updateLastUpdateTime();
                this.updateGeminiStatus();
            }

            this.updateConnectionStatus(true); // 假设初始连接正常
        }

        /**
         * 更新登录UI状态
         * @param {boolean} isLoggedIn - 是否已登录
         */
        updateLoginUI(isLoggedIn) {
            const { loginPanel, workspace, userInfo, currentUser, historyBtn, logoutBtn, clearSavedBtn } = this.elements;

            if (isLoggedIn) {
                if (loginPanel) loginPanel.style.display = 'none';
                if (workspace) workspace.style.display = 'block';
                if (userInfo) userInfo.style.display = 'flex';

                const user = getAppState().get('auth.user');
                if (user && currentUser) {
                    // **修复**: 优先显示用户邮箱
                    currentUser.textContent = user.email || user.name || '未知用户';
                }
                if (clearSavedBtn) {
                    clearSavedBtn.style.display = 'block';
                }

                // **修复**: 确保历史订单和退出登录按钮在登录后可见
                if (historyBtn) historyBtn.style.display = 'inline-block';
                if (logoutBtn) logoutBtn.style.display = 'inline-block';

                // **修复**: 使用UIManager获取唯一的FormManager实例
                const formManager = window.OTA.uiManager?.getManager('form');
                if (formManager) {
                    formManager.populateFormOptions();
                    // **修复1**: 确保登录成功后OTA渠道被正确填充
                    formManager.populateOtaChannelOptions();
                    // **修复2**: 确保登录成功后设置默认负责人
                    formManager.setDefaultValues();
                }
                
                getLogger().log('UI已切换到工作区', 'info');
            } else {
                if (loginPanel) loginPanel.style.display = 'block';
                if (workspace) workspace.style.display = 'none';
                if (userInfo) userInfo.style.display = 'none';

                // **修复**: 确保历史订单和退出登录按钮在未登录时隐藏
                if (historyBtn) historyBtn.style.display = 'none';
                if (logoutBtn) logoutBtn.style.display = 'none';

                getLogger().log('UI已切换到登录界面', 'info');
            }
        }

        /**
         * 更新Gemini状态显示
         * @param {string} status - 状态文本
         */
        updateGeminiStatus(status = null) {
            if (!this.elements.geminiStatus) return;
            
            if (status) {
                this.elements.geminiStatus.textContent = status;
            } else {
                const available = getGeminiService().isAvailable();
                const realtimeEnabled = getGeminiService().isRealtimeEnabled();
                
                if (available && realtimeEnabled) {
                    this.elements.geminiStatus.textContent = '🤖 AI实时分析已启用';
                } else if (available) {
                    this.elements.geminiStatus.textContent = '🤖 AI服务可用';
                } else {
                    this.elements.geminiStatus.textContent = '🤖 AI服务未配置';
                }
            }
        }

        /**
         * 更新数据状态
         */
        updateDataStatus() {
            const hasData = getAppState().get('systemData.lastUpdated') !== null;
            const status = hasData ? '📊 数据已就绪' : '📊 等待数据';
            if (this.elements.dataStatus) {
                this.elements.dataStatus.textContent = status;
            }
        }

        /**
         * 更新连接状态
         * @param {boolean} connected - 是否已连接
         */
        updateConnectionStatus(connected) {
            const status = connected ? '🔌 已连接' : '🔌 未连接';
            if (this.elements.connectionStatus) {
                this.elements.connectionStatus.textContent = status;
            }
        }

        /**
         * 更新最后更新时间
         */
        updateLastUpdateTime() {
            const lastUpdate = getAppState().get('systemData.lastUpdated');
            if (lastUpdate && this.elements.lastUpdate) {
                const time = getUtils().formatTime(new Date(lastUpdate));
                this.elements.lastUpdate.textContent = `⏰ ${time}`;
            }
        }

        /**
         * 更新订单表单
         * 从应用状态中获取当前订单数据并更新表单UI
         */
        async updateOrderForm() {
            try {
                const currentOrder = getAppState().get('currentOrder');
                
                if (!currentOrder || Object.keys(currentOrder).length === 0) {
                    getLogger().log('当前无订单数据，跳过表单更新');
                    return;
                }

                // **修复**: 使用UIManager获取唯一的FormManager实例
                const formManager = window.OTA.uiManager?.getManager('form');
                if (!formManager) {
                    getLogger().log('FormManager实例不可用，无法更新表单', 'error');
                    return;
                }

                // 检查系统数据是否已加载
                const hasSystemData = getAppState().get('systemData.lastUpdated') !== null;
                
                if (!hasSystemData) {
                    getLogger().log('AppState中无系统数据，开始加载', 'info');
                    try {
                        // **修复**: 触发数据加载并等待完成
                        await window.OTA.getService('apiService').getAllSystemData();
                    } catch (error) {
                        getLogger().log('系统数据加载失败', 'error', { error: error.message });
                    }
                }

                // 检查DOM选项是否已填充
                const carTypeOptions = this.elements.carTypeId ? this.elements.carTypeId.options.length : 0;
                if (carTypeOptions <= 1) { // 用一个下拉框作为代表
                    getLogger().log('检测到DOM未被填充，从AppState重新填充选项', 'info');
                    formManager.populateFormOptions();
                }

                // 填充表单数据
                const formData = currentOrder.parsedData || currentOrder;
                if (formData) {
                    getLogger().log('开始从当前订单状态更新表单');
                    formManager.fillFormFromData(formData);
                }
            } catch (error) {
                getLogger().log('更新订单表单失败', 'error', { error: error.message });
            }
        }

        /**
         * 更新主题图标
         * @param {string} theme - 主题名称
         */
        updateThemeIcon(theme) {
            if (this.elements.themeToggle) {
                this.elements.themeToggle.textContent = theme === 'light' ? '🌙' : '☀️';
                this.elements.themeToggle.title = theme === 'light' ? '切换到暗色主题' : '切换到亮色主题';
            }
        }

        /**
         * 设置按钮加载状态
         * @param {HTMLButtonElement} button - 按钮元素
         * @param {boolean} loading - 是否加载中
         */
        setButtonLoading(button, loading) {
            if (!button) return;
            
            const textSpan = button.querySelector('.btn-text');
            const loadingSpan = button.querySelector('.btn-loading');
            
            if (loading) {
                button.disabled = true;
                if (textSpan) textSpan.style.display = 'none';
                if (loadingSpan) loadingSpan.style.display = 'inline';
            } else {
                button.disabled = false;
                if (textSpan) textSpan.style.display = 'inline';
                if (loadingSpan) loadingSpan.style.display = 'none';
            }
        }

        /**
         * 显示提示消息
         * @param {string} message - 消息内容
         * @param {string} type - 消息类型
         * @param {number} duration - 显示时长
         */
        showAlert(message, type = 'info', duration = 5000) {
            // 创建提示元素
            const alert = document.createElement('div');
            alert.className = 'alert-message';
            alert.innerHTML = `
                <div class="alert-content">
                    <span class="alert-icon">${this.getAlertIcon(type)}</span>
                    <span class="alert-text">${message}</span>
                    <button class="alert-close" onclick="this.parentElement.parentElement.remove()">×</button>
                </div>
            `;

            // 设置样式
            alert.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                min-width: 300px;
                max-width: 500px;
                background: var(--color-surface);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                border-left: 4px solid var(--color-primary);
                animation: slideInRight 0.3s ease-out;
            `;

            // 设置内容样式
            const content = alert.querySelector('.alert-content');
            content.style.cssText = `
                display: flex;
                align-items: center;
                padding: 12px 16px;
                gap: 8px;
            `;

            // 根据类型设置颜色
            switch (type) {
                case 'success':
                    alert.style.borderLeftColor = 'var(--color-success)';
                    break;
                case 'error':
                    alert.style.borderLeftColor = 'var(--color-error)';
                    break;
                case 'warning':
                    alert.style.borderLeftColor = 'var(--color-warning)';
                    break;
                default:
                    alert.style.borderLeftColor = 'var(--color-info)';
            }

            // 添加到页面
            document.body.appendChild(alert);

            // 自动关闭
            const closeAlert = () => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            };

            // 点击关闭按钮
            alert.querySelector('.alert-close').addEventListener('click', closeAlert);

            // 自动关闭
            if (duration > 0) {
                setTimeout(closeAlert, duration);
            }

            getLogger().log('提示消息已显示', 'info', { message, type });
        }

        /**
         * 获取提示图标
         * @param {string} type - 提示类型
         * @returns {string} 图标
         */
        getAlertIcon(type) {
            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: 'ℹ️'
            };
            return icons[type] || icons.info;
        }

        /**
         * 显示快速提示
         * @param {string} message - 提示消息
         * @param {string} type - 提示类型
         */
        showQuickToast(message, type = 'info') {
            // 创建简单的提示元素
            const toast = document.createElement('div');
            toast.className = 'quick-toast';
            toast.textContent = message;
            
            // 设置样式
            toast.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 10001;
                padding: 12px 20px;
                background: var(--color-surface);
                color: var(--color-text);
                border-radius: 6px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
                font-size: 14px;
                animation: slideInUp 0.3s ease-out;
            `;

            document.body.appendChild(toast);

            // 3秒后自动移除
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 3000);
        }

        /**
         * 获取当前UI状态
         * @returns {Object} UI状态对象
         */
        getCurrentState() {
            return {
                isLoggedIn: getAppState().get('auth.isLoggedIn'),
                theme: getAppState().get('config.theme'),
                hasSystemData: getAppState().get('systemData.lastUpdated') !== null,
                currentOrder: getAppState().get('currentOrder'),
                geminiAvailable: getGeminiService().isAvailable(),
                realtimeEnabled: getGeminiService().isRealtimeEnabled()
            };
        }

        /**
         * 重置UI状态
         */
        resetUIState() {
            const logger = getLogger();
            logger.log('🔄 开始重置UI状态...', 'info');

            try {
                // **修复**: 使用UIManager获取唯一的Manager实例
                const formManager = window.OTA.uiManager?.getManager('form');
                if (formManager) {
                    formManager.resetForm();
                }

                const priceManager = window.OTA.uiManager?.getManager('price');
                if (priceManager) {
                    priceManager.resetPriceFields();
                }

                // 清除当前订单状态（但保护用户认证和系统配置）
                const protectedKeys = ['auth', 'systemData', 'carTypes', 'regions', 'orderHistory', 'learningData'];
                const currentState = getAppState().getAll();

                Object.keys(currentState).forEach(key => {
                    if (!protectedKeys.includes(key)) {
                        getAppState().set(key, null);
                    }
                });

                logger.log('✅ UI状态重置完成', 'success');
            } catch (error) {
                logger.logError('UI状态重置失败', error);
            }
        }

        /**
         * 获取需要保护的数据键列表
         * @returns {Array<string>} 受保护的数据键
         */
        getProtectedDataKeys() {
            return [
                'auth',           // 用户认证信息
                'systemData',     // 系统配置
                'carTypes',       // 车型数据
                'regions',        // 区域数据
                'orderHistory',   // 历史记录
                'learningData',   // 学习规则数据
                'userPreferences' // 用户偏好设置
            ];
        }
    }

    // 导出到全局命名空间
    window.OTA.managers.StateManager = StateManager;

})();
