/**
 * @MONITORING 性能监控系统
 * 🏷️ 标签: @PERFORMANCE_MONITOR
 * 📝 说明: 监控Gemini处理器系统的性能指标，提供实时监控和性能分析
 * 🎯 功能: 性能指标收集、实时监控、性能分析、告警系统
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.monitoring = window.OTA.gemini.monitoring || {};

(function() {
    'use strict';

    /**
     * 性能监控器类
     * 提供全面的性能监控和分析功能
     */
    class PerformanceMonitor {
        constructor() {
            this.logger = window.getLogger?.() || console;
            this.isEnabled = true;
            this.metrics = new Map();
            this.alerts = [];
            this.listeners = new Map();
            
            // 监控配置
            this.config = {
                // 采样率（0-1）
                samplingRate: 1.0,
                
                // 数据保留时间（毫秒）
                dataRetentionTime: 24 * 60 * 60 * 1000, // 24小时
                
                // 指标收集间隔（毫秒）
                metricsCollectionInterval: 5000, // 5秒
                
                // 告警检查间隔（毫秒）
                alertCheckInterval: 10000, // 10秒
                
                // 性能阈值（优化后）
                performanceThresholds: {
                    processingTime: 8000,       // 处理时间超过8秒告警
                    memoryUsage: 200 * 1024 * 1024, // 内存使用超过200MB告警
                    errorRate: 0.05,            // 错误率超过5%告警
                    fallbackRate: 0.2,          // 降级率超过20%告警
                    concurrentRequests: 30      // 并发请求超过30个告警
                }
            };
            
            // 性能指标存储
            this.performanceMetrics = {
                // 处理时间指标
                processingTimes: [],
                
                // 内存使用指标
                memoryUsage: [],
                
                // 错误统计
                errorStats: {
                    total: 0,
                    byProcessor: new Map(),
                    byErrorType: new Map(),
                    recentErrors: []
                },
                
                // 降级统计
                fallbackStats: {
                    total: 0,
                    byProcessor: new Map(),
                    byReason: new Map(),
                    recentFallbacks: []
                },
                
                // 处理器性能
                processorPerformance: new Map(),
                
                // 并发统计
                concurrencyStats: {
                    current: 0,
                    peak: 0,
                    history: []
                },
                
                // 吞吐量统计
                throughputStats: {
                    requestsPerSecond: 0,
                    requestsPerMinute: 0,
                    totalRequests: 0,
                    history: []
                }
            };
            
            this.init();
        }

        /**
         * 初始化性能监控器
         */
        init() {
            try {
                // 启动定期指标收集
                this.startMetricsCollection();
                
                // 启动告警检查
                this.startAlertMonitoring();
                
                // 注册性能观察者
                this.registerPerformanceObservers();
                
                // 监听页面卸载事件
                this.registerUnloadHandlers();
                
                this.logger.log('性能监控器初始化完成', 'info');
                
            } catch (error) {
                this.logger.logError('性能监控器初始化失败', error);
            }
        }

        /**
         * 记录处理开始
         * @param {string} processorName - 处理器名称
         * @param {string} requestId - 请求ID
         * @param {Object} context - 上下文信息
         * @returns {Object} 性能跟踪对象
         */
        startProcessingTracking(processorName, requestId, context = {}) {
            if (!this.isEnabled || Math.random() > this.config.samplingRate) {
                return null;
            }

            const trackingData = {
                processorName,
                requestId,
                startTime: performance.now(),
                startMemory: this.getMemoryUsage(),
                context,
                metrics: {}
            };

            // 更新并发统计
            this.performanceMetrics.concurrencyStats.current++;
            if (this.performanceMetrics.concurrencyStats.current > this.performanceMetrics.concurrencyStats.peak) {
                this.performanceMetrics.concurrencyStats.peak = this.performanceMetrics.concurrencyStats.current;
            }

            return trackingData;
        }

        /**
         * 记录处理结束
         * @param {Object} trackingData - 跟踪数据
         * @param {Object} result - 处理结果
         */
        endProcessingTracking(trackingData, result) {
            if (!trackingData || !this.isEnabled) {
                return;
            }

            const endTime = performance.now();
            const endMemory = this.getMemoryUsage();
            const processingTime = endTime - trackingData.startTime;
            const memoryDelta = endMemory - trackingData.startMemory;

            // 记录处理时间
            this.recordProcessingTime(trackingData.processorName, processingTime);

            // 记录内存使用
            this.recordMemoryUsage(memoryDelta);

            // 记录处理结果
            if (result.success) {
                this.recordSuccessfulProcessing(trackingData.processorName, processingTime, result);
            } else {
                this.recordFailedProcessing(trackingData.processorName, processingTime, result.error);
            }

            // 检查降级处理
            if (result.metadata && result.metadata.fallback && result.metadata.fallback.isFallback) {
                this.recordFallbackProcessing(
                    trackingData.processorName,
                    result.metadata.fallback.fallbackProcessor,
                    result.metadata.fallback.fallbackReason
                );
            }

            // 更新并发统计
            this.performanceMetrics.concurrencyStats.current--;

            // 更新吞吐量统计
            this.updateThroughputStats();

            // 触发性能事件
            this.emitPerformanceEvent('processingCompleted', {
                processorName: trackingData.processorName,
                processingTime,
                memoryDelta,
                success: result.success
            });
        }

        /**
         * 记录处理时间
         * @param {string} processorName - 处理器名称
         * @param {number} processingTime - 处理时间
         */
        recordProcessingTime(processorName, processingTime) {
            const timestamp = Date.now();
            
            // 记录全局处理时间
            this.performanceMetrics.processingTimes.push({
                timestamp,
                processorName,
                time: processingTime
            });

            // 记录处理器特定性能
            if (!this.performanceMetrics.processorPerformance.has(processorName)) {
                this.performanceMetrics.processorPerformance.set(processorName, {
                    totalRequests: 0,
                    totalTime: 0,
                    averageTime: 0,
                    minTime: Infinity,
                    maxTime: 0,
                    recentTimes: []
                });
            }

            const processorStats = this.performanceMetrics.processorPerformance.get(processorName);
            processorStats.totalRequests++;
            processorStats.totalTime += processingTime;
            processorStats.averageTime = processorStats.totalTime / processorStats.totalRequests;
            processorStats.minTime = Math.min(processorStats.minTime, processingTime);
            processorStats.maxTime = Math.max(processorStats.maxTime, processingTime);
            processorStats.recentTimes.push({ timestamp, time: processingTime });

            // 保持最近100条记录
            if (processorStats.recentTimes.length > 100) {
                processorStats.recentTimes.shift();
            }

            // 清理旧数据
            this.cleanupOldData();
        }

        /**
         * 记录内存使用
         * @param {number} memoryDelta - 内存变化量
         */
        recordMemoryUsage(memoryDelta) {
            const timestamp = Date.now();
            const currentMemory = this.getMemoryUsage();
            
            this.performanceMetrics.memoryUsage.push({
                timestamp,
                usage: currentMemory,
                delta: memoryDelta
            });

            // 保持最近1000条记录
            if (this.performanceMetrics.memoryUsage.length > 1000) {
                this.performanceMetrics.memoryUsage.shift();
            }
        }

        /**
         * 记录成功处理
         * @param {string} processorName - 处理器名称
         * @param {number} processingTime - 处理时间
         * @param {Object} result - 结果
         */
        recordSuccessfulProcessing(processorName, processingTime, result) {
            // 更新处理器成功统计
            if (!this.performanceMetrics.processorPerformance.has(processorName)) {
                this.performanceMetrics.processorPerformance.set(processorName, {
                    successCount: 0,
                    errorCount: 0,
                    successRate: 1.0
                });
            }

            const stats = this.performanceMetrics.processorPerformance.get(processorName);
            stats.successCount = (stats.successCount || 0) + 1;
            const totalRequests = (stats.successCount || 0) + (stats.errorCount || 0);
            stats.successRate = totalRequests > 0 ? stats.successCount / totalRequests : 1.0;
        }

        /**
         * 记录失败处理
         * @param {string} processorName - 处理器名称
         * @param {number} processingTime - 处理时间
         * @param {string} error - 错误信息
         */
        recordFailedProcessing(processorName, processingTime, error) {
            const timestamp = Date.now();
            
            // 更新全局错误统计
            this.performanceMetrics.errorStats.total++;
            
            // 更新处理器错误统计
            const processorErrorCount = this.performanceMetrics.errorStats.byProcessor.get(processorName) || 0;
            this.performanceMetrics.errorStats.byProcessor.set(processorName, processorErrorCount + 1);
            
            // 更新错误类型统计
            const errorType = this.categorizeError(error);
            const errorTypeCount = this.performanceMetrics.errorStats.byErrorType.get(errorType) || 0;
            this.performanceMetrics.errorStats.byErrorType.set(errorType, errorTypeCount + 1);
            
            // 记录最近错误
            this.performanceMetrics.errorStats.recentErrors.push({
                timestamp,
                processorName,
                error,
                errorType,
                processingTime
            });

            // 保持最近100条错误记录
            if (this.performanceMetrics.errorStats.recentErrors.length > 100) {
                this.performanceMetrics.errorStats.recentErrors.shift();
            }

            // 更新处理器错误统计
            if (!this.performanceMetrics.processorPerformance.has(processorName)) {
                this.performanceMetrics.processorPerformance.set(processorName, {
                    successCount: 0,
                    errorCount: 0,
                    successRate: 1.0
                });
            }

            const stats = this.performanceMetrics.processorPerformance.get(processorName);
            stats.errorCount = (stats.errorCount || 0) + 1;
            const totalRequests = (stats.successCount || 0) + (stats.errorCount || 0);
            stats.successRate = totalRequests > 0 ? stats.successCount / totalRequests : 0.0;
        }

        /**
         * 记录降级处理
         * @param {string} originalProcessor - 原始处理器
         * @param {string} fallbackProcessor - 降级处理器
         * @param {string} reason - 降级原因
         */
        recordFallbackProcessing(originalProcessor, fallbackProcessor, reason) {
            const timestamp = Date.now();
            
            // 更新全局降级统计
            this.performanceMetrics.fallbackStats.total++;
            
            // 更新处理器降级统计
            const processorFallbackCount = this.performanceMetrics.fallbackStats.byProcessor.get(originalProcessor) || 0;
            this.performanceMetrics.fallbackStats.byProcessor.set(originalProcessor, processorFallbackCount + 1);
            
            // 更新降级原因统计
            const reasonCount = this.performanceMetrics.fallbackStats.byReason.get(reason) || 0;
            this.performanceMetrics.fallbackStats.byReason.set(reason, reasonCount + 1);
            
            // 记录最近降级
            this.performanceMetrics.fallbackStats.recentFallbacks.push({
                timestamp,
                originalProcessor,
                fallbackProcessor,
                reason
            });

            // 保持最近100条降级记录
            if (this.performanceMetrics.fallbackStats.recentFallbacks.length > 100) {
                this.performanceMetrics.fallbackStats.recentFallbacks.shift();
            }
        }

        /**
         * 更新吞吐量统计
         */
        updateThroughputStats() {
            const now = Date.now();
            this.performanceMetrics.throughputStats.totalRequests++;
            
            // 记录历史数据点
            this.performanceMetrics.throughputStats.history.push({
                timestamp: now,
                totalRequests: this.performanceMetrics.throughputStats.totalRequests
            });

            // 计算每秒请求数（基于最近60秒）
            const oneMinuteAgo = now - 60000;
            const recentRequests = this.performanceMetrics.throughputStats.history.filter(
                point => point.timestamp > oneMinuteAgo
            );
            
            if (recentRequests.length > 1) {
                const timeSpan = (recentRequests[recentRequests.length - 1].timestamp - recentRequests[0].timestamp) / 1000;
                const requestCount = recentRequests[recentRequests.length - 1].totalRequests - recentRequests[0].totalRequests;
                this.performanceMetrics.throughputStats.requestsPerSecond = timeSpan > 0 ? requestCount / timeSpan : 0;
            }

            // 清理旧的历史数据（保留最近1小时）
            const oneHourAgo = now - 3600000;
            this.performanceMetrics.throughputStats.history = this.performanceMetrics.throughputStats.history.filter(
                point => point.timestamp > oneHourAgo
            );
        }

        /**
         * 启动指标收集
         */
        startMetricsCollection() {
            setInterval(() => {
                if (!this.isEnabled) return;

                // 收集系统指标
                this.collectSystemMetrics();
                
                // 检查性能阈值
                this.checkPerformanceThresholds();
                
            }, this.config.metricsCollectionInterval);
        }

        /**
         * 启动告警监控
         */
        startAlertMonitoring() {
            setInterval(() => {
                if (!this.isEnabled) return;

                this.checkAlerts();
                
            }, this.config.alertCheckInterval);
        }

        /**
         * 收集系统指标
         */
        collectSystemMetrics() {
            // 收集内存使用情况
            const memoryUsage = this.getMemoryUsage();
            this.recordMemoryUsage(0); // 记录当前内存使用，delta为0

            // 收集并发统计
            this.performanceMetrics.concurrencyStats.history.push({
                timestamp: Date.now(),
                current: this.performanceMetrics.concurrencyStats.current,
                peak: this.performanceMetrics.concurrencyStats.peak
            });

            // 保持最近1000条并发历史记录
            if (this.performanceMetrics.concurrencyStats.history.length > 1000) {
                this.performanceMetrics.concurrencyStats.history.shift();
            }
        }

        /**
         * 检查性能阈值
         */
        checkPerformanceThresholds() {
            const thresholds = this.config.performanceThresholds;
            
            // 检查处理时间
            const recentProcessingTimes = this.performanceMetrics.processingTimes
                .filter(item => Date.now() - item.timestamp < 300000) // 最近5分钟
                .map(item => item.time);
            
            if (recentProcessingTimes.length > 0) {
                const avgProcessingTime = recentProcessingTimes.reduce((a, b) => a + b, 0) / recentProcessingTimes.length;
                if (avgProcessingTime > thresholds.processingTime) {
                    this.createAlert('high_processing_time', `平均处理时间过高: ${avgProcessingTime.toFixed(2)}ms`);
                }
            }

            // 检查内存使用
            const currentMemory = this.getMemoryUsage();
            if (currentMemory > thresholds.memoryUsage) {
                this.createAlert('high_memory_usage', `内存使用过高: ${(currentMemory / 1024 / 1024).toFixed(2)}MB`);
            }

            // 检查错误率
            const totalRequests = this.performanceMetrics.throughputStats.totalRequests;
            const errorRate = totalRequests > 0 ? this.performanceMetrics.errorStats.total / totalRequests : 0;
            if (errorRate > thresholds.errorRate) {
                this.createAlert('high_error_rate', `错误率过高: ${(errorRate * 100).toFixed(2)}%`);
            }

            // 检查降级率
            const fallbackRate = totalRequests > 0 ? this.performanceMetrics.fallbackStats.total / totalRequests : 0;
            if (fallbackRate > thresholds.fallbackRate) {
                this.createAlert('high_fallback_rate', `降级率过高: ${(fallbackRate * 100).toFixed(2)}%`);
            }

            // 检查并发请求数
            if (this.performanceMetrics.concurrencyStats.current > thresholds.concurrentRequests) {
                this.createAlert('high_concurrency', `并发请求数过高: ${this.performanceMetrics.concurrencyStats.current}`);
            }
        }

        /**
         * 检查告警
         */
        checkAlerts() {
            const now = Date.now();
            
            // 清理过期告警（1小时后过期）
            this.alerts = this.alerts.filter(alert => now - alert.timestamp < 3600000);
            
            // 检查是否有需要升级的告警
            const criticalAlerts = this.alerts.filter(alert => 
                alert.level === 'critical' && !alert.notified
            );
            
            if (criticalAlerts.length > 0) {
                this.notifyOperators(criticalAlerts);
                criticalAlerts.forEach(alert => alert.notified = true);
            }
        }

        /**
         * 创建告警
         * @param {string} type - 告警类型
         * @param {string} message - 告警消息
         * @param {string} level - 告警级别
         */
        createAlert(type, message, level = 'warning') {
            const alert = {
                id: this.generateAlertId(),
                type,
                message,
                level,
                timestamp: Date.now(),
                notified: false
            };

            this.alerts.push(alert);
            
            this.logger.log(`🚨 性能告警: ${message}`, level === 'critical' ? 'error' : 'warning');
            
            // 触发告警事件
            this.emitPerformanceEvent('alert', alert);
        }

        /**
         * 获取内存使用情况
         * @returns {number} 内存使用量（字节）
         */
        getMemoryUsage() {
            if (performance.memory) {
                return performance.memory.usedJSHeapSize;
            }
            return 0;
        }

        /**
         * 分类错误类型
         * @param {string} error - 错误信息
         * @returns {string} 错误类型
         */
        categorizeError(error) {
            if (!error) return 'unknown';
            
            const errorStr = error.toString().toLowerCase();
            
            if (errorStr.includes('timeout')) return 'timeout';
            if (errorStr.includes('network')) return 'network';
            if (errorStr.includes('validation')) return 'validation';
            if (errorStr.includes('parsing')) return 'parsing';
            if (errorStr.includes('processor')) return 'processor';
            if (errorStr.includes('configuration')) return 'configuration';
            
            return 'other';
        }

        /**
         * 清理旧数据
         */
        cleanupOldData() {
            const cutoffTime = Date.now() - this.config.dataRetentionTime;
            
            // 清理处理时间数据
            this.performanceMetrics.processingTimes = this.performanceMetrics.processingTimes.filter(
                item => item.timestamp > cutoffTime
            );
            
            // 清理内存使用数据
            this.performanceMetrics.memoryUsage = this.performanceMetrics.memoryUsage.filter(
                item => item.timestamp > cutoffTime
            );
        }

        /**
         * 注册性能观察者
         */
        registerPerformanceObservers() {
            // 注册Performance Observer（如果支持）
            if (typeof PerformanceObserver !== 'undefined') {
                try {
                    const observer = new PerformanceObserver((list) => {
                        for (const entry of list.getEntries()) {
                            if (entry.entryType === 'measure') {
                                this.recordCustomMetric(entry.name, entry.duration);
                            }
                        }
                    });
                    
                    observer.observe({ entryTypes: ['measure'] });
                } catch (error) {
                    this.logger.log('Performance Observer不支持', 'warning');
                }
            }
        }

        /**
         * 注册卸载处理器
         */
        registerUnloadHandlers() {
            window.addEventListener('beforeunload', () => {
                this.exportMetrics();
            });
        }

        /**
         * 记录自定义指标
         * @param {string} name - 指标名称
         * @param {number} value - 指标值
         */
        recordCustomMetric(name, value) {
            if (!this.metrics.has(name)) {
                this.metrics.set(name, []);
            }
            
            this.metrics.get(name).push({
                timestamp: Date.now(),
                value
            });
        }

        /**
         * 触发性能事件
         * @param {string} eventType - 事件类型
         * @param {Object} data - 事件数据
         */
        emitPerformanceEvent(eventType, data) {
            const listeners = this.listeners.get(eventType) || [];
            listeners.forEach(listener => {
                try {
                    listener(data);
                } catch (error) {
                    this.logger.logError('性能事件监听器执行失败', error);
                }
            });
        }

        /**
         * 添加性能事件监听器
         * @param {string} eventType - 事件类型
         * @param {Function} listener - 监听器函数
         */
        addEventListener(eventType, listener) {
            if (!this.listeners.has(eventType)) {
                this.listeners.set(eventType, []);
            }
            this.listeners.get(eventType).push(listener);
        }

        /**
         * 移除性能事件监听器
         * @param {string} eventType - 事件类型
         * @param {Function} listener - 监听器函数
         */
        removeEventListener(eventType, listener) {
            const listeners = this.listeners.get(eventType) || [];
            const index = listeners.indexOf(listener);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }

        /**
         * 获取性能报告
         * @returns {Object} 性能报告
         */
        getPerformanceReport() {
            const now = Date.now();
            
            return {
                timestamp: now,
                summary: {
                    totalRequests: this.performanceMetrics.throughputStats.totalRequests,
                    requestsPerSecond: this.performanceMetrics.throughputStats.requestsPerSecond,
                    currentConcurrency: this.performanceMetrics.concurrencyStats.current,
                    peakConcurrency: this.performanceMetrics.concurrencyStats.peak,
                    totalErrors: this.performanceMetrics.errorStats.total,
                    totalFallbacks: this.performanceMetrics.fallbackStats.total,
                    currentMemoryUsage: this.getMemoryUsage()
                },
                processorPerformance: Object.fromEntries(this.performanceMetrics.processorPerformance),
                errorStats: {
                    total: this.performanceMetrics.errorStats.total,
                    byProcessor: Object.fromEntries(this.performanceMetrics.errorStats.byProcessor),
                    byErrorType: Object.fromEntries(this.performanceMetrics.errorStats.byErrorType),
                    recentErrors: this.performanceMetrics.errorStats.recentErrors.slice(-10)
                },
                fallbackStats: {
                    total: this.performanceMetrics.fallbackStats.total,
                    byProcessor: Object.fromEntries(this.performanceMetrics.fallbackStats.byProcessor),
                    byReason: Object.fromEntries(this.performanceMetrics.fallbackStats.byReason),
                    recentFallbacks: this.performanceMetrics.fallbackStats.recentFallbacks.slice(-10)
                },
                alerts: this.alerts.filter(alert => now - alert.timestamp < 3600000), // 最近1小时的告警
                customMetrics: Object.fromEntries(this.metrics)
            };
        }

        /**
         * 导出指标数据
         * @returns {Object} 导出的指标数据
         */
        exportMetrics() {
            const report = this.getPerformanceReport();
            
            // 保存到localStorage（如果可用）
            try {
                localStorage.setItem('ota_performance_metrics', JSON.stringify(report));
            } catch (error) {
                this.logger.log('无法保存性能指标到localStorage', 'warning');
            }
            
            return report;
        }

        /**
         * 通知操作员
         * @param {Array} alerts - 告警列表
         */
        notifyOperators(alerts) {
            // 这里可以集成实际的通知系统
            this.logger.log(`🚨 关键告警需要处理: ${alerts.length}个`, 'error');
            alerts.forEach(alert => {
                this.logger.log(`  - ${alert.type}: ${alert.message}`, 'error');
            });
        }

        /**
         * 生成告警ID
         * @returns {string} 告警ID
         */
        generateAlertId() {
            return 'alert_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        /**
         * 启用/禁用监控
         * @param {boolean} enabled - 是否启用
         */
        setEnabled(enabled) {
            this.isEnabled = enabled;
            this.logger.log(`性能监控${enabled ? '已启用' : '已禁用'}`, 'info');
        }

        /**
         * 重置所有指标
         */
        reset() {
            this.performanceMetrics = {
                processingTimes: [],
                memoryUsage: [],
                errorStats: {
                    total: 0,
                    byProcessor: new Map(),
                    byErrorType: new Map(),
                    recentErrors: []
                },
                fallbackStats: {
                    total: 0,
                    byProcessor: new Map(),
                    byReason: new Map(),
                    recentFallbacks: []
                },
                processorPerformance: new Map(),
                concurrencyStats: {
                    current: 0,
                    peak: 0,
                    history: []
                },
                throughputStats: {
                    requestsPerSecond: 0,
                    requestsPerMinute: 0,
                    totalRequests: 0,
                    history: []
                }
            };
            
            this.metrics.clear();
            this.alerts = [];
            
            this.logger.log('性能监控指标已重置', 'info');
        }
    }

    // 创建全局单例实例
    function getPerformanceMonitor() {
        if (!window.OTA.gemini.monitoring.performanceMonitor) {
            window.OTA.gemini.monitoring.performanceMonitor = new PerformanceMonitor();
        }
        return window.OTA.gemini.monitoring.performanceMonitor;
    }

    // 暴露到全局命名空间
    window.OTA.gemini.monitoring.PerformanceMonitor = PerformanceMonitor;
    window.OTA.gemini.monitoring.getPerformanceMonitor = getPerformanceMonitor;

    // 向后兼容
    window.getPerformanceMonitor = getPerformanceMonitor;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('performanceMonitor', getPerformanceMonitor(), '@PERFORMANCE_MONITOR');
        window.OTA.Registry.registerFactory('getPerformanceMonitor', getPerformanceMonitor, '@PERFORMANCE_MONITOR_FACTORY');
    }

    console.log('✅ 性能监控系统已加载');

})();
