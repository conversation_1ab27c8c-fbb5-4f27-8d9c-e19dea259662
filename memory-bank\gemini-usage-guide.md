# Gemini AI系统使用说明文档

## 📋 文档概述

本文档为OTA订单处理系统中Gemini AI组件的详细使用指南，涵盖基础使用、高级功能、最佳实践和故障排除。

**适用版本**: 2.0.0 (重构版本)  
**目标用户**: 开发者、系统管理员、业务用户  
**更新时间**: 2024-01-01

## 🚀 快速入门

### 系统要求
- **浏览器**: Chrome 90+, Firefox 88+, Safari 14+
- **网络**: 需要访问Google Gemini API
- **存储**: 本地存储支持（用于缓存）

### 基本使用流程

#### 1. 系统初始化
系统会在页面加载时自动初始化，无需手动操作。

```javascript
// 检查系统是否就绪
if (window.OTA && window.OTA.geminiService) {
    console.log('Gemini系统已就绪');
}
```

#### 2. 解析单个订单
```javascript
// 准备订单文本
const orderText = `
    客户姓名：张三
    联系电话：+60123456789
    接送地点：吉隆坡国际机场
    目的地：双子塔
    航班信息：MH123 15:30抵达
    订单号：ABC123456789
`;

// 解析订单
const result = await window.OTA.geminiService.parseOrder(orderText);

// 处理结果
if (result.success) {
    console.log('解析成功:', result.data);
    // 使用解析后的数据填充表单
} else {
    console.error('解析失败:', result.error);
    // 显示错误信息给用户
}
```

#### 3. 解析多个订单
```javascript
const multipleOrders = `
    订单1：
    客户：李四
    电话：+60198765432
    接机：KLIA2
    
    ---
    
    订单2：
    客户：王五
    电话：+60187654321
    送机：KLIA
`;

const results = await window.OTA.geminiService.parseMultipleOrders(multipleOrders);
results.forEach((result, index) => {
    console.log(`订单${index + 1}:`, result);
});
```

## 🔧 功能详解

### 1. 订单解析功能

#### 支持的订单格式
- **自然语言**: 支持中文、英文自然语言描述
- **结构化文本**: 支持键值对格式
- **混合格式**: 支持自然语言和结构化混合

#### 可识别的信息字段
- **客户信息**: 姓名、电话、邮箱
- **服务信息**: 接机、送机、包车
- **地点信息**: 出发地、目的地、酒店
- **时间信息**: 日期、时间、航班号
- **特殊要求**: 车型、语言、备注

#### 示例订单格式
```text
// 格式1：自然语言
明天下午3点在吉隆坡机场接张先生，电话+60123456789，送到双子塔酒店

// 格式2：结构化
客户姓名：李女士
联系电话：+60198765432
服务类型：送机
出发地点：希尔顿酒店
目的地：KLIA2
出发时间：2024-01-02 08:00
航班号：AK456

// 格式3：Chong Dealer格式
【Chong Dealer订单】
客户：陈先生
联系：+60176543210
服务：包车8小时
出发：酒店A
行程：市区观光
参考号：CD2024010001
```

### 2. OTA渠道识别

#### 自动渠道识别
系统会根据订单内容自动识别OTA渠道：
- **Agoda**: 识别Agoda特有的订单格式和字段
- **Booking.com**: 识别Booking特有的预订信息
- **Chong Dealer**: 识别专门的举牌服务格式
- **通用格式**: 处理其他平台或直接预订

#### 渠道特定处理
每个OTA渠道都有专门的处理逻辑：
```javascript
// 获取渠道识别结果
const result = await geminiService.parseOrder(orderText);
console.log('识别的OTA渠道:', result.detectedChannel);
console.log('置信度:', result.confidence);
```

### 3. 实时分析功能

#### 启用实时分析
```javascript
// 配置实时分析
window.OTA.geminiService.configureRealtimeAnalysis({
    enabled: true,           // 启用实时分析
    analysisDelay: 1500,     // 1.5秒延迟
    minTextLength: 20,       // 最小20字符触发
    maxConcurrent: 3         // 最大3个并发
});
```

#### 实时分析事件
```javascript
// 监听实时分析结果
document.addEventListener('realtimeAnalysisComplete', (event) => {
    const result = event.detail;
    console.log('实时分析结果:', result);
    // 自动填充表单字段
});
```

### 4. 图片分析功能

#### 上传图片分析
```javascript
// 处理图片上传
const fileInput = document.getElementById('imageUpload');
fileInput.addEventListener('change', async (event) => {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = async (e) => {
            const imageData = e.target.result;
            const result = await geminiService.analyzeImage(imageData);
            
            if (result.success) {
                console.log('提取的文本:', result.extractedText);
                console.log('解析的订单:', result.orderData);
            }
        };
        reader.readAsDataURL(file);
    }
});
```

## ⚙️ 高级配置

### 1. 性能优化配置

#### 缓存设置
```javascript
// 获取协调器实例
const coordinator = window.OTA.gemini.getGeminiCoordinator();

// 调整缓存配置
coordinator.config.cache = {
    enabled: true,
    maxSize: 5000,           // 增加缓存大小
    maxAge: 15 * 60 * 1000,  // 15分钟缓存
    enableLRU: true          // 启用LRU策略
};
```

#### 并发处理配置
```javascript
// 调整并发处理配置
coordinator.config.processing = {
    timeout: 15000,          // 15秒超时
    maxConcurrent: 20,       // 20个并发
    queueSize: 200,          // 队列大小200
    enableBatching: true     // 启用批处理
};
```

### 2. 监控和调试

#### 启用性能监控
```javascript
// 启用详细监控
coordinator.config.monitoring = {
    enabled: true,
    enableProfiling: true,   // 启用性能分析
    logSlowRequests: true,   // 记录慢请求
    slowRequestThreshold: 5000 // 5秒阈值
};
```

#### 获取系统状态
```javascript
// 获取详细状态信息
const status = geminiService.getStatus();
console.log('系统状态:', status);

// 获取性能指标
const metrics = coordinator.getMetrics();
console.log('性能指标:', metrics);
```

### 3. 错误处理配置

#### 自定义错误处理
```javascript
// 设置全局错误处理器
window.addEventListener('geminiError', (event) => {
    const error = event.detail;
    console.error('Gemini错误:', error);
    
    // 根据错误类型进行处理
    switch (error.code) {
        case 'E001':
            // 输入验证错误
            showUserMessage('请检查输入内容');
            break;
        case 'E002':
            // 超时错误
            showUserMessage('处理超时，请稍后重试');
            break;
        default:
            showUserMessage('系统错误，请联系技术支持');
    }
});
```

## 📊 最佳实践

### 1. 性能优化建议

#### 合理使用缓存
```javascript
// 对于重复的订单文本，系统会自动使用缓存
// 如需跳过缓存，可以设置skipCache选项
const result = await geminiService.parseOrder(orderText, { skipCache: true });
```

#### 批量处理优化
```javascript
// 对于多个订单，使用批量处理更高效
const results = await geminiService.parseMultipleOrders(multipleOrdersText);
// 而不是多次调用parseOrder
```

### 2. 错误处理最佳实践

#### 优雅降级
```javascript
async function parseOrderWithFallback(orderText) {
    try {
        const result = await geminiService.parseOrder(orderText);
        if (result.success) {
            return result;
        } else {
            // 使用降级处理
            return handleFallback(orderText);
        }
    } catch (error) {
        console.error('解析失败:', error);
        return handleFallback(orderText);
    }
}

function handleFallback(orderText) {
    // 实现基本的文本解析逻辑
    return {
        success: true,
        data: extractBasicInfo(orderText),
        source: 'fallback'
    };
}
```

### 3. 用户体验优化

#### 加载状态提示
```javascript
async function parseOrderWithUI(orderText) {
    // 显示加载状态
    showLoadingIndicator('正在解析订单...');
    
    try {
        const result = await geminiService.parseOrder(orderText);
        
        // 隐藏加载状态
        hideLoadingIndicator();
        
        if (result.success) {
            showSuccessMessage('订单解析成功');
            return result;
        } else {
            showErrorMessage('订单解析失败: ' + result.error);
        }
    } catch (error) {
        hideLoadingIndicator();
        showErrorMessage('系统错误: ' + error.message);
    }
}
```

#### 实时反馈
```javascript
// 为文本输入添加实时分析
const orderInput = document.getElementById('orderInput');
let analysisTimeout;

orderInput.addEventListener('input', () => {
    clearTimeout(analysisTimeout);
    analysisTimeout = setTimeout(async () => {
        const text = orderInput.value;
        if (text.length >= 20) {
            const result = await geminiService.parseOrder(text, true);
            updatePreview(result);
        }
    }, 1500);
});
```

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 系统初始化失败
**症状**: 页面加载后Gemini服务不可用
**解决方案**:
```javascript
// 检查初始化状态
if (!window.OTA || !window.OTA.geminiService) {
    console.error('Gemini服务未初始化');
    // 尝试手动初始化
    setTimeout(() => {
        if (window.OTA && window.OTA.geminiService) {
            console.log('延迟初始化成功');
        }
    }, 2000);
}
```

#### 2. 解析结果不准确
**症状**: 订单信息提取不完整或错误
**解决方案**:
- 检查订单文本格式是否清晰
- 尝试使用更结构化的文本格式
- 检查是否包含必要的关键信息

#### 3. 性能问题
**症状**: 解析速度慢或系统响应迟缓
**解决方案**:
```javascript
// 检查性能指标
const metrics = coordinator.getMetrics();
console.log('缓存命中率:', metrics.cacheStats.hitRate);
console.log('平均处理时间:', metrics.processingStats.averageProcessingTime);

// 如果缓存命中率低，考虑调整缓存配置
// 如果处理时间长，检查网络连接和API响应
```

## 📞 技术支持

### 获取帮助
- **文档**: 查看完整的API文档和架构说明
- **日志**: 检查浏览器控制台的错误信息
- **状态页面**: 访问 `/status.html` 查看系统状态
- **测试页面**: 使用测试页面验证功能

### 联系方式
- **技术团队**: OTA系统开发组
- **更新频率**: 定期更新以反映最新功能

---

**文档版本**: 2.0.0  
**最后更新**: 2024-01-01  
**维护状态**: 活跃维护中
