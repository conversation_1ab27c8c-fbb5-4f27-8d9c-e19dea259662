/**
 * @OTA_CORE 模块热替换系统
 * 🏷️ 标签: @HOT_REPLACEMENT @MODULE_RELOAD @DEVELOPMENT_TOOL
 * 📝 说明: 实现模块热替换功能，支持开发时无刷新更新模块代码
 * ⚠️ 警告: 开发环境专用功能，生产环境请禁用
 */

(function() {
    'use strict';

    // 延迟获取依赖，确保加载顺序
    function getConfigCenter() {
        return window.OTA?.configCenter || window.getConfigCenter?.();
    }

    function getDependencyResolver() {
        return window.OTA?.dependencyResolver || window.getDependencyResolver?.();
    }

    function getLazyLoader() {
        return window.OTA?.lazyLoader || window.getLazyLoader?.();
    }

    /**
     * @OTA_CORE 模块热替换系统类
     * 提供开发时模块热替换、状态保持和依赖更新功能
     */
    class ModuleHotReplacement {
        constructor() {
            this.logger = window.OTA.getService('logger');
            this.configCenter = getConfigCenter();
            this.dependencyResolver = getDependencyResolver();
            this.lazyLoader = getLazyLoader();
            
            // 热替换配置
            this.hmrConfig = this.loadHMRConfig();
            
            // 模块注册表
            this.moduleRegistry = new Map();
            
            // 模块状态存储
            this.moduleStates = new Map();
            
            // 依赖关系图
            this.dependencyGraph = new Map();
            
            // 文件监听器
            this.fileWatchers = new Map();
            
            // 热替换历史
            this.replacementHistory = [];
            
            // 回调注册表
            this.callbacks = {
                beforeReplace: new Set(),
                afterReplace: new Set(),
                onError: new Set(),
                onStateRestore: new Set()
            };
            
            // 性能指标
            this.metrics = {
                totalReplacements: 0,
                successfulReplacements: 0,
                failedReplacements: 0,
                averageReplacementTime: 0,
                lastReplacementTime: 0,
                stateRestoreCount: 0
            };
            
            // 开发服务器连接
            this.devServerConnection = null;
            
            // 热替换状态
            this.hmrState = {
                enabled: false,
                watching: false,
                connected: false,
                lastUpdate: null
            };
            
            this.initialize();
            
            this.logger.log('✅ 模块热替换系统已初始化', 'info', {
                enabled: this.hmrConfig.enabled,
                watchMode: this.hmrConfig.watchMode,
                devServer: this.hmrConfig.devServer.enabled
            });
        }

        /**
         * 加载热替换配置
         * @returns {Object} 热替换配置
         */
        loadHMRConfig() {
            const defaultConfig = {
                // 基础配置
                enabled: this.isDevelopmentMode(),
                watchMode: 'polling', // 'polling' | 'websocket' | 'sse'
                pollInterval: 1000,
                
                // 开发服务器配置
                devServer: {
                    enabled: false,
                    host: 'localhost',
                    port: 3001,
                    protocol: 'ws',
                    reconnectInterval: 3000,
                    maxReconnectAttempts: 10
                },
                
                // 模块配置
                modules: {
                    autoAccept: true,
                    preserveState: true,
                    cascadeUpdate: true,
                    updateTimeout: 5000
                },
                
                // 文件监听配置
                fileWatching: {
                    enabled: true,
                    extensions: ['.js', '.css', '.html'],
                    excludePatterns: [
                        /node_modules/,
                        /\.git/,
                        /\.vscode/,
                        /test-.*\.html$/
                    ],
                    debounceDelay: 300
                },
                
                // 状态管理配置
                stateManagement: {
                    enabled: true,
                    autoSave: true,
                    saveInterval: 2000,
                    maxStateHistory: 10,
                    excludeKeys: ['_internal', '_temp', '_cache']
                },
                
                // 错误处理配置
                errorHandling: {
                    fallbackToReload: true,
                    maxRetries: 3,
                    retryDelay: 1000,
                    logErrors: true
                },
                
                // 通知配置
                notifications: {
                    enabled: true,
                    showSuccess: true,
                    showErrors: true,
                    position: 'top-right',
                    duration: 3000
                }
            };

            if (this.configCenter) {
                const hmrConfig = this.configCenter.getConfig('hotReplacement') || {};
                return {
                    ...defaultConfig,
                    ...hmrConfig
                };
            }

            return defaultConfig;
        }

        /**
         * 检测是否为开发模式
         * @returns {boolean} 是否为开发模式
         */
        isDevelopmentMode() {
            // 检测开发环境的多种方式
            return (
                window.location.hostname === 'localhost' ||
                window.location.hostname === '127.0.0.1' ||
                window.location.protocol === 'file:' ||
                window.location.search.includes('dev=true') ||
                localStorage.getItem('ota-dev-mode') === 'true' ||
                document.documentElement.hasAttribute('data-dev-mode')
            );
        }

        /**
         * 初始化热替换系统
         */
        initialize() {
            if (!this.hmrConfig.enabled) {
                this.logger.log('🔧 热替换系统已禁用', 'info');
                return;
            }

            // 设置文件监听
            if (this.hmrConfig.fileWatching.enabled) {
                this.setupFileWatching();
            }

            // 连接开发服务器
            if (this.hmrConfig.devServer.enabled) {
                this.connectToDevServer();
            }

            // 设置全局热替换API
            this.setupGlobalAPI();

            // 设置错误处理
            this.setupErrorHandling();

            // 设置状态管理
            if (this.hmrConfig.stateManagement.enabled) {
                this.setupStateManagement();
            }

            // 设置通知系统
            if (this.hmrConfig.notifications.enabled) {
                this.setupNotificationSystem();
            }

            this.hmrState.enabled = true;
        }

        /**
         * 设置文件监听
         */
        setupFileWatching() {
            if (this.hmrConfig.watchMode === 'polling') {
                this.setupPollingWatcher();
            } else if (this.hmrConfig.watchMode === 'websocket') {
                this.setupWebSocketWatcher();
            } else if (this.hmrConfig.watchMode === 'sse') {
                this.setupSSEWatcher();
            }
        }

        /**
         * 设置轮询监听器
         */
        setupPollingWatcher() {
            // 获取所有已加载的脚本文件
            const scripts = Array.from(document.querySelectorAll('script[src]'));
            const watchedFiles = new Map();

            scripts.forEach(script => {
                const src = script.src;
                if (this.shouldWatchFile(src)) {
                    watchedFiles.set(src, {
                        lastModified: null,
                        element: script,
                        path: this.getRelativePath(src)
                    });
                }
            });

            // 开始轮询
            const pollInterval = setInterval(async () => {
                for (const [url, fileInfo] of watchedFiles) {
                    try {
                        const response = await fetch(url, { 
                            method: 'HEAD',
                            cache: 'no-cache'
                        });
                        
                        const lastModified = response.headers.get('Last-Modified');
                        
                        if (fileInfo.lastModified && lastModified !== fileInfo.lastModified) {
                            this.logger.log(`📁 检测到文件变化: ${fileInfo.path}`, 'info');
                            await this.handleFileChange(fileInfo.path, url);
                        }
                        
                        fileInfo.lastModified = lastModified;
                        
                    } catch (error) {
                        // 忽略网络错误，继续监听
                    }
                }
            }, this.hmrConfig.pollInterval);

            this.fileWatchers.set('polling', pollInterval);
            this.hmrState.watching = true;
        }

        /**
         * 设置WebSocket监听器
         */
        setupWebSocketWatcher() {
            const wsUrl = `${this.hmrConfig.devServer.protocol}://${this.hmrConfig.devServer.host}:${this.hmrConfig.devServer.port}/hmr`;
            
            try {
                const ws = new WebSocket(wsUrl);
                
                ws.onopen = () => {
                    this.logger.log('🔗 WebSocket连接已建立', 'success');
                    this.hmrState.connected = true;
                    this.hmrState.watching = true;
                };
                
                ws.onmessage = async (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        
                        if (data.type === 'file-changed') {
                            await this.handleFileChange(data.path, data.url);
                        } else if (data.type === 'full-reload') {
                            this.performFullReload();
                        }
                    } catch (error) {
                        this.logger.logError('WebSocket消息处理失败', error);
                    }
                };
                
                ws.onclose = () => {
                    this.logger.log('🔗 WebSocket连接已断开', 'warning');
                    this.hmrState.connected = false;
                    this.attemptReconnect();
                };
                
                ws.onerror = (error) => {
                    this.logger.logError('WebSocket连接错误', error);
                };
                
                this.fileWatchers.set('websocket', ws);
                
            } catch (error) {
                this.logger.logError('WebSocket设置失败', error);
                // 回退到轮询模式
                this.hmrConfig.watchMode = 'polling';
                this.setupPollingWatcher();
            }
        }

        /**
         * 设置Server-Sent Events监听器
         */
        setupSSEWatcher() {
            const sseUrl = `http://${this.hmrConfig.devServer.host}:${this.hmrConfig.devServer.port}/hmr-events`;
            
            try {
                const eventSource = new EventSource(sseUrl);
                
                eventSource.onopen = () => {
                    this.logger.log('📡 SSE连接已建立', 'success');
                    this.hmrState.connected = true;
                    this.hmrState.watching = true;
                };
                
                eventSource.onmessage = async (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        
                        if (data.type === 'file-changed') {
                            await this.handleFileChange(data.path, data.url);
                        }
                    } catch (error) {
                        this.logger.logError('SSE消息处理失败', error);
                    }
                };
                
                eventSource.onerror = () => {
                    this.logger.log('📡 SSE连接错误', 'warning');
                    this.hmrState.connected = false;
                };
                
                this.fileWatchers.set('sse', eventSource);
                
            } catch (error) {
                this.logger.logError('SSE设置失败', error);
                // 回退到轮询模式
                this.hmrConfig.watchMode = 'polling';
                this.setupPollingWatcher();
            }
        }

        /**
         * 判断是否应该监听文件
         * @param {string} url - 文件URL
         * @returns {boolean} 是否应该监听
         */
        shouldWatchFile(url) {
            // 检查文件扩展名
            const hasValidExtension = this.hmrConfig.fileWatching.extensions.some(ext => 
                url.endsWith(ext)
            );
            
            if (!hasValidExtension) return false;
            
            // 检查排除模式
            const isExcluded = this.hmrConfig.fileWatching.excludePatterns.some(pattern => 
                pattern.test(url)
            );
            
            return !isExcluded;
        }

        /**
         * 获取相对路径
         * @param {string} url - 完整URL
         * @returns {string} 相对路径
         */
        getRelativePath(url) {
            const baseUrl = window.location.origin + window.location.pathname.replace(/\/[^\/]*$/, '/');
            return url.replace(baseUrl, '');
        }

        /**
         * 处理文件变化
         * @param {string} filePath - 文件路径
         * @param {string} fileUrl - 文件URL
         */
        async handleFileChange(filePath, fileUrl) {
            const startTime = performance.now();
            
            try {
                this.logger.log(`🔄 开始热替换: ${filePath}`, 'info');
                
                // 触发替换前回调
                await this.triggerCallbacks('beforeReplace', { filePath, fileUrl });
                
                // 保存当前状态
                if (this.hmrConfig.stateManagement.enabled) {
                    await this.saveModuleState(filePath);
                }
                
                // 执行热替换
                const success = await this.performHotReplacement(filePath, fileUrl);
                
                if (success) {
                    // 恢复状态
                    if (this.hmrConfig.stateManagement.enabled) {
                        await this.restoreModuleState(filePath);
                    }
                    
                    // 更新依赖
                    if (this.hmrConfig.modules.cascadeUpdate) {
                        await this.updateDependentModules(filePath);
                    }
                    
                    // 记录成功
                    this.metrics.successfulReplacements++;
                    this.showNotification(`✅ ${filePath} 热替换成功`, 'success');
                    
                    // 触发替换后回调
                    await this.triggerCallbacks('afterReplace', { filePath, fileUrl, success: true });
                    
                } else {
                    throw new Error('热替换执行失败');
                }
                
            } catch (error) {
                this.logger.logError(`热替换失败: ${filePath}`, error);
                this.metrics.failedReplacements++;
                
                // 触发错误回调
                await this.triggerCallbacks('onError', { filePath, fileUrl, error });
                
                // 错误处理
                if (this.hmrConfig.errorHandling.fallbackToReload) {
                    this.showNotification(`❌ ${filePath} 热替换失败，将刷新页面`, 'error');
                    setTimeout(() => window.location.reload(), 2000);
                } else {
                    this.showNotification(`❌ ${filePath} 热替换失败`, 'error');
                }
            } finally {
                const endTime = performance.now();
                const replacementTime = endTime - startTime;
                
                // 更新性能指标
                this.metrics.totalReplacements++;
                this.metrics.lastReplacementTime = replacementTime;
                this.metrics.averageReplacementTime = 
                    (this.metrics.averageReplacementTime * (this.metrics.totalReplacements - 1) + replacementTime) / 
                    this.metrics.totalReplacements;
                
                // 记录历史
                this.replacementHistory.push({
                    filePath,
                    fileUrl,
                    timestamp: new Date().toISOString(),
                    duration: replacementTime,
                    success: this.metrics.successfulReplacements > 0
                });
                
                // 限制历史记录数量
                if (this.replacementHistory.length > 50) {
                    this.replacementHistory.shift();
                }
                
                this.hmrState.lastUpdate = new Date().toISOString();
            }
        }

        /**
         * 执行热替换
         * @param {string} filePath - 文件路径
         * @param {string} fileUrl - 文件URL
         * @returns {boolean} 是否成功
         */
        async performHotReplacement(filePath, fileUrl) {
            try {
                // 根据文件类型执行不同的替换策略
                if (filePath.endsWith('.js')) {
                    return await this.replaceJavaScriptModule(filePath, fileUrl);
                } else if (filePath.endsWith('.css')) {
                    return await this.replaceCSSModule(filePath, fileUrl);
                } else if (filePath.endsWith('.html')) {
                    return await this.replaceHTMLContent(filePath, fileUrl);
                }
                
                return false;
                
            } catch (error) {
                this.logger.logError('热替换执行失败', error);
                return false;
            }
        }

        /**
         * 替换JavaScript模块
         * @param {string} filePath - 文件路径
         * @param {string} fileUrl - 文件URL
         * @returns {boolean} 是否成功
         */
        async replaceJavaScriptModule(filePath, fileUrl) {
            try {
                // 查找对应的script标签
                const existingScript = document.querySelector(`script[src*="${filePath}"]`);
                
                if (!existingScript) {
                    this.logger.log(`未找到对应的script标签: ${filePath}`, 'warning');
                    return false;
                }
                
                // 创建新的script标签
                const newScript = document.createElement('script');
                newScript.src = fileUrl + '?t=' + Date.now(); // 添加时间戳避免缓存
                newScript.type = existingScript.type || 'text/javascript';
                
                // 复制其他属性
                Array.from(existingScript.attributes).forEach(attr => {
                    if (attr.name !== 'src') {
                        newScript.setAttribute(attr.name, attr.value);
                    }
                });
                
                // 等待新脚本加载
                await new Promise((resolve, reject) => {
                    newScript.onload = resolve;
                    newScript.onerror = reject;
                    
                    // 插入新脚本
                    existingScript.parentNode.insertBefore(newScript, existingScript.nextSibling);
                    
                    // 设置超时
                    setTimeout(() => reject(new Error('脚本加载超时')), this.hmrConfig.modules.updateTimeout);
                });
                
                // 移除旧脚本
                existingScript.remove();
                
                this.logger.log(`✅ JavaScript模块已替换: ${filePath}`, 'success');
                return true;
                
            } catch (error) {
                this.logger.logError(`JavaScript模块替换失败: ${filePath}`, error);
                return false;
            }
        }

        /**
         * 替换CSS模块
         * @param {string} filePath - 文件路径
         * @param {string} fileUrl - 文件URL
         * @returns {boolean} 是否成功
         */
        async replaceCSSModule(filePath, fileUrl) {
            try {
                // 查找对应的link标签
                const existingLink = document.querySelector(`link[href*="${filePath}"]`);
                
                if (!existingLink) {
                    this.logger.log(`未找到对应的link标签: ${filePath}`, 'warning');
                    return false;
                }
                
                // 创建新的link标签
                const newLink = document.createElement('link');
                newLink.rel = 'stylesheet';
                newLink.href = fileUrl + '?t=' + Date.now(); // 添加时间戳避免缓存
                
                // 复制其他属性
                Array.from(existingLink.attributes).forEach(attr => {
                    if (attr.name !== 'href') {
                        newLink.setAttribute(attr.name, attr.value);
                    }
                });
                
                // 等待新样式表加载
                await new Promise((resolve, reject) => {
                    newLink.onload = resolve;
                    newLink.onerror = reject;
                    
                    // 插入新样式表
                    document.head.appendChild(newLink);
                    
                    // 设置超时
                    setTimeout(() => reject(new Error('样式表加载超时')), this.hmrConfig.modules.updateTimeout);
                });
                
                // 移除旧样式表
                existingLink.remove();
                
                this.logger.log(`✅ CSS模块已替换: ${filePath}`, 'success');
                return true;
                
            } catch (error) {
                this.logger.logError(`CSS模块替换失败: ${filePath}`, error);
                return false;
            }
        }

        /**
         * 替换HTML内容
         * @param {string} filePath - 文件路径
         * @param {string} fileUrl - 文件URL
         * @returns {boolean} 是否成功
         */
        async replaceHTMLContent(filePath, fileUrl) {
            try {
                // 获取新的HTML内容
                const response = await fetch(fileUrl + '?t=' + Date.now());
                const newHTML = await response.text();
                
                // 解析新HTML
                const parser = new DOMParser();
                const newDoc = parser.parseFromString(newHTML, 'text/html');
                
                // 更新特定部分（避免全页面替换）
                const updateableElements = document.querySelectorAll('[data-hmr-update]');
                
                updateableElements.forEach(element => {
                    const selector = element.getAttribute('data-hmr-update');
                    const newElement = newDoc.querySelector(selector);
                    
                    if (newElement) {
                        element.innerHTML = newElement.innerHTML;
                    }
                });
                
                this.logger.log(`✅ HTML内容已替换: ${filePath}`, 'success');
                return true;
                
            } catch (error) {
                this.logger.logError(`HTML内容替换失败: ${filePath}`, error);
                return false;
            }
        }

        /**
         * 保存模块状态
         * @param {string} filePath - 文件路径
         */
        async saveModuleState(filePath) {
            try {
                // 获取模块相关的状态
                const moduleState = this.extractModuleState(filePath);
                
                if (moduleState) {
                    this.moduleStates.set(filePath, {
                        state: moduleState,
                        timestamp: Date.now(),
                        version: this.getModuleVersion(filePath)
                    });
                    
                    this.logger.log(`💾 模块状态已保存: ${filePath}`, 'info');
                }
                
            } catch (error) {
                this.logger.logError(`模块状态保存失败: ${filePath}`, error);
            }
        }

        /**
         * 恢复模块状态
         * @param {string} filePath - 文件路径
         */
        async restoreModuleState(filePath) {
            try {
                const savedState = this.moduleStates.get(filePath);
                
                if (savedState) {
                    await this.applyModuleState(filePath, savedState.state);
                    this.metrics.stateRestoreCount++;
                    
                    // 触发状态恢复回调
                    await this.triggerCallbacks('onStateRestore', { filePath, state: savedState.state });
                    
                    this.logger.log(`🔄 模块状态已恢复: ${filePath}`, 'success');
                }
                
            } catch (error) {
                this.logger.logError(`模块状态恢复失败: ${filePath}`, error);
            }
        }

        /**
         * 提取模块状态
         * @param {string} filePath - 文件路径
         * @returns {Object|null} 模块状态
         */
        extractModuleState(filePath) {
            // 根据文件路径确定模块名称
            const moduleName = this.getModuleName(filePath);
            
            // 尝试从全局对象中提取状态
            if (window.OTA && window.OTA[moduleName]) {
                const module = window.OTA[moduleName];
                
                if (typeof module.getState === 'function') {
                    return module.getState();
                }
                
                // 提取公共属性（排除私有和方法）
                const state = {};
                Object.keys(module).forEach(key => {
                    if (!key.startsWith('_') && 
                        typeof module[key] !== 'function' &&
                        !this.hmrConfig.stateManagement.excludeKeys.includes(key)) {
                        state[key] = module[key];
                    }
                });
                
                return Object.keys(state).length > 0 ? state : null;
            }
            
            return null;
        }

        /**
         * 应用模块状态
         * @param {string} filePath - 文件路径
         * @param {Object} state - 状态对象
         */
        async applyModuleState(filePath, state) {
            const moduleName = this.getModuleName(filePath);
            
            if (window.OTA && window.OTA[moduleName]) {
                const module = window.OTA[moduleName];
                
                if (typeof module.setState === 'function') {
                    module.setState(state);
                } else {
                    // 直接设置属性
                    Object.keys(state).forEach(key => {
                        if (key in module) {
                            module[key] = state[key];
                        }
                    });
                }
            }
        }

        /**
         * 获取模块名称
         * @param {string} filePath - 文件路径
         * @returns {string} 模块名称
         */
        getModuleName(filePath) {
            // 从文件路径提取模块名称
            const fileName = filePath.split('/').pop().replace(/\.(js|css|html)$/, '');
            
            // 转换为驼峰命名
            return fileName.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
        }

        /**
         * 获取模块版本
         * @param {string} filePath - 文件路径
         * @returns {string} 模块版本
         */
        getModuleVersion(filePath) {
            // 简单的版本标识（基于时间戳）
            return Date.now().toString();
        }

        /**
         * 更新依赖模块
         * @param {string} filePath - 文件路径
         */
        async updateDependentModules(filePath) {
            try {
                // 查找依赖此模块的其他模块
                const dependents = this.findDependentModules(filePath);
                
                for (const dependent of dependents) {
                    this.logger.log(`🔄 更新依赖模块: ${dependent}`, 'info');
                    
                    // 重新初始化依赖模块
                    await this.reinitializeModule(dependent);
                }
                
            } catch (error) {
                this.logger.logError('依赖模块更新失败', error);
            }
        }

        /**
         * 查找依赖模块
         * @param {string} filePath - 文件路径
         * @returns {Array} 依赖模块列表
         */
        findDependentModules(filePath) {
            const dependents = [];
            
            // 从依赖解析器获取依赖关系
            if (this.dependencyResolver) {
                const deps = this.dependencyResolver.getDependencyGraph?.();
                
                if (deps) {
                    Object.keys(deps).forEach(module => {
                        if (deps[module].includes(filePath)) {
                            dependents.push(module);
                        }
                    });
                }
            }
            
            return dependents;
        }

        /**
         * 重新初始化模块
         * @param {string} modulePath - 模块路径
         */
        async reinitializeModule(modulePath) {
            try {
                const moduleName = this.getModuleName(modulePath);
                
                if (window.OTA && window.OTA[moduleName]) {
                    const module = window.OTA[moduleName];
                    
                    // 如果模块有重新初始化方法
                    if (typeof module.reinitialize === 'function') {
                        await module.reinitialize();
                    } else if (typeof module.initialize === 'function') {
                        await module.initialize();
                    }
                }
                
            } catch (error) {
                this.logger.logError(`模块重新初始化失败: ${modulePath}`, error);
            }
        }

        /**
         * 设置全局API
         */
        setupGlobalAPI() {
            // 设置全局热替换API
            window.module = window.module || {};
            window.module.hot = {
                accept: (dependencies, callback) => {
                    this.registerCallback('afterReplace', callback);
                },
                
                decline: (dependencies) => {
                    // 标记模块不接受热替换
                    if (Array.isArray(dependencies)) {
                        dependencies.forEach(dep => {
                            this.moduleRegistry.set(dep, { acceptsHMR: false });
                        });
                    }
                },
                
                dispose: (callback) => {
                    this.registerCallback('beforeReplace', callback);
                },
                
                invalidate: () => {
                    // 强制刷新页面
                    window.location.reload();
                },
                
                status: () => {
                    return this.hmrState.enabled ? 'ready' : 'disabled';
                }
            };
        }

        /**
         * 设置错误处理
         */
        setupErrorHandling() {
            // 捕获全局错误
            window.addEventListener('error', (event) => {
                if (this.hmrConfig.errorHandling.logErrors) {
                    this.logger.logError('全局错误', event.error);
                }
            });
            
            // 捕获Promise拒绝
            window.addEventListener('unhandledrejection', (event) => {
                if (this.hmrConfig.errorHandling.logErrors) {
                    this.logger.logError('未处理的Promise拒绝', event.reason);
                }
            });
        }

        /**
         * 设置状态管理
         */
        setupStateManagement() {
            if (this.hmrConfig.stateManagement.autoSave) {
                // 定期保存状态
                setInterval(() => {
                    this.saveAllModuleStates();
                }, this.hmrConfig.stateManagement.saveInterval);
            }
        }

        /**
         * 保存所有模块状态
         */
        saveAllModuleStates() {
            try {
                // 获取所有已注册的模块
                const modules = Object.keys(window.OTA || {});
                
                modules.forEach(moduleName => {
                    const filePath = this.getFilePathFromModuleName(moduleName);
                    if (filePath) {
                        this.saveModuleState(filePath);
                    }
                });
                
            } catch (error) {
                this.logger.logError('批量状态保存失败', error);
            }
        }

        /**
         * 从模块名获取文件路径
         * @param {string} moduleName - 模块名称
         * @returns {string|null} 文件路径
         */
        getFilePathFromModuleName(moduleName) {
            // 简单的映射逻辑，实际项目中可能需要更复杂的映射
            const fileName = moduleName.replace(/([A-Z])/g, '-$1').toLowerCase().replace(/^-/, '');
            return `js/${fileName}.js`;
        }

        /**
         * 设置通知系统
         */
        setupNotificationSystem() {
            // 创建通知容器
            if (!document.getElementById('hmr-notifications')) {
                const container = document.createElement('div');
                container.id = 'hmr-notifications';
                container.style.cssText = `
                    position: fixed;
                    ${this.hmrConfig.notifications.position.includes('top') ? 'top: 20px;' : 'bottom: 20px;'}
                    ${this.hmrConfig.notifications.position.includes('right') ? 'right: 20px;' : 'left: 20px;'}
                    z-index: 10001;
                    pointer-events: none;
                `;
                document.body.appendChild(container);
            }
        }

        /**
         * 显示通知
         * @param {string} message - 通知消息
         * @param {string} type - 通知类型
         */
        showNotification(message, type = 'info') {
            if (!this.hmrConfig.notifications.enabled) return;
            
            const container = document.getElementById('hmr-notifications');
            if (!container) return;
            
            const notification = document.createElement('div');
            notification.style.cssText = `
                background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
                color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
                border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
                padding: 12px 16px;
                border-radius: 4px;
                margin-bottom: 8px;
                font-size: 14px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                pointer-events: auto;
                opacity: 0;
                transform: translateX(${this.hmrConfig.notifications.position.includes('right') ? '100%' : '-100%'});
                transition: all 0.3s ease;
            `;
            
            notification.textContent = message;
            container.appendChild(notification);
            
            // 动画显示
            requestAnimationFrame(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            });
            
            // 自动隐藏
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = `translateX(${this.hmrConfig.notifications.position.includes('right') ? '100%' : '-100%'})`;
                
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, this.hmrConfig.notifications.duration);
        }

        /**
         * 连接到开发服务器
         */
        connectToDevServer() {
            // 这里可以实现与开发服务器的连接逻辑
            // 例如建立WebSocket连接来接收文件变化通知
            this.logger.log('🔗 尝试连接到开发服务器...', 'info');
        }

        /**
         * 尝试重新连接
         */
        attemptReconnect() {
            let attempts = 0;
            const maxAttempts = this.hmrConfig.devServer.maxReconnectAttempts;
            const interval = this.hmrConfig.devServer.reconnectInterval;
            
            const reconnect = () => {
                if (attempts >= maxAttempts) {
                    this.logger.log('🔗 重连次数已达上限，停止重连', 'warning');
                    return;
                }
                
                attempts++;
                this.logger.log(`🔗 尝试重连 (${attempts}/${maxAttempts})...`, 'info');
                
                setTimeout(() => {
                    if (this.hmrConfig.watchMode === 'websocket') {
                        this.setupWebSocketWatcher();
                    } else if (this.hmrConfig.watchMode === 'sse') {
                        this.setupSSEWatcher();
                    }
                }, interval);
            };
            
            reconnect();
        }

        /**
         * 执行完整重载
         */
        performFullReload() {
            this.logger.log('🔄 执行完整页面重载', 'info');
            this.showNotification('🔄 检测到重大变化，正在重载页面...', 'info');
            
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }

        /**
         * 注册回调
         * @param {string} event - 事件名称
         * @param {Function} callback - 回调函数
         */
        registerCallback(event, callback) {
            if (this.callbacks[event]) {
                this.callbacks[event].add(callback);
            }
        }

        /**
         * 触发回调
         * @param {string} event - 事件名称
         * @param {Object} data - 事件数据
         */
        async triggerCallbacks(event, data) {
            if (this.callbacks[event]) {
                const promises = Array.from(this.callbacks[event]).map(callback => {
                    try {
                        return Promise.resolve(callback(data));
                    } catch (error) {
                        this.logger.logError(`回调执行失败: ${event}`, error);
                        return Promise.resolve();
                    }
                });
                
                await Promise.all(promises);
            }
        }

        /**
         * 启用热替换
         */
        enable() {
            if (!this.hmrState.enabled) {
                this.hmrConfig.enabled = true;
                this.initialize();
                this.logger.log('✅ 热替换系统已启用', 'success');
            }
        }

        /**
         * 禁用热替换
         */
        disable() {
            if (this.hmrState.enabled) {
                // 清理文件监听器
                this.fileWatchers.forEach((watcher, type) => {
                    if (type === 'polling') {
                        clearInterval(watcher);
                    } else if (type === 'websocket') {
                        watcher.close();
                    } else if (type === 'sse') {
                        watcher.close();
                    }
                });
                
                this.fileWatchers.clear();
                this.hmrState.enabled = false;
                this.hmrState.watching = false;
                this.hmrState.connected = false;
                
                this.logger.log('⏹️ 热替换系统已禁用', 'info');
            }
        }

        /**
         * 获取热替换状态
         * @returns {Object} 状态信息
         */
        getStatus() {
            return {
                enabled: this.hmrState.enabled,
                watching: this.hmrState.watching,
                connected: this.hmrState.connected,
                watchMode: this.hmrConfig.watchMode,
                lastUpdate: this.hmrState.lastUpdate,
                metrics: { ...this.metrics },
                activeWatchers: Array.from(this.fileWatchers.keys()),
                moduleStatesCount: this.moduleStates.size,
                replacementHistoryCount: this.replacementHistory.length
            };
        }

        /**
         * 获取性能指标
         * @returns {Object} 性能指标
         */
        getMetrics() {
            return { ...this.metrics };
        }

        /**
         * 获取替换历史
         * @returns {Array} 替换历史
         */
        getReplacementHistory() {
            return [...this.replacementHistory];
        }

        /**
         * 手动触发热替换
         * @param {string} filePath - 文件路径
         * @returns {Promise<boolean>} 是否成功
         */
        async manualReplace(filePath) {
            try {
                const fileUrl = this.resolveFileUrl(filePath);
                await this.handleFileChange(filePath, fileUrl);
                return true;
            } catch (error) {
                this.logger.logError(`手动热替换失败: ${filePath}`, error);
                return false;
            }
        }

        /**
         * 解析文件URL
         * @param {string} filePath - 文件路径
         * @returns {string} 完整URL
         */
        resolveFileUrl(filePath) {
            const baseUrl = window.location.origin + window.location.pathname.replace(/\/[^\/]*$/, '/');
            return baseUrl + filePath;
        }

        /**
         * 批量替换模块
         * @param {Array} filePaths - 文件路径数组
         * @returns {Promise<Object>} 替换结果
         */
        async batchReplace(filePaths) {
            const results = {
                successful: [],
                failed: [],
                total: filePaths.length
            };

            for (const filePath of filePaths) {
                try {
                    const success = await this.manualReplace(filePath);
                    if (success) {
                        results.successful.push(filePath);
                    } else {
                        results.failed.push(filePath);
                    }
                } catch (error) {
                    results.failed.push(filePath);
                    this.logger.logError(`批量替换失败: ${filePath}`, error);
                }

                // 添加延迟避免过快替换
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            this.logger.log(`📦 批量替换完成: ${results.successful.length}成功, ${results.failed.length}失败`, 'info');
            return results;
        }

        /**
         * 获取模块依赖树
         * @param {string} filePath - 文件路径
         * @returns {Object} 依赖树
         */
        getDependencyTree(filePath) {
            const tree = {
                module: filePath,
                dependencies: [],
                dependents: []
            };

            // 获取直接依赖
            if (this.dependencyResolver) {
                const deps = this.dependencyResolver.getDependencies?.(filePath) || [];
                tree.dependencies = deps.map(dep => ({
                    module: dep,
                    type: 'direct'
                }));
            }

            // 获取依赖此模块的模块
            tree.dependents = this.findDependentModules(filePath).map(dep => ({
                module: dep,
                type: 'direct'
            }));

            return tree;
        }

        /**
         * 预热模块缓存
         * @param {Array} filePaths - 文件路径数组
         */
        async preloadModules(filePaths) {
            this.logger.log(`🔥 开始预热模块缓存: ${filePaths.length}个模块`, 'info');

            const preloadPromises = filePaths.map(async (filePath) => {
                try {
                    const fileUrl = this.resolveFileUrl(filePath);

                    // 预加载文件内容
                    await fetch(fileUrl, { cache: 'force-cache' });

                    // 保存当前状态
                    if (this.hmrConfig.stateManagement.enabled) {
                        await this.saveModuleState(filePath);
                    }

                    this.logger.log(`✅ 模块预热完成: ${filePath}`, 'success');

                } catch (error) {
                    this.logger.logError(`模块预热失败: ${filePath}`, error);
                }
            });

            await Promise.all(preloadPromises);
            this.logger.log('🔥 模块缓存预热完成', 'success');
        }

        /**
         * 导出配置
         * @returns {Object} 当前配置
         */
        exportConfig() {
            return JSON.parse(JSON.stringify(this.hmrConfig));
        }

        /**
         * 导入配置
         * @param {Object} config - 新配置
         */
        importConfig(config) {
            const oldEnabled = this.hmrConfig.enabled;

            // 合并配置
            this.hmrConfig = {
                ...this.hmrConfig,
                ...config
            };

            // 如果启用状态发生变化，重新初始化
            if (oldEnabled !== this.hmrConfig.enabled) {
                if (this.hmrConfig.enabled) {
                    this.enable();
                } else {
                    this.disable();
                }
            }

            this.logger.log('⚙️ 热替换配置已更新', 'info');
        }

        /**
         * 生成诊断报告
         * @returns {Object} 诊断报告
         */
        generateDiagnosticReport() {
            const report = {
                timestamp: new Date().toISOString(),
                system: {
                    userAgent: navigator.userAgent,
                    url: window.location.href,
                    isDevelopment: this.isDevelopmentMode()
                },
                hmrStatus: this.getStatus(),
                configuration: this.exportConfig(),
                performance: {
                    metrics: this.getMetrics(),
                    recentHistory: this.replacementHistory.slice(-10)
                },
                modules: {
                    registered: this.moduleRegistry.size,
                    withSavedState: this.moduleStates.size,
                    watchers: Array.from(this.fileWatchers.keys())
                },
                issues: this.detectIssues()
            };

            return report;
        }

        /**
         * 检测潜在问题
         * @returns {Array} 问题列表
         */
        detectIssues() {
            const issues = [];

            // 检查是否在生产环境启用
            if (this.hmrState.enabled && !this.isDevelopmentMode()) {
                issues.push({
                    type: 'warning',
                    message: '热替换系统在生产环境中启用，建议禁用以提高性能'
                });
            }

            // 检查失败率
            const failureRate = this.metrics.totalReplacements > 0 ?
                (this.metrics.failedReplacements / this.metrics.totalReplacements) * 100 : 0;

            if (failureRate > 20) {
                issues.push({
                    type: 'error',
                    message: `热替换失败率过高: ${failureRate.toFixed(1)}%`
                });
            }

            // 检查连接状态
            if (this.hmrConfig.devServer.enabled && !this.hmrState.connected) {
                issues.push({
                    type: 'warning',
                    message: '开发服务器连接断开，文件变化检测可能不准确'
                });
            }

            // 检查内存使用
            if (this.moduleStates.size > 100) {
                issues.push({
                    type: 'warning',
                    message: `模块状态缓存过多: ${this.moduleStates.size}个，可能影响内存使用`
                });
            }

            return issues;
        }

        /**
         * 清理资源
         */
        cleanup() {
            this.disable();
            this.moduleStates.clear();
            this.replacementHistory.length = 0;

            // 清理通知容器
            const container = document.getElementById('hmr-notifications');
            if (container) {
                container.remove();
            }

            // 清理全局API
            if (window.module && window.module.hot) {
                delete window.module.hot;
            }

            this.logger.log('🧹 热替换系统资源已清理', 'info');
        }
    }

    // 创建全局实例
    const moduleHotReplacement = new ModuleHotReplacement();

    // 导出到全局作用域
    window.OTA = window.OTA || {};
    window.OTA.moduleHotReplacement = moduleHotReplacement;
    window.OTA.getModuleHotReplacement = () => moduleHotReplacement;

    // 向后兼容
    window.getModuleHotReplacement = () => moduleHotReplacement;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('moduleHotReplacement', moduleHotReplacement, '@OTA_HOT_REPLACEMENT');
        window.OTA.Registry.registerFactory('getModuleHotReplacement', () => moduleHotReplacement, '@OTA_HOT_REPLACEMENT_FACTORY');
    }

})();
