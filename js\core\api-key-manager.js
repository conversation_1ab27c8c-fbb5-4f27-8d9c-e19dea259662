/**
 * @OTA_CORE API密钥统一管理器
 * 🏷️ 标签: @API_KEY_MANAGER
 * 📝 功能: 统一管理所有API密钥，支持环境变量和默认值
 * 🎯 特性: 向后兼容、环境变量支持、安全访问控制
 * ⚠️ 警告: 核心安全模块，请勿重复开发
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * API密钥配置定义
     * 包含所有服务的密钥配置和环境变量映射
     */
    const API_KEYS_CONFIG = {
        // Gemini AI服务密钥
        gemini: {
            default: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s', // 保持现有硬编码
            envVar: 'GEMINI_API_KEY',
            description: 'Google Gemini AI API密钥',
            required: true,
            service: 'Gemini AI'
        },
        
        // Kimi AI服务密钥
        kimi: {
            default: 'sk-JcWzWW8FEnbrEvmvIY6N4vbcFzPxqXJBiFSbqej3Ff2uJBsY', // 从kimi-service.js提取
            envVar: 'KIMI_API_KEY',
            description: 'Kimi AI API密钥',
            required: false,
            service: 'Kimi AI'
        },
        
        // OpenAI服务密钥（预留）
        openai: {
            default: null,
            envVar: 'OPENAI_API_KEY',
            description: 'OpenAI API密钥',
            required: false,
            service: 'OpenAI'
        },
        
        // GoMyHire API密钥（预留）
        gomyhire: {
            default: null,
            envVar: 'GOMYHIRE_API_KEY',
            description: 'GoMyHire API密钥',
            required: false,
            service: 'GoMyHire'
        }
    };

    /**
     * API密钥管理器类
     * 提供统一的API密钥管理和访问接口
     */
    class ApiKeyManager {
        constructor() {
            // 延迟获取logger，避免初始化时序问题
            this.logger = null;
            
            // 运行时密钥存储
            this.runtimeKeys = new Map();
            
            // 环境变量缓存
            this.envCache = new Map();
            
            // 访问统计
            this.accessStats = {
                totalAccess: 0,
                keyAccess: new Map(),
                lastAccess: new Map()
            };
            
            // 初始化管理器
            this.initialize();
        }

        /**
         * 获取logger实例，使用延迟加载避免初始化时序问题
         */
        getLogger() {
            if (!this.logger) {
                const baseLogger = window.OTA?.getService?.('logger') || 
                                 window.getLogger?.() || 
                                 console;
                
                // 确保logger具有所需的方法
                this.logger = {
                    log: baseLogger.log?.bind(baseLogger) || console.log.bind(console),
                    logError: baseLogger.logError?.bind(baseLogger) || console.error.bind(console),
                    logWarning: baseLogger.logWarning?.bind(baseLogger) || console.warn.bind(console),
                    logInfo: baseLogger.logInfo?.bind(baseLogger) || console.info.bind(console)
                };
            }
            return this.logger;
        }

        /**
         * 初始化API密钥管理器
         */
        initialize() {
            try {
                this.getLogger().log('API密钥管理器初始化开始', 'info');
                
                // 加载环境变量
                this.loadEnvironmentKeys();
                
                // 验证必需的密钥
                this.validateRequiredKeys();
                
                this.getLogger().log('API密钥管理器初始化完成', 'info', {
                    configuredServices: Object.keys(API_KEYS_CONFIG).length,
                    availableKeys: this.getAvailableServices().length
                });
                
            } catch (error) {
                this.getLogger().logError('API密钥管理器初始化失败', error);
                throw error;
            }
        }

        /**
         * 获取指定服务的API密钥
         * @param {string} service - 服务名称
         * @returns {string|null} API密钥
         */
        getApiKey(service) {
            try {
                // 更新访问统计
                this.updateAccessStats(service);
                
                // 检查服务是否存在
                if (!API_KEYS_CONFIG[service]) {
                    this.getLogger().logWarning(`未知的服务: ${service}`);
                    return null;
                }
                
                const config = API_KEYS_CONFIG[service];
                
                // 优先级：运行时设置 > 环境变量 > 默认值
                let apiKey = null;
                
                // 1. 检查运行时设置
                if (this.runtimeKeys.has(service)) {
                    apiKey = this.runtimeKeys.get(service);
                    this.getLogger().log(`使用运行时密钥: ${service}`, 'debug');
                }
                
                // 2. 检查环境变量
                if (!apiKey && this.envCache.has(service)) {
                    apiKey = this.envCache.get(service);
                    this.getLogger().log(`使用环境变量密钥: ${service}`, 'debug');
                }
                
                // 3. 使用默认值
                if (!apiKey && config.default) {
                    apiKey = config.default;
                    this.getLogger().log(`使用默认密钥: ${service}`, 'debug');
                }
                
                if (!apiKey) {
                    this.getLogger().logWarning(`服务 ${service} 的API密钥不可用`);
                    return null;
                }
                
                return apiKey;
                
            } catch (error) {
                this.getLogger().logError(`获取API密钥失败: ${service}`, error);
                return null;
            }
        }

        /**
         * 设置运行时API密钥
         * @param {string} service - 服务名称
         * @param {string} key - API密钥
         * @returns {boolean} 设置是否成功
         */
        setApiKey(service, key) {
            try {
                if (!API_KEYS_CONFIG[service]) {
                    this.getLogger().logWarning(`未知的服务: ${service}`);
                    return false;
                }
                
                if (!key || typeof key !== 'string') {
                    this.getLogger().logWarning(`无效的API密钥: ${service}`);
                    return false;
                }
                
                this.runtimeKeys.set(service, key);
                this.getLogger().log(`运行时密钥已设置: ${service}`, 'info');
                
                return true;
                
            } catch (error) {
                this.getLogger().logError(`设置API密钥失败: ${service}`, error);
                return false;
            }
        }

        /**
         * 检查指定服务的API密钥是否存在
         * @param {string} service - 服务名称
         * @returns {boolean} 密钥是否存在
         */
        hasApiKey(service) {
            const key = this.getApiKey(service);
            return key !== null && key !== undefined && key.trim() !== '';
        }

        /**
         * 获取所有可用的API密钥信息（不包含实际密钥）
         * @returns {Object} 密钥信息对象
         */
        getAllApiKeys() {
            const result = {};
            
            for (const [service, config] of Object.entries(API_KEYS_CONFIG)) {
                result[service] = {
                    available: this.hasApiKey(service),
                    source: this.getKeySource(service),
                    description: config.description,
                    required: config.required,
                    service: config.service
                };
            }
            
            return result;
        }

        /**
         * 获取指定服务的密钥信息（不含实际密钥）
         * @param {string} service - 服务名称
         * @returns {Object|null} 密钥信息
         */
        getApiKeyInfo(service) {
            if (!API_KEYS_CONFIG[service]) {
                return null;
            }
            
            const config = API_KEYS_CONFIG[service];
            
            return {
                service: service,
                available: this.hasApiKey(service),
                source: this.getKeySource(service),
                description: config.description,
                required: config.required,
                serviceName: config.service,
                envVar: config.envVar
            };
        }

        /**
         * 获取可用服务列表
         * @returns {Array} 可用服务名称数组
         */
        getAvailableServices() {
            return Object.keys(API_KEYS_CONFIG).filter(service => this.hasApiKey(service));
        }

        /**
         * 从环境变量加载密钥
         */
        loadFromEnvironment() {
            this.loadEnvironmentKeys();
            this.getLogger().log('环境变量密钥已重新加载', 'info');
        }

        /**
         * 获取环境变量中的密钥
         * @returns {Object} 环境变量密钥对象
         */
        getEnvironmentKeys() {
            const result = {};
            
            for (const [service, config] of Object.entries(API_KEYS_CONFIG)) {
                const envValue = this.getEnvironmentVariable(config.envVar);
                if (envValue) {
                    result[service] = {
                        envVar: config.envVar,
                        available: true,
                        description: config.description
                    };
                }
            }
            
            return result;
        }

        /**
         * 获取访问统计信息
         * @returns {Object} 访问统计数据
         */
        getAccessStats() {
            return {
                totalAccess: this.accessStats.totalAccess,
                keyAccess: Object.fromEntries(this.accessStats.keyAccess),
                lastAccess: Object.fromEntries(this.accessStats.lastAccess),
                availableServices: this.getAvailableServices().length,
                configuredServices: Object.keys(API_KEYS_CONFIG).length
            };
        }

        /**
         * 重置访问统计
         */
        resetAccessStats() {
            this.accessStats = {
                totalAccess: 0,
                keyAccess: new Map(),
                lastAccess: new Map()
            };
            this.getLogger().log('访问统计已重置', 'info');
        }

        // ==================== 私有方法 ====================

        /**
         * 加载环境变量密钥
         * @private
         */
        loadEnvironmentKeys() {
            this.envCache.clear();
            
            for (const [service, config] of Object.entries(API_KEYS_CONFIG)) {
                const envValue = this.getEnvironmentVariable(config.envVar);
                if (envValue) {
                    this.envCache.set(service, envValue);
                    this.getLogger().log(`环境变量密钥已加载: ${service}`, 'debug');
                }
            }
        }

        /**
         * 获取环境变量值
         * @param {string} varName - 环境变量名
         * @returns {string|null} 环境变量值
         * @private
         */
        getEnvironmentVariable(varName) {
            // 浏览器环境中的环境变量获取
            // 可以从多个来源获取：process.env、window.env、localStorage等
            
            // 1. 检查 process.env（Node.js环境或webpack注入）
            if (typeof process !== 'undefined' && process.env && process.env[varName]) {
                return process.env[varName];
            }
            
            // 2. 检查 window.env（自定义环境变量对象）
            if (window.env && window.env[varName]) {
                return window.env[varName];
            }
            
            // 3. 检查 localStorage（本地存储的环境变量）
            try {
                const localEnvKey = `env_${varName}`;
                const localValue = localStorage.getItem(localEnvKey);
                if (localValue) {
                    return localValue;
                }
            } catch (error) {
                // localStorage可能不可用，忽略错误
            }
            
            return null;
        }

        /**
         * 验证必需的密钥
         * @private
         */
        validateRequiredKeys() {
            const missingKeys = [];
            
            for (const [service, config] of Object.entries(API_KEYS_CONFIG)) {
                if (config.required && !this.hasApiKey(service)) {
                    missingKeys.push(service);
                }
            }
            
            if (missingKeys.length > 0) {
                this.getLogger().logWarning(`缺少必需的API密钥: ${missingKeys.join(', ')}`);
            }
        }

        /**
         * 获取密钥来源
         * @param {string} service - 服务名称
         * @returns {string} 密钥来源
         * @private
         */
        getKeySource(service) {
            if (this.runtimeKeys.has(service)) {
                return 'runtime';
            }
            if (this.envCache.has(service)) {
                return 'environment';
            }
            if (API_KEYS_CONFIG[service]?.default) {
                return 'default';
            }
            return 'none';
        }

        /**
         * 更新访问统计
         * @param {string} service - 服务名称
         * @private
         */
        updateAccessStats(service) {
            this.accessStats.totalAccess++;
            
            const currentCount = this.accessStats.keyAccess.get(service) || 0;
            this.accessStats.keyAccess.set(service, currentCount + 1);
            
            this.accessStats.lastAccess.set(service, new Date().toISOString());
        }
    }

    // 创建全局实例
    const apiKeyManager = new ApiKeyManager();

    // 注册到OTA命名空间
    window.OTA.apiKeyManager = apiKeyManager;
    window.OTA.ApiKeyManager = ApiKeyManager;

    // 注册到OTA Registry（如果存在）
    if (window.OTA.Registry) {
        window.OTA.Registry.registerService('apiKeyManager', apiKeyManager, '@API_KEY_MANAGER');
        window.OTA.Registry.registerFactory('getApiKeyManager', () => apiKeyManager, '@API_KEY_MANAGER_FACTORY');
    }

    // 提供全局访问函数（向后兼容）
    window.getApiKeyManager = function() {
        return apiKeyManager;
    };

    // 日志记录
    const logger = window.getLogger?.() || console;
    logger.log('API密钥管理器模块加载完成', 'info', {
        version: '1.0.0',
        configuredServices: Object.keys(API_KEYS_CONFIG).length,
        availableServices: apiKeyManager.getAvailableServices().length
    });

})();
