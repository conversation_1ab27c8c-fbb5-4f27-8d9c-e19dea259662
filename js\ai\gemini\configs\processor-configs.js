/**
 * @CONFIG 处理器配置文件
 * 🏷️ 标签: @PROCESSOR_CONFIG @OTA_MAPPING
 * 📝 说明: 所有OTA专用处理器的统一配置文件，包括字段映射、预设值、验证规则等
 * 🎯 目标: 为所有处理器提供标准化的配置管理，支持热更新和动态配置
 */

(function() {
    'use strict';
    
    /**
     * 处理器配置管理器
     * 统一管理所有OTA处理器的配置信息
     */
    class ProcessorConfigs {
        constructor() {
            this.configs = new Map();
            this.globalConfig = {};
            this.initialized = false;
            
            this.initializeConfigs();
        }
        
        /**
         * 初始化所有配置
         * @private
         */
        initializeConfigs() {
            // 初始化全局配置
            this.initializeGlobalConfig();
            
            // 初始化各平台配置
            this.initializeFliggyConfig();
            this.initializeKKdayConfig();
            this.initializeKlookConfig();
            this.initializeCtripConfig();
            this.initializeAgodaConfig();
            this.initializeBookingConfig();
            
            this.initialized = true;
        }
        
        /**
         * 初始化全局配置
         * @private
         */
        initializeGlobalConfig() {
            this.globalConfig = {
                // 通用字段映射
                commonFieldMapping: {
                    'customer_name': ['客户姓名', '联系人', '预订人', '姓名', 'Name', 'Guest Name', 'Contact Person'],
                    'phone': ['电话', '手机', '联系电话', 'Phone', 'Mobile', 'Contact Number'],
                    'email': ['邮箱', '电子邮件', 'Email', 'E-mail'],
                    'pickup_location': ['出发地', '接送地点', '上车地点', 'Pickup Location', 'Departure'],
                    'destination': ['目的地', '下车地点', 'Destination', 'Drop-off Location'],
                    'pickup_date': ['日期', '出发日期', 'Date', 'Pickup Date'],
                    'pickup_time': ['时间', '出发时间', 'Time', 'Pickup Time'],
                    'service_type': ['服务类型', 'Service Type'],
                    'passenger_count': ['人数', '乘客人数', 'Passengers', 'Pax'],
                    'luggage_count': ['行李', '行李数量', 'Luggage', 'Baggage'],
                    'order_reference': ['订单号', '订单编号', 'Order Number', 'Reference'],
                    'price': ['价格', '费用', 'Price', 'Amount'],
                    'currency': ['货币', 'Currency']
                },
                
                // 通用预设值
                commonPresetValues: {
                    service_type_id: 2, // 默认接机服务
                    car_type_id: 1, // 默认Comfort 5 Seater
                    languages_id: 2, // 默认英文
                    responsible_person_id: 1 // 默认负责人
                },
                
                // 通用验证规则
                commonValidationRules: {
                    order_reference: {
                        required: true,
                        minLength: 6,
                        maxLength: 20
                    },
                    customer_name: {
                        required: true,
                        minLength: 2,
                        maxLength: 50
                    },
                    phone: {
                        required: false,
                        pattern: /^[\+\-\d\s\(\)]+$/
                    },
                    pickup_location: {
                        required: true,
                        minLength: 3,
                        maxLength: 200
                    }
                },
                
                // 日期时间格式
                dateTimeFormats: [
                    'YYYY-MM-DD',
                    'DD/MM/YYYY',
                    'MM/DD/YYYY',
                    'DD-MM-YYYY',
                    'YYYY年MM月DD日'
                ],
                
                // 性能配置
                performance: {
                    maxProcessingTime: 5000, // 5秒
                    maxRetries: 3,
                    timeout: 10000 // 10秒
                }
            };
        }
        
        /**
         * 初始化Fliggy配置
         * @private
         */
        initializeFliggyConfig() {
            this.configs.set('fliggy', {
                platform: 'fliggy',
                platformName: '飞猪旅行',
                ota_id: 7,
                
                // 参考号识别模式
                referencePatterns: [
                    /FL[A-Z0-9]{8,12}/gi,
                    /FLIGGY[A-Z0-9]{6,10}/gi,
                    /飞猪订单[：:]\s*([A-Z0-9]{8,15})/gi,
                    /\b[0-9]{10,15}\b/g
                ],
                
                // 字段映射（继承通用映射并扩展）
                fieldMapping: {
                    ...this.globalConfig.commonFieldMapping,
                    'customer_name': [...this.globalConfig.commonFieldMapping.customer_name, '预订人', '旅客姓名'],
                    'pickup_location': [...this.globalConfig.commonFieldMapping.pickup_location, '出发地', '起飞地'],
                    'destination': [...this.globalConfig.commonFieldMapping.destination, '到达地', '降落地']
                },
                
                // 预设值
                presetValues: {
                    ...this.globalConfig.commonPresetValues,
                    ota: 'fliggy',
                    ota_id: 7,
                    languages_id: 4 // 中文
                },
                
                // 识别关键词
                identificationKeywords: [
                    'fliggy', 'Fliggy', 'FLIGGY',
                    '飞猪', '飞猪旅行',
                    'fliggy.com', 'www.fliggy.com'
                ],
                
                // 特殊处理规则
                specialRules: {
                    chinesePrimary: true,
                    supportTraditionalChinese: true,
                    alipayIntegration: true,
                    dateFormatChinese: true
                }
            });
        }
        
        /**
         * 初始化KKday配置
         * @private
         */
        initializeKKdayConfig() {
            this.configs.set('kkday', {
                platform: 'kkday',
                platformName: 'KKday',
                ota_id: 8,
                
                referencePatterns: [
                    /KK[A-Z0-9]{8,12}/gi,
                    /KKDAY[A-Z0-9]{6,10}/gi,
                    /KKD-[A-Z0-9]{6,10}/gi,
                    /订单编号[：:]\s*([A-Z0-9]{8,15})/gi
                ],
                
                fieldMapping: {
                    ...this.globalConfig.commonFieldMapping,
                    'customer_name': [...this.globalConfig.commonFieldMapping.customer_name, '预订人', '联系人'],
                    'pickup_date': [...this.globalConfig.commonFieldMapping.pickup_date, '体验日期', '使用日期'],
                    'pickup_time': [...this.globalConfig.commonFieldMapping.pickup_time, '集合时间']
                },
                
                presetValues: {
                    ...this.globalConfig.commonPresetValues,
                    ota: 'kkday',
                    ota_id: 8
                },
                
                identificationKeywords: [
                    'kkday', 'KKday', 'KKDAY',
                    'kkday.com', 'www.kkday.com',
                    'KK旅游', 'KK体验'
                ],
                
                specialRules: {
                    multiLanguage: true,
                    traditionalChinese: true,
                    experienceBooking: true
                }
            });
        }
        
        /**
         * 初始化Klook配置
         * @private
         */
        initializeKlookConfig() {
            this.configs.set('klook', {
                platform: 'klook',
                platformName: 'Klook客路',
                ota_id: 9,
                
                referencePatterns: [
                    /KL[A-Z0-9]{8,12}/gi,
                    /KLOOK[A-Z0-9]{6,10}/gi,
                    /KLK-[A-Z0-9]{6,10}/gi,
                    /确认号[：:]\s*([A-Z0-9]{8,15})/gi
                ],
                
                fieldMapping: {
                    ...this.globalConfig.commonFieldMapping,
                    'customer_name': [...this.globalConfig.commonFieldMapping.customer_name, '旅客姓名', '参与者'],
                    'pickup_location': [...this.globalConfig.commonFieldMapping.pickup_location, '集合地点', '会合点'],
                    'pickup_date': [...this.globalConfig.commonFieldMapping.pickup_date, '体验日期', '活动日期'],
                    'pickup_time': [...this.globalConfig.commonFieldMapping.pickup_time, '集合时间', '活动时间']
                },
                
                presetValues: {
                    ...this.globalConfig.commonPresetValues,
                    ota: 'klook',
                    ota_id: 9
                },
                
                identificationKeywords: [
                    'klook', 'Klook', 'KLOOK',
                    '客路', '客路旅行',
                    'klook.com', 'www.klook.com'
                ],
                
                specialRules: {
                    multiLanguage: true,
                    experienceActivity: true,
                    eVoucherSupport: true
                }
            });
        }
        
        /**
         * 初始化Ctrip配置
         * @private
         */
        initializeCtripConfig() {
            this.configs.set('ctrip', {
                platform: 'ctrip',
                platformName: '携程旅行',
                ota_id: 10,
                
                referencePatterns: [
                    /CT[A-Z0-9]{8,12}/gi,
                    /CTRIP[A-Z0-9]{6,10}/gi,
                    /CTP-[A-Z0-9]{6,10}/gi,
                    /携程订单[：:]\s*([A-Z0-9]{8,15})/gi,
                    /产品订单号[：:]\s*([A-Z0-9]{8,15})/gi
                ],
                
                fieldMapping: {
                    ...this.globalConfig.commonFieldMapping,
                    'pickup_location': [...this.globalConfig.commonFieldMapping.pickup_location, '上车地点', '起点'],
                    'destination': [...this.globalConfig.commonFieldMapping.destination, '下车地点', '终点'],
                    'pickup_date': [...this.globalConfig.commonFieldMapping.pickup_date, '用车日期', '服务日期'],
                    'pickup_time': [...this.globalConfig.commonFieldMapping.pickup_time, '用车时间', '服务时间']
                },
                
                presetValues: {
                    ...this.globalConfig.commonPresetValues,
                    ota: 'ctrip',
                    ota_id: 10,
                    languages_id: 4 // 中文
                },
                
                identificationKeywords: [
                    'ctrip', 'Ctrip', 'CTRIP',
                    '携程', '携程旅行', '携程网',
                    'ctrip.com', 'www.ctrip.com'
                ],
                
                specialRules: {
                    chinesePrimary: true,
                    traditionalChinese: true,
                    multiProductSupport: true
                }
            });
        }
        
        /**
         * 初始化Agoda配置
         * @private
         */
        initializeAgodaConfig() {
            this.configs.set('agoda', {
                platform: 'agoda',
                platformName: 'Agoda',
                ota_id: 11,
                
                referencePatterns: [
                    /AG[A-Z0-9]{8,12}/gi,
                    /AGODA[A-Z0-9]{6,10}/gi,
                    /AGD-[A-Z0-9]{6,10}/gi,
                    /Booking\s*(?:Reference|Number)[：:]\s*([A-Z0-9]{8,15})/gi
                ],
                
                fieldMapping: {
                    ...this.globalConfig.commonFieldMapping,
                    'customer_name': [...this.globalConfig.commonFieldMapping.customer_name, 'Guest Name', 'Main Guest'],
                    'pickup_location': [...this.globalConfig.commonFieldMapping.pickup_location, 'Hotel Address', 'Property Address'],
                    'pickup_date': [...this.globalConfig.commonFieldMapping.pickup_date, 'Check-in Date', 'Arrival Date'],
                    'pickup_time': [...this.globalConfig.commonFieldMapping.pickup_time, 'Check-in Time', 'Arrival Time']
                },
                
                presetValues: {
                    ...this.globalConfig.commonPresetValues,
                    ota: 'agoda',
                    ota_id: 11
                },
                
                identificationKeywords: [
                    'agoda', 'Agoda', 'AGODA',
                    'agoda.com', 'www.agoda.com',
                    'Property booking', 'Hotel booking'
                ],
                
                specialRules: {
                    englishPrimary: true,
                    multiLanguage: true,
                    hotelBookingSupport: true,
                    transferServiceSupport: true
                }
            });
        }
        
        /**
         * 初始化Booking.com配置
         * @private
         */
        initializeBookingConfig() {
            this.configs.set('booking', {
                platform: 'booking',
                platformName: 'Booking.com',
                ota_id: 12,
                
                referencePatterns: [
                    /BK[A-Z0-9]{8,12}/gi,
                    /BOOKING[A-Z0-9]{6,10}/gi,
                    /BKG-[A-Z0-9]{6,10}/gi,
                    /Confirmation\s*(?:Number|Code)[：:]\s*([A-Z0-9]{8,15})/gi,
                    /PIN[：:]\s*([A-Z0-9]{4,8})/gi
                ],
                
                fieldMapping: {
                    ...this.globalConfig.commonFieldMapping,
                    'customer_name': [...this.globalConfig.commonFieldMapping.customer_name, 'Main Guest', 'Booker Name'],
                    'pickup_location': [...this.globalConfig.commonFieldMapping.pickup_location, 'Property Address', 'Accommodation Address'],
                    'pickup_date': [...this.globalConfig.commonFieldMapping.pickup_date, 'Check-in Date', 'Arrival Date'],
                    'pickup_time': [...this.globalConfig.commonFieldMapping.pickup_time, 'Check-in Time', 'Arrival Time']
                },
                
                presetValues: {
                    ...this.globalConfig.commonPresetValues,
                    ota: 'booking',
                    ota_id: 12
                },
                
                identificationKeywords: [
                    'booking', 'Booking', 'BOOKING',
                    'booking.com', 'www.booking.com',
                    'Booking.com', 'Property booking'
                ],
                
                specialRules: {
                    englishPrimary: true,
                    globalSupport: true,
                    accommodationBooking: true,
                    pinCodeSupport: true
                }
            });
        }
        
        /**
         * 获取处理器配置
         * @param {string} platform - 平台名称
         * @returns {Object} 配置对象
         */
        getConfig(platform) {
            return this.configs.get(platform);
        }
        
        /**
         * 获取全局配置
         * @returns {Object} 全局配置
         */
        getGlobalConfig() {
            return this.globalConfig;
        }
        
        /**
         * 获取所有平台配置
         * @returns {Map} 所有配置
         */
        getAllConfigs() {
            return this.configs;
        }
        
        /**
         * 更新配置
         * @param {string} platform - 平台名称
         * @param {Object} newConfig - 新配置
         */
        updateConfig(platform, newConfig) {
            const existingConfig = this.configs.get(platform) || {};
            this.configs.set(platform, { ...existingConfig, ...newConfig });
        }
        
        /**
         * 验证配置完整性
         * @param {string} platform - 平台名称
         * @returns {Object} 验证结果
         */
        validateConfig(platform) {
            const config = this.configs.get(platform);
            if (!config) {
                return { valid: false, errors: ['配置不存在'] };
            }
            
            const errors = [];
            const requiredFields = ['platform', 'platformName', 'ota_id', 'referencePatterns', 'fieldMapping', 'presetValues'];
            
            for (const field of requiredFields) {
                if (!config[field]) {
                    errors.push(`缺少必需字段: ${field}`);
                }
            }
            
            return {
                valid: errors.length === 0,
                errors
            };
        }
        
        /**
         * 获取支持的平台列表
         * @returns {Array} 平台列表
         */
        getSupportedPlatforms() {
            return Array.from(this.configs.keys());
        }
        
        /**
         * 获取配置统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                totalPlatforms: this.configs.size,
                platforms: this.getSupportedPlatforms(),
                initialized: this.initialized,
                globalConfigLoaded: !!this.globalConfig
            };
        }
    }
    
    // 创建全局配置实例
    const processorConfigs = new ProcessorConfigs();
    
    // 注册到全局
    if (typeof window !== 'undefined') {
        window.OTA = window.OTA || {};
        window.OTA.processorConfigs = processorConfigs;
        
        // 注册到服务注册中心
        if (window.OTA.Registry) {
            window.OTA.Registry.registerService('processor-configs', processorConfigs, {
                dependencies: [],
                description: '处理器配置管理器，统一管理所有OTA处理器的配置信息'
            });
        }
    }
    
    // Node.js环境支持
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = ProcessorConfigs;
    }
    
})();
