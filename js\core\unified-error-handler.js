/**
 * @OTA_CORE 统一错误处理器
 * 🏷️ 标签: @UNIFIED_ERROR_HANDLER @ERROR_MANAGEMENT
 * 📝 说明: 为整个OTA系统提供统一的错误处理模式和标准化错误处理流程
 * ⚠️ 警告: 核心错误处理基础设施，请勿重复开发
 */

(function() {
    'use strict';

    /**
     * 统一错误处理器类
     */
    class UnifiedErrorHandler {
        constructor() {
            this.logger = window.OTA.getService('logger');
            this.errorPatterns = new Map();
            this.errorStats = {
                totalErrors: 0,
                errorsByType: new Map(),
                errorsByModule: new Map(),
                lastError: null
            };
            
            this.initializeErrorPatterns();
            this.registerGlobalHandlers();
            
            this.logger.log('✅ 统一错误处理器已初始化', 'info');
        }

        /**
         * 初始化错误模式
         */
        initializeErrorPatterns() {
            // API错误模式
            this.errorPatterns.set('API_ERROR', {
                pattern: /API|fetch|request|response/i,
                handler: this.handleApiError.bind(this),
                severity: 'high',
                userMessage: 'API请求失败，请稍后重试'
            });

            // 验证错误模式
            this.errorPatterns.set('VALIDATION_ERROR', {
                pattern: /validation|invalid|required|format/i,
                handler: this.handleValidationError.bind(this),
                severity: 'medium',
                userMessage: '数据验证失败，请检查输入'
            });

            // 网络错误模式
            this.errorPatterns.set('NETWORK_ERROR', {
                pattern: /network|connection|timeout|offline/i,
                handler: this.handleNetworkError.bind(this),
                severity: 'high',
                userMessage: '网络连接失败，请检查网络状态'
            });

            // 权限错误模式
            this.errorPatterns.set('PERMISSION_ERROR', {
                pattern: /permission|unauthorized|forbidden|access/i,
                handler: this.handlePermissionError.bind(this),
                severity: 'high',
                userMessage: '权限不足，请联系管理员'
            });

            // 系统错误模式
            this.errorPatterns.set('SYSTEM_ERROR', {
                pattern: /system|internal|server|500/i,
                handler: this.handleSystemError.bind(this),
                severity: 'critical',
                userMessage: '系统错误，请联系技术支持'
            });
        }

        /**
         * 注册全局错误处理器
         */
        registerGlobalHandlers() {
            // 未捕获的Promise拒绝
            window.addEventListener('unhandledrejection', (event) => {
                this.handleError(event.reason, {
                    type: 'unhandledPromiseRejection',
                    source: 'global'
                });
                event.preventDefault(); // 防止控制台输出
            });

            // 全局JavaScript错误
            window.addEventListener('error', (event) => {
                this.handleError(event.error || event.message, {
                    type: 'globalError',
                    source: 'global',
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno
                });
            });
        }

        /**
         * 统一错误处理入口
         * @param {Error|string} error - 错误对象或消息
         * @param {Object} context - 错误上下文
         * @returns {Object} 处理结果
         */
        handleError(error, context = {}) {
            try {
                // 标准化错误对象
                const standardizedError = this.standardizeError(error, context);
                
                // 分类错误
                const errorType = this.classifyError(standardizedError);
                
                // 更新统计
                this.updateErrorStats(errorType, standardizedError);
                
                // 获取处理器
                const pattern = this.errorPatterns.get(errorType);
                
                // 执行特定处理
                let result = { success: false, message: '未知错误' };
                if (pattern && pattern.handler) {
                    result = pattern.handler(standardizedError, context);
                } else {
                    result = this.handleGenericError(standardizedError, context);
                }
                
                // 记录错误
                this.logger.logError(`[${errorType}] ${standardizedError.message}`, {
                    error: standardizedError,
                    context,
                    result
                });
                
                return {
                    ...result,
                    errorType,
                    errorId: standardizedError.id,
                    timestamp: standardizedError.timestamp
                };
                
            } catch (handlingError) {
                // 错误处理器本身出错时的降级处理
                console.error('错误处理器异常:', handlingError);
                return {
                    success: false,
                    message: '错误处理失败',
                    errorType: 'HANDLER_ERROR'
                };
            }
        }

        /**
         * 标准化错误对象
         * @param {Error|string} error - 原始错误
         * @param {Object} context - 上下文
         * @returns {Object} 标准化错误对象
         */
        standardizeError(error, context) {
            const standardized = {
                id: this.generateErrorId(),
                timestamp: new Date().toISOString(),
                message: '',
                stack: null,
                name: 'Error',
                context: context || {}
            };

            if (error instanceof Error) {
                standardized.message = error.message;
                standardized.stack = error.stack;
                standardized.name = error.name;
            } else if (typeof error === 'string') {
                standardized.message = error;
            } else {
                standardized.message = String(error);
            }

            return standardized;
        }

        /**
         * 分类错误
         * @param {Object} error - 标准化错误对象
         * @returns {string} 错误类型
         */
        classifyError(error) {
            const message = error.message.toLowerCase();
            
            for (const [type, pattern] of this.errorPatterns) {
                if (pattern.pattern.test(message)) {
                    return type;
                }
            }
            
            return 'GENERIC_ERROR';
        }

        /**
         * 更新错误统计
         * @param {string} errorType - 错误类型
         * @param {Object} error - 错误对象
         */
        updateErrorStats(errorType, error) {
            this.errorStats.totalErrors++;
            this.errorStats.lastError = error;
            
            // 按类型统计
            const typeCount = this.errorStats.errorsByType.get(errorType) || 0;
            this.errorStats.errorsByType.set(errorType, typeCount + 1);
            
            // 按模块统计
            const module = error.context.module || 'unknown';
            const moduleCount = this.errorStats.errorsByModule.get(module) || 0;
            this.errorStats.errorsByModule.set(module, moduleCount + 1);
        }

        /**
         * 处理API错误
         * @param {Object} error - 错误对象
         * @param {Object} context - 上下文
         * @returns {Object} 处理结果
         */
        handleApiError(error, context) {
            return {
                success: false,
                message: 'API请求失败',
                userMessage: 'API请求失败，请稍后重试',
                shouldRetry: true,
                retryDelay: 3000
            };
        }

        /**
         * 处理验证错误
         * @param {Object} error - 错误对象
         * @param {Object} context - 上下文
         * @returns {Object} 处理结果
         */
        handleValidationError(error, context) {
            return {
                success: false,
                message: '数据验证失败',
                userMessage: '数据验证失败，请检查输入',
                shouldRetry: false,
                validationDetails: error.context.validationErrors || []
            };
        }

        /**
         * 处理网络错误
         * @param {Object} error - 错误对象
         * @param {Object} context - 上下文
         * @returns {Object} 处理结果
         */
        handleNetworkError(error, context) {
            return {
                success: false,
                message: '网络连接失败',
                userMessage: '网络连接失败，请检查网络状态',
                shouldRetry: true,
                retryDelay: 5000
            };
        }

        /**
         * 处理权限错误
         * @param {Object} error - 错误对象
         * @param {Object} context - 上下文
         * @returns {Object} 处理结果
         */
        handlePermissionError(error, context) {
            return {
                success: false,
                message: '权限不足',
                userMessage: '权限不足，请联系管理员',
                shouldRetry: false,
                requiresAuth: true
            };
        }

        /**
         * 处理系统错误
         * @param {Object} error - 错误对象
         * @param {Object} context - 上下文
         * @returns {Object} 处理结果
         */
        handleSystemError(error, context) {
            return {
                success: false,
                message: '系统错误',
                userMessage: '系统错误，请联系技术支持',
                shouldRetry: false,
                severity: 'critical'
            };
        }

        /**
         * 处理通用错误
         * @param {Object} error - 错误对象
         * @param {Object} context - 上下文
         * @returns {Object} 处理结果
         */
        handleGenericError(error, context) {
            return {
                success: false,
                message: error.message || '未知错误',
                userMessage: '操作失败，请稍后重试',
                shouldRetry: true,
                retryDelay: 2000
            };
        }

        /**
         * 生成错误ID
         * @returns {string} 错误ID
         */
        generateErrorId() {
            return `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }

        /**
         * 获取错误统计
         * @returns {Object} 错误统计信息
         */
        getErrorStats() {
            return {
                ...this.errorStats,
                errorsByType: Object.fromEntries(this.errorStats.errorsByType),
                errorsByModule: Object.fromEntries(this.errorStats.errorsByModule)
            };
        }

        /**
         * 清理错误统计
         */
        clearStats() {
            this.errorStats = {
                totalErrors: 0,
                errorsByType: new Map(),
                errorsByModule: new Map(),
                lastError: null
            };
            this.logger.log('错误统计已清理', 'info');
        }
    }

    // 创建全局实例
    const unifiedErrorHandler = new UnifiedErrorHandler();

    // 注册到OTA命名空间
    window.OTA.unifiedErrorHandler = unifiedErrorHandler;
    window.OTA.handleError = (error, context) => unifiedErrorHandler.handleError(error, context);

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('unifiedErrorHandler', unifiedErrorHandler, '@OTA_UNIFIED_ERROR_HANDLER');
        window.OTA.Registry.registerFactory('getUnifiedErrorHandler', () => unifiedErrorHandler, '@OTA_UNIFIED_ERROR_HANDLER_FACTORY');
    }

    // 提供全局便捷函数
    window.handleOTAError = (error, context) => unifiedErrorHandler.handleError(error, context);

})();
