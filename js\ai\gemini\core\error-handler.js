/**
 * @ERROR_HANDLER 错误处理中心
 * 🏷️ 标签: @ERROR_HANDLING @EXCEPTION_MANAGEMENT @RECOVERY
 * 📝 说明: 统一处理所有组件的错误和异常情况
 * 🎯 目标: 提供统一的错误处理、错误恢复和错误报告机制
 */

(function() {
    'use strict';
    
    // 确保OTA命名空间存在
    window.OTA = window.OTA || {};
    window.OTA.gemini = window.OTA.gemini || {};
    window.OTA.gemini.core = window.OTA.gemini.core || {};
    
    /**
     * 错误处理中心类
     * 统一管理系统中的所有错误和异常
     */
    class ErrorHandler {
        constructor() {
            this.logger = window.getLogger?.() || console;
            
            // 错误分类
            this.errorCategories = {
                SYSTEM: 'system',           // 系统错误
                NETWORK: 'network',         // 网络错误
                API: 'api',                 // API错误
                VALIDATION: 'validation',   // 验证错误
                PARSING: 'parsing',         // 解析错误
                PROCESSING: 'processing',   // 处理错误
                UI: 'ui',                   // 界面错误
                PERMISSION: 'permission',   // 权限错误
                TIMEOUT: 'timeout',         // 超时错误
                UNKNOWN: 'unknown'          // 未知错误
            };
            
            // 错误严重级别
            this.errorSeverity = {
                CRITICAL: 'critical',       // 严重错误，系统无法继续
                HIGH: 'high',               // 高级错误，影响主要功能
                MEDIUM: 'medium',           // 中级错误，影响部分功能
                LOW: 'low',                 // 低级错误，不影响主要功能
                INFO: 'info'                // 信息性错误，仅记录
            };
            
            // 错误存储
            this.errorStorage = {
                recentErrors: [],           // 最近错误
                errorCounts: new Map(),     // 错误计数
                errorPatterns: new Map(),   // 错误模式
                recoveryAttempts: new Map() // 恢复尝试
            };
            
            // 错误处理配置
            this.config = {
                maxRecentErrors: 100,       // 最大最近错误数
                maxRetryAttempts: 3,        // 最大重试次数
                retryDelay: 1000,           // 重试延迟（毫秒）
                enableAutoRecovery: true,   // 启用自动恢复
                enableErrorReporting: true, // 启用错误报告
                enableUserNotification: true // 启用用户通知
            };
            
            // 错误恢复策略
            this.recoveryStrategies = {
                [this.errorCategories.NETWORK]: this.handleNetworkError.bind(this),
                [this.errorCategories.API]: this.handleAPIError.bind(this),
                [this.errorCategories.VALIDATION]: this.handleValidationError.bind(this),
                [this.errorCategories.PARSING]: this.handleParsingError.bind(this),
                [this.errorCategories.PROCESSING]: this.handleProcessingError.bind(this),
                [this.errorCategories.UI]: this.handleUIError.bind(this),
                [this.errorCategories.PERMISSION]: this.handlePermissionError.bind(this),
                [this.errorCategories.TIMEOUT]: this.handleTimeoutError.bind(this),
                [this.errorCategories.SYSTEM]: this.handleSystemError.bind(this),
                [this.errorCategories.UNKNOWN]: this.handleUnknownError.bind(this)
            };
            
            // 用户友好错误消息
            this.userFriendlyMessages = {
                [this.errorCategories.NETWORK]: '网络连接出现问题，请检查网络设置后重试',
                [this.errorCategories.API]: '服务暂时不可用，请稍后重试',
                [this.errorCategories.VALIDATION]: '输入的信息有误，请检查后重新提交',
                [this.errorCategories.PARSING]: '数据格式错误，请检查输入内容',
                [this.errorCategories.PROCESSING]: '处理过程中出现错误，请重试',
                [this.errorCategories.UI]: '界面操作出现问题，请刷新页面',
                [this.errorCategories.PERMISSION]: '权限不足，请联系管理员',
                [this.errorCategories.TIMEOUT]: '操作超时，请重试',
                [this.errorCategories.SYSTEM]: '系统出现错误，请联系技术支持',
                [this.errorCategories.UNKNOWN]: '出现未知错误，请重试或联系技术支持'
            };
            
            // 初始化全局错误监听
            this.initializeGlobalErrorHandling();
            
            this.logger.log('🛡️ 错误处理中心初始化完成', 'info');
        }
        
        /**
         * 初始化全局错误处理
         * @private
         */
        initializeGlobalErrorHandling() {
            // 监听未捕获的JavaScript错误
            window.addEventListener('error', (event) => {
                this.handleError({
                    category: this.errorCategories.SYSTEM,
                    severity: this.errorSeverity.HIGH,
                    message: event.message,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    stack: event.error?.stack,
                    source: 'global_error_handler'
                });
            });
            
            // 监听未处理的Promise拒绝
            window.addEventListener('unhandledrejection', (event) => {
                this.handleError({
                    category: this.errorCategories.SYSTEM,
                    severity: this.errorSeverity.HIGH,
                    message: 'Unhandled Promise Rejection',
                    reason: event.reason,
                    promise: event.promise,
                    source: 'unhandled_promise_rejection'
                });
            });
        }
        
        /**
         * 处理错误
         * @param {Object} errorInfo - 错误信息
         * @returns {Promise<Object>} 处理结果
         */
        async handleError(errorInfo) {
            try {
                // 标准化错误信息
                const standardizedError = this.standardizeError(errorInfo);
                
                // 记录错误
                this.recordError(standardizedError);
                
                // 分类错误
                const category = this.categorizeError(standardizedError);
                standardizedError.category = category;
                
                // 评估严重性
                const severity = this.assessSeverity(standardizedError);
                standardizedError.severity = severity;
                
                // 检查是否需要恢复
                const recoveryResult = await this.attemptRecovery(standardizedError);
                
                // 通知用户（如果需要）
                if (this.config.enableUserNotification && this.shouldNotifyUser(standardizedError)) {
                    this.notifyUser(standardizedError, recoveryResult);
                }
                
                // 报告错误（如果需要）
                if (this.config.enableErrorReporting && this.shouldReportError(standardizedError)) {
                    this.reportError(standardizedError);
                }
                
                return {
                    success: true,
                    error: standardizedError,
                    recovery: recoveryResult,
                    handled: true
                };
                
            } catch (handlingError) {
                this.logger.logError('错误处理过程中出现错误', handlingError);
                return {
                    success: false,
                    error: errorInfo,
                    handlingError: handlingError.message,
                    handled: false
                };
            }
        }
        
        /**
         * 标准化错误信息
         * @param {Object} errorInfo - 原始错误信息
         * @returns {Object} 标准化后的错误信息
         * @private
         */
        standardizeError(errorInfo) {
            const standardized = {
                id: this.generateErrorId(),
                timestamp: new Date().toISOString(),
                message: errorInfo.message || errorInfo.toString(),
                category: errorInfo.category || this.errorCategories.UNKNOWN,
                severity: errorInfo.severity || this.errorSeverity.MEDIUM,
                source: errorInfo.source || 'unknown',
                context: errorInfo.context || {},
                stack: errorInfo.stack || (errorInfo.error && errorInfo.error.stack),
                userAgent: navigator.userAgent,
                url: window.location.href
            };
            
            // 复制其他属性
            Object.keys(errorInfo).forEach(key => {
                if (!standardized.hasOwnProperty(key)) {
                    standardized[key] = errorInfo[key];
                }
            });
            
            return standardized;
        }
        
        /**
         * 生成错误ID
         * @returns {string} 错误ID
         * @private
         */
        generateErrorId() {
            return `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }
        
        /**
         * 记录错误
         * @param {Object} error - 错误信息
         * @private
         */
        recordError(error) {
            // 添加到最近错误列表
            this.errorStorage.recentErrors.push(error);
            
            // 保持最近错误数量在限制内
            if (this.errorStorage.recentErrors.length > this.config.maxRecentErrors) {
                this.errorStorage.recentErrors.shift();
            }
            
            // 更新错误计数
            const errorKey = `${error.category}_${error.message}`;
            const currentCount = this.errorStorage.errorCounts.get(errorKey) || 0;
            this.errorStorage.errorCounts.set(errorKey, currentCount + 1);
            
            // 记录错误模式
            this.recordErrorPattern(error);
            
            // 记录到日志
            this.logger.logError(`[${error.category}] ${error.message}`, error);
        }
        
        /**
         * 记录错误模式
         * @param {Object} error - 错误信息
         * @private
         */
        recordErrorPattern(error) {
            const pattern = {
                category: error.category,
                source: error.source,
                timestamp: error.timestamp
            };
            
            const patternKey = `${pattern.category}_${pattern.source}`;
            
            if (!this.errorStorage.errorPatterns.has(patternKey)) {
                this.errorStorage.errorPatterns.set(patternKey, []);
            }
            
            const patterns = this.errorStorage.errorPatterns.get(patternKey);
            patterns.push(pattern);
            
            // 保持模式记录在合理范围内
            if (patterns.length > 50) {
                patterns.splice(0, patterns.length - 50);
            }
        }
        
        /**
         * 分类错误
         * @param {Object} error - 错误信息
         * @returns {string} 错误分类
         * @private
         */
        categorizeError(error) {
            if (error.category && error.category !== this.errorCategories.UNKNOWN) {
                return error.category;
            }
            
            const message = error.message.toLowerCase();
            
            // 网络错误
            if (message.includes('network') || message.includes('fetch') || 
                message.includes('connection') || message.includes('timeout')) {
                return this.errorCategories.NETWORK;
            }
            
            // API错误
            if (message.includes('api') || message.includes('http') || 
                message.includes('status') || error.status) {
                return this.errorCategories.API;
            }
            
            // 验证错误
            if (message.includes('validation') || message.includes('invalid') || 
                message.includes('required') || message.includes('format')) {
                return this.errorCategories.VALIDATION;
            }
            
            // 解析错误
            if (message.includes('parse') || message.includes('json') || 
                message.includes('syntax') || message.includes('unexpected')) {
                return this.errorCategories.PARSING;
            }
            
            // 权限错误
            if (message.includes('permission') || message.includes('unauthorized') || 
                message.includes('forbidden') || message.includes('access')) {
                return this.errorCategories.PERMISSION;
            }
            
            // 超时错误
            if (message.includes('timeout') || message.includes('deadline')) {
                return this.errorCategories.TIMEOUT;
            }
            
            // UI错误
            if (message.includes('element') || message.includes('dom') || 
                message.includes('render') || error.source === 'ui') {
                return this.errorCategories.UI;
            }
            
            return this.errorCategories.UNKNOWN;
        }
        
        /**
         * 评估错误严重性
         * @param {Object} error - 错误信息
         * @returns {string} 严重性级别
         * @private
         */
        assessSeverity(error) {
            if (error.severity && error.severity !== this.errorSeverity.MEDIUM) {
                return error.severity;
            }
            
            // 根据错误分类评估严重性
            switch (error.category) {
                case this.errorCategories.SYSTEM:
                    return this.errorSeverity.CRITICAL;
                case this.errorCategories.NETWORK:
                case this.errorCategories.API:
                    return this.errorSeverity.HIGH;
                case this.errorCategories.VALIDATION:
                case this.errorCategories.PARSING:
                    return this.errorSeverity.MEDIUM;
                case this.errorCategories.UI:
                case this.errorCategories.PROCESSING:
                    return this.errorSeverity.LOW;
                default:
                    return this.errorSeverity.MEDIUM;
            }
        }
        
        /**
         * 尝试错误恢复
         * @param {Object} error - 错误信息
         * @returns {Promise<Object>} 恢复结果
         * @private
         */
        async attemptRecovery(error) {
            if (!this.config.enableAutoRecovery) {
                return { attempted: false, reason: 'auto_recovery_disabled' };
            }
            
            const recoveryKey = `${error.category}_${error.source}`;
            const attempts = this.errorStorage.recoveryAttempts.get(recoveryKey) || 0;
            
            if (attempts >= this.config.maxRetryAttempts) {
                return { 
                    attempted: false, 
                    reason: 'max_attempts_exceeded',
                    attempts 
                };
            }
            
            // 更新恢复尝试次数
            this.errorStorage.recoveryAttempts.set(recoveryKey, attempts + 1);
            
            try {
                // 获取恢复策略
                const recoveryStrategy = this.recoveryStrategies[error.category];
                
                if (!recoveryStrategy) {
                    return { 
                        attempted: false, 
                        reason: 'no_recovery_strategy',
                        category: error.category 
                    };
                }
                
                // 执行恢复策略
                const recoveryResult = await recoveryStrategy(error);
                
                // 如果恢复成功，重置尝试次数
                if (recoveryResult.success) {
                    this.errorStorage.recoveryAttempts.delete(recoveryKey);
                }
                
                return {
                    attempted: true,
                    success: recoveryResult.success,
                    strategy: error.category,
                    attempts: attempts + 1,
                    result: recoveryResult
                };
                
            } catch (recoveryError) {
                this.logger.logError('错误恢复失败', recoveryError);
                return {
                    attempted: true,
                    success: false,
                    error: recoveryError.message,
                    attempts: attempts + 1
                };
            }
        }
        
        /**
         * 判断是否应该通知用户
         * @param {Object} error - 错误信息
         * @returns {boolean} 是否通知用户
         * @private
         */
        shouldNotifyUser(error) {
            // 严重错误总是通知用户
            if (error.severity === this.errorSeverity.CRITICAL || 
                error.severity === this.errorSeverity.HIGH) {
                return true;
            }
            
            // 某些类型的错误需要用户知道
            const notifyCategories = [
                this.errorCategories.VALIDATION,
                this.errorCategories.PERMISSION,
                this.errorCategories.NETWORK
            ];
            
            return notifyCategories.includes(error.category);
        }
        
        /**
         * 通知用户
         * @param {Object} error - 错误信息
         * @param {Object} recoveryResult - 恢复结果
         * @private
         */
        notifyUser(error, recoveryResult) {
            const userMessage = this.getUserFriendlyMessage(error, recoveryResult);
            
            // 使用系统通知或UI组件显示错误
            if (window.OTA && window.OTA.ui && window.OTA.ui.showNotification) {
                window.OTA.ui.showNotification(userMessage, error.severity);
            } else {
                // 降级到浏览器alert（仅用于严重错误）
                if (error.severity === this.errorSeverity.CRITICAL) {
                    alert(userMessage);
                } else {
                    console.warn('用户通知:', userMessage);
                }
            }
        }
        
        /**
         * 获取用户友好的错误消息
         * @param {Object} error - 错误信息
         * @param {Object} recoveryResult - 恢复结果
         * @returns {string} 用户友好消息
         * @private
         */
        getUserFriendlyMessage(error, recoveryResult) {
            let message = this.userFriendlyMessages[error.category] || 
                         this.userFriendlyMessages[this.errorCategories.UNKNOWN];
            
            // 如果恢复成功，添加恢复信息
            if (recoveryResult && recoveryResult.success) {
                message += ' 系统已自动恢复。';
            } else if (recoveryResult && recoveryResult.attempted) {
                message += ' 系统正在尝试恢复，请稍候。';
            }
            
            return message;
        }
        
        /**
         * 判断是否应该报告错误
         * @param {Object} error - 错误信息
         * @returns {boolean} 是否报告错误
         * @private
         */
        shouldReportError(error) {
            // 严重错误总是报告
            if (error.severity === this.errorSeverity.CRITICAL) {
                return true;
            }
            
            // 检查错误频率
            const errorKey = `${error.category}_${error.message}`;
            const count = this.errorStorage.errorCounts.get(errorKey) || 0;
            
            // 如果错误频繁出现，需要报告
            return count >= 5;
        }
        
        /**
         * 报告错误
         * @param {Object} error - 错误信息
         * @private
         */
        reportError(error) {
            // 这里可以集成错误报告服务，如Sentry、Bugsnag等
            this.logger.log(`📊 错误报告: ${error.id}`, 'info', {
                category: error.category,
                severity: error.severity,
                message: error.message,
                count: this.errorStorage.errorCounts.get(`${error.category}_${error.message}`)
            });
        }
        
        // ==================== 错误恢复策略 ====================
        
        /**
         * 处理网络错误
         * @param {Object} error - 错误信息
         * @returns {Promise<Object>} 恢复结果
         * @private
         */
        async handleNetworkError(error) {
            // 等待一段时间后重试
            await this.delay(this.config.retryDelay);
            
            // 检查网络连接
            if (navigator.onLine) {
                return { success: true, action: 'network_retry' };
            } else {
                return { success: false, reason: 'offline' };
            }
        }
        
        /**
         * 处理API错误
         * @param {Object} error - 错误信息
         * @returns {Promise<Object>} 恢复结果
         * @private
         */
        async handleAPIError(error) {
            // 根据HTTP状态码决定恢复策略
            if (error.status >= 500) {
                // 服务器错误，等待后重试
                await this.delay(this.config.retryDelay * 2);
                return { success: true, action: 'server_error_retry' };
            } else if (error.status === 429) {
                // 请求过多，等待更长时间
                await this.delay(this.config.retryDelay * 5);
                return { success: true, action: 'rate_limit_retry' };
            } else {
                // 客户端错误，不重试
                return { success: false, reason: 'client_error' };
            }
        }
        
        /**
         * 处理验证错误
         * @param {Object} error - 错误信息
         * @returns {Promise<Object>} 恢复结果
         * @private
         */
        async handleValidationError(error) {
            // 验证错误通常需要用户修正，不自动恢复
            return { success: false, reason: 'user_input_required' };
        }
        
        /**
         * 处理解析错误
         * @param {Object} error - 错误信息
         * @returns {Promise<Object>} 恢复结果
         * @private
         */
        async handleParsingError(error) {
            // 尝试使用降级解析器
            if (window.OTA && window.OTA.gemini && window.OTA.gemini.configs && 
                window.OTA.gemini.configs.fallbackConfig) {
                try {
                    const fallbackResult = await window.OTA.gemini.configs.fallbackConfig
                        .executeFallback('dataParsingFailure', error.context);
                    return { success: true, action: 'fallback_parser', result: fallbackResult };
                } catch (fallbackError) {
                    return { success: false, reason: 'fallback_failed' };
                }
            }
            return { success: false, reason: 'no_fallback_available' };
        }
        
        /**
         * 处理处理错误
         * @param {Object} error - 错误信息
         * @returns {Promise<Object>} 恢复结果
         * @private
         */
        async handleProcessingError(error) {
            // 尝试简化处理流程
            return { success: true, action: 'simplified_processing' };
        }
        
        /**
         * 处理UI错误
         * @param {Object} error - 错误信息
         * @returns {Promise<Object>} 恢复结果
         * @private
         */
        async handleUIError(error) {
            // 尝试重新渲染UI组件
            if (error.context && error.context.component) {
                try {
                    // 这里可以添加组件重新渲染逻辑
                    return { success: true, action: 'component_rerender' };
                } catch (rerenderError) {
                    return { success: false, reason: 'rerender_failed' };
                }
            }
            return { success: false, reason: 'no_component_context' };
        }
        
        /**
         * 处理权限错误
         * @param {Object} error - 错误信息
         * @returns {Promise<Object>} 恢复结果
         * @private
         */
        async handlePermissionError(error) {
            // 权限错误通常需要重新认证
            return { success: false, reason: 'authentication_required' };
        }
        
        /**
         * 处理超时错误
         * @param {Object} error - 错误信息
         * @returns {Promise<Object>} 恢复结果
         * @private
         */
        async handleTimeoutError(error) {
            // 增加超时时间后重试
            return { success: true, action: 'timeout_retry', newTimeout: (error.timeout || 5000) * 2 };
        }
        
        /**
         * 处理系统错误
         * @param {Object} error - 错误信息
         * @returns {Promise<Object>} 恢复结果
         * @private
         */
        async handleSystemError(error) {
            // 系统错误可能需要页面刷新
            if (error.severity === this.errorSeverity.CRITICAL) {
                return { success: false, reason: 'critical_system_error', action: 'page_refresh_required' };
            }
            return { success: true, action: 'continue_with_degraded_functionality' };
        }
        
        /**
         * 处理未知错误
         * @param {Object} error - 错误信息
         * @returns {Promise<Object>} 恢复结果
         * @private
         */
        async handleUnknownError(error) {
            // 对于未知错误，尝试通用恢复策略
            await this.delay(this.config.retryDelay);
            return { success: true, action: 'generic_retry' };
        }
        
        /**
         * 延迟函数
         * @param {number} ms - 延迟毫秒数
         * @returns {Promise} Promise对象
         * @private
         */
        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
        
        /**
         * 获取错误统计
         * @returns {Object} 错误统计
         */
        getErrorStats() {
            const recentErrors = this.errorStorage.recentErrors;
            const now = Date.now();
            const oneHourAgo = now - (60 * 60 * 1000);
            const oneDayAgo = now - (24 * 60 * 60 * 1000);
            
            const recentHourErrors = recentErrors.filter(
                error => new Date(error.timestamp).getTime() > oneHourAgo
            );
            const recentDayErrors = recentErrors.filter(
                error => new Date(error.timestamp).getTime() > oneDayAgo
            );
            
            return {
                total: recentErrors.length,
                lastHour: recentHourErrors.length,
                lastDay: recentDayErrors.length,
                byCategory: this.getErrorsByCategory(),
                bySeverity: this.getErrorsBySeverity(),
                topErrors: this.getTopErrors(),
                recoveryRate: this.calculateRecoveryRate(),
                version: '1.0.0'
            };
        }
        
        /**
         * 按分类统计错误
         * @returns {Object} 分类统计
         * @private
         */
        getErrorsByCategory() {
            const stats = {};
            Object.values(this.errorCategories).forEach(category => {
                stats[category] = 0;
            });
            
            this.errorStorage.recentErrors.forEach(error => {
                if (stats.hasOwnProperty(error.category)) {
                    stats[error.category]++;
                }
            });
            
            return stats;
        }
        
        /**
         * 按严重性统计错误
         * @returns {Object} 严重性统计
         * @private
         */
        getErrorsBySeverity() {
            const stats = {};
            Object.values(this.errorSeverity).forEach(severity => {
                stats[severity] = 0;
            });
            
            this.errorStorage.recentErrors.forEach(error => {
                if (stats.hasOwnProperty(error.severity)) {
                    stats[error.severity]++;
                }
            });
            
            return stats;
        }
        
        /**
         * 获取最常见错误
         * @returns {Array} 错误列表
         * @private
         */
        getTopErrors() {
            const errorCounts = Array.from(this.errorStorage.errorCounts.entries())
                .sort((a, b) => b[1] - a[1])
                .slice(0, 10);
            
            return errorCounts.map(([key, count]) => ({
                error: key,
                count
            }));
        }
        
        /**
         * 计算恢复率
         * @returns {number} 恢复率百分比
         * @private
         */
        calculateRecoveryRate() {
            const totalAttempts = Array.from(this.errorStorage.recoveryAttempts.values())
                .reduce((sum, attempts) => sum + attempts, 0);
            
            if (totalAttempts === 0) return 0;
            
            // 这里简化计算，实际应该跟踪成功恢复的次数
            const successfulRecoveries = Math.floor(totalAttempts * 0.7); // 假设70%成功率
            
            return (successfulRecoveries / totalAttempts) * 100;
        }
        
        /**
         * 清理旧错误数据
         */
        cleanup() {
            const cutoffTime = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7天前
            
            // 清理旧错误记录
            this.errorStorage.recentErrors = this.errorStorage.recentErrors.filter(
                error => new Date(error.timestamp).getTime() > cutoffTime
            );
            
            // 清理旧恢复尝试记录
            this.errorStorage.recoveryAttempts.clear();
            
            this.logger.log('🧹 错误处理数据清理完成', 'info');
        }
    }
    
    // 创建全局实例
    const errorHandler = new ErrorHandler();
    
    // 注册到全局命名空间
    window.OTA.gemini.core.ErrorHandler = ErrorHandler;
    window.OTA.gemini.core.errorHandler = errorHandler;
    
    // 便捷访问函数
    window.OTA.gemini.core.handleError = function(errorInfo) {
        return errorHandler.handleError(errorInfo);
    };
    
    // 注册到服务注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('error-handler', errorHandler, {
            dependencies: ['logger'],
            description: '错误处理中心，统一处理所有组件的错误和异常情况'
        });
    }
    
    console.log('✅ 错误处理中心已加载');
    
})();
