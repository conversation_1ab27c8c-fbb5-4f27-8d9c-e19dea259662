/**
 * @CORE 数据规范化处理器
 * 🏷️ 标签: @DATA_NORMALIZER
 * 📝 说明: 负责订单数据的规范化处理，包括格式转换、字段验证和数据清理
 * 🎯 功能: 电话号码规范化、日期时间格式化、地址清理、数据类型转换
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.core = window.OTA.gemini.core || {};

(function() {
    'use strict';

    /**
     * 数据规范化处理器类
     * 负责订单数据的标准化和验证
     */
    class DataNormalizer {
        constructor() {
            this.logger = window.getLogger?.() || console;
            
            // 初始化验证规则
            this.initializeValidationRules();
        }

        /**
         * 初始化验证规则
         */
        initializeValidationRules() {
            // 整数字段列表
            this.integerFields = [
                'passenger_count', 'luggage_count', 'car_type_id', 
                'sub_category_id', 'driving_region_id', 'backend_user_id'
            ];

            // ID字段列表
            this.idFields = [
                'car_type_id', 'sub_category_id', 'driving_region_id', 'backend_user_id'
            ];

            // 布尔字段列表
            this.booleanFields = [
                'meet_and_greet', 'child_seat_required', 'wheelchair_accessible'
            ];

            // 有效货币列表
            this.validCurrencies = ['MYR', 'USD', 'SGD', 'CNY'];

            // 有效服务类型ID
            this.validServiceTypeIds = [2, 3, 4]; // 接机、送机、包车
        }

        /**
         * 规范化订单数据格式
         * @param {Object} data - 原始数据对象
         * @returns {Object} 规范化后的数据对象
         */
        normalizeDataFormats(data) {
            if (!data || typeof data !== 'object') {
                this.logger.log('数据规范化失败：输入数据无效', 'error');
                return null;
            }

            const normalizedData = { ...data };

            try {
                // 规范化电话号码
                if (normalizedData.customer_contact) {
                    normalizedData.customer_contact = this.normalizePhoneNumber(normalizedData.customer_contact);
                }
                
                // 规范化日期
                if (normalizedData.pickup_date) {
                    normalizedData.pickup_date = this.normalizeDate(normalizedData.pickup_date);
                }

                // 规范化时间字段
                const timeFields = ['pickup_time', 'flight_time', 'departure_time', 'arrival_time'];
                timeFields.forEach(field => {
                    if (normalizedData[field]) {
                        normalizedData[field] = this.normalizeTime(normalizedData[field]);
                    }
                });

                // 规范化地点字段
                const locationFields = ['pickup', 'dropoff'];
                locationFields.forEach(field => {
                    if (normalizedData[field]) {
                        normalizedData[field] = this.normalizeLocation(normalizedData[field]);
                    }
                });

                // 转换整数字段
                this.integerFields.forEach(field => {
                    if (normalizedData[field] !== null && normalizedData[field] !== undefined) {
                        normalizedData[field] = parseInt(normalizedData[field], 10) || null;
                    }
                });

                // 处理价格字段（内部处理使用驼峰命名）
                if (normalizedData.ota_price !== undefined) {
                    normalizedData.otaPrice = parseFloat(normalizedData.ota_price) || null;
                    delete normalizedData.ota_price;
                }

                // 验证ID字段
                this.idFields.forEach(field => {
                    if (normalizedData[field] !== null && normalizedData[field] !== undefined) {
                        normalizedData[field] = parseInt(normalizedData[field], 10) || null;
                    }
                });

                // 转换布尔字段
                this.booleanFields.forEach(field => {
                    if (normalizedData[field] !== null && normalizedData[field] !== undefined) {
                        normalizedData[field] = String(normalizedData[field]).toLowerCase() === 'true';
                    }
                });

                this.logger.log('数据规范化完成', 'info');
                return normalizedData;

            } catch (error) {
                this.logger.logError('数据规范化过程中发生错误', error);
                return data; // 返回原始数据作为降级方案
            }
        }

        /**
         * 规范化电话号码格式
         * @param {string} phone - 原始电话号码
         * @returns {string} 规范化后的电话号码
         */
        normalizePhoneNumber(phone) {
            if (!phone || typeof phone !== 'string') return '';
            
            // 移除常见的分隔符，保留数字和+号
            let normalized = phone.replace(/[+\-\s\(\)]/g, '');
            
            // 如果是马来西亚号码，确保格式正确
            if (normalized.startsWith('60') && normalized.length >= 10) {
                normalized = '+' + normalized;
            } else if (normalized.startsWith('0') && normalized.length >= 9) {
                // 本地号码转换为国际格式
                normalized = '+60' + normalized.substring(1);
            }
            
            return normalized;
        }

        /**
         * 规范化日期格式
         * @param {string} date - 原始日期字符串
         * @returns {string} YYYY-MM-DD格式的日期
         */
        normalizeDate(date) {
            if (!date || typeof date !== 'string') return '';
            
            try {
                // 尝试直接解析
                const parsedDate = new Date(date);
                if (!isNaN(parsedDate.getTime())) {
                    return parsedDate.toISOString().split('T')[0];
                }

                // 尝试解析DD-MM-YYYY格式
                const ddmmyyyyMatch = date.match(/(\d{1,2})-(\d{1,2})-(\d{4})/);
                if (ddmmyyyyMatch) {
                    const [, day, month, year] = ddmmyyyyMatch;
                    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
                }

                return date; // 如果无法解析，返回原始值
            } catch (error) {
                this.logger.logError('日期规范化失败', error);
                return date;
            }
        }

        /**
         * 规范化时间格式
         * @param {string} time - 原始时间字符串
         * @returns {string} HH:MM格式的时间
         */
        normalizeTime(time) {
            if (!time || typeof time !== 'string') return '';
            
            const match = time.match(/(\d{1,2}):(\d{2})/);
            if (match) {
                const hour = match[1].padStart(2, '0');
                const minute = match[2];
                return `${hour}:${minute}`;
            }
            
            return time;
        }

        /**
         * 规范化地点信息
         * @param {string} location - 原始地点字符串
         * @returns {string} 清理后的地点信息
         */
        normalizeLocation(location) {
            if (!location || typeof location !== 'string') return '';
            
            // 移除特殊字符和多余空格
            return location.replace(/\*/g, '').trim();
        }

        /**
         * 清理电话号码格式（用于字段验证）
         * @param {string} phone - 电话号码
         * @returns {string|null} 清理后的电话号码
         */
        cleanPhoneNumber(phone) {
            if (!phone) return null;
            // 清理电话号码格式，保留数字、+、-、空格
            return phone.toString().replace(/[^\d+\-\s]/g, '').trim() || null;
        }

        /**
         * 验证邮箱格式
         * @param {string} email - 邮箱地址
         * @returns {string|null} 有效的邮箱或null
         */
        validateEmail(email) {
            if (!email) return null;
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email) ? email : null;
        }

        /**
         * 验证日期格式
         * @param {string} date - 日期字符串
         * @returns {string|null} 有效的日期或null
         */
        validateDate(date) {
            if (!date) return null;
            // 验证YYYY-MM-DD格式
            const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
            if (dateRegex.test(date)) {
                const parsedDate = new Date(date);
                if (!isNaN(parsedDate.getTime())) {
                    return date;
                }
            }
            return null;
        }

        /**
         * 验证时间格式
         * @param {string} time - 时间字符串
         * @returns {string|null} 有效的时间或null
         */
        validateTime(time) {
            if (!time) return null;
            // 验证HH:MM格式
            const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
            return timeRegex.test(time) ? time : null;
        }

        /**
         * 验证价格
         * @param {number|string} price - 价格
         * @returns {number|null} 有效的价格或null
         */
        validatePrice(price) {
            const numPrice = parseFloat(price);
            return (!isNaN(numPrice) && numPrice >= 0) ? numPrice : null;
        }

        /**
         * 验证货币代码
         * @param {string} currency - 货币代码
         * @returns {string} 有效的货币代码
         */
        validateCurrency(currency) {
            return this.validCurrencies.includes(currency) ? currency : 'MYR';
        }

        /**
         * 验证车型ID（根据乘客数量推荐）
         * @param {number} carTypeId - 车型ID
         * @param {number} passengerCount - 乘客数量
         * @returns {number} 有效的车型ID
         */
        validateCarTypeId(carTypeId, passengerCount) {
            const id = parseInt(carTypeId);
            if (!isNaN(id) && id > 0) return id;
            
            // 根据乘客数量推荐车型
            if (!passengerCount || passengerCount <= 3) return 1; // Comfort 5 Seater
            if (passengerCount <= 4) return 37; // Extended 5
            if (passengerCount <= 5) return 15; // 7 Seater MPV
            if (passengerCount <= 7) return 16; // 9 Seater MPV
            return 17; // 13 Seater Van
        }

        /**
         * 验证服务子类别ID
         * @param {number} subCategoryId - 子类别ID
         * @returns {number} 有效的子类别ID
         */
        validateSubCategoryId(subCategoryId) {
            const id = parseInt(subCategoryId);
            return this.validServiceTypeIds.includes(id) ? id : 2; // 默认接机
        }

        /**
         * 验证驾驶区域ID
         * @param {number} drivingRegionId - 驾驶区域ID
         * @returns {number} 有效的驾驶区域ID
         */
        validateDrivingRegionId(drivingRegionId) {
            const id = parseInt(drivingRegionId);
            return (!isNaN(id) && id > 0) ? id : 1; // 默认吉隆坡
        }

        /**
         * 验证语言数组
         * @param {Array} languages - 语言ID数组
         * @returns {Array} 有效的语言ID数组
         */
        validateLanguagesArray(languages) {
            try {
                const languageManager = window.getLanguageManager?.();
                if (languageManager) {
                    const validation = languageManager.validateLanguageIdsSync(languages);
                    
                    if (validation.valid && validation.validIds.length > 0) {
                        return validation.validIds;
                    } else {
                        // 获取默认语言选择
                        return languageManager.getDefaultLanguageIds();
                    }
                } else {
                    // 降级方案：返回默认语言
                    return [2]; // English
                }
            } catch (error) {
                this.logger.logError('语言验证失败', error);
                return [2]; // 默认英语
            }
        }

        /**
         * 计算数据置信度
         * @param {Object} data - 数据对象
         * @returns {number} 置信度分数 (0-1)
         */
        calculateConfidence(data) {
            if (!data || typeof data !== 'object') return 0;

            const totalFields = Object.keys(data).length;
            if (totalFields === 0) return 0;

            let filledFields = 0;
            for (const key in data) {
                if (data[key] !== null && data[key] !== undefined && data[key] !== '') {
                    filledFields++;
                }
            }

            return filledFields / totalFields;
        }

        /**
         * 创建默认订单对象
         * @param {string} rawText - 原始文本
         * @returns {Object} 默认订单对象
         */
        createDefaultOrder(rawText) {
            return {
                rawText: rawText,
                customerName: null,
                customerContact: null,
                customerEmail: null,
                pickup: null,
                dropoff: null,
                pickupDate: null,
                pickupTime: null,
                passengerCount: null,
                luggageCount: null,
                carTypeId: 1, // 默认Comfort 5 Seater
                subCategoryId: 2, // 默认接机
                drivingRegionId: 1, // 默认吉隆坡
                backendUserId: null,
                languagesIdArray: [2], // 默认英语
                otaPrice: null,
                otaCurrency: 'MYR',
                otaReferenceNumber: null,
                specialRequests: null,
                flightInfo: null,
                meetAndGreet: false,
                childSeatRequired: false,
                wheelchairAccessible: false,
                _confidence: 0.1
            };
        }

        /**
         * 获取规范化器统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                integerFieldCount: this.integerFields.length,
                idFieldCount: this.idFields.length,
                booleanFieldCount: this.booleanFields.length,
                supportedCurrencies: this.validCurrencies.length,
                validServiceTypes: this.validServiceTypeIds.length
            };
        }
    }

    // 暴露到全局命名空间
    window.OTA.gemini.core.DataNormalizer = DataNormalizer;

    // 创建单例实例
    let dataNormalizerInstance = null;

    /**
     * 获取数据规范化器单例实例
     * @returns {DataNormalizer} 数据规范化器实例
     */
    function getDataNormalizer() {
        if (!dataNormalizerInstance) {
            dataNormalizerInstance = new DataNormalizer();
        }
        return dataNormalizerInstance;
    }

    // 暴露工厂函数
    window.OTA.gemini.core.getDataNormalizer = getDataNormalizer;

    // 注册到服务注册中心
    if (window.OTA?.gemini?.core?.ServiceRegistry) {
        window.OTA.gemini.core.ServiceRegistry.register('dataNormalizer', getDataNormalizer, '@DATA_NORMALIZER');
    }

    console.log('✅ 数据规范化处理器模块已加载');

})();
