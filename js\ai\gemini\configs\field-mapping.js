/**
 * @CONFIG_FILE 字段映射配置
 * 🏷️ 标签: @FIELD_MAPPING @OTA_CONFIG
 * 📝 说明: 定义所有OTA平台的字段映射规则和数据转换逻辑
 * 🎯 目标: 统一管理不同OTA平台的字段映射，支持动态配置和热更新
 */

(function() {
    'use strict';
    
    // 确保OTA命名空间存在
    window.OTA = window.OTA || {};
    window.OTA.gemini = window.OTA.gemini || {};
    window.OTA.gemini.configs = window.OTA.gemini.configs || {};
    
    /**
     * 字段映射配置类
     * 管理所有OTA平台的字段映射规则和数据转换逻辑
     */
    class FieldMappingConfig {
        constructor() {
            this.logger = window.getLogger?.() || console;
            
            // 通用字段映射（所有OTA平台共享）
            this.commonFieldMapping = {
                // 基本信息字段
                'pickup_location': ['pickup', 'pickup_address', 'from', 'departure', 'origin'],
                'dropoff_location': ['dropoff', 'destination', 'to', 'arrival', 'drop_off'],
                'pickup_date': ['date', 'pickup_date', 'departure_date', 'travel_date'],
                'pickup_time': ['time', 'pickup_time', 'departure_time', 'travel_time'],
                'passenger_count': ['passengers', 'pax', 'passenger_number', 'guest_count', 'traveler_count'],
                'luggage_count': ['luggage', 'bags', 'luggage_number', 'baggage_count'],
                
                // 联系信息字段
                'customer_name': ['name', 'passenger_name', 'guest_name', 'contact_name', 'traveler_name'],
                'customer_phone': ['phone', 'mobile', 'contact_number', 'telephone', 'cell_phone'],
                'customer_email': ['email', 'contact_email', 'guest_email'],
                
                // 订单信息字段
                'ota_reference_number': ['reference', 'booking_reference', 'confirmation_number', 'order_number'],
                'ota_price': ['price', 'amount', 'cost', 'total_price', 'booking_price'],
                'currency': ['currency', 'currency_code'],
                'special_requirements': ['notes', 'remarks', 'special_requests', 'comments', 'instructions'],
                
                // 航班信息字段
                'flight_number': ['flight', 'flight_no', 'flight_code'],
                'airline': ['airline', 'carrier', 'airline_name'],
                'terminal': ['terminal', 'terminal_number'],
                
                // 服务类型字段
                'service_type': ['service', 'service_type', 'booking_type', 'trip_type'],
                'car_type': ['vehicle', 'car_type', 'vehicle_type', 'car_category']
            };
            
            // OTA平台特定字段映射
            this.otaSpecificMapping = {
                'fliggy': {
                    // 飞猪特定字段映射
                    'ota_reference_number': ['订单号', '确认号', '飞猪订单号'],
                    'pickup_location': ['出发地', '接机地点', '上车地点'],
                    'dropoff_location': ['目的地', '送达地点', '下车地点'],
                    'customer_name': ['乘客姓名', '联系人', '客户姓名'],
                    'customer_phone': ['联系电话', '手机号码', '电话'],
                    'special_requirements': ['特殊要求', '备注', '说明'],
                    'flight_number': ['航班号', '班次'],
                    'pickup_date': ['出行日期', '接机日期', '服务日期'],
                    'pickup_time': ['出行时间', '接机时间', '服务时间']
                },
                
                'ctrip': {
                    // 携程特定字段映射
                    'ota_reference_number': ['携程订单号', '确认号', '订单编号'],
                    'pickup_location': ['出发地', '接送地点', '上车点'],
                    'dropoff_location': ['目的地', '送达地点', '下车点'],
                    'customer_name': ['乘客姓名', '联系人姓名', '客人姓名'],
                    'customer_phone': ['联系电话', '手机', '电话号码'],
                    'special_requirements': ['特殊需求', '备注信息', '其他要求'],
                    'flight_number': ['航班信息', '航班号'],
                    'pickup_date': ['用车日期', '服务日期', '出行日期'],
                    'pickup_time': ['用车时间', '服务时间', '出行时间']
                },
                
                'kkday': {
                    // KKday特定字段映射
                    'ota_reference_number': ['KKday訂單號', '確認號', 'Order Number'],
                    'pickup_location': ['接送地點', '上車地點', 'Pickup Location'],
                    'dropoff_location': ['目的地', '下車地點', 'Drop-off Location'],
                    'customer_name': ['旅客姓名', '聯絡人', 'Passenger Name'],
                    'customer_phone': ['聯絡電話', '手機號碼', 'Contact Number'],
                    'special_requirements': ['特殊需求', '備註', 'Special Requirements'],
                    'flight_number': ['航班號', 'Flight Number'],
                    'pickup_date': ['服務日期', '出行日期', 'Service Date'],
                    'pickup_time': ['服務時間', '出行時間', 'Service Time']
                },
                
                'klook': {
                    // Klook特定字段映射
                    'ota_reference_number': ['Klook Order', 'Booking Reference', 'Confirmation Number'],
                    'pickup_location': ['Pickup Point', 'Meeting Point', 'Departure Location'],
                    'dropoff_location': ['Drop-off Point', 'Destination', 'Arrival Location'],
                    'customer_name': ['Guest Name', 'Traveler Name', 'Contact Person'],
                    'customer_phone': ['Contact Number', 'Mobile Number', 'Phone'],
                    'special_requirements': ['Special Requests', 'Additional Notes', 'Remarks'],
                    'flight_number': ['Flight Information', 'Flight Number'],
                    'pickup_date': ['Travel Date', 'Service Date', 'Activity Date'],
                    'pickup_time': ['Travel Time', 'Service Time', 'Activity Time']
                },
                
                'agoda': {
                    // Agoda特定字段映射
                    'ota_reference_number': ['Agoda Booking', 'Reservation Number', 'Booking ID'],
                    'pickup_location': ['Pickup Location', 'Hotel Address', 'Starting Point'],
                    'dropoff_location': ['Destination', 'Drop-off Location', 'End Point'],
                    'customer_name': ['Guest Name', 'Primary Guest', 'Booker Name'],
                    'customer_phone': ['Contact Number', 'Guest Phone', 'Mobile'],
                    'special_requirements': ['Special Requests', 'Guest Notes', 'Additional Information'],
                    'flight_number': ['Flight Details', 'Flight Number'],
                    'pickup_date': ['Check-in Date', 'Service Date', 'Travel Date'],
                    'pickup_time': ['Check-in Time', 'Service Time', 'Pickup Time']
                },
                
                'booking': {
                    // Booking.com特定字段映射
                    'ota_reference_number': ['Booking.com Reference', 'Reservation Number', 'PIN Code'],
                    'pickup_location': ['Property Address', 'Pickup Location', 'Hotel Location'],
                    'dropoff_location': ['Destination', 'Drop-off Point', 'Transfer Destination'],
                    'customer_name': ['Guest Name', 'Main Guest', 'Reservation Holder'],
                    'customer_phone': ['Guest Phone', 'Contact Number', 'Mobile Number'],
                    'special_requirements': ['Special Requests', 'Guest Comments', 'Additional Notes'],
                    'flight_number': ['Flight Information', 'Arrival Flight'],
                    'pickup_date': ['Check-in Date', 'Arrival Date', 'Service Date'],
                    'pickup_time': ['Check-in Time', 'Arrival Time', 'Service Time']
                },
                
                'jrcoach': {
                    // JRCoach特定字段映射
                    'ota_reference_number': ['JRCoach Order', 'Booking Number', 'Reference Code'],
                    'pickup_location': ['Pickup Point', 'Departure Station', 'Starting Location'],
                    'dropoff_location': ['Destination', 'Arrival Station', 'End Location'],
                    'customer_name': ['Passenger Name', 'Traveler', 'Guest Name'],
                    'customer_phone': ['Contact Phone', 'Mobile', 'Phone Number'],
                    'special_requirements': ['Special Needs', 'Notes', 'Additional Requirements'],
                    'flight_number': ['Flight Info', 'Flight Number'],
                    'pickup_date': ['Travel Date', 'Departure Date', 'Service Date'],
                    'pickup_time': ['Departure Time', 'Travel Time', 'Service Time']
                },
                
                'generic': {
                    // 通用映射（降级使用）
                    'ota_reference_number': ['order', 'booking', 'reference', 'confirmation'],
                    'pickup_location': ['from', 'pickup', 'origin', 'departure'],
                    'dropoff_location': ['to', 'destination', 'arrival', 'dropoff'],
                    'customer_name': ['name', 'passenger', 'guest', 'customer'],
                    'customer_phone': ['phone', 'mobile', 'contact', 'number'],
                    'special_requirements': ['notes', 'remarks', 'comments', 'special'],
                    'flight_number': ['flight', 'airline', 'plane'],
                    'pickup_date': ['date', 'day', 'when'],
                    'pickup_time': ['time', 'hour', 'clock']
                }
            };
            
            // 数据转换规则
            this.dataTransformRules = {
                // 日期格式转换
                'pickup_date': {
                    patterns: [
                        /(\d{4})[年\-\/](\d{1,2})[月\-\/](\d{1,2})[日]?/,  // 中文日期格式
                        /(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})/,           // DD/MM/YYYY 或 MM/DD/YYYY
                        /(\d{4})[\/\-](\d{1,2})[\/\-](\d{1,2})/,           // YYYY/MM/DD
                        /(\d{1,2})\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+(\d{4})/i // DD MMM YYYY
                    ],
                    transform: (value) => this.transformDate(value)
                },
                
                // 时间格式转换
                'pickup_time': {
                    patterns: [
                        /(\d{1,2}):(\d{2})\s*(AM|PM)?/i,                   // HH:MM AM/PM
                        /(\d{1,2})[时点](\d{1,2})?[分]?/,                   // 中文时间格式
                        /(\d{1,2})\.(\d{2})/                               // HH.MM
                    ],
                    transform: (value) => this.transformTime(value)
                },
                
                // 电话号码格式转换
                'customer_phone': {
                    patterns: [
                        /[\+]?[\d\s\-\(\)]{8,}/,                           // 国际电话格式
                        /1[3-9]\d{9}/,                                     // 中国手机号
                        /\d{3,4}\-\d{7,8}/                                 // 固定电话
                    ],
                    transform: (value) => this.transformPhone(value)
                },
                
                // 价格格式转换
                'ota_price': {
                    patterns: [
                        /[\$¥￥€£]\s*(\d+(?:\.\d{2})?)/,                   // 货币符号+数字
                        /(\d+(?:\.\d{2})?)\s*(USD|CNY|MYR|SGD|EUR|GBP)/i,  // 数字+货币代码
                        /(\d+(?:\.\d{2})?)/                                // 纯数字
                    ],
                    transform: (value) => this.transformPrice(value)
                },
                
                // 乘客数量转换
                'passenger_count': {
                    patterns: [
                        /(\d+)\s*[人位个名]/,                              // 中文数量
                        /(\d+)\s*(passenger|pax|people|person)s?/i,        // 英文数量
                        /(\d+)/                                            // 纯数字
                    ],
                    transform: (value) => this.transformPassengerCount(value)
                }
            };
            
            this.logger.log('🗺️ 字段映射配置初始化完成', 'info');
        }
        
        /**
         * 获取OTA平台的字段映射
         * @param {string} otaChannel - OTA渠道名称
         * @returns {Object} 字段映射配置
         */
        getFieldMapping(otaChannel) {
            const normalizedChannel = this.normalizeOTAChannel(otaChannel);
            const specificMapping = this.otaSpecificMapping[normalizedChannel] || this.otaSpecificMapping['generic'];
            
            // 合并通用映射和特定映射
            const combinedMapping = {};
            
            // 先添加通用映射
            Object.keys(this.commonFieldMapping).forEach(field => {
                combinedMapping[field] = [...this.commonFieldMapping[field]];
            });
            
            // 再添加特定映射
            Object.keys(specificMapping).forEach(field => {
                if (combinedMapping[field]) {
                    // 合并数组，去重
                    combinedMapping[field] = [...new Set([...combinedMapping[field], ...specificMapping[field]])];
                } else {
                    combinedMapping[field] = [...specificMapping[field]];
                }
            });
            
            return combinedMapping;
        }
        
        /**
         * 映射字段值
         * @param {string} fieldName - 字段名称
         * @param {any} value - 原始值
         * @param {string} otaChannel - OTA渠道
         * @returns {any} 转换后的值
         */
        mapFieldValue(fieldName, value, otaChannel = 'generic') {
            if (value === null || value === undefined || value === '') {
                return value;
            }
            
            try {
                // 获取转换规则
                const transformRule = this.dataTransformRules[fieldName];
                if (transformRule && transformRule.transform) {
                    return transformRule.transform(value);
                }
                
                // 如果没有特定转换规则，返回清理后的值
                return this.cleanFieldValue(value);
            } catch (error) {
                this.logger.logError(`字段值映射失败: ${fieldName}`, error);
                return value; // 返回原始值
            }
        }
        
        /**
         * 批量映射字段
         * @param {Object} data - 原始数据
         * @param {string} otaChannel - OTA渠道
         * @returns {Object} 映射后的数据
         */
        mapFields(data, otaChannel = 'generic') {
            const mapping = this.getFieldMapping(otaChannel);
            const mappedData = {};
            
            // 遍历所有目标字段
            Object.keys(mapping).forEach(targetField => {
                const possibleFields = mapping[targetField];
                
                // 查找匹配的源字段
                for (const sourceField of possibleFields) {
                    const value = this.findFieldValue(data, sourceField);
                    if (value !== null && value !== undefined && value !== '') {
                        mappedData[targetField] = this.mapFieldValue(targetField, value, otaChannel);
                        break; // 找到第一个匹配的值就停止
                    }
                }
            });
            
            return mappedData;
        }
        
        /**
         * 验证映射结果
         * @param {Object} mappedData - 映射后的数据
         * @param {string} otaChannel - OTA渠道
         * @returns {Object} 验证结果
         */
        validateMappedData(mappedData, otaChannel = 'generic') {
            const validation = {
                valid: true,
                errors: [],
                warnings: [],
                completeness: 0,
                requiredFields: ['pickup_location', 'dropoff_location', 'pickup_date', 'customer_name'],
                optionalFields: ['pickup_time', 'customer_phone', 'ota_reference_number', 'passenger_count']
            };
            
            // 检查必填字段
            validation.requiredFields.forEach(field => {
                if (!mappedData[field] || mappedData[field] === '') {
                    validation.valid = false;
                    validation.errors.push(`缺少必填字段: ${field}`);
                }
            });
            
            // 检查可选字段
            validation.optionalFields.forEach(field => {
                if (!mappedData[field] || mappedData[field] === '') {
                    validation.warnings.push(`缺少可选字段: ${field}`);
                }
            });
            
            // 计算完整度
            const totalFields = validation.requiredFields.length + validation.optionalFields.length;
            const filledFields = Object.keys(mappedData).filter(field => 
                mappedData[field] !== null && mappedData[field] !== undefined && mappedData[field] !== ''
            ).length;
            validation.completeness = Math.round((filledFields / totalFields) * 100);
            
            return validation;
        }
        
        // ==================== 私有方法 ====================
        
        /**
         * 标准化OTA渠道名称
         * @param {string} channel - 原始渠道名称
         * @returns {string} 标准化后的渠道名称
         * @private
         */
        normalizeOTAChannel(channel) {
            if (!channel) return 'generic';
            
            const normalized = channel.toLowerCase().trim();
            const channelMap = {
                'fliggy': 'fliggy',
                '飞猪': 'fliggy',
                'ctrip': 'ctrip',
                '携程': 'ctrip',
                'kkday': 'kkday',
                'klook': 'klook',
                'agoda': 'agoda',
                'booking': 'booking',
                'booking.com': 'booking',
                'jrcoach': 'jrcoach',
                'jr coach': 'jrcoach'
            };
            
            return channelMap[normalized] || 'generic';
        }
        
        /**
         * 查找字段值（支持模糊匹配）
         * @param {Object} data - 数据对象
         * @param {string} fieldName - 字段名称
         * @returns {any} 字段值
         * @private
         */
        findFieldValue(data, fieldName) {
            // 精确匹配
            if (data.hasOwnProperty(fieldName)) {
                return data[fieldName];
            }
            
            // 不区分大小写匹配
            const lowerFieldName = fieldName.toLowerCase();
            for (const key of Object.keys(data)) {
                if (key.toLowerCase() === lowerFieldName) {
                    return data[key];
                }
            }
            
            // 模糊匹配（包含关系）
            for (const key of Object.keys(data)) {
                if (key.toLowerCase().includes(lowerFieldName) || 
                    lowerFieldName.includes(key.toLowerCase())) {
                    return data[key];
                }
            }
            
            return null;
        }
        
        /**
         * 清理字段值
         * @param {any} value - 原始值
         * @returns {any} 清理后的值
         * @private
         */
        cleanFieldValue(value) {
            if (typeof value === 'string') {
                return value.trim().replace(/\s+/g, ' '); // 清理多余空格
            }
            return value;
        }
        
        /**
         * 转换日期格式
         * @param {string} dateStr - 日期字符串
         * @returns {string} 标准日期格式 (DD-MM-YYYY)
         * @private
         */
        transformDate(dateStr) {
            try {
                // 尝试各种日期格式的解析
                const patterns = this.dataTransformRules.pickup_date.patterns;
                
                for (const pattern of patterns) {
                    const match = dateStr.match(pattern);
                    if (match) {
                        let day, month, year;
                        
                        if (pattern.source.includes('年')) {
                            // 中文格式: YYYY年MM月DD日
                            year = match[1];
                            month = match[2].padStart(2, '0');
                            day = match[3].padStart(2, '0');
                        } else if (pattern.source.includes('Jan|Feb')) {
                            // 英文月份格式: DD MMM YYYY
                            day = match[1].padStart(2, '0');
                            month = this.getMonthNumber(match[2]).padStart(2, '0');
                            year = match[3];
                        } else if (match[1].length === 4) {
                            // YYYY/MM/DD 格式
                            year = match[1];
                            month = match[2].padStart(2, '0');
                            day = match[3].padStart(2, '0');
                        } else {
                            // DD/MM/YYYY 格式（假设）
                            day = match[1].padStart(2, '0');
                            month = match[2].padStart(2, '0');
                            year = match[3];
                        }
                        
                        return `${day}-${month}-${year}`;
                    }
                }
                
                // 如果都不匹配，尝试使用Date对象解析
                const date = new Date(dateStr);
                if (!isNaN(date.getTime())) {
                    const day = date.getDate().toString().padStart(2, '0');
                    const month = (date.getMonth() + 1).toString().padStart(2, '0');
                    const year = date.getFullYear();
                    return `${day}-${month}-${year}`;
                }
                
                return dateStr; // 无法解析，返回原值
            } catch (error) {
                this.logger.logError('日期转换失败', error);
                return dateStr;
            }
        }
        
        /**
         * 转换时间格式
         * @param {string} timeStr - 时间字符串
         * @returns {string} 标准时间格式 (HH:MM)
         * @private
         */
        transformTime(timeStr) {
            try {
                const patterns = this.dataTransformRules.pickup_time.patterns;
                
                for (const pattern of patterns) {
                    const match = timeStr.match(pattern);
                    if (match) {
                        let hours = parseInt(match[1]);
                        let minutes = parseInt(match[2] || 0);
                        
                        // 处理AM/PM
                        if (match[3]) {
                            const ampm = match[3].toUpperCase();
                            if (ampm === 'PM' && hours !== 12) {
                                hours += 12;
                            } else if (ampm === 'AM' && hours === 12) {
                                hours = 0;
                            }
                        }
                        
                        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
                    }
                }
                
                return timeStr; // 无法解析，返回原值
            } catch (error) {
                this.logger.logError('时间转换失败', error);
                return timeStr;
            }
        }
        
        /**
         * 转换电话号码格式
         * @param {string} phoneStr - 电话号码字符串
         * @returns {string} 清理后的电话号码
         * @private
         */
        transformPhone(phoneStr) {
            try {
                // 移除所有非数字字符，保留+号
                let cleaned = phoneStr.replace(/[^\d\+]/g, '');
                
                // 如果是中国手机号，确保格式正确
                if (/^1[3-9]\d{9}$/.test(cleaned)) {
                    return cleaned;
                }
                
                // 如果有国际区号，保留
                if (cleaned.startsWith('+')) {
                    return cleaned;
                }
                
                return cleaned;
            } catch (error) {
                this.logger.logError('电话号码转换失败', error);
                return phoneStr;
            }
        }
        
        /**
         * 转换价格格式
         * @param {string} priceStr - 价格字符串
         * @returns {Object} 价格对象 {amount: number, currency: string}
         * @private
         */
        transformPrice(priceStr) {
            try {
                const patterns = this.dataTransformRules.ota_price.patterns;
                
                for (const pattern of patterns) {
                    const match = priceStr.match(pattern);
                    if (match) {
                        const amount = parseFloat(match[1]);
                        let currency = 'MYR'; // 默认马币
                        
                        // 检测货币类型
                        if (priceStr.includes('$') || match[2] === 'USD') currency = 'USD';
                        else if (priceStr.includes('¥') || priceStr.includes('￥') || match[2] === 'CNY') currency = 'CNY';
                        else if (match[2] === 'SGD') currency = 'SGD';
                        else if (match[2] === 'EUR') currency = 'EUR';
                        else if (match[2] === 'GBP') currency = 'GBP';
                        
                        return { amount, currency };
                    }
                }
                
                // 如果都不匹配，尝试提取数字
                const numMatch = priceStr.match(/(\d+(?:\.\d{2})?)/);
                if (numMatch) {
                    return { amount: parseFloat(numMatch[1]), currency: 'MYR' };
                }
                
                return priceStr; // 无法解析，返回原值
            } catch (error) {
                this.logger.logError('价格转换失败', error);
                return priceStr;
            }
        }
        
        /**
         * 转换乘客数量
         * @param {string} countStr - 数量字符串
         * @returns {number} 乘客数量
         * @private
         */
        transformPassengerCount(countStr) {
            try {
                const patterns = this.dataTransformRules.passenger_count.patterns;
                
                for (const pattern of patterns) {
                    const match = countStr.match(pattern);
                    if (match) {
                        return parseInt(match[1]);
                    }
                }
                
                // 尝试直接转换为数字
                const num = parseInt(countStr);
                return isNaN(num) ? 1 : num; // 默认1人
            } catch (error) {
                this.logger.logError('乘客数量转换失败', error);
                return 1;
            }
        }
        
        /**
         * 获取月份数字
         * @param {string} monthName - 月份名称
         * @returns {string} 月份数字
         * @private
         */
        getMonthNumber(monthName) {
            const months = {
                'jan': '01', 'feb': '02', 'mar': '03', 'apr': '04',
                'may': '05', 'jun': '06', 'jul': '07', 'aug': '08',
                'sep': '09', 'oct': '10', 'nov': '11', 'dec': '12'
            };
            return months[monthName.toLowerCase()] || '01';
        }

        /**
         * 获取字段映射统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                totalOTAChannels: Object.keys(this.otaSpecificMapping).length,
                commonFields: Object.keys(this.commonFieldMapping).length,
                transformRules: Object.keys(this.dataTransformRules).length,
                supportedChannels: Object.keys(this.otaSpecificMapping),
                version: '1.0.0',
                lastUpdated: new Date().toISOString()
            };
        }

        /**
         * 添加自定义字段映射
         * @param {string} otaChannel - OTA渠道
         * @param {string} targetField - 目标字段
         * @param {Array} sourceFields - 源字段数组
         */
        addCustomMapping(otaChannel, targetField, sourceFields) {
            const normalizedChannel = this.normalizeOTAChannel(otaChannel);

            if (!this.otaSpecificMapping[normalizedChannel]) {
                this.otaSpecificMapping[normalizedChannel] = {};
            }

            this.otaSpecificMapping[normalizedChannel][targetField] = sourceFields;
            this.logger.log(`✅ 添加自定义映射: ${normalizedChannel}.${targetField}`, 'info');
        }

        /**
         * 移除字段映射
         * @param {string} otaChannel - OTA渠道
         * @param {string} targetField - 目标字段
         */
        removeMapping(otaChannel, targetField) {
            const normalizedChannel = this.normalizeOTAChannel(otaChannel);

            if (this.otaSpecificMapping[normalizedChannel] &&
                this.otaSpecificMapping[normalizedChannel][targetField]) {
                delete this.otaSpecificMapping[normalizedChannel][targetField];
                this.logger.log(`🗑️ 移除映射: ${normalizedChannel}.${targetField}`, 'info');
            }
        }

        /**
         * 导出配置
         * @returns {Object} 完整配置对象
         */
        exportConfig() {
            return {
                commonFieldMapping: this.commonFieldMapping,
                otaSpecificMapping: this.otaSpecificMapping,
                dataTransformRules: this.dataTransformRules,
                metadata: this.getStats()
            };
        }

        /**
         * 导入配置
         * @param {Object} config - 配置对象
         */
        importConfig(config) {
            try {
                if (config.commonFieldMapping) {
                    this.commonFieldMapping = { ...this.commonFieldMapping, ...config.commonFieldMapping };
                }

                if (config.otaSpecificMapping) {
                    this.otaSpecificMapping = { ...this.otaSpecificMapping, ...config.otaSpecificMapping };
                }

                if (config.dataTransformRules) {
                    this.dataTransformRules = { ...this.dataTransformRules, ...config.dataTransformRules };
                }

                this.logger.log('✅ 配置导入成功', 'info');
            } catch (error) {
                this.logger.logError('配置导入失败', error);
                throw error;
            }
        }

        /**
         * 验证配置完整性
         * @returns {Object} 验证结果
         */
        validateConfig() {
            const validation = {
                valid: true,
                errors: [],
                warnings: []
            };

            // 检查必要的OTA渠道
            const requiredChannels = ['fliggy', 'ctrip', 'kkday', 'klook', 'agoda', 'booking', 'jrcoach', 'generic'];
            requiredChannels.forEach(channel => {
                if (!this.otaSpecificMapping[channel]) {
                    validation.valid = false;
                    validation.errors.push(`缺少OTA渠道配置: ${channel}`);
                }
            });

            // 检查必要的通用字段
            const requiredCommonFields = ['pickup_location', 'dropoff_location', 'pickup_date', 'customer_name'];
            requiredCommonFields.forEach(field => {
                if (!this.commonFieldMapping[field]) {
                    validation.valid = false;
                    validation.errors.push(`缺少通用字段映射: ${field}`);
                }
            });

            // 检查转换规则
            const requiredTransformRules = ['pickup_date', 'pickup_time'];
            requiredTransformRules.forEach(rule => {
                if (!this.dataTransformRules[rule]) {
                    validation.warnings.push(`缺少转换规则: ${rule}`);
                }
            });

            return validation;
        }

        /**
         * 重置为默认配置
         */
        resetToDefaults() {
            this.logger.log('🔄 重置字段映射配置为默认值', 'info');
            // 重新初始化配置（通过重新创建实例的方式）
            const newInstance = new FieldMappingConfig();
            this.commonFieldMapping = newInstance.commonFieldMapping;
            this.otaSpecificMapping = newInstance.otaSpecificMapping;
            this.dataTransformRules = newInstance.dataTransformRules;
        }
    }
    
    // 创建全局实例
    const fieldMappingConfig = new FieldMappingConfig();
    
    // 注册到全局命名空间
    window.OTA.gemini.configs.FieldMappingConfig = FieldMappingConfig;
    window.OTA.gemini.configs.fieldMappingConfig = fieldMappingConfig;
    
    // 便捷访问函数
    window.OTA.gemini.configs.getFieldMapping = function(otaChannel) {
        return fieldMappingConfig.getFieldMapping(otaChannel);
    };
    
    window.OTA.gemini.configs.mapFields = function(data, otaChannel) {
        return fieldMappingConfig.mapFields(data, otaChannel);
    };
    
    // 注册到服务注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('field-mapping-config', fieldMappingConfig, {
            dependencies: ['logger'],
            description: '字段映射配置管理器，提供OTA平台字段映射和数据转换功能'
        });
    }
    
    console.log('✅ 字段映射配置已加载');
    
})();
