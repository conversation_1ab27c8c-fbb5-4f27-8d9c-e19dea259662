/**
 * @TEST 向后兼容性测试
 * 🏷️ 标签: @BACKWARD_COMPATIBILITY_TEST
 * 📝 说明: 测试新的GeminiCoordinator与原GeminiService的向后兼容性
 * 🎯 功能: 验证所有原有接口的行为一致性
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.tests = window.OTA.gemini.tests || {};

(function() {
    'use strict';

    /**
     * 向后兼容性测试套件
     */
    class BackwardCompatibilityTest {
        constructor() {
            this.logger = window.getLogger?.() || console;
            this.testResults = [];
            this.coordinator = null;
        }

        /**
         * 运行所有兼容性测试
         */
        async runAllTests() {
            this.logger.log('开始向后兼容性测试', 'info');
            
            try {
                // 初始化协调器
                await this.initializeCoordinator();
                
                // 运行各项测试
                await this.testParseOrderInterface();
                await this.testParseMultipleOrdersInterface();
                await this.testAnalyzeImageInterface();
                await this.testGetStatusInterface();
                await this.testConfigureRealtimeAnalysisInterface();
                await this.testUpdateIdMappingsInterface();
                await this.testGlobalGeminiServiceInterface();
                await this.testCurrentMethodCompatibility();
                
                // 生成测试报告
                this.generateTestReport();
                
            } catch (error) {
                this.logger.logError('向后兼容性测试失败', error);
                this.addTestResult('总体测试', false, error.message);
            }
        }

        /**
         * 初始化协调器
         */
        async initializeCoordinator() {
            try {
                this.coordinator = window.OTA?.gemini?.getGeminiCoordinator?.();
                if (!this.coordinator) {
                    throw new Error('无法获取GeminiCoordinator实例');
                }
                
                // 等待初始化完成
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                this.addTestResult('协调器初始化', true, '成功获取协调器实例');
            } catch (error) {
                this.addTestResult('协调器初始化', false, error.message);
                throw error;
            }
        }

        /**
         * 测试parseOrder接口
         */
        async testParseOrderInterface() {
            try {
                // 测试基本调用
                const result1 = await this.coordinator.parseOrder('测试订单文本', false);
                const hasCorrectStructure = result1 === null || (typeof result1 === 'object');
                
                // 测试实时分析调用
                const result2 = await this.coordinator.parseOrder('测试实时分析订单文本内容', true);
                
                // 测试参数验证
                const result3 = await this.coordinator.parseOrder('', false);
                const correctNullReturn = result3 === null;
                
                const success = hasCorrectStructure && correctNullReturn;
                this.addTestResult('parseOrder接口', success, 
                    success ? '接口行为符合预期' : '接口行为不符合预期');
                    
            } catch (error) {
                this.addTestResult('parseOrder接口', false, error.message);
            }
        }

        /**
         * 测试parseMultipleOrders接口
         */
        async testParseMultipleOrdersInterface() {
            try {
                // 测试正常调用
                const result1 = await this.coordinator.parseMultipleOrders(['订单1', '订单2']);
                const isArray = Array.isArray(result1);
                
                // 测试空数组
                const result2 = await this.coordinator.parseMultipleOrders([]);
                const emptyArrayReturn = Array.isArray(result2) && result2.length === 0;
                
                // 测试无效参数
                const result3 = await this.coordinator.parseMultipleOrders(null);
                const nullHandling = Array.isArray(result3) && result3.length === 0;
                
                const success = isArray && emptyArrayReturn && nullHandling;
                this.addTestResult('parseMultipleOrders接口', success,
                    success ? '接口行为符合预期' : '接口行为不符合预期');
                    
            } catch (error) {
                this.addTestResult('parseMultipleOrders接口', false, error.message);
            }
        }

        /**
         * 测试analyzeImage接口
         */
        async testAnalyzeImageInterface() {
            try {
                // 测试无效参数处理
                let errorThrown = false;
                try {
                    await this.coordinator.analyzeImage('');
                } catch (e) {
                    errorThrown = true;
                }
                
                // 测试null参数
                let nullErrorThrown = false;
                try {
                    await this.coordinator.analyzeImage(null);
                } catch (e) {
                    nullErrorThrown = true;
                }
                
                const success = errorThrown && nullErrorThrown;
                this.addTestResult('analyzeImage接口', success,
                    success ? '错误处理符合预期' : '错误处理不符合预期');
                    
            } catch (error) {
                this.addTestResult('analyzeImage接口', false, error.message);
            }
        }

        /**
         * 测试getStatus接口
         */
        async testGetStatusInterface() {
            try {
                const status = this.coordinator.getStatus();
                
                // 验证返回结构
                const hasRequiredFields = status && 
                    typeof status.isAnalyzing === 'boolean' &&
                    typeof status.lastAnalyzedText === 'string' &&
                    typeof status.totalRequests === 'number';
                
                this.addTestResult('getStatus接口', hasRequiredFields,
                    hasRequiredFields ? '状态结构符合预期' : '状态结构不符合预期');
                    
            } catch (error) {
                this.addTestResult('getStatus接口', false, error.message);
            }
        }

        /**
         * 测试configureRealtimeAnalysis接口
         */
        async testConfigureRealtimeAnalysisInterface() {
            try {
                // 测试正常配置
                this.coordinator.configureRealtimeAnalysis({
                    enabled: true,
                    minInputLength: 25
                });
                
                // 测试无效参数处理
                this.coordinator.configureRealtimeAnalysis(null);
                this.coordinator.configureRealtimeAnalysis('invalid');
                
                // 验证配置是否生效
                const configApplied = this.coordinator.realtimeConfig.minInputLength === 25;
                
                this.addTestResult('configureRealtimeAnalysis接口', configApplied,
                    configApplied ? '配置更新成功' : '配置更新失败');
                    
            } catch (error) {
                this.addTestResult('configureRealtimeAnalysis接口', false, error.message);
            }
        }

        /**
         * 测试updateIdMappings接口
         */
        async testUpdateIdMappingsInterface() {
            try {
                const testSystemData = {
                    backend_users: [
                        { id: 1, email: '<EMAIL>' },
                        { id: 2, email: '<EMAIL>' }
                    ],
                    service_types: [{ id: 1, name: 'Airport Transfer' }],
                    car_types: [{ id: 1, name: 'Sedan' }],
                    languages: [{ id: 1, name: 'English' }]
                };
                
                // 测试正常更新
                this.coordinator.updateIdMappings(testSystemData);
                
                // 验证映射是否更新
                const mappingUpdated = this.coordinator.idMappings.backendUsers['<EMAIL>'] === 1;
                
                // 测试无效参数处理
                this.coordinator.updateIdMappings(null);
                this.coordinator.updateIdMappings('invalid');
                
                this.addTestResult('updateIdMappings接口', mappingUpdated,
                    mappingUpdated ? 'ID映射更新成功' : 'ID映射更新失败');
                    
            } catch (error) {
                this.addTestResult('updateIdMappings接口', false, error.message);
            }
        }

        /**
         * 测试全局GeminiService接口
         */
        async testGlobalGeminiServiceInterface() {
            try {
                // 验证全局GeminiService对象存在
                const geminiServiceExists = typeof window.GeminiService === 'object';
                
                // 验证所有必需方法存在
                const requiredMethods = [
                    'parseOrder', 'parseMultipleOrders', 'analyzeImage', 
                    'getStatus', 'configureRealtimeAnalysis', 'updateIdMappings'
                ];
                
                const allMethodsExist = requiredMethods.every(method => 
                    typeof window.GeminiService[method] === 'function'
                );
                
                // 测试方法调用
                const status = window.GeminiService.getStatus();
                const statusCallWorks = typeof status === 'object';
                
                const success = geminiServiceExists && allMethodsExist && statusCallWorks;
                this.addTestResult('全局GeminiService接口', success,
                    success ? '全局接口完整可用' : '全局接口不完整或不可用');
                    
            } catch (error) {
                this.addTestResult('全局GeminiService接口', false, error.message);
            }
        }

        /**
         * 测试当前有效方法的兼容性
         */
        async testCurrentMethodCompatibility() {
            try {
                // 测试configureRealtimeAnalysis方法（当前有效方法）
                const configureRealtimeAnalysisExists = typeof window.GeminiService.configureRealtimeAnalysis === 'function';

                // 测试调用
                window.GeminiService.configureRealtimeAnalysis({ enabled: false });

                this.addTestResult('当前方法兼容性', configureRealtimeAnalysisExists,
                    configureRealtimeAnalysisExists ? '当前方法正常工作' : '当前方法不可用');

            } catch (error) {
                this.addTestResult('当前方法兼容性', false, error.message);
            }
        }

        /**
         * 添加测试结果
         */
        addTestResult(testName, success, message) {
            this.testResults.push({
                testName,
                success,
                message,
                timestamp: new Date().toISOString()
            });
        }

        /**
         * 生成测试报告
         */
        generateTestReport() {
            const totalTests = this.testResults.length;
            const passedTests = this.testResults.filter(r => r.success).length;
            const failedTests = totalTests - passedTests;
            
            const report = {
                summary: {
                    total: totalTests,
                    passed: passedTests,
                    failed: failedTests,
                    passRate: ((passedTests / totalTests) * 100).toFixed(2) + '%'
                },
                details: this.testResults
            };
            
            this.logger.log('向后兼容性测试报告', 'info', report);
            
            // 输出到控制台
            console.group('🔄 向后兼容性测试报告');
            console.log(`📊 总测试数: ${totalTests}`);
            console.log(`✅ 通过: ${passedTests}`);
            console.log(`❌ 失败: ${failedTests}`);
            console.log(`📈 通过率: ${report.summary.passRate}`);
            
            this.testResults.forEach(result => {
                const icon = result.success ? '✅' : '❌';
                console.log(`${icon} ${result.testName}: ${result.message}`);
            });
            
            console.groupEnd();
            
            return report;
        }
    }

    // 暴露到全局命名空间
    window.OTA.gemini.tests.BackwardCompatibilityTest = BackwardCompatibilityTest;
    
    // 提供便捷的测试运行函数
    window.runBackwardCompatibilityTest = async function() {
        const test = new BackwardCompatibilityTest();
        return await test.runAllTests();
    };

    console.log('✅ 向后兼容性测试模块已加载');

})();
