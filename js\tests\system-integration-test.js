/**
 * 系统集成测试
 * 验证架构修复的有效性和系统整体健康度
 * 
 * 测试范围:
 * - 控制台日志污染修复验证
 * - 双重依赖模式修复验证
 * - 模块初始化时序验证
 * - 性能瓶颈修复验证
 * - 配置管理统一验证
 * - 架构守护系统验证
 * - 代码质量监控验证
 * - 自动化架构检查验证
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.tests = window.OTA.tests || {};

(function() {
    'use strict';

    /**
     * 系统集成测试类
     */
    class SystemIntegrationTest {
        constructor() {
            this.testResults = {
                totalTests: 0,
                passedTests: 0,
                failedTests: 0,
                skippedTests: 0,
                testDetails: [],
                startTime: null,
                endTime: null,
                duration: 0
            };
            
            this.testSuites = [
                'consoleLogPollutionFix',
                'dualDependencyPatternFix',
                'moduleInitializationTiming',
                'performanceBottleneckFix',
                'configurationManagementUnification',
                'architectureGuardianSystem',
                'codeQualityMonitoring',
                'automatedArchitectureChecking'
            ];
            
            this.logger = null;
            this.initialize();
        }

        /**
         * 初始化测试系统
         */
        initialize() {
            // 延迟获取依赖
            setTimeout(() => {
                this.logger = window.OTA?.getService?.('logger') || console;
                console.log('✅ 系统集成测试已初始化');
            }, 100);
        }

        /**
         * 运行所有集成测试
         */
        async runAllTests() {
            console.group('🧪 开始系统集成测试');
            this.testResults.startTime = new Date().toISOString();
            
            try {
                // 运行所有测试套件
                for (const suiteName of this.testSuites) {
                    await this.runTestSuite(suiteName);
                }
                
                // 计算测试结果
                this.calculateTestResults();
                
                // 生成测试报告
                const report = this.generateTestReport();
                
                console.log('📊 集成测试完成');
                console.log('测试报告:', report);
                
                return report;
                
            } catch (error) {
                console.error('集成测试执行失败:', error);
                throw error;
            } finally {
                this.testResults.endTime = new Date().toISOString();
                this.testResults.duration = new Date(this.testResults.endTime) - new Date(this.testResults.startTime);
                console.groupEnd();
            }
        }

        /**
         * 运行测试套件
         */
        async runTestSuite(suiteName) {
            console.group(`🔍 测试套件: ${suiteName}`);
            
            try {
                const methodName = `test${suiteName.charAt(0).toUpperCase() + suiteName.slice(1)}`;
                
                if (typeof this[methodName] === 'function') {
                    const suiteResults = await this[methodName]();
                    this.recordTestSuiteResults(suiteName, suiteResults);
                } else {
                    this.recordTestResult(suiteName, false, `测试方法 ${methodName} 不存在`);
                }
                
            } catch (error) {
                this.recordTestResult(suiteName, false, `测试套件执行失败: ${error.message}`);
            } finally {
                console.groupEnd();
            }
        }

        /**
         * 测试控制台日志污染修复
         */
        async testConsoleLogPollutionFix() {
            const results = [];
            
            // 测试1: 验证警告管理器存在
            const warningManager = window.OTA?.core?.warningManager;
            results.push({
                name: '警告管理器存在性',
                passed: !!warningManager,
                message: warningManager ? '警告管理器已正确加载' : '警告管理器不存在'
            });
            
            // 测试2: 验证警告频率控制
            if (warningManager) {
                const testWarningType = 'TEST_WARNING_FREQUENCY';
                let warningCount = 0;
                
                // 模拟控制台警告计数
                const originalWarn = console.warn;
                console.warn = (...args) => {
                    if (args[0] && args[0].includes(testWarningType)) {
                        warningCount++;
                    }
                    originalWarn.apply(console, args);
                };
                
                // 发出多次相同警告
                for (let i = 0; i < 5; i++) {
                    warningManager.warn(testWarningType, '测试警告频率控制', 'WARNING');
                }
                
                // 恢复原始console.warn
                console.warn = originalWarn;
                
                results.push({
                    name: '警告频率控制',
                    passed: warningCount <= 3,
                    message: `警告次数: ${warningCount} (期望 ≤ 3)`
                });
            }
            
            // 测试3: 验证环境检测
            if (warningManager) {
                const hasEnvironmentDetection = typeof warningManager.isProduction === 'function';
                results.push({
                    name: '环境检测功能',
                    passed: hasEnvironmentDetection,
                    message: hasEnvironmentDetection ? '环境检测功能可用' : '环境检测功能缺失'
                });
            }
            
            return results;
        }

        /**
         * 测试双重依赖模式修复
         */
        async testDualDependencyPatternFix() {
            const results = [];
            
            // 测试1: 验证统一服务访问器存在
            const unifiedServiceAccessor = window.OTA?.core?.unifiedServiceAccessor;
            results.push({
                name: '统一服务访问器存在性',
                passed: !!unifiedServiceAccessor,
                message: unifiedServiceAccessor ? '统一服务访问器已正确加载' : '统一服务访问器不存在'
            });
            
            // 测试2: 验证getService方法可用
            const getServiceMethod = window.OTA?.getService;
            results.push({
                name: 'getService方法可用性',
                passed: typeof getServiceMethod === 'function',
                message: typeof getServiceMethod === 'function' ? 'getService方法可用' : 'getService方法不可用'
            });
            
            // 测试3: 测试服务获取功能
            if (getServiceMethod) {
                try {
                    const logger = getServiceMethod('logger');
                    results.push({
                        name: '服务获取功能',
                        passed: !!logger,
                        message: logger ? '服务获取功能正常' : '服务获取失败'
                    });
                } catch (error) {
                    results.push({
                        name: '服务获取功能',
                        passed: false,
                        message: `服务获取异常: ${error.message}`
                    });
                }
            }
            
            // 测试4: 验证回退机制
            if (unifiedServiceAccessor && typeof unifiedServiceAccessor.getService === 'function') {
                try {
                    const nonExistentService = unifiedServiceAccessor.getService('nonExistentService');
                    results.push({
                        name: '回退机制',
                        passed: nonExistentService === null || nonExistentService === undefined,
                        message: '回退机制正常工作'
                    });
                } catch (error) {
                    results.push({
                        name: '回退机制',
                        passed: true,
                        message: '回退机制通过异常处理工作'
                    });
                }
            }
            
            return results;
        }

        /**
         * 测试模块初始化时序
         */
        async testModuleInitializationTiming() {
            const results = [];
            
            // 测试1: 验证循环依赖解析器存在
            const circularDependencyResolver = window.OTA?.core?.circularDependencyResolver;
            results.push({
                name: '循环依赖解析器存在性',
                passed: !!circularDependencyResolver,
                message: circularDependencyResolver ? '循环依赖解析器已加载' : '循环依赖解析器不存在'
            });
            
            // 测试2: 验证应用启动器存在
            const applicationBootstrap = window.OTA?.bootstrap?.applicationBootstrap;
            results.push({
                name: '应用启动器存在性',
                passed: !!applicationBootstrap,
                message: applicationBootstrap ? '应用启动器已加载' : '应用启动器不存在'
            });
            
            // 测试3: 验证关键服务初始化
            const criticalServices = ['logger', 'configCenter', 'performanceMonitor'];
            let initializedServices = 0;
            
            criticalServices.forEach(serviceName => {
                const service = window.OTA?.getService?.(serviceName);
                if (service) {
                    initializedServices++;
                }
            });
            
            results.push({
                name: '关键服务初始化',
                passed: initializedServices >= 2,
                message: `已初始化服务: ${initializedServices}/${criticalServices.length}`
            });
            
            // 测试4: 验证UIManager和EventManager循环依赖解决
            const uiManager = window.OTA?.getService?.('uiManager');
            const eventManager = window.OTA?.getService?.('eventManager');
            
            results.push({
                name: 'UIManager-EventManager循环依赖解决',
                passed: !!uiManager && !!eventManager,
                message: (uiManager && eventManager) ? '循环依赖已解决' : '循环依赖未解决'
            });
            
            return results;
        }

        /**
         * 测试性能瓶颈修复
         */
        async testPerformanceBottleneckFix() {
            const results = [];
            
            // 测试1: 验证性能监控器存在
            const performanceMonitor = window.OTA?.performanceMonitor || window.OTA?.core?.performanceMonitor;
            results.push({
                name: '性能监控器存在性',
                passed: !!performanceMonitor,
                message: performanceMonitor ? '性能监控器已加载' : '性能监控器不存在'
            });
            
            // 测试2: 验证内存阈值更新
            if (performanceMonitor && performanceMonitor.thresholds) {
                const memoryThreshold = performanceMonitor.thresholds.memory;
                const expectedThreshold = 100 * 1024 * 1024; // 100MB
                
                results.push({
                    name: '内存阈值更新',
                    passed: memoryThreshold >= expectedThreshold,
                    message: `内存阈值: ${(memoryThreshold / 1024 / 1024).toFixed(2)}MB`
                });
            }
            
            // 测试3: 验证DOM查询阈值更新
            if (performanceMonitor && performanceMonitor.thresholds) {
                const domQueryThreshold = performanceMonitor.thresholds.domQueries;
                const expectedThreshold = 200;
                
                results.push({
                    name: 'DOM查询阈值更新',
                    passed: domQueryThreshold >= expectedThreshold,
                    message: `DOM查询阈值: ${domQueryThreshold}`
                });
            }
            
            // 测试4: 验证性能监控功能
            if (performanceMonitor) {
                const hasMonitoringMethods = typeof performanceMonitor.checkMemoryThreshold === 'function' &&
                                           typeof performanceMonitor.checkDOMQueryThreshold === 'function';
                
                results.push({
                    name: '性能监控功能',
                    passed: hasMonitoringMethods,
                    message: hasMonitoringMethods ? '性能监控功能完整' : '性能监控功能不完整'
                });
            }
            
            return results;
        }

        /**
         * 测试配置管理统一
         */
        async testConfigurationManagementUnification() {
            const results = [];
            
            // 测试1: 验证配置管理协调器存在
            const configManagementCoordinator = window.OTA?.core?.configManagementCoordinator;
            results.push({
                name: '配置管理协调器存在性',
                passed: !!configManagementCoordinator,
                message: configManagementCoordinator ? '配置管理协调器已加载' : '配置管理协调器不存在'
            });
            
            // 测试2: 验证统一配置访问
            const getConfigMethod = window.OTA?.getConfig;
            results.push({
                name: '统一配置访问',
                passed: typeof getConfigMethod === 'function',
                message: typeof getConfigMethod === 'function' ? '统一配置访问可用' : '统一配置访问不可用'
            });
            
            // 测试3: 验证配置缓存优化器存在
            const configCacheOptimizer = window.OTA?.core?.configCacheOptimizer;
            results.push({
                name: '配置缓存优化器存在性',
                passed: !!configCacheOptimizer,
                message: configCacheOptimizer ? '配置缓存优化器已加载' : '配置缓存优化器不存在'
            });
            
            // 测试4: 测试配置获取功能
            if (getConfigMethod) {
                try {
                    const testConfig = getConfigMethod('test.config', 'default_value');
                    results.push({
                        name: '配置获取功能',
                        passed: testConfig === 'default_value',
                        message: '配置获取功能正常'
                    });
                } catch (error) {
                    results.push({
                        name: '配置获取功能',
                        passed: false,
                        message: `配置获取异常: ${error.message}`
                    });
                }
            }
            
            return results;
        }

        /**
         * 测试架构守护系统
         */
        async testArchitectureGuardianSystem() {
            const results = [];
            
            // 测试1: 验证架构守护者存在
            const architectureGuardian = window.OTA?.core?.architectureGuardian;
            results.push({
                name: '架构守护者存在性',
                passed: !!architectureGuardian,
                message: architectureGuardian ? '架构守护者已加载' : '架构守护者不存在'
            });
            
            // 测试2: 验证实时监控功能
            const startRealTimeMonitoring = window.startRealTimeMonitoring;
            results.push({
                name: '实时监控功能',
                passed: typeof startRealTimeMonitoring === 'function',
                message: typeof startRealTimeMonitoring === 'function' ? '实时监控功能可用' : '实时监控功能不可用'
            });
            
            // 测试3: 验证监控状态获取
            const getMonitoringStatus = window.getMonitoringStatus;
            results.push({
                name: '监控状态获取',
                passed: typeof getMonitoringStatus === 'function',
                message: typeof getMonitoringStatus === 'function' ? '监控状态获取可用' : '监控状态获取不可用'
            });
            
            // 测试4: 验证违规检测功能
            if (architectureGuardian) {
                const hasViolationDetection = typeof architectureGuardian.detectViolation === 'function';
                results.push({
                    name: '违规检测功能',
                    passed: hasViolationDetection,
                    message: hasViolationDetection ? '违规检测功能可用' : '违规检测功能不可用'
                });
            }
            
            return results;
        }

        /**
         * 测试代码质量监控
         */
        async testCodeQualityMonitoring() {
            const results = [];
            
            // 测试1: 验证代码质量监控器存在
            const codeQualityMonitor = window.OTA?.core?.codeQualityMonitor;
            results.push({
                name: '代码质量监控器存在性',
                passed: !!codeQualityMonitor,
                message: codeQualityMonitor ? '代码质量监控器已加载' : '代码质量监控器不存在'
            });
            
            // 测试2: 验证质量分析功能
            const analyzeCodeQuality = window.analyzeCodeQuality;
            results.push({
                name: '质量分析功能',
                passed: typeof analyzeCodeQuality === 'function',
                message: typeof analyzeCodeQuality === 'function' ? '质量分析功能可用' : '质量分析功能不可用'
            });
            
            // 测试3: 验证质量统计获取
            const getQualityStats = window.getQualityStats;
            results.push({
                name: '质量统计获取',
                passed: typeof getQualityStats === 'function',
                message: typeof getQualityStats === 'function' ? '质量统计获取可用' : '质量统计获取不可用'
            });
            
            // 测试4: 测试质量分析执行
            if (codeQualityMonitor && typeof codeQualityMonitor.performQualityAnalysis === 'function') {
                try {
                    // 执行快速质量分析测试
                    const analysisPromise = codeQualityMonitor.performQualityAnalysis();
                    const isPromise = analysisPromise && typeof analysisPromise.then === 'function';
                    
                    results.push({
                        name: '质量分析执行',
                        passed: isPromise,
                        message: isPromise ? '质量分析可以执行' : '质量分析执行失败'
                    });
                } catch (error) {
                    results.push({
                        name: '质量分析执行',
                        passed: false,
                        message: `质量分析执行异常: ${error.message}`
                    });
                }
            }
            
            return results;
        }

        /**
         * 测试自动化架构检查
         */
        async testAutomatedArchitectureChecking() {
            const results = [];
            
            // 测试1: 验证自动化架构检查器存在
            const automatedArchitectureChecker = window.OTA?.core?.automatedArchitectureChecker;
            results.push({
                name: '自动化架构检查器存在性',
                passed: !!automatedArchitectureChecker,
                message: automatedArchitectureChecker ? '自动化架构检查器已加载' : '自动化架构检查器不存在'
            });
            
            // 测试2: 验证架构检查控制功能
            const startArchitectureChecking = window.startArchitectureChecking;
            const stopArchitectureChecking = window.stopArchitectureChecking;
            
            results.push({
                name: '架构检查控制功能',
                passed: typeof startArchitectureChecking === 'function' && typeof stopArchitectureChecking === 'function',
                message: '架构检查控制功能可用'
            });
            
            // 测试3: 验证检查状态获取
            const getArchitectureCheckStatus = window.getArchitectureCheckStatus;
            results.push({
                name: '检查状态获取',
                passed: typeof getArchitectureCheckStatus === 'function',
                message: typeof getArchitectureCheckStatus === 'function' ? '检查状态获取可用' : '检查状态获取不可用'
            });
            
            // 测试4: 验证快速检查功能
            const performQuickArchitectureCheck = window.performQuickArchitectureCheck;
            results.push({
                name: '快速检查功能',
                passed: typeof performQuickArchitectureCheck === 'function',
                message: typeof performQuickArchitectureCheck === 'function' ? '快速检查功能可用' : '快速检查功能不可用'
            });
            
            // 测试5: 测试检查状态获取
            if (getArchitectureCheckStatus) {
                try {
                    const status = getArchitectureCheckStatus();
                    const hasValidStatus = status && typeof status === 'object' && status.checkState;
                    
                    results.push({
                        name: '检查状态获取执行',
                        passed: hasValidStatus,
                        message: hasValidStatus ? '检查状态获取正常' : '检查状态获取异常'
                    });
                } catch (error) {
                    results.push({
                        name: '检查状态获取执行',
                        passed: false,
                        message: `检查状态获取异常: ${error.message}`
                    });
                }
            }
            
            return results;
        }

        /**
         * 记录测试套件结果
         */
        recordTestSuiteResults(suiteName, suiteResults) {
            suiteResults.forEach(result => {
                this.recordTestResult(`${suiteName}.${result.name}`, result.passed, result.message);
            });
        }

        /**
         * 记录测试结果
         */
        recordTestResult(testName, passed, message) {
            this.testResults.totalTests++;
            
            if (passed) {
                this.testResults.passedTests++;
                console.log(`✅ ${testName}: ${message}`);
            } else {
                this.testResults.failedTests++;
                console.error(`❌ ${testName}: ${message}`);
            }
            
            this.testResults.testDetails.push({
                name: testName,
                passed,
                message,
                timestamp: new Date().toISOString()
            });
        }

        /**
         * 计算测试结果
         */
        calculateTestResults() {
            const { totalTests, passedTests, failedTests } = this.testResults;
            this.testResults.successRate = totalTests > 0 ? (passedTests / totalTests * 100).toFixed(2) : 0;
            this.testResults.failureRate = totalTests > 0 ? (failedTests / totalTests * 100).toFixed(2) : 0;
        }

        /**
         * 生成测试报告
         */
        generateTestReport() {
            const report = {
                summary: {
                    totalTests: this.testResults.totalTests,
                    passedTests: this.testResults.passedTests,
                    failedTests: this.testResults.failedTests,
                    skippedTests: this.testResults.skippedTests,
                    successRate: `${this.testResults.successRate}%`,
                    failureRate: `${this.testResults.failureRate}%`,
                    duration: `${this.testResults.duration}ms`,
                    startTime: this.testResults.startTime,
                    endTime: this.testResults.endTime
                },
                testSuites: this.groupTestResultsBySuite(),
                failedTests: this.testResults.testDetails.filter(test => !test.passed),
                recommendations: this.generateRecommendations()
            };
            
            return report;
        }

        /**
         * 按测试套件分组结果
         */
        groupTestResultsBySuite() {
            const suites = {};
            
            this.testResults.testDetails.forEach(test => {
                const suiteName = test.name.split('.')[0];
                if (!suites[suiteName]) {
                    suites[suiteName] = {
                        name: suiteName,
                        tests: [],
                        passed: 0,
                        failed: 0,
                        total: 0
                    };
                }
                
                suites[suiteName].tests.push(test);
                suites[suiteName].total++;
                
                if (test.passed) {
                    suites[suiteName].passed++;
                } else {
                    suites[suiteName].failed++;
                }
            });
            
            return Object.values(suites);
        }

        /**
         * 生成改进建议
         */
        generateRecommendations() {
            const recommendations = [];
            const failedTests = this.testResults.testDetails.filter(test => !test.passed);
            
            if (failedTests.length === 0) {
                recommendations.push('🎉 所有测试通过！系统架构修复成功。');
                recommendations.push('💡 建议定期运行集成测试以确保系统健康。');
            } else {
                recommendations.push(`⚠️ 发现 ${failedTests.length} 个失败测试，需要进一步修复。`);
                
                // 分析失败模式
                const failurePatterns = this.analyzeFailurePatterns(failedTests);
                failurePatterns.forEach(pattern => {
                    recommendations.push(`🔧 ${pattern}`);
                });
            }
            
            return recommendations;
        }

        /**
         * 分析失败模式
         */
        analyzeFailurePatterns(failedTests) {
            const patterns = [];
            const failureBySuite = {};
            
            failedTests.forEach(test => {
                const suiteName = test.name.split('.')[0];
                failureBySuite[suiteName] = (failureBySuite[suiteName] || 0) + 1;
            });
            
            Object.entries(failureBySuite).forEach(([suite, count]) => {
                if (count > 1) {
                    patterns.push(`${suite} 套件有多个失败测试，需要重点关注`);
                }
            });
            
            return patterns;
        }

        /**
         * 获取测试状态
         */
        getTestStatus() {
            return {
                isRunning: false,
                lastRun: this.testResults.endTime,
                results: this.testResults
            };
        }
    }

    // 创建全局唯一的系统集成测试实例
    const systemIntegrationTest = new SystemIntegrationTest();

    // 暴露到OTA命名空间
    window.OTA.tests.systemIntegrationTest = systemIntegrationTest;

    // 提供全局命令
    window.runSystemIntegrationTest = async () => {
        console.group('🧪 系统集成测试');
        const report = await systemIntegrationTest.runAllTests();
        console.log('📊 测试报告:', report);
        console.groupEnd();
        return report;
    };

    window.getSystemTestStatus = () => {
        return systemIntegrationTest.getTestStatus();
    };

    console.log('✅ 系统集成测试模块已加载');

})();
