/**
 * @OTA_CORE DOM操作助手
 * 🏷️ 标签: @DOM_HELPER @UTILITY_FUNCTIONS
 * 📝 说明: 提供便捷的DOM操作API，集成优化引擎功能
 * ⚠️ 警告: DOM工具函数，请勿重复开发
 */

(function() {
    'use strict';

    // 延迟获取依赖，确保加载顺序
    function getDOMOptimizationEngine() {
        return window.OTA?.domOptimizationEngine || window.getDOMOptimizationEngine?.();
    }

    /**
     * @OTA_CORE DOM操作助手类
     * 提供高级DOM操作功能
     */
    class DOMHelper {
        constructor() {
            this.logger = window.OTA.getService('logger');
            this.optimizationEngine = getDOMOptimizationEngine();
            
            // 事件监听器缓存
            this.eventListeners = new Map();
            
            // 元素观察器
            this.observers = new Map();
            
            this.logger.log('✅ DOM操作助手已初始化', 'info');
        }

        /**
         * 优化的元素查找
         * @param {string} selector - CSS选择器
         * @param {Element} context - 查找上下文
         * @returns {Element|null} 找到的元素
         */
        $(selector, context = document) {
            if (this.optimizationEngine) {
                return this.optimizationEngine.querySelector(selector, context);
            }
            return context.querySelector(selector);
        }

        /**
         * 优化的多元素查找
         * @param {string} selector - CSS选择器
         * @param {Element} context - 查找上下文
         * @returns {NodeList} 找到的元素列表
         */
        $$(selector, context = document) {
            if (this.optimizationEngine) {
                return this.optimizationEngine.querySelectorAll(selector, context);
            }
            return context.querySelectorAll(selector);
        }

        /**
         * 批量创建元素
         * @param {Array} elementConfigs - 元素配置数组
         * @returns {DocumentFragment} 文档片段
         */
        createElements(elementConfigs) {
            const fragment = document.createDocumentFragment();
            
            elementConfigs.forEach(config => {
                const element = this.createElement(config);
                if (element) {
                    fragment.appendChild(element);
                }
            });
            
            return fragment;
        }

        /**
         * 创建单个元素
         * @param {Object} config - 元素配置
         * @returns {Element} 创建的元素
         */
        createElement(config) {
            const {
                tag = 'div',
                id,
                className,
                attributes = {},
                styles = {},
                textContent,
                innerHTML,
                children = [],
                events = {}
            } = config;

            const element = document.createElement(tag);

            // 设置ID
            if (id) element.id = id;

            // 设置类名
            if (className) {
                if (Array.isArray(className)) {
                    element.classList.add(...className);
                } else {
                    element.className = className;
                }
            }

            // 设置属性
            Object.entries(attributes).forEach(([key, value]) => {
                element.setAttribute(key, value);
            });

            // 设置样式
            Object.entries(styles).forEach(([key, value]) => {
                element.style[key] = value;
            });

            // 设置内容
            if (textContent !== undefined) {
                element.textContent = textContent;
            } else if (innerHTML !== undefined) {
                element.innerHTML = innerHTML;
            }

            // 添加子元素
            children.forEach(childConfig => {
                const child = this.createElement(childConfig);
                element.appendChild(child);
            });

            // 绑定事件
            Object.entries(events).forEach(([eventType, handler]) => {
                this.addEventListener(element, eventType, handler);
            });

            return element;
        }

        /**
         * 智能更新元素内容
         * @param {Element|string} target - 目标元素或选择器
         * @param {string} content - 新内容
         * @param {Object} options - 选项
         */
        updateContent(target, content, options = {}) {
            const element = typeof target === 'string' ? this.$(target) : target;
            
            if (!element) {
                this.logger.log(`更新内容失败: 元素未找到 ${target}`, 'warning');
                return;
            }

            if (this.optimizationEngine) {
                this.optimizationEngine.smartUpdate(element, content, options);
            } else {
                const updateType = options.updateType || 'innerHTML';
                element[updateType] = content;
            }
        }

        /**
         * 批量更新多个元素
         * @param {Array} updates - 更新配置数组
         * @returns {Promise} 更新完成的Promise
         */
        batchUpdate(updates) {
            const updateFunction = () => {
                updates.forEach(({ target, content, options = {} }) => {
                    this.updateContent(target, content, options);
                });
            };

            if (this.optimizationEngine) {
                return this.optimizationEngine.batchUpdate(updateFunction);
            } else {
                updateFunction();
                return Promise.resolve();
            }
        }

        /**
         * 优化的事件监听器绑定
         * @param {Element|string} target - 目标元素或选择器
         * @param {string} eventType - 事件类型
         * @param {Function} handler - 事件处理函数
         * @param {Object} options - 选项
         */
        addEventListener(target, eventType, handler, options = {}) {
            const element = typeof target === 'string' ? this.$(target) : target;
            
            if (!element) {
                this.logger.log(`绑定事件失败: 元素未找到 ${target}`, 'warning');
                return;
            }

            // 防止重复绑定
            const eventKey = `${element.id || 'unknown'}:${eventType}`;
            if (this.eventListeners.has(eventKey) && !options.allowMultiple) {
                this.logger.log(`事件已绑定，跳过重复绑定: ${eventKey}`, 'debug');
                return;
            }

            // 包装处理函数以添加错误处理
            const wrappedHandler = (event) => {
                try {
                    handler(event);
                } catch (error) {
                    this.logger.logError(`事件处理函数执行失败: ${eventType}`, error);
                }
            };

            element.addEventListener(eventType, wrappedHandler, options);
            
            // 记录事件监听器
            this.eventListeners.set(eventKey, {
                element,
                eventType,
                handler: wrappedHandler,
                originalHandler: handler,
                options
            });
        }

        /**
         * 移除事件监听器
         * @param {Element|string} target - 目标元素或选择器
         * @param {string} eventType - 事件类型
         */
        removeEventListener(target, eventType) {
            const element = typeof target === 'string' ? this.$(target) : target;
            
            if (!element) {
                return;
            }

            const eventKey = `${element.id || 'unknown'}:${eventType}`;
            const listener = this.eventListeners.get(eventKey);
            
            if (listener) {
                element.removeEventListener(eventType, listener.handler, listener.options);
                this.eventListeners.delete(eventKey);
            }
        }

        /**
         * 显示/隐藏元素
         * @param {Element|string} target - 目标元素或选择器
         * @param {boolean} show - 是否显示
         * @param {Object} options - 选项
         */
        toggle(target, show, options = {}) {
            const element = typeof target === 'string' ? this.$(target) : target;
            
            if (!element) {
                return;
            }

            const { 
                useTransition = false, 
                transitionDuration = 300,
                displayValue = 'block'
            } = options;

            if (useTransition) {
                if (show) {
                    element.style.display = displayValue;
                    element.style.opacity = '0';
                    element.style.transition = `opacity ${transitionDuration}ms ease`;
                    
                    requestAnimationFrame(() => {
                        element.style.opacity = '1';
                    });
                } else {
                    element.style.transition = `opacity ${transitionDuration}ms ease`;
                    element.style.opacity = '0';
                    
                    setTimeout(() => {
                        element.style.display = 'none';
                    }, transitionDuration);
                }
            } else {
                element.style.display = show ? displayValue : 'none';
            }
        }

        /**
         * 添加CSS类
         * @param {Element|string} target - 目标元素或选择器
         * @param {string|Array} classes - CSS类名
         */
        addClass(target, classes) {
            const element = typeof target === 'string' ? this.$(target) : target;
            
            if (!element) {
                return;
            }

            if (Array.isArray(classes)) {
                element.classList.add(...classes);
            } else {
                element.classList.add(classes);
            }
        }

        /**
         * 移除CSS类
         * @param {Element|string} target - 目标元素或选择器
         * @param {string|Array} classes - CSS类名
         */
        removeClass(target, classes) {
            const element = typeof target === 'string' ? this.$(target) : target;
            
            if (!element) {
                return;
            }

            if (Array.isArray(classes)) {
                element.classList.remove(...classes);
            } else {
                element.classList.remove(classes);
            }
        }

        /**
         * 切换CSS类
         * @param {Element|string} target - 目标元素或选择器
         * @param {string} className - CSS类名
         * @param {boolean} force - 强制添加/移除
         */
        toggleClass(target, className, force) {
            const element = typeof target === 'string' ? this.$(target) : target;
            
            if (!element) {
                return;
            }

            return element.classList.toggle(className, force);
        }

        /**
         * 设置元素样式
         * @param {Element|string} target - 目标元素或选择器
         * @param {Object} styles - 样式对象
         */
        setStyles(target, styles) {
            const element = typeof target === 'string' ? this.$(target) : target;
            
            if (!element) {
                return;
            }

            Object.entries(styles).forEach(([property, value]) => {
                element.style[property] = value;
            });
        }

        /**
         * 获取元素尺寸和位置
         * @param {Element|string} target - 目标元素或选择器
         * @returns {Object} 尺寸和位置信息
         */
        getBounds(target) {
            const element = typeof target === 'string' ? this.$(target) : target;
            
            if (!element) {
                return null;
            }

            const rect = element.getBoundingClientRect();
            const computedStyle = window.getComputedStyle(element);
            
            return {
                x: rect.x,
                y: rect.y,
                width: rect.width,
                height: rect.height,
                top: rect.top,
                right: rect.right,
                bottom: rect.bottom,
                left: rect.left,
                marginTop: parseFloat(computedStyle.marginTop),
                marginRight: parseFloat(computedStyle.marginRight),
                marginBottom: parseFloat(computedStyle.marginBottom),
                marginLeft: parseFloat(computedStyle.marginLeft),
                paddingTop: parseFloat(computedStyle.paddingTop),
                paddingRight: parseFloat(computedStyle.paddingRight),
                paddingBottom: parseFloat(computedStyle.paddingBottom),
                paddingLeft: parseFloat(computedStyle.paddingLeft)
            };
        }

        /**
         * 观察元素变化
         * @param {Element|string} target - 目标元素或选择器
         * @param {Function} callback - 回调函数
         * @param {Object} options - 观察选项
         */
        observe(target, callback, options = {}) {
            const element = typeof target === 'string' ? this.$(target) : target;
            
            if (!element || !window.MutationObserver) {
                return null;
            }

            const observer = new MutationObserver(callback);
            const observeOptions = {
                childList: true,
                attributes: true,
                subtree: true,
                ...options
            };

            observer.observe(element, observeOptions);
            
            const observerId = `${element.id || 'unknown'}:${Date.now()}`;
            this.observers.set(observerId, observer);
            
            return observerId;
        }

        /**
         * 停止观察元素
         * @param {string} observerId - 观察器ID
         */
        unobserve(observerId) {
            const observer = this.observers.get(observerId);
            if (observer) {
                observer.disconnect();
                this.observers.delete(observerId);
            }
        }

        /**
         * 清理所有事件监听器和观察器
         */
        cleanup() {
            // 清理事件监听器
            for (const [eventKey, listener] of this.eventListeners.entries()) {
                listener.element.removeEventListener(
                    listener.eventType, 
                    listener.handler, 
                    listener.options
                );
            }
            this.eventListeners.clear();

            // 清理观察器
            for (const observer of this.observers.values()) {
                observer.disconnect();
            }
            this.observers.clear();

            this.logger.log('DOM助手清理完成', 'info');
        }

        /**
         * 获取助手状态
         * @returns {Object} 状态信息
         */
        getStatus() {
            return {
                eventListeners: this.eventListeners.size,
                observers: this.observers.size,
                optimizationEngineAvailable: !!this.optimizationEngine
            };
        }
    }

    // 创建全局实例
    const domHelper = new DOMHelper();

    // 导出到全局作用域
    window.OTA = window.OTA || {};
    window.OTA.domHelper = domHelper;
    window.OTA.getDOMHelper = () => domHelper;

    // 向后兼容
    window.getDOMHelper = () => domHelper;

    // 便捷的全局函数
    window.$ = (selector, context) => domHelper.$(selector, context);
    window.$$ = (selector, context) => domHelper.$$(selector, context);

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('domHelper', domHelper, '@OTA_DOM_HELPER');
        window.OTA.Registry.registerFactory('getDOMHelper', () => domHelper, '@OTA_DOM_HELPER_FACTORY');
    }

})();
