/**
 * @PROCESSOR Fliggy飞猪专用处理器
 * 🏷️ 标签: @FLIGGY_PROCESSOR
 * 📝 说明: 专门处理飞猪旅行订单的智能处理器，包含飞猪特定的参考号识别、字段映射和预设值应用
 * 🎯 功能: 飞猪订单解析、参考号识别、字段映射、预设值应用、数据验证
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.processors = window.OTA.gemini.processors || {};

(function() {
    'use strict';

    /**
     * Fliggy飞猪专用处理器类
     * 继承自BaseProcessor，实现飞猪订单的专业化处理
     */
    class FliggyProcessor {
        constructor() {
            this.logger = window.getLogger?.() || console;
            this.processorName = 'FliggyProcessor';
            this.version = '1.0.0';
            this.platform = 'Fliggy';
            this.platformDisplayName = '飞猪旅行';
            
            // 飞猪特定配置
            this.fliggyConfig = {
                // 参考号模式
                referencePatterns: [
                    /FLY[A-Z0-9]{8,15}/gi,           // FLY开头的订单号
                    /TB[A-Z0-9]{10,18}/gi,           // TB开头的淘宝订单号
                    /\d{15,20}/g,                    // 长数字订单号
                    /[A-Z]{2}\d{12,16}/gi,           // 两字母+数字组合
                    /飞猪订单[：:]\s*([A-Z0-9]{8,20})/gi, // 中文标识的订单号
                    /订单号[：:]\s*([A-Z0-9]{8,20})/gi   // 通用订单号标识
                ],
                
                // 字段映射规则
                fieldMappings: {
                    // 基础信息映射
                    'pickup_location': [
                        '接机地点', '出发地', '起点', '接送地点', '上车地点',
                        'pickup', 'from', 'departure', 'origin'
                    ],
                    'dropoff_location': [
                        '送达地点', '目的地', '终点', '下车地点', '到达地',
                        'dropoff', 'to', 'destination', 'arrival'
                    ],
                    'passenger_name': [
                        '乘客姓名', '旅客姓名', '联系人', '姓名', '乘客',
                        'passenger', 'guest', 'name', 'traveler'
                    ],
                    'contact_number': [
                        '联系电话', '手机号', '电话', '联系方式', '手机',
                        'phone', 'mobile', 'contact', 'tel', 'number'
                    ],
                    'pickup_date': [
                        '接机日期', '出发日期', '用车日期', '服务日期', '日期',
                        'date', 'pickup_date', 'service_date', 'departure_date'
                    ],
                    'pickup_time': [
                        '接机时间', '出发时间', '用车时间', '服务时间', '时间',
                        'time', 'pickup_time', 'service_time', 'departure_time'
                    ],
                    'flight_number': [
                        '航班号', '班次', '航班', 'flight', 'flight_number', 'flight_no'
                    ],
                    'luggage_count': [
                        '行李数量', '行李件数', '行李', '箱数', 'luggage', 'bags', 'baggage'
                    ],
                    'passenger_count': [
                        '乘客人数', '人数', '旅客数', '乘客数', 'passengers', 'pax', 'people'
                    ],
                    'special_requirements': [
                        '特殊要求', '备注', '说明', '要求', '特别说明',
                        'requirements', 'notes', 'remarks', 'special', 'memo'
                    ]
                },
                
                // 预设值配置
                presetValues: {
                    car_type_id: 1,                    // 默认舒适型5座
                    service_type_id: 2,                // 默认接机服务
                    languages_id_array: {"0": "4"},    // 默认中文
                    luggage_number: 2,                 // 默认2件行李
                    is_return: false,                  // 默认单程
                    pickup_sign: false,                // 默认不举牌
                    currency: 'CNY',                   // 默认人民币
                    payment_method: 'online'           // 默认在线支付
                },
                
                // 数据验证规则
                validationRules: {
                    required_fields: [
                        'ota_reference_number', 'pickup_location', 'dropoff_location',
                        'passenger_name', 'contact_number', 'pickup_date', 'pickup_time'
                    ],
                    field_formats: {
                        contact_number: /^1[3-9]\d{9}$|^\+86[1-9]\d{10}$|^\d{3,4}-\d{7,8}$/,
                        pickup_date: /^\d{4}-\d{2}-\d{2}$|^\d{2}\/\d{2}\/\d{4}$|^\d{2}-\d{2}-\d{4}$/,
                        pickup_time: /^([01]?\d|2[0-3]):[0-5]\d$|^([01]?\d|2[0-3])[0-5]\d$/,
                        flight_number: /^[A-Z]{2}\d{3,4}$|^[A-Z]\d{4,5}$/i
                    }
                },
                
                // 智能识别关键词
                identificationKeywords: [
                    '飞猪', 'fliggy', '淘宝旅行', 'taobao travel',
                    '阿里旅行', 'alitrip', '天猫旅行', 'tmall travel'
                ],
                
                // 服务类型识别
                serviceTypeKeywords: {
                    2: ['接机', '机场接', '到达接', 'pickup', 'arrival'],
                    3: ['送机', '机场送', '出发送', 'dropoff', 'departure'],
                    4: ['包车', '租车', '专车', 'charter', 'rental', 'private car']
                },
                
                // 车型识别关键词
                carTypeKeywords: {
                    1: ['舒适', '经济', '标准', '5座', 'comfort', 'economy', 'standard'],
                    2: ['商务', '7座', '8座', 'business', 'mpv'],
                    3: ['豪华', '奔驰', '宝马', '奥迪', 'luxury', 'premium'],
                    4: ['面包车', '9座', '12座', 'van', 'minibus']
                }
            };
            
            // 处理统计
            this.stats = {
                totalProcessed: 0,
                successfulProcessed: 0,
                failedProcessed: 0,
                averageProcessingTime: 0,
                referenceNumberMatches: 0,
                fieldExtractionSuccess: 0,
                validationErrors: 0
            };
            
            // 初始化处理器
            this.initialize();
        }

        /**
         * 初始化处理器
         */
        initialize() {
            this.logger.log(`${this.platformDisplayName}处理器初始化开始`, 'info');
            
            // 获取基础处理器
            this.baseProcessor = window.OTA?.gemini?.core?.getBaseProcessor?.();
            
            // 获取配置管理器
            this.configManager = window.OTA?.gemini?.core?.getConfigManager?.();
            
            this.logger.log(`${this.platformDisplayName}处理器初始化完成`, 'info');
        }

        /**
         * 处理订单 - 主要入口方法
         * @param {string} orderText - 订单文本
         * @param {Object} options - 处理选项
         * @returns {Promise<Object>} 处理结果
         */
        async processOrder(orderText, options = {}) {
            const startTime = Date.now();
            this.stats.totalProcessed++;
            
            try {
                this.logger.log(`开始处理${this.platformDisplayName}订单`, 'info');
                
                // 1. 预处理订单文本
                const preprocessedText = this.preprocessOrderText(orderText);
                
                // 2. 识别参考号
                const referenceNumber = this.extractReferenceNumber(preprocessedText);
                
                // 3. 提取基础字段
                const extractedFields = this.extractFields(preprocessedText);
                
                // 4. 智能字段映射
                const mappedFields = this.mapFields(extractedFields);
                
                // 5. 应用预设值
                const fieldsWithPresets = this.applyPresetValues(mappedFields);
                
                // 6. 数据验证和清理
                const validatedFields = this.validateAndCleanFields(fieldsWithPresets);
                
                // 7. 生成最终结果
                const result = this.generateResult(validatedFields, referenceNumber, options);
                
                // 8. 更新统计信息
                this.updateStats(startTime, true);
                
                this.logger.log(`${this.platformDisplayName}订单处理完成`, 'info');
                
                return result;
                
            } catch (error) {
                this.stats.failedProcessed++;
                this.logger.logError(`${this.platformDisplayName}订单处理失败`, error);
                
                // 返回降级结果
                return this.generateFallbackResult(orderText, options, error);
            }
        }

        /**
         * 预处理订单文本
         * @param {string} orderText - 原始订单文本
         * @returns {string} 预处理后的文本
         */
        preprocessOrderText(orderText) {
            if (!orderText || typeof orderText !== 'string') {
                throw new Error('订单文本必须是非空字符串');
            }
            
            // 统一换行符
            let processed = orderText.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
            
            // 清理多余空白
            processed = processed.replace(/\s+/g, ' ').trim();
            
            // 标准化中文标点
            processed = processed.replace(/：/g, ':').replace(/，/g, ',');
            
            return processed;
        }

        /**
         * 提取参考号
         * @param {string} orderText - 订单文本
         * @returns {string|null} 参考号
         */
        extractReferenceNumber(orderText) {
            for (const pattern of this.fliggyConfig.referencePatterns) {
                const matches = orderText.match(pattern);
                if (matches && matches.length > 0) {
                    this.stats.referenceNumberMatches++;
                    
                    // 如果是带中文标识的模式，提取括号内容
                    if (pattern.source.includes('订单')) {
                        const match = pattern.exec(orderText);
                        return match ? match[1] : matches[0];
                    }
                    
                    return matches[0];
                }
            }
            
            // 使用通用模式作为后备
            const genericPatterns = [
                /[A-Z0-9]{8,20}/g,
                /\d{8,15}/g
            ];
            
            for (const pattern of genericPatterns) {
                const matches = orderText.match(pattern);
                if (matches && matches.length > 0) {
                    return matches[0];
                }
            }
            
            return null;
        }

        /**
         * 提取字段
         * @param {string} orderText - 订单文本
         * @returns {Object} 提取的字段
         */
        extractFields(orderText) {
            const fields = {};
            const lines = orderText.split('\n');
            
            for (const line of lines) {
                const trimmedLine = line.trim();
                if (!trimmedLine) continue;
                
                // 尝试匹配键值对模式
                const keyValueMatch = trimmedLine.match(/^(.+?)[：:]\s*(.+)$/);
                if (keyValueMatch) {
                    const key = keyValueMatch[1].trim();
                    const value = keyValueMatch[2].trim();
                    
                    if (value && value !== '-' && value !== 'N/A') {
                        fields[key] = value;
                    }
                }
                
                // 尝试匹配特定模式
                this.extractSpecificPatterns(trimmedLine, fields);
            }
            
            return fields;
        }

        /**
         * 提取特定模式
         * @param {string} line - 文本行
         * @param {Object} fields - 字段对象
         */
        extractSpecificPatterns(line, fields) {
            // 提取航班号
            const flightMatch = line.match(/([A-Z]{2}\d{3,4})/i);
            if (flightMatch && !fields.flight_number) {
                fields.flight_number = flightMatch[1].toUpperCase();
            }
            
            // 提取电话号码
            const phoneMatch = line.match(/(1[3-9]\d{9}|\+86[1-9]\d{10}|\d{3,4}-\d{7,8})/);
            if (phoneMatch && !fields.contact_number) {
                fields.contact_number = phoneMatch[1];
            }
            
            // 提取日期
            const dateMatch = line.match(/(\d{4}[-\/]\d{1,2}[-\/]\d{1,2}|\d{1,2}[-\/]\d{1,2}[-\/]\d{4})/);
            if (dateMatch && !fields.pickup_date) {
                fields.pickup_date = this.standardizeDate(dateMatch[1]);
            }
            
            // 提取时间
            const timeMatch = line.match(/([01]?\d|2[0-3])[:\s]?([0-5]\d)/);
            if (timeMatch && !fields.pickup_time) {
                fields.pickup_time = `${timeMatch[1].padStart(2, '0')}:${timeMatch[2]}`;
            }
            
            // 提取人数
            const passengerMatch = line.match(/(\d+)\s*[人位座]/);
            if (passengerMatch && !fields.passenger_count) {
                fields.passenger_count = parseInt(passengerMatch[1]);
            }
        }

        /**
         * 映射字段
         * @param {Object} extractedFields - 提取的字段
         * @returns {Object} 映射后的字段
         */
        mapFields(extractedFields) {
            const mappedFields = {};
            
            for (const [standardField, aliases] of Object.entries(this.fliggyConfig.fieldMappings)) {
                for (const [extractedKey, extractedValue] of Object.entries(extractedFields)) {
                    // 检查是否匹配任何别名
                    const isMatch = aliases.some(alias => 
                        extractedKey.toLowerCase().includes(alias.toLowerCase()) ||
                        alias.toLowerCase().includes(extractedKey.toLowerCase())
                    );
                    
                    if (isMatch && !mappedFields[standardField]) {
                        mappedFields[standardField] = extractedValue;
                        break;
                    }
                }
            }
            
            // 智能服务类型识别
            mappedFields.service_type_id = this.identifyServiceType(extractedFields);
            
            // 智能车型识别
            mappedFields.car_type_id = this.identifyCarType(extractedFields);
            
            this.stats.fieldExtractionSuccess++;
            return mappedFields;
        }

        /**
         * 识别服务类型
         * @param {Object} fields - 字段对象
         * @returns {number} 服务类型ID
         */
        identifyServiceType(fields) {
            const fullText = Object.values(fields).join(' ').toLowerCase();
            
            for (const [typeId, keywords] of Object.entries(this.fliggyConfig.serviceTypeKeywords)) {
                if (keywords.some(keyword => fullText.includes(keyword.toLowerCase()))) {
                    return parseInt(typeId);
                }
            }
            
            return this.fliggyConfig.presetValues.service_type_id; // 默认接机
        }

        /**
         * 识别车型
         * @param {Object} fields - 字段对象
         * @returns {number} 车型ID
         */
        identifyCarType(fields) {
            const fullText = Object.values(fields).join(' ').toLowerCase();
            const passengerCount = fields.passenger_count || 0;
            
            // 根据人数判断
            if (passengerCount > 7) {
                return 4; // 面包车
            } else if (passengerCount > 5) {
                return 2; // 商务车
            }
            
            // 根据关键词判断
            for (const [typeId, keywords] of Object.entries(this.fliggyConfig.carTypeKeywords)) {
                if (keywords.some(keyword => fullText.includes(keyword.toLowerCase()))) {
                    return parseInt(typeId);
                }
            }
            
            return this.fliggyConfig.presetValues.car_type_id; // 默认舒适型
        }

        /**
         * 应用预设值
         * @param {Object} mappedFields - 映射后的字段
         * @returns {Object} 应用预设值后的字段
         */
        applyPresetValues(mappedFields) {
            const fieldsWithPresets = { ...this.fliggyConfig.presetValues, ...mappedFields };
            
            // 特殊处理逻辑
            if (!fieldsWithPresets.ota_reference_number) {
                fieldsWithPresets.ota_reference_number = 'FLIGGY_' + Date.now();
            }
            
            // 根据服务类型调整预设值
            if (fieldsWithPresets.service_type_id === 4) { // 包车服务
                fieldsWithPresets.is_return = false;
                fieldsWithPresets.pickup_sign = false;
            }
            
            return fieldsWithPresets;
        }

        /**
         * 验证和清理字段
         * @param {Object} fields - 字段对象
         * @returns {Object} 验证后的字段
         */
        validateAndCleanFields(fields) {
            const validatedFields = { ...fields };
            const errors = [];
            
            // 验证必填字段
            for (const requiredField of this.fliggyConfig.validationRules.required_fields) {
                if (!validatedFields[requiredField] || validatedFields[requiredField].toString().trim() === '') {
                    errors.push(`缺少必填字段: ${requiredField}`);
                }
            }
            
            // 验证字段格式
            for (const [field, pattern] of Object.entries(this.fliggyConfig.validationRules.field_formats)) {
                if (validatedFields[field] && !pattern.test(validatedFields[field])) {
                    errors.push(`字段格式错误: ${field} = ${validatedFields[field]}`);
                }
            }
            
            // 数据清理
            if (validatedFields.pickup_date) {
                validatedFields.pickup_date = this.standardizeDate(validatedFields.pickup_date);
            }
            
            if (validatedFields.pickup_time) {
                validatedFields.pickup_time = this.standardizeTime(validatedFields.pickup_time);
            }
            
            if (validatedFields.contact_number) {
                validatedFields.contact_number = this.standardizePhoneNumber(validatedFields.contact_number);
            }
            
            if (errors.length > 0) {
                this.stats.validationErrors++;
                this.logger.logWarning(`${this.platformDisplayName}订单验证警告`, errors);
            }
            
            return validatedFields;
        }

        /**
         * 标准化日期格式
         * @param {string} dateStr - 日期字符串
         * @returns {string} 标准化后的日期
         */
        standardizeDate(dateStr) {
            if (!dateStr) return '';
            
            // 尝试解析各种日期格式
            const formats = [
                /^(\d{4})[-\/](\d{1,2})[-\/](\d{1,2})$/,  // YYYY-MM-DD 或 YYYY/MM/DD
                /^(\d{1,2})[-\/](\d{1,2})[-\/](\d{4})$/   // DD-MM-YYYY 或 DD/MM/YYYY
            ];
            
            for (const format of formats) {
                const match = dateStr.match(format);
                if (match) {
                    if (match[1].length === 4) {
                        // YYYY-MM-DD 格式
                        return `${match[1]}-${match[2].padStart(2, '0')}-${match[3].padStart(2, '0')}`;
                    } else {
                        // DD-MM-YYYY 格式
                        return `${match[3]}-${match[2].padStart(2, '0')}-${match[1].padStart(2, '0')}`;
                    }
                }
            }
            
            return dateStr; // 无法解析时返回原值
        }

        /**
         * 标准化时间格式
         * @param {string} timeStr - 时间字符串
         * @returns {string} 标准化后的时间
         */
        standardizeTime(timeStr) {
            if (!timeStr) return '';
            
            const timeMatch = timeStr.match(/([01]?\d|2[0-3])[:\s]?([0-5]\d)/);
            if (timeMatch) {
                return `${timeMatch[1].padStart(2, '0')}:${timeMatch[2]}`;
            }
            
            return timeStr;
        }

        /**
         * 标准化电话号码
         * @param {string} phoneStr - 电话号码字符串
         * @returns {string} 标准化后的电话号码
         */
        standardizePhoneNumber(phoneStr) {
            if (!phoneStr) return '';
            
            // 移除所有非数字和+号
            let cleaned = phoneStr.replace(/[^\d+]/g, '');
            
            // 处理中国手机号
            if (cleaned.match(/^1[3-9]\d{9}$/)) {
                return cleaned;
            }
            
            // 处理带国际区号的号码
            if (cleaned.startsWith('+86')) {
                return cleaned;
            }
            
            return phoneStr; // 无法标准化时返回原值
        }

        /**
         * 生成处理结果
         * @param {Object} fields - 处理后的字段
         * @param {string} referenceNumber - 参考号
         * @param {Object} options - 选项
         * @returns {Object} 处理结果
         */
        generateResult(fields, referenceNumber, options) {
            const result = {
                success: true,
                processor: this.processorName,
                platform: this.platform,
                version: this.version,
                confidence: this.calculateConfidence(fields, referenceNumber),
                data: {
                    ...fields,
                    ota_reference_number: referenceNumber || fields.ota_reference_number,
                    ota: this.platform.toLowerCase(),
                    original_platform: this.platformDisplayName
                },
                metadata: {
                    processingTime: Date.now(),
                    extractedFields: Object.keys(fields).length,
                    validationPassed: true,
                    processorSpecific: {
                        referencePatternMatched: !!referenceNumber,
                        serviceTypeDetected: !!fields.service_type_id,
                        carTypeDetected: !!fields.car_type_id
                    }
                }
            };
            
            // 添加测试模式标记
            if (options.test) {
                result.metadata.testMode = true;
            }
            
            return result;
        }

        /**
         * 计算置信度
         * @param {Object} fields - 字段对象
         * @param {string} referenceNumber - 参考号
         * @returns {number} 置信度分数 (0-1)
         */
        calculateConfidence(fields, referenceNumber) {
            let confidence = 0.5; // 基础置信度
            
            // 参考号匹配加分
            if (referenceNumber) {
                confidence += 0.2;
            }
            
            // 必填字段完整性加分
            const requiredFields = this.fliggyConfig.validationRules.required_fields;
            const presentFields = requiredFields.filter(field => fields[field]);
            confidence += (presentFields.length / requiredFields.length) * 0.2;
            
            // 特定字段识别加分
            if (fields.flight_number) confidence += 0.05;
            if (fields.passenger_count) confidence += 0.05;
            
            return Math.min(confidence, 1.0);
        }

        /**
         * 生成降级结果
         * @param {string} orderText - 原始订单文本
         * @param {Object} options - 选项
         * @param {Error} error - 错误对象
         * @returns {Object} 降级结果
         */
        generateFallbackResult(orderText, options, error) {
            return {
                success: false,
                processor: this.processorName,
                platform: this.platform,
                version: this.version,
                confidence: 0.1,
                error: error.message,
                data: {
                    ...this.fliggyConfig.presetValues,
                    ota_reference_number: 'FLIGGY_ERROR_' + Date.now(),
                    ota: this.platform.toLowerCase(),
                    original_text: orderText.substring(0, 200) // 限制长度
                },
                metadata: {
                    processingTime: Date.now(),
                    fallback: true,
                    error: {
                        message: error.message,
                        type: error.name
                    }
                }
            };
        }

        /**
         * 更新统计信息
         * @param {number} startTime - 开始时间
         * @param {boolean} success - 是否成功
         */
        updateStats(startTime, success) {
            const processingTime = Date.now() - startTime;
            
            if (success) {
                this.stats.successfulProcessed++;
            }
            
            // 更新平均处理时间
            const totalProcessed = this.stats.totalProcessed;
            this.stats.averageProcessingTime = 
                ((this.stats.averageProcessingTime * (totalProcessed - 1)) + processingTime) / totalProcessed;
        }

        /**
         * 获取处理器统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                ...this.stats,
                successRate: this.stats.totalProcessed > 0 ? 
                    (this.stats.successfulProcessed / this.stats.totalProcessed) : 0,
                platform: this.platform,
                processorName: this.processorName
            };
        }

        /**
         * 重置统计信息
         */
        resetStats() {
            this.stats = {
                totalProcessed: 0,
                successfulProcessed: 0,
                failedProcessed: 0,
                averageProcessingTime: 0,
                referenceNumberMatches: 0,
                fieldExtractionSuccess: 0,
                validationErrors: 0
            };
        }

        /**
         * 获取处理器信息
         * @returns {Object} 处理器信息
         */
        getProcessorInfo() {
            return {
                name: this.processorName,
                platform: this.platform,
                platformDisplayName: this.platformDisplayName,
                version: this.version,
                supportedPatterns: this.fliggyConfig.referencePatterns.length,
                supportedFields: Object.keys(this.fliggyConfig.fieldMappings).length,
                identificationKeywords: this.fliggyConfig.identificationKeywords
            };
        }
    }

    // 创建全局单例实例
    function getFliggyProcessor() {
        if (!window.OTA.gemini.processors.fliggyProcessor) {
            window.OTA.gemini.processors.fliggyProcessor = new FliggyProcessor();
        }
        return window.OTA.gemini.processors.fliggyProcessor;
    }

    // 暴露到全局命名空间
    window.OTA.gemini.processors.FliggyProcessor = FliggyProcessor;
    window.OTA.gemini.processors.getFliggyProcessor = getFliggyProcessor;

    // 向后兼容
    window.getFliggyProcessor = getFliggyProcessor;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('fliggyProcessor', getFliggyProcessor(), '@FLIGGY_PROCESSOR');
        window.OTA.Registry.registerFactory('getFliggyProcessor', getFliggyProcessor, '@FLIGGY_PROCESSOR_FACTORY');
    }

    console.log('✅ Fliggy飞猪专用处理器已加载');

})();
