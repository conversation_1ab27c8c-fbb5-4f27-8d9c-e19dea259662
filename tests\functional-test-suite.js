/**
 * @TEST 功能测试套件
 * 🏷️ 标签: @FUNCTIONAL_TEST_SUITE
 * 📝 说明: 测试重构后Gemini系统的各项核心功能
 * 🎯 功能: 订单解析测试、OTA渠道识别测试、数据处理测试、错误处理测试
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保测试环境
if (typeof window === 'undefined') {
    global.window = {};
}

// 功能测试套件
describe('Gemini系统功能测试', function() {
    let geminiService;
    let coordinator;
    let testData;

    // 测试前置设置
    beforeAll(async function() {
        // 初始化测试数据
        testData = {
            simpleOrder: `
                客户姓名：张三
                联系电话：+60123456789
                接送地点：吉隆坡国际机场
                目的地：双子塔
                航班信息：MH123 15:30抵达
                订单号：ABC123456789
            `,
            multipleOrders: `
                订单1：
                客户：李四
                电话：+60198765432
                接机：KLIA2
                送往：酒店A
                航班：AK456 16:00
                
                ---
                
                订单2：
                客户：王五
                电话：+60187654321
                送机：酒店B
                目的地：KLIA
                航班：MH789 09:30
            `,
            chongDealerOrder: `
                【Chong Dealer订单】
                客户：陈六
                联系：+60176543210
                服务：包车8小时
                出发：酒店C
                行程：市区观光
                参考号：CD2024010001
            `,
            invalidOrder: `
                这是一段无效的订单文本
                没有包含任何有用的信息
                应该被正确处理
            `
        };

        // 获取服务实例
        geminiService = window.OTA?.geminiService;
        coordinator = window.OTA?.gemini?.getGeminiCoordinator?.();
        
        if (!geminiService) {
            throw new Error('Gemini服务未初始化');
        }
    });

    // 基础订单解析功能测试
    describe('基础订单解析功能', function() {
        
        it('应该能够解析简单订单', async function() {
            const result = await geminiService.parseOrder(testData.simpleOrder);
            
            assertTrue(result.success, '解析应该成功');
            assertTrue(result.data, '应该返回解析数据');
            assertEqual(result.data.customer_name, '张三', '客户姓名应该正确');
            assertEqual(result.data.customer_contact, '+60123456789', '联系电话应该正确');
            assertTrue(result.data.flight_info?.includes('MH123'), '航班信息应该包含MH123');
        });

        it('应该能够解析多个订单', async function() {
            const orderSegments = testData.multipleOrders.split('---').map(s => s.trim());
            const result = await geminiService.parseMultipleOrders(orderSegments);
            
            assertTrue(result.success, '多订单解析应该成功');
            assertTrue(Array.isArray(result.data), '应该返回数组');
            assertEqual(result.data.length, 2, '应该解析出2个订单');
            
            // 检查第一个订单
            const order1 = result.data[0];
            assertTrue(order1.customer_name?.includes('李四'), '第一个订单客户名应该正确');
            
            // 检查第二个订单
            const order2 = result.data[1];
            assertTrue(order2.customer_name?.includes('王五'), '第二个订单客户名应该正确');
        });

        it('应该正确处理无效订单', async function() {
            const result = await geminiService.parseOrder(testData.invalidOrder);
            
            // 即使是无效订单，也应该返回结果（可能是降级处理）
            assertTrue(result, '应该返回结果对象');
            assertTrue(typeof result.success === 'boolean', '应该有success字段');
        });
    });

    // OTA渠道识别功能测试
    describe('OTA渠道识别功能', function() {
        
        it('应该能够识别Chong Dealer订单', async function() {
            const result = await geminiService.parseOrder(testData.chongDealerOrder);
            
            assertTrue(result.success, 'Chong Dealer订单解析应该成功');
            
            // 如果有渠道识别功能，检查渠道类型
            if (result.data.ota_channel) {
                assertTrue(result.data.ota_channel.includes('chong'), '应该识别为Chong Dealer渠道');
            }
        });

        it('应该为不同渠道生成正确的参考号', async function() {
            const result = await geminiService.parseOrder(testData.chongDealerOrder);
            
            if (result.data.ota_reference_number) {
                assertTrue(result.data.ota_reference_number.length > 0, '应该生成参考号');
            }
        });
    });

    // 数据处理功能测试
    describe('数据处理功能', function() {
        
        it('应该正确提取航班信息', async function() {
            const result = await geminiService.parseOrder(testData.simpleOrder);
            
            if (result.data.flight_info) {
                assertTrue(result.data.flight_info.includes('MH123'), '应该提取航班号');
            }
            
            if (result.data.flight_time) {
                assertTrue(result.data.flight_time.includes('15:30'), '应该提取航班时间');
            }
        });

        it('应该正确处理联系电话格式', async function() {
            const result = await geminiService.parseOrder(testData.simpleOrder);
            
            if (result.data.customer_contact) {
                assertTrue(result.data.customer_contact.startsWith('+60'), '电话号码应该包含国家代码');
            }
        });

        it('应该正确识别服务类型', async function() {
            const result = await geminiService.parseOrder(testData.simpleOrder);
            
            // 基于关键词应该能识别为接机服务
            if (result.data.service_type_id || result.data.service_type) {
                // 检查是否正确识别为接机服务（ID通常为2）
                assertTrue(true, '服务类型识别功能正常');
            }
        });
    });

    // 错误处理功能测试
    describe('错误处理功能', function() {
        
        it('应该优雅处理空输入', async function() {
            const result = await geminiService.parseOrder('');
            
            assertTrue(result, '应该返回结果对象');
            // 不应该抛出异常
        });

        it('应该优雅处理null输入', async function() {
            const result = await geminiService.parseOrder(null);
            
            assertTrue(result, '应该返回结果对象');
            // 不应该抛出异常
        });

        it('应该处理超长文本输入', async function() {
            const longText = 'A'.repeat(10000); // 10KB文本
            const result = await geminiService.parseOrder(longText);
            
            assertTrue(result, '应该返回结果对象');
            // 不应该抛出异常
        });

        it('应该处理特殊字符输入', async function() {
            const specialText = '🚗🛫✈️🏨📞💰中文English123!@#$%^&*()';
            const result = await geminiService.parseOrder(specialText);
            
            assertTrue(result, '应该返回结果对象');
            // 不应该抛出异常
        });
    });

    // 性能功能测试
    describe('性能功能测试', function() {
        
        it('单次解析应该在合理时间内完成', async function() {
            const startTime = Date.now();
            const result = await geminiService.parseOrder(testData.simpleOrder);
            const duration = Date.now() - startTime;
            
            assertTrue(duration < 30000, `解析时间应该少于30秒，实际：${duration}ms`);
            assertTrue(result, '应该返回结果');
        });

        it('批量解析应该能够处理', async function() {
            const orders = [
                testData.simpleOrder,
                testData.chongDealerOrder,
                testData.invalidOrder
            ];
            
            const startTime = Date.now();
            const results = await Promise.all(
                orders.map(order => geminiService.parseOrder(order))
            );
            const duration = Date.now() - startTime;
            
            assertEqual(results.length, 3, '应该处理所有订单');
            assertTrue(duration < 60000, `批量处理时间应该合理，实际：${duration}ms`);
        });
    });

    // 向后兼容性测试
    describe('向后兼容性测试', function() {
        
        it('全局parseOrderWithGemini函数应该可用', async function() {
            assertTrue(typeof window.parseOrderWithGemini === 'function', 'parseOrderWithGemini应该是函数');
            
            const result = await window.parseOrderWithGemini(testData.simpleOrder);
            assertTrue(result, '应该返回结果');
        });

        it('全局GeminiService对象应该可用', function() {
            assertTrue(typeof window.GeminiService === 'object', 'GeminiService应该是对象');
            assertTrue(typeof window.GeminiService.parseOrder === 'function', 'parseOrder方法应该存在');
        });

        it('原有接口方法应该都存在', function() {
            const requiredMethods = [
                'parseOrder',
                'parseMultipleOrders',
                'analyzeImage',
                'getStatus',
                'configureRealtimeAnalysis',
                'updateIdMappings'
            ];

            for (const method of requiredMethods) {
                assertTrue(
                    typeof geminiService[method] === 'function',
                    `${method}方法应该存在`
                );
            }
        });
    });

    // 配置功能测试
    describe('配置功能测试', function() {
        
        it('应该能够获取服务状态', function() {
            const status = geminiService.getStatus();
            
            assertTrue(status, '应该返回状态对象');
            assertTrue(typeof status.isInitialized === 'boolean', '应该有初始化状态');
        });

        it('应该能够配置实时分析', function() {
            const config = {
                enabled: true,
                minInputLength: 25,
                debounceDelay: 2000
            };
            
            const result = geminiService.configureRealtimeAnalysis(config);
            assertTrue(result, '应该返回配置结果');
        });

        it('应该能够更新ID映射', function() {
            const systemData = {
                backend_users: { '1': 'Test User' },
                service_types: { '2': 'Airport Pickup' },
                car_types: { '1': 'Comfort 5 Seater' },
                languages: { '2': 'English' }
            };
            
            const result = geminiService.updateIdMappings(systemData);
            assertTrue(result, '应该返回更新结果');
        });
    });
});

// 日志记录
const logger = window.getLogger?.() || console;
logger.log('功能测试套件加载完成', 'info', {
    version: '1.0.0',
    testCategories: [
        'basic_parsing',
        'ota_channel_recognition', 
        'data_processing',
        'error_handling',
        'performance',
        'backward_compatibility',
        'configuration'
    ]
});
