/**
 * @OTA_CORE DOM操作优化引擎
 * 🏷️ 标签: @DOM_OPTIMIZATION @PERFORMANCE_ENGINE
 * 📝 说明: 优化频繁的DOM操作，实现批量更新、虚拟DOM缓存和智能重渲染机制
 * ⚠️ 警告: 核心性能优化，请勿重复开发
 */

(function() {
    'use strict';

    // 延迟获取依赖，确保加载顺序
    function getConfigCenter() {
        return window.OTA?.configCenter || window.getConfigCenter?.();
    }

    /**
     * @OTA_CORE DOM操作优化引擎类
     * 提供高性能的DOM操作优化功能
     */
    class DOMOptimizationEngine {
        constructor() {
            this.logger = window.OTA.getService('logger');
            this.configCenter = getConfigCenter();
            
            // DOM元素缓存
            this.elementCache = new Map();
            this.selectorCache = new Map();
            
            // 批量更新队列
            this.updateQueue = [];
            this.isUpdating = false;
            this.updateScheduled = false;
            
            // 虚拟DOM缓存
            this.virtualDOMCache = new Map();
            
            // 性能监控
            this.performanceMetrics = {
                cacheHits: 0,
                cacheMisses: 0,
                batchUpdates: 0,
                virtualDOMHits: 0,
                renderTime: 0,
                lastOptimization: Date.now()
            };
            
            // 配置
            this.config = this.loadConfig();
            
            // 初始化优化引擎
            this.initialize();
            
            this.logger.log('✅ DOM操作优化引擎已初始化', 'info', {
                cacheEnabled: this.config.cacheEnabled,
                batchUpdates: this.config.batchUpdates,
                virtualDOM: this.config.virtualDOM
            });
        }

        /**
         * 加载配置
         * @returns {Object} 配置对象
         */
        loadConfig() {
            const defaultConfig = {
                cacheEnabled: true,
                cacheSize: 1000,
                cacheTTL: 5 * 60 * 1000, // 5分钟
                batchUpdates: true,
                batchDelay: 16, // 60fps
                virtualDOM: true,
                virtualDOMSize: 500,
                performanceMonitoring: true,
                autoCleanup: true,
                cleanupInterval: 2 * 60 * 1000 // 2分钟
            };

            if (this.configCenter) {
                const uiConfig = this.configCenter.getConfig('ui') || {};
                const performanceConfig = this.configCenter.getConfig('performance') || {};
                
                return {
                    ...defaultConfig,
                    ...uiConfig.domOptimization,
                    ...performanceConfig.domOptimization
                };
            }

            return defaultConfig;
        }

        /**
         * 初始化优化引擎
         */
        initialize() {
            // 启动自动清理
            if (this.config.autoCleanup) {
                this.startAutoCleanup();
            }

            // 监听页面可见性变化
            this.setupVisibilityListener();

            // 设置性能监控
            if (this.config.performanceMonitoring) {
                this.setupPerformanceMonitoring();
            }
        }

        /**
         * 优化的元素查找
         * @param {string} selector - CSS选择器
         * @param {Element} context - 查找上下文
         * @returns {Element|null} 找到的元素
         */
        querySelector(selector, context = document) {
            if (!this.config.cacheEnabled) {
                return context.querySelector(selector);
            }

            const cacheKey = `${selector}:${context === document ? 'document' : context.id || 'unknown'}`;
            
            // 检查缓存
            if (this.selectorCache.has(cacheKey)) {
                const cached = this.selectorCache.get(cacheKey);
                if (cached.element && cached.element.parentNode) {
                    this.performanceMetrics.cacheHits++;
                    return cached.element;
                } else {
                    // 元素已被移除，清除缓存
                    this.selectorCache.delete(cacheKey);
                }
            }

            // 查找元素
            const element = context.querySelector(selector);
            
            if (element) {
                // 缓存元素
                this.selectorCache.set(cacheKey, {
                    element,
                    timestamp: Date.now(),
                    selector,
                    context
                });
                
                // 限制缓存大小
                if (this.selectorCache.size > this.config.cacheSize) {
                    this.cleanupSelectorCache();
                }
            }

            this.performanceMetrics.cacheMisses++;
            return element;
        }

        /**
         * 优化的元素查找（多个）
         * @param {string} selector - CSS选择器
         * @param {Element} context - 查找上下文
         * @returns {NodeList} 找到的元素列表
         */
        querySelectorAll(selector, context = document) {
            // 对于querySelectorAll，通常不缓存结果，因为结果可能变化
            return context.querySelectorAll(selector);
        }

        /**
         * 批量DOM更新
         * @param {Function} updateFunction - 更新函数
         * @param {Object} options - 选项
         * @returns {Promise} 更新完成的Promise
         */
        batchUpdate(updateFunction, options = {}) {
            if (!this.config.batchUpdates) {
                // 如果不启用批量更新，直接执行
                return Promise.resolve(updateFunction());
            }

            return new Promise((resolve, reject) => {
                this.updateQueue.push({
                    updateFunction,
                    resolve,
                    reject,
                    options,
                    timestamp: Date.now()
                });

                this.scheduleBatchUpdate();
            });
        }

        /**
         * 调度批量更新
         */
        scheduleBatchUpdate() {
            if (this.updateScheduled) {
                return;
            }

            this.updateScheduled = true;
            
            // 使用requestAnimationFrame确保在下一帧执行
            requestAnimationFrame(() => {
                this.executeBatchUpdate();
            });
        }

        /**
         * 执行批量更新
         */
        executeBatchUpdate() {
            if (this.isUpdating || this.updateQueue.length === 0) {
                this.updateScheduled = false;
                return;
            }

            this.isUpdating = true;
            const startTime = performance.now();

            try {
                // 创建文档片段以减少重排
                const fragment = document.createDocumentFragment();
                const updates = [...this.updateQueue];
                this.updateQueue = [];

                // 执行所有更新
                updates.forEach(({ updateFunction, resolve, reject, options }) => {
                    try {
                        const result = updateFunction(fragment);
                        resolve(result);
                    } catch (error) {
                        this.logger.logError('批量更新执行失败', error);
                        reject(error);
                    }
                });

                // 记录性能指标
                const endTime = performance.now();
                this.performanceMetrics.batchUpdates++;
                this.performanceMetrics.renderTime += (endTime - startTime);

                this.logger.log(`批量更新完成: ${updates.length}个操作, 耗时${(endTime - startTime).toFixed(2)}ms`, 'debug');

            } catch (error) {
                this.logger.logError('批量更新失败', error);
            } finally {
                this.isUpdating = false;
                this.updateScheduled = false;

                // 如果队列中还有更新，继续调度
                if (this.updateQueue.length > 0) {
                    this.scheduleBatchUpdate();
                }
            }
        }

        /**
         * 虚拟DOM缓存
         * @param {string} key - 缓存键
         * @param {Function} renderFunction - 渲染函数
         * @param {any} data - 数据
         * @returns {string} HTML字符串
         */
        virtualRender(key, renderFunction, data) {
            if (!this.config.virtualDOM) {
                return renderFunction(data);
            }

            // 生成数据哈希
            const dataHash = this.generateDataHash(data);
            const cacheKey = `${key}:${dataHash}`;

            // 检查虚拟DOM缓存
            if (this.virtualDOMCache.has(cacheKey)) {
                const cached = this.virtualDOMCache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.config.cacheTTL) {
                    this.performanceMetrics.virtualDOMHits++;
                    return cached.html;
                } else {
                    this.virtualDOMCache.delete(cacheKey);
                }
            }

            // 渲染新内容
            const html = renderFunction(data);
            
            // 缓存结果
            this.virtualDOMCache.set(cacheKey, {
                html,
                timestamp: Date.now(),
                dataHash
            });

            // 限制缓存大小
            if (this.virtualDOMCache.size > this.config.virtualDOMSize) {
                this.cleanupVirtualDOMCache();
            }

            return html;
        }

        /**
         * 智能元素更新
         * @param {Element} element - 目标元素
         * @param {string} content - 新内容
         * @param {Object} options - 选项
         */
        smartUpdate(element, content, options = {}) {
            if (!element) {
                return;
            }

            const { 
                updateType = 'innerHTML', 
                compareContent = true,
                useTransition = false 
            } = options;

            // 内容比较优化
            if (compareContent && element[updateType] === content) {
                return; // 内容相同，跳过更新
            }

            // 使用过渡效果
            if (useTransition && element.style.transition) {
                element.style.opacity = '0';
                setTimeout(() => {
                    element[updateType] = content;
                    element.style.opacity = '1';
                }, 150);
            } else {
                element[updateType] = content;
            }
        }

        /**
         * 生成数据哈希
         * @param {any} data - 数据
         * @returns {string} 哈希值
         */
        generateDataHash(data) {
            const str = JSON.stringify(data);
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // 转换为32位整数
            }
            return hash.toString(36);
        }

        /**
         * 清理选择器缓存
         */
        cleanupSelectorCache() {
            const now = Date.now();
            const toDelete = [];

            for (const [key, cached] of this.selectorCache.entries()) {
                if (now - cached.timestamp > this.config.cacheTTL || 
                    !cached.element.parentNode) {
                    toDelete.push(key);
                }
            }

            toDelete.forEach(key => this.selectorCache.delete(key));
            
            this.logger.log(`选择器缓存清理完成: 删除${toDelete.length}个过期项`, 'debug');
        }

        /**
         * 清理虚拟DOM缓存
         */
        cleanupVirtualDOMCache() {
            const now = Date.now();
            const entries = Array.from(this.virtualDOMCache.entries());
            
            // 按时间戳排序，删除最旧的一半
            entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
            const toDelete = entries.slice(0, Math.floor(entries.length / 2));
            
            toDelete.forEach(([key]) => this.virtualDOMCache.delete(key));
            
            this.logger.log(`虚拟DOM缓存清理完成: 删除${toDelete.length}个项目`, 'debug');
        }

        /**
         * 启动自动清理
         */
        startAutoCleanup() {
            setInterval(() => {
                this.cleanupSelectorCache();
                this.cleanupVirtualDOMCache();
            }, this.config.cleanupInterval);
        }

        /**
         * 设置页面可见性监听
         */
        setupVisibilityListener() {
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    // 页面隐藏时暂停更新
                    this.pauseUpdates = true;
                } else {
                    // 页面显示时恢复更新
                    this.pauseUpdates = false;
                    if (this.updateQueue.length > 0) {
                        this.scheduleBatchUpdate();
                    }
                }
            });
        }

        /**
         * 设置性能监控
         */
        setupPerformanceMonitoring() {
            // 每分钟记录一次性能指标
            setInterval(() => {
                this.logger.log('DOM优化性能指标', 'debug', this.performanceMetrics);
            }, 60000);
        }

        /**
         * 获取性能指标
         * @returns {Object} 性能指标
         */
        getPerformanceMetrics() {
            return {
                ...this.performanceMetrics,
                cacheSize: this.selectorCache.size,
                virtualDOMSize: this.virtualDOMCache.size,
                updateQueueSize: this.updateQueue.length,
                cacheHitRate: this.performanceMetrics.cacheHits / 
                    (this.performanceMetrics.cacheHits + this.performanceMetrics.cacheMisses) * 100
            };
        }

        /**
         * 清除所有缓存
         */
        clearCache() {
            this.selectorCache.clear();
            this.virtualDOMCache.clear();
            this.updateQueue = [];
            
            this.logger.log('DOM优化缓存已清除', 'info');
        }

        /**
         * 获取优化引擎状态
         * @returns {Object} 状态信息
         */
        getStatus() {
            return {
                config: this.config,
                performanceMetrics: this.getPerformanceMetrics(),
                cacheStatus: {
                    selectorCache: this.selectorCache.size,
                    virtualDOMCache: this.virtualDOMCache.size
                },
                updateQueue: this.updateQueue.length,
                isUpdating: this.isUpdating
            };
        }
    }

    // 创建全局实例
    const domOptimizationEngine = new DOMOptimizationEngine();

    // 导出到全局作用域
    window.OTA = window.OTA || {};
    window.OTA.domOptimizationEngine = domOptimizationEngine;
    window.OTA.getDOMOptimizationEngine = () => domOptimizationEngine;

    // 向后兼容
    window.getDOMOptimizationEngine = () => domOptimizationEngine;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('domOptimizationEngine', domOptimizationEngine, '@OTA_DOM_OPTIMIZATION_ENGINE');
        window.OTA.Registry.registerFactory('getDOMOptimizationEngine', () => domOptimizationEngine, '@OTA_DOM_OPTIMIZATION_ENGINE_FACTORY');
    }

})();
