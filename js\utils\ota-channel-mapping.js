
(function() {
    'use strict';

    // 确保OTA命名空间存在
    window.OTA = window.OTA || {};

    /**
     * 账号到OTA配置的静态映射
     * - 键可以是用户ID (数字或字符串) 或 邮箱 (小写)
     * - 值是一个对象，包含默认渠道 `default` 和可选渠道列表 `options`
     */
    const otaChannelMapping = {
        // JR Coach (ID: 2666)
        2666: {
            default: 'JR Coach Credit',
            options: [
                { value: 'JR Coach Credit', text: 'JR Coach Credit' },
                { value: 'JR Coach Cash', text: 'JR Coach Cash' }
            ]
        },
        '<EMAIL>': {
            default: 'JR Coach Credit',
            options: [
                { value: 'JR Coach Credit', text: 'JR Coach Credit' },
                { value: 'JR Coach Cash', text: 'JR Coach Cash' }
            ]
        },

        // Super Admin (ID: 1)
        1: {
            default: 'Ctrip',
            options: [
                { value: 'Ctrip', text: '携程' },
                { value: 'Klook', text: 'Klook客路' },
                { value: 'KKday', text: 'KKday' }
            ]
        },
        '<EMAIL>': {
            default: 'Ctrip',
            options: [
                { value: 'Ctrip', text: '携程' },
                { value: 'Klook', text: 'Klook客路' },
                { value: 'KKday', text: 'KKday' }
            ]
        }
    };

    /**
     * 通用OTA渠道列表 (当没有特定用户配置时使用)
     * 基于 OTA List.md 中的完整列表
     */
    const commonChannels = [
        { value: 'Klook West Malaysia', text: 'Klook West Malaysia' },
        { value: 'Heycar', text: 'Heycar' },
        { value: 'Kkday', text: 'Kkday' },
        { value: 'Ctrip West Malaysia', text: 'Ctrip West Malaysia' },
        { value: 'SMW Eric', text: 'SMW Eric' },
        { value: 'Fliggy', text: 'Fliggy' },
        { value: 'Jing Ge', text: 'Jing Ge' },
        { value: 'Smw Wilson', text: 'Smw Wilson' },
        { value: 'YenNei', text: 'YenNei' },
        { value: 'Traveloka', text: 'Traveloka' },
        { value: 'GMH Sabah', text: 'GMH Sabah' },
        { value: 'Reward', text: 'Reward' },
        { value: 'Smw Josua', text: 'Smw Josua' },
        { value: 'Smw Jcyap', text: 'Smw Jcyap' },
        { value: 'Smw Vivian Lim', text: 'Smw Vivian Lim' },
        { value: 'Smw Wendy', text: 'Smw Wendy' },
        { value: 'Smw Annie', text: 'Smw Annie' },
        { value: 'GMH Terry', text: 'GMH Terry' },
        { value: 'SMW Xiaohongshu', text: 'SMW Xiaohongshu' },
        { value: 'M.I.C.E Tour', text: 'M.I.C.E Tour' },
        { value: 'UCSI - Cheras', text: 'UCSI - Cheras' },
        { value: 'UCSI - Port Dickson', text: 'UCSI - Port Dickson' },
        { value: 'MapleHome - The Robertson KL', text: 'MapleHome - The Robertson KL' },
        { value: 'Sabah Adventure', text: 'Sabah Adventure' },
        { value: '全景旅游', text: '全景旅游' },
        { value: 'MapleHome - Swiss Garden Kuala Lumpur', text: 'MapleHome - Swiss Garden Kuala Lumpur' },
        { value: 'MapleHome - D\'Majestic Premier Suites Kuala Lumpur', text: 'MapleHome - D\'Majestic Premier Suites Kuala Lumpur' },
        { value: 'MapleHome- Chambers Premier Suites Kuala Lumpur', text: 'MapleHome- Chambers Premier Suites Kuala Lumpur' },
        { value: 'MapleHome - Geo38 Premier Suites Kuala Lumpur', text: 'MapleHome - Geo38 Premier Suites Kuala Lumpur' },
        { value: 'MapleHome - The Apple Premier Suites Melaka', text: 'MapleHome - The Apple Premier Suites Melaka' },
        { value: 'MapleHome - Amber Cove Premier Suites Melaka', text: 'MapleHome - Amber Cove Premier Suites Melaka' },
        { value: 'Chong Dealer', text: 'Chong Dealer' },
        { value: 'EHTT 徐杰', text: 'EHTT 徐杰' },
        { value: 'Joydeer', text: 'Joydeer' },
        { value: 'KL Eric', text: 'KL Eric' },
        { value: 'WelcomePickups Sabah', text: 'WelcomePickups Sabah' },
        { value: 'WelcomePickups West Malaysia', text: 'WelcomePickups West Malaysia' },
        { value: 'BNI Member', text: 'BNI Member' },
        { value: 'GoMyHire - KL', text: 'GoMyHire - KL' },
        { value: 'Co-operate Stan', text: 'Co-operate Stan' },
        { value: 'GMH Ms Yong', text: 'GMH Ms Yong' },
        { value: 'Mozio', text: 'Mozio' },
        { value: 'The Maple Suite - Bukit Bintang', text: 'The Maple Suite - Bukit Bintang' },
        { value: 'PS Member', text: 'PS Member' },
        { value: 'PS Badminton Team & Family', text: 'PS Badminton Team & Family' },
        { value: '7deer Travel', text: '7deer Travel' },
        { value: 'Columbia', text: 'Columbia' },
        { value: 'Asia Trail', text: 'Asia Trail' },
        { value: 'Bob', text: 'Bob' },
        { value: 'Sim Card', text: 'Sim Card' },
        { value: 'SIM Card + Paging', text: 'SIM Card + Paging' },
        { value: 'Paging', text: 'Paging' },
        { value: 'The Pearl Kuala Lumpur Hotel', text: 'The Pearl Kuala Lumpur Hotel' },
        { value: '携程专车', text: '携程专车' },
        { value: 'Le Méridien Putrajaya', text: 'Le Méridien Putrajaya' },
        { value: 'Gomyhire Pohchengfatt', text: 'Gomyhire Pohchengfatt' },
        { value: '789 Genting', text: '789 Genting' },
        { value: 'The Little Series', text: 'The Little Series' },
        { value: 'Syn', text: 'Syn' },
        { value: 'Bintang Collectionz Hotel', text: 'Bintang Collectionz Hotel' },
        { value: 'GoMyHire Webpage', text: 'GoMyHire Webpage' },
        { value: 'Jing Ge Htp', text: 'Jing Ge Htp' },
        { value: 'ReSkill', text: 'ReSkill' },
        { value: 'JC666', text: 'JC666' },
        { value: 'Thousand Travel', text: 'Thousand Travel' },
        { value: 'CEO Chaffer Premium', text: 'CEO Chaffer Premium' },
        { value: 'GMH Ashley', text: 'GMH Ashley' },
        { value: 'GMH Calvin', text: 'GMH Calvin' },
        { value: 'Pg Sue', text: 'Pg Sue' },
        { value: 'Rental', text: 'Rental' },
        { value: 'Rent To Own', text: 'Rent To Own' },
        { value: 'Penalty', text: 'Penalty' },
        { value: 'GMH Ads', text: 'GMH Ads' },
        { value: 'ONE18 Boutique Hotel', text: 'ONE18 Boutique Hotel' },
        { value: 'Wiracle Vincent', text: 'Wiracle Vincent' },
        { value: 'GMH May', text: 'GMH May' },
        { value: 'KK Lucas', text: 'KK Lucas' },
        { value: 'GMH Daniel Fong', text: 'GMH Daniel Fong' },
        { value: 'GMH BNI', text: 'GMH BNI' },
        { value: 'GMH SQ', text: 'GMH SQ' },
        { value: 'GMH Jiahui', text: 'GMH Jiahui' },
        { value: 'GMH Vikki', text: 'GMH Vikki' },
        { value: '上海佳禾', text: '上海佳禾' },
        { value: 'Pg Afzan', text: 'Pg Afzan' },
        { value: 'Link Center (SBH)', text: 'Link Center (SBH)' },
        { value: 'ATV Borneo Sabah', text: 'ATV Borneo Sabah' },
        { value: 'Agent Victor', text: 'Agent Victor' },
        { value: 'SMW Whatsapp', text: 'SMW Whatsapp' },
        { value: 'SMW Agent', text: 'SMW Agent' },
        { value: 'GMH Qijun', text: 'GMH Qijun' },
        { value: 'GMH Venus', text: 'GMH Venus' },
        { value: 'GMH Karen', text: 'GMH Karen' },
        { value: 'SMW Walk In', text: 'SMW Walk In' },
        { value: 'Good Earth Travel', text: 'Good Earth Travel' },
        { value: 'Hotel - Secret Garden Homestay', text: 'Hotel - Secret Garden Homestay' },
        { value: 'HTP - 空港嘉华', text: 'HTP - 空港嘉华' },
        { value: 'Hotel - Leshore Hotel', text: 'Hotel - Leshore Hotel' },
        { value: 'Hotel - VI Boutique', text: 'Hotel - VI Boutique' },
        { value: 'Hotel - East Sun Hotel', text: 'Hotel - East Sun Hotel' },
        { value: 'Hotel - Padibox Homestay', text: 'Hotel - Padibox Homestay' },
        { value: 'Hotel - Padi Sentral Homestay', text: 'Hotel - Padi Sentral Homestay' },
        { value: 'B2B Lewis', text: 'B2B Lewis' },
        { value: 'Klook Singapore', text: 'Klook Singapore' },
        { value: 'Ctrip API', text: 'Ctrip API' },
        { value: 'Want To Eat Restaurant', text: 'Want To Eat Restaurant' },
        { value: 'GMH Cynthia B10', text: 'GMH Cynthia B10' },
        { value: 'GMH Cynthia', text: 'GMH Cynthia' },
        { value: 'Driver Own Job', text: 'Driver Own Job' },
        { value: 'GMH Jing Soon', text: 'GMH Jing Soon' },
        { value: 'Smartryde HTP', text: 'Smartryde HTP' },
        { value: 'GMH Driver', text: 'GMH Driver' },
        { value: 'B TN Holiday Sdn Bhd-Eunice', text: 'B TN Holiday Sdn Bhd-Eunice' },
        { value: '携程商铺 - CN', text: '携程商铺 - CN' },
        { value: 'diagnosis-test', text: 'diagnosis-test' },
        { value: 'GMH Xiaoxuan', text: 'GMH Xiaoxuan' },
        { value: 'JR Coast Credit', text: 'JR Coast Credit' },
        { value: 'KTMB', text: 'KTMB' },
        { value: 'Other', text: '其他' }
    ];

    /**
     * 获取指定用户ID或邮箱的OTA配置
     * @param {number|string} identifier - 用户ID或邮箱
     * @returns {object|null} 对应的OTA配置，未找到则返回null
     */
    function getConfig(identifier) {
        if (!identifier) return null;
        // 统一将标识符转为小写字符串，以匹配邮箱
        const key = typeof identifier === 'string' ? identifier.toLowerCase() : identifier;
        return otaChannelMapping[key] || null;
    }

    // 暴露到OTA命名空间
    window.OTA.otaChannelMapping = {
        getConfig,
        commonChannels
    };

})();