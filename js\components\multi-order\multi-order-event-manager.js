/**
 * 🎯 多订单事件管理器
 * 负责处理多订单系统的事件绑定、事件处理和事件委托
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-20
 */

(function() {
    'use strict';

    /**
     * 多订单事件管理器类
     * 专门处理事件绑定、事件处理、键盘快捷键、拖拽事件等功能
     */
    class MultiOrderEventManager {
        /**
         * 构造函数
         * @param {Object} dependencies - 依赖注入对象
         * @param {Object} dependencies.logger - 日志服务
         * @param {Object} dependencies.config - 配置对象
         * @param {Object} dependencies.stateManager - 状态管理器
         * @param {Object} dependencies.uiManager - UI管理器
         * @param {Object} dependencies.quickEditManager - 快捷编辑管理器
         */
        constructor(dependencies = {}) {
            this.logger = dependencies.logger || this.getLogger();
            this.config = {
                // 事件管理配置
                debounceDelay: 300,
                dragThreshold: 5,
                keyboardShortcuts: {
                    'Escape': 'exitQuickEdit',
                    'Enter': 'confirmAction',
                    'Delete': 'deleteSelected',
                    'Ctrl+A': 'selectAll',
                    'Ctrl+D': 'deselectAll'
                },
                touchEvents: {
                    enabled: true,
                    swipeThreshold: 50,
                    tapDelay: 300
                },
                ...dependencies.config
            };

            // 依赖注入
            this.stateManager = dependencies.stateManager;
            this.uiManager = dependencies.uiManager;
            this.quickEditManager = dependencies.quickEditManager;

            // 事件管理状态
            this.boundEventHandlers = new Map();
            this.debounceTimers = new Map();
            this.activeEvents = new Set();
            this.eventHistory = [];

            // 拖拽状态
            this.dragState = {
                isDragging: false,
                startX: 0,
                startY: 0,
                currentX: 0,
                currentY: 0,
                target: null
            };

            // 触摸状态
            this.touchState = {
                startTime: 0,
                startX: 0,
                startY: 0,
                lastTap: 0
            };

            // 初始化
            this.init();
        }

        /**
         * 初始化事件管理器
         */
        init() {
            this.logger?.log('🎯 事件管理器初始化开始', 'info');
            
            try {
                // 绑定全局事件处理器
                this.bindGlobalEventHandlers();
                
                // 绑定键盘快捷键
                this.bindKeyboardShortcuts();
                
                // 绑定触摸事件（如果启用）
                if (this.config.touchEvents.enabled) {
                    this.bindTouchEvents();
                }
                
                this.logger?.log('✅ 事件管理器初始化完成', 'success');
            } catch (error) {
                this.logger?.logError('事件管理器初始化失败', error);
                throw error;
            }
        }

        /**
         * 绑定全局事件处理器
         */
        bindGlobalEventHandlers() {
            // 文档级别事件
            this.addEventHandler(document, 'click', this.handleDocumentClick.bind(this));
            this.addEventHandler(document, 'keydown', this.handleKeyDown.bind(this));
            this.addEventHandler(document, 'keyup', this.handleKeyUp.bind(this));
            
            // 窗口级别事件
            this.addEventHandler(window, 'resize', this.debounce(this.handleWindowResize.bind(this), this.config.debounceDelay));
            this.addEventHandler(window, 'beforeunload', this.handleBeforeUnload.bind(this));
            
            // 多订单面板事件
            this.bindPanelEvents();
        }

        /**
         * 绑定多订单面板事件
         */
        bindPanelEvents() {
            const multiOrderPanel = document.getElementById('multiOrderPanel');
            if (!multiOrderPanel) return;

            // 面板内部事件委托
            this.addEventHandler(multiOrderPanel, 'click', this.handlePanelClick.bind(this));
            this.addEventHandler(multiOrderPanel, 'change', this.handlePanelChange.bind(this));
            this.addEventHandler(multiOrderPanel, 'input', this.handlePanelInput.bind(this));
            this.addEventHandler(multiOrderPanel, 'blur', this.handlePanelBlur.bind(this), true);
            this.addEventHandler(multiOrderPanel, 'focus', this.handlePanelFocus.bind(this), true);

            // 拖拽事件
            this.addEventHandler(multiOrderPanel, 'mousedown', this.handleMouseDown.bind(this));
            this.addEventHandler(document, 'mousemove', this.handleMouseMove.bind(this));
            this.addEventHandler(document, 'mouseup', this.handleMouseUp.bind(this));
        }

        /**
         * 绑定键盘快捷键
         */
        bindKeyboardShortcuts() {
            Object.entries(this.config.keyboardShortcuts).forEach(([key, action]) => {
                this.registerKeyboardShortcut(key, action);
            });
        }

        /**
         * 注册键盘快捷键
         * @param {string} key - 键盘组合键
         * @param {string} action - 动作名称
         */
        registerKeyboardShortcut(key, action) {
            const handler = (event) => {
                if (this.matchesKeyboardShortcut(event, key)) {
                    event.preventDefault();
                    this.executeKeyboardAction(action, event);
                }
            };

            this.addEventHandler(document, 'keydown', handler);
            this.logger?.log(`键盘快捷键已注册: ${key} -> ${action}`, 'info');
        }

        /**
         * 匹配键盘快捷键
         * @param {KeyboardEvent} event - 键盘事件
         * @param {string} shortcut - 快捷键组合
         * @returns {boolean} 是否匹配
         */
        matchesKeyboardShortcut(event, shortcut) {
            const parts = shortcut.split('+');
            const key = parts.pop();
            
            const modifiers = {
                'Ctrl': event.ctrlKey,
                'Alt': event.altKey,
                'Shift': event.shiftKey,
                'Meta': event.metaKey
            };

            // 检查修饰键
            for (const part of parts) {
                if (!modifiers[part]) return false;
            }

            // 检查主键
            return event.key === key || event.code === key;
        }

        /**
         * 执行键盘动作
         * @param {string} action - 动作名称
         * @param {KeyboardEvent} event - 键盘事件
         */
        executeKeyboardAction(action, event) {
            switch (action) {
                case 'exitQuickEdit':
                    this.handleExitQuickEdit(event);
                    break;
                case 'confirmAction':
                    this.handleConfirmAction(event);
                    break;
                case 'deleteSelected':
                    this.handleDeleteSelected(event);
                    break;
                case 'selectAll':
                    this.handleSelectAll(event);
                    break;
                case 'deselectAll':
                    this.handleDeselectAll(event);
                    break;
                default:
                    this.logger?.log(`未知的键盘动作: ${action}`, 'warn');
            }
        }

        /**
         * 绑定触摸事件
         */
        bindTouchEvents() {
            const multiOrderPanel = document.getElementById('multiOrderPanel');
            if (!multiOrderPanel) return;

            this.addEventHandler(multiOrderPanel, 'touchstart', this.handleTouchStart.bind(this));
            this.addEventHandler(multiOrderPanel, 'touchmove', this.handleTouchMove.bind(this));
            this.addEventHandler(multiOrderPanel, 'touchend', this.handleTouchEnd.bind(this));
        }

        /**
         * 添加事件处理器
         * @param {Element} element - 目标元素
         * @param {string} event - 事件名称
         * @param {Function} handler - 事件处理函数
         * @param {boolean} useCapture - 是否使用捕获阶段
         */
        addEventHandler(element, event, handler, useCapture = false) {
            if (!element || !event || !handler) return;

            const key = `${element.constructor.name}-${event}-${handler.name}`;
            
            // 避免重复绑定
            if (this.boundEventHandlers.has(key)) {
                return;
            }

            element.addEventListener(event, handler, useCapture);
            this.boundEventHandlers.set(key, { element, event, handler, useCapture });
            this.activeEvents.add(event);

            this.logger?.log(`事件处理器已绑定: ${event}`, 'info');
        }

        /**
         * 移除事件处理器
         * @param {string} key - 事件处理器键
         */
        removeEventHandler(key) {
            const handlerInfo = this.boundEventHandlers.get(key);
            if (handlerInfo) {
                const { element, event, handler, useCapture } = handlerInfo;
                element.removeEventListener(event, handler, useCapture);
                this.boundEventHandlers.delete(key);
                this.logger?.log(`事件处理器已移除: ${event}`, 'info');
            }
        }

        /**
         * 防抖函数
         * @param {Function} func - 要防抖的函数
         * @param {number} delay - 延迟时间
         * @returns {Function} 防抖后的函数
         */
        debounce(func, delay) {
            return (...args) => {
                const key = func.name || 'anonymous';
                
                if (this.debounceTimers.has(key)) {
                    clearTimeout(this.debounceTimers.get(key));
                }
                
                const timer = setTimeout(() => {
                    func.apply(this, args);
                    this.debounceTimers.delete(key);
                }, delay);
                
                this.debounceTimers.set(key, timer);
            };
        }

        /**
         * 处理文档点击事件
         * @param {MouseEvent} event - 鼠标事件
         */
        handleDocumentClick(event) {
            // 记录事件
            this.recordEvent('documentClick', event);

            // 处理面板外点击
            const multiOrderPanel = document.getElementById('multiOrderPanel');
            if (multiOrderPanel && !multiOrderPanel.contains(event.target)) {
                this.handleClickOutsidePanel(event);
            }

            // 处理订单选择
            if (event.target.classList.contains('order-checkbox')) {
                this.handleOrderCheckboxClick(event);
            }

            // 处理快捷编辑按钮
            if (event.target.classList.contains('quick-edit-btn')) {
                this.handleQuickEditButtonClick(event);
            }
        }

        /**
         * 处理面板点击事件
         * @param {MouseEvent} event - 鼠标事件
         */
        handlePanelClick(event) {
            this.recordEvent('panelClick', event);

            // 阻止事件冒泡到文档级别
            event.stopPropagation();

            // 处理特定元素点击
            const target = event.target;

            if (target.classList.contains('order-item')) {
                this.handleOrderItemClick(event);
            } else if (target.classList.contains('batch-action-btn')) {
                this.handleBatchActionClick(event);
            } else if (target.classList.contains('close-panel-btn')) {
                this.handleClosePanelClick(event);
            }
        }

        /**
         * 处理面板变更事件
         * @param {Event} event - 变更事件
         */
        handlePanelChange(event) {
            this.recordEvent('panelChange', event);

            const target = event.target;

            if (target.classList.contains('order-checkbox')) {
                this.handleOrderCheckboxChange(event);
            } else if (target.classList.contains('batch-select')) {
                this.handleBatchSelectChange(event);
            }
        }

        /**
         * 处理面板输入事件
         * @param {InputEvent} event - 输入事件
         */
        handlePanelInput(event) {
            this.recordEvent('panelInput', event);

            // 防抖处理输入事件
            this.debounce(() => {
                this.handleDebouncedInput(event);
            }, this.config.debounceDelay)();
        }

        /**
         * 处理键盘按下事件
         * @param {KeyboardEvent} event - 键盘事件
         */
        handleKeyDown(event) {
            this.recordEvent('keyDown', event);

            // 处理快捷键（已在绑定时处理）
            // 这里处理其他键盘事件
            if (event.key === 'Tab') {
                this.handleTabNavigation(event);
            }
        }

        /**
         * 处理键盘释放事件
         * @param {KeyboardEvent} event - 键盘事件
         */
        handleKeyUp(event) {
            this.recordEvent('keyUp', event);
        }

        /**
         * 处理窗口大小变化事件
         * @param {Event} event - 窗口事件
         */
        handleWindowResize(event) {
            this.recordEvent('windowResize', event);

            // 通知UI管理器调整布局
            if (this.uiManager && this.uiManager.handleResize) {
                this.uiManager.handleResize();
            }
        }

        /**
         * 处理页面卸载前事件
         * @param {BeforeUnloadEvent} event - 卸载事件
         */
        handleBeforeUnload(event) {
            this.recordEvent('beforeUnload', event);

            // 检查是否有未保存的更改
            if (this.stateManager && this.stateManager.getStateValue('batchProgress.isRunning')) {
                event.preventDefault();
                event.returnValue = '批量处理正在进行中，确定要离开吗？';
                return event.returnValue;
            }
        }

        /**
         * 处理鼠标按下事件（拖拽开始）
         * @param {MouseEvent} event - 鼠标事件
         */
        handleMouseDown(event) {
            if (event.target.classList.contains('draggable')) {
                this.dragState.isDragging = true;
                this.dragState.startX = event.clientX;
                this.dragState.startY = event.clientY;
                this.dragState.target = event.target;

                event.preventDefault();
                this.recordEvent('dragStart', event);
            }
        }

        /**
         * 处理鼠标移动事件（拖拽中）
         * @param {MouseEvent} event - 鼠标事件
         */
        handleMouseMove(event) {
            if (this.dragState.isDragging) {
                this.dragState.currentX = event.clientX;
                this.dragState.currentY = event.clientY;

                const deltaX = this.dragState.currentX - this.dragState.startX;
                const deltaY = this.dragState.currentY - this.dragState.startY;

                // 检查是否超过拖拽阈值
                if (Math.abs(deltaX) > this.config.dragThreshold || Math.abs(deltaY) > this.config.dragThreshold) {
                    this.handleDragMove(event, deltaX, deltaY);
                }
            }
        }

        /**
         * 处理鼠标释放事件（拖拽结束）
         * @param {MouseEvent} event - 鼠标事件
         */
        handleMouseUp(event) {
            if (this.dragState.isDragging) {
                this.handleDragEnd(event);
                this.resetDragState();
                this.recordEvent('dragEnd', event);
            }
        }

        /**
         * 处理触摸开始事件
         * @param {TouchEvent} event - 触摸事件
         */
        handleTouchStart(event) {
            const touch = event.touches[0];
            this.touchState.startTime = Date.now();
            this.touchState.startX = touch.clientX;
            this.touchState.startY = touch.clientY;

            this.recordEvent('touchStart', event);
        }

        /**
         * 处理触摸移动事件
         * @param {TouchEvent} event - 触摸事件
         */
        handleTouchMove(event) {
            this.recordEvent('touchMove', event);

            // 处理滑动手势
            const touch = event.touches[0];
            const deltaX = touch.clientX - this.touchState.startX;
            const deltaY = touch.clientY - this.touchState.startY;

            if (Math.abs(deltaX) > this.config.touchEvents.swipeThreshold) {
                this.handleSwipeGesture(deltaX > 0 ? 'right' : 'left', event);
            }
        }

        /**
         * 处理触摸结束事件
         * @param {TouchEvent} event - 触摸事件
         */
        handleTouchEnd(event) {
            const duration = Date.now() - this.touchState.startTime;

            // 检测双击
            if (duration < this.config.touchEvents.tapDelay) {
                const now = Date.now();
                if (now - this.touchState.lastTap < this.config.touchEvents.tapDelay) {
                    this.handleDoubleTap(event);
                }
                this.touchState.lastTap = now;
            }

            this.recordEvent('touchEnd', event);
        }

        /**
         * 记录事件到历史记录
         * @param {string} eventType - 事件类型
         * @param {Event} event - 事件对象
         */
        recordEvent(eventType, event) {
            this.eventHistory.push({
                type: eventType,
                timestamp: Date.now(),
                target: event.target?.tagName || 'unknown',
                detail: {
                    clientX: event.clientX,
                    clientY: event.clientY,
                    key: event.key,
                    button: event.button
                }
            });

            // 限制历史记录大小
            if (this.eventHistory.length > 100) {
                this.eventHistory = this.eventHistory.slice(-50);
            }
        }

        /**
         * 重置拖拽状态
         */
        resetDragState() {
            this.dragState = {
                isDragging: false,
                startX: 0,
                startY: 0,
                currentX: 0,
                currentY: 0,
                target: null
            };
        }

        /**
         * 清理所有事件处理器
         */
        cleanup() {
            // 移除所有绑定的事件处理器
            this.boundEventHandlers.forEach((handlerInfo, key) => {
                this.removeEventHandler(key);
            });

            // 清理防抖定时器
            this.debounceTimers.forEach(timer => clearTimeout(timer));
            this.debounceTimers.clear();

            // 清理状态
            this.activeEvents.clear();
            this.eventHistory = [];
            this.resetDragState();

            this.logger?.log('🎯 事件管理器已清理', 'info');
        }

        /**
         * 销毁事件管理器
         */
        destroy() {
            this.cleanup();
            this.logger?.log('🎯 事件管理器已销毁', 'info');
        }

        /**
         * 处理面板失焦事件
         * @param {FocusEvent} event - 失焦事件
         */
        handlePanelBlur(event) {
            this.logger?.log('🔍 面板失焦', 'debug', { 
                target: event.target?.tagName,
                id: event.target?.id 
            });
            
            // 保存当前编辑状态
            if (event.target.matches('input, textarea, select')) {
                this.saveFieldState(event.target);
            }
            
            // 更新焦点状态
            if (this.stateManager && typeof this.stateManager.updateFocusState === 'function') {
                this.stateManager.updateFocusState(event.target, false);
            }
        }

        /**
         * 处理面板获焦事件
         * @param {FocusEvent} event - 获焦事件
         */
        handlePanelFocus(event) {
            this.logger?.log('🎯 面板获焦', 'debug', { 
                target: event.target?.tagName,
                id: event.target?.id 
            });
            
            // 恢复编辑状态
            if (event.target.matches('input, textarea, select')) {
                this.restoreFieldState(event.target);
            }
            
            // 更新焦点状态
            if (this.stateManager && typeof this.stateManager.updateFocusState === 'function') {
                this.stateManager.updateFocusState(event.target, true);
            }
            
            // 自动选择文本（对于文本输入框）
            if (event.target.matches('input[type="text"], input[type="number"], textarea')) {
                setTimeout(() => {
                    event.target.select?.();
                }, 10);
            }
        }

        /**
         * 保存字段状态
         * @param {HTMLElement} field - 表单字段
         */
        saveFieldState(field) {
            if (field && field.id) {
                const state = {
                    value: field.value,
                    selectionStart: field.selectionStart,
                    selectionEnd: field.selectionEnd,
                    timestamp: Date.now()
                };
                
                // 存储到会话存储
                try {
                    sessionStorage.setItem(`field_state_${field.id}`, JSON.stringify(state));
                } catch (error) {
                    this.logger?.log('字段状态保存失败', 'warning', error);
                }
            }
        }

        /**
         * 恢复字段状态
         * @param {HTMLElement} field - 表单字段
         */
        restoreFieldState(field) {
            if (field && field.id) {
                try {
                    const stateStr = sessionStorage.getItem(`field_state_${field.id}`);
                    if (stateStr) {
                        const state = JSON.parse(stateStr);
                        
                        // 5分钟内的状态才恢复
                        if (Date.now() - state.timestamp < 5 * 60 * 1000) {
                            if (field.value !== state.value) {
                                field.value = state.value;
                            }
                            
                            // 恢复光标位置
                            if (typeof field.setSelectionRange === 'function') {
                                field.setSelectionRange(state.selectionStart, state.selectionEnd);
                            }
                        }
                    }
                } catch (error) {
                    this.logger?.log('字段状态恢复失败', 'warning', error);
                }
            }
        }

        /**
         * 获取事件统计信息
         * @returns {Object} 事件统计
         */
        getEventStats() {
            return {
                boundHandlers: this.boundEventHandlers.size,
                activeEvents: this.activeEvents.size,
                eventHistory: this.eventHistory.length,
                debounceTimers: this.debounceTimers.size,
                isDragging: this.dragState.isDragging
            };
        }

        /**
         * 获取日志服务
         * @returns {Object} 日志服务对象
         */
        getLogger() {
            return window.getLogger?.() || {
                log: console.log.bind(console),
                logError: console.error.bind(console)
            };
        }
    }

    /**
     * 创建事件管理器实例的工厂函数
     * @param {Object} dependencies - 依赖注入对象
     * @returns {MultiOrderEventManager} 事件管理器实例
     */
    function createMultiOrderEventManager(dependencies = {}) {
        return new MultiOrderEventManager(dependencies);
    }

    // 导出到全局作用域
    window.getMultiOrderEventManager = createMultiOrderEventManager;
    window.MultiOrderEventManager = MultiOrderEventManager;

    // 确保OTA命名空间存在
    if (typeof window.OTA === 'undefined') {
        window.OTA = {};
    }
    if (typeof window.OTA.multiOrder === 'undefined') {
        window.OTA.multiOrder = {};
    }

    // 注册到OTA命名空间
    window.OTA.multiOrder.EventManager = MultiOrderEventManager;
    window.OTA.multiOrder.getEventManager = createMultiOrderEventManager;

})();
