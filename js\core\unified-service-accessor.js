/**
 * 统一依赖获取接口
 * 解决双重依赖获取模式混乱问题，提供单一入口的服务获取方式
 * 
 * 功能特性:
 * - 单一入口：window.OTA.getService(serviceName)
 * - 服务注册中心管理
 * - 智能降级机制
 * - 性能优化和缓存
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.core = window.OTA.core || {};

(function() {
    'use strict';

    /**
     * 统一服务访问器类
     */
    class UnifiedServiceAccessor {
        constructor() {
            // 服务注册中心
            this.serviceRegistry = new Map();
            
            // 服务缓存
            this.serviceCache = new Map();
            
            // 配置
            this.config = {
                // 启用服务缓存
                enableCache: true,
                
                // 缓存过期时间（毫秒）
                cacheExpiry: 5 * 60 * 1000, // 5分钟
                
                // 启用性能监控
                enablePerformanceMonitoring: true,
                
                // 启用健康检查
                enableHealthCheck: true
            };
            
            // 性能统计
            this.stats = {
                totalRequests: 0,
                cacheHits: 0,
                cacheMisses: 0,
                averageResponseTime: 0,
                failedRequests: 0,
                lastCleanup: Date.now()
            };
            
            // 获取警告管理器
            this.warningManager = null;
            
            this.initialize();
        }

        /**
         * 初始化统一服务访问器
         */
        initialize() {
            // 延迟获取警告管理器避免循环依赖
            setTimeout(() => {
                this.warningManager = window.OTA?.core?.warningManager;
            }, 100);
            
            // 注册核心服务
            this.registerCoreServices();
            
            // 启动定期清理
            this.startPeriodicCleanup();
            
            console.log('✅ 统一服务访问器已初始化');
        }

        /**
         * 注册核心服务
         */
        registerCoreServices() {
            // 注册核心服务的获取策略
            const coreServices = [
                'appState',
                'logger', 
                'apiService',
                'geminiService',
                'uiManager',
                'eventCoordinator',
                'configManager'
            ];

            coreServices.forEach(serviceName => {
                this.registerService(serviceName, () => {
                    return this.resolveService(serviceName);
                });
            });
        }

        /**
         * 注册服务
         * @param {string} serviceName - 服务名称
         * @param {Function} factory - 服务工厂函数
         * @param {Object} options - 选项
         */
        registerService(serviceName, factory, options = {}) {
            this.serviceRegistry.set(serviceName, {
                factory,
                singleton: options.singleton !== false,
                priority: options.priority || 0,
                healthCheck: options.healthCheck,
                metadata: options.metadata || {}
            });
        }

        /**
         * 获取服务（主要入口）
         * @param {string} serviceName - 服务名称
         * @returns {any} 服务实例
         */
        getService(serviceName) {
            const startTime = performance.now();
            this.stats.totalRequests++;

            try {
                // 检查缓存
                if (this.config.enableCache) {
                    const cached = this.getCachedService(serviceName);
                    if (cached) {
                        this.stats.cacheHits++;
                        return cached;
                    }
                }

                this.stats.cacheMisses++;

                // 从注册中心获取
                const service = this.getServiceFromRegistry(serviceName);
                
                // 缓存服务
                if (this.config.enableCache && service) {
                    this.cacheService(serviceName, service);
                }

                // 更新性能统计
                this.updatePerformanceStats(startTime);

                return service;

            } catch (error) {
                this.stats.failedRequests++;
                
                if (this.warningManager) {
                    this.warningManager.warn(
                        'SERVICE_ACCESS_ERROR',
                        `获取服务 ${serviceName} 失败: ${error.message}`,
                        'CRITICAL',
                        { serviceName, error: error.message }
                    );
                }
                
                throw error;
            }
        }

        /**
         * 从注册中心获取服务
         * @param {string} serviceName - 服务名称
         * @returns {any} 服务实例
         */
        getServiceFromRegistry(serviceName) {
            // 优先从注册中心获取
            if (this.serviceRegistry.has(serviceName)) {
                const serviceConfig = this.serviceRegistry.get(serviceName);
                return serviceConfig.factory();
            }

            // 降级到传统解析方式
            return this.resolveService(serviceName);
        }

        /**
         * 解析服务（降级机制）
         * @param {string} serviceName - 服务名称
         * @returns {any} 服务实例
         */
        resolveService(serviceName) {
            // 策略1: 依赖容器
            const container = window.OTA?.container;
            if (container && container.has && container.has(serviceName)) {
                try {
                    return container.get(serviceName);
                } catch (error) {
                    // 继续尝试其他策略
                }
            }

            // 策略2: 服务定位器
            const serviceLocator = window.OTA?.serviceLocator;
            if (serviceLocator && serviceLocator.getService) {
                try {
                    return serviceLocator.getService(serviceName);
                } catch (error) {
                    // 继续尝试其他策略
                }
            }

            // 策略3: OTA命名空间
            const otaService = window.OTA?.[serviceName];
            if (otaService) {
                return otaService;
            }

            // 策略4: 全局工厂函数
            const factoryName = this.getFactoryName(serviceName);
            const factory = window[factoryName];
            if (typeof factory === 'function') {
                return factory();
            }

            // 策略5: 全局命名空间（最后的降级）
            const globalService = window[serviceName];
            if (globalService) {
                if (this.warningManager) {
                    this.warningManager.warn(
                        'SERVICE_GLOBAL_FALLBACK',
                        `服务 ${serviceName} 使用全局降级获取，建议注册到服务中心`,
                        'WARNING',
                        { serviceName }
                    );
                }
                return globalService;
            }

            throw new Error(`服务 ${serviceName} 未找到`);
        }

        /**
         * 获取工厂函数名称
         * @param {string} serviceName - 服务名称
         * @returns {string} 工厂函数名称
         */
        getFactoryName(serviceName) {
            // 将服务名转换为工厂函数名
            // 例如: appState -> getAppState
            return 'get' + serviceName.charAt(0).toUpperCase() + serviceName.slice(1);
        }

        /**
         * 获取缓存的服务
         * @param {string} serviceName - 服务名称
         * @returns {any|null} 缓存的服务实例或null
         */
        getCachedService(serviceName) {
            const cached = this.serviceCache.get(serviceName);
            if (!cached) {
                return null;
            }

            // 检查是否过期
            if (Date.now() - cached.timestamp > this.config.cacheExpiry) {
                this.serviceCache.delete(serviceName);
                return null;
            }

            return cached.service;
        }

        /**
         * 缓存服务
         * @param {string} serviceName - 服务名称
         * @param {any} service - 服务实例
         */
        cacheService(serviceName, service) {
            this.serviceCache.set(serviceName, {
                service,
                timestamp: Date.now()
            });
        }

        /**
         * 更新性能统计
         * @param {number} startTime - 开始时间
         */
        updatePerformanceStats(startTime) {
            const responseTime = performance.now() - startTime;
            
            // 计算平均响应时间
            this.stats.averageResponseTime = 
                (this.stats.averageResponseTime * (this.stats.totalRequests - 1) + responseTime) / 
                this.stats.totalRequests;
        }

        /**
         * 启动定期清理
         */
        startPeriodicCleanup() {
            setInterval(() => {
                this.cleanupExpiredCache();
            }, 60000); // 每分钟清理一次
        }

        /**
         * 清理过期缓存
         */
        cleanupExpiredCache() {
            const now = Date.now();
            let cleanedCount = 0;

            for (const [serviceName, cached] of this.serviceCache.entries()) {
                if (now - cached.timestamp > this.config.cacheExpiry) {
                    this.serviceCache.delete(serviceName);
                    cleanedCount++;
                }
            }

            if (cleanedCount > 0) {
                console.log(`🧹 清理了 ${cleanedCount} 个过期的服务缓存`);
            }

            this.stats.lastCleanup = now;
        }

        /**
         * 检查服务是否可用
         * @param {string} serviceName - 服务名称
         * @returns {boolean} 是否可用
         */
        hasService(serviceName) {
            try {
                const service = this.getService(serviceName);
                return !!service;
            } catch {
                return false;
            }
        }

        /**
         * 获取性能统计报告
         * @returns {Object} 统计报告
         */
        getStatsReport() {
            const cacheHitRate = this.stats.totalRequests > 0 ? 
                (this.stats.cacheHits / this.stats.totalRequests * 100).toFixed(2) : 0;

            return {
                总请求数: this.stats.totalRequests,
                缓存命中数: this.stats.cacheHits,
                缓存未命中数: this.stats.cacheMisses,
                缓存命中率: `${cacheHitRate}%`,
                平均响应时间: `${this.stats.averageResponseTime.toFixed(2)}ms`,
                失败请求数: this.stats.failedRequests,
                注册服务数: this.serviceRegistry.size,
                缓存服务数: this.serviceCache.size,
                上次清理时间: new Date(this.stats.lastCleanup).toLocaleTimeString()
            };
        }

        /**
         * 清空所有缓存
         */
        clearCache() {
            const cacheSize = this.serviceCache.size;
            this.serviceCache.clear();
            console.log(`🗑️ 已清空 ${cacheSize} 个服务缓存`);
        }
    }

    // 创建全局唯一的统一服务访问器实例
    const unifiedServiceAccessor = new UnifiedServiceAccessor();

    // 暴露到OTA命名空间
    window.OTA.core.unifiedServiceAccessor = unifiedServiceAccessor;

    // 重写OTA.getService方法，使用统一访问器
    window.OTA.getService = function(serviceName) {
        return unifiedServiceAccessor.getService(serviceName);
    };

    // 提供服务注册接口
    window.OTA.registerService = function(serviceName, factory, options) {
        return unifiedServiceAccessor.registerService(serviceName, factory, options);
    };

    console.log('✅ 统一服务访问器模块已加载');

})();
