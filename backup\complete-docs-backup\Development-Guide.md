# OTA系统开发指南

## 🛠️ 概述

本文档为OTA订单处理系统的开发者提供全面的开发指导，包括架构设计、代码规范、开发流程和最佳实践。

### 技术栈
- **前端**: Vanilla JavaScript (ES6+), HTML5, CSS3
- **架构模式**: 依赖注入、服务定位器、组件生命周期管理
- **AI集成**: Gemini API, Kimi API
- **数据存储**: LocalStorage, SessionStorage
- **性能优化**: 懒加载、组件复用池、内存管理

## 🏢 项目架构

### 目录结构
```
OTA-System/
├── 🚀 js/bootstrap/        # 核心引导层
│   ├── app-state.js
│   └── application-bootstrap.js
├── ⚙️ js/core/            # 核心架构层
│   ├── dependency-container.js
│   ├── service-locator.js
│   ├── lazy-loader.js
│   ├── component-lifecycle-manager.js
│   ├── performance-monitor.js
│   └── ...
├── 🔧 js/services/        # 业务服务层
│   ├── api-service.js
│   ├── logger.js
│   └── ...
├── 🎛️ js/managers/        # 管理器层
│   ├── ui-manager.js
│   ├── multi-order-manager.js
│   └── ...
├── 🤖 js/ai/              # AI服务层
│   ├── gemini-service.js
│   ├── kimi-service.js
│   └── gemini/
├── 🧩 js/components/      # UI组件层
│   ├── image-upload-manager.js
│   └── multi-order/
└── 🔠 js/utils/          # 工具函数层
    ├── utils.js
    └── ...
```

### 架构分层

#### 1. 引导层 (Bootstrap)
负责系统初始化和核心服务启动。

#### 2. 核心层 (Core)
提供底层架构服务，包括依赖管理、服务定位、性能监控等。

#### 3. 服务层 (Services)
封装业务逻辑和外部集成，如API调用、日志记录等。

#### 4. 管理器层 (Managers)
协调业务流程和组件间交互。

#### 5. 组件层 (Components)
UI组件和交互逻辑。

#### 6. 工具层 (Utils)
通用工具函数和辅助类。

## 📝 代码规范

### JavaScript 编码规范

#### 1. 命名约定
```javascript
// ✅ 推荐 - 驼峰命名
const userManager = new UserManager();
const apiService = window.OTA.getService('apiService');

// ✅ 推荐 - 常量全大写下划线
const MAX_RETRY_COUNT = 3;
const API_BASE_URL = 'https://api.example.com';

// ✅ 推荐 - 类名帕斯卡命名
class OrderProcessor {
    constructor() {
        this.isProcessing = false;
    }
}

// ❌ 避免 - 单字母变量
let a = getData(); // 不好
let orderData = getData(); // 好
```

#### 2. 代码组织
```javascript
// ✅ 推荐 - 模块化结构
class ServiceName {
    constructor() {
        this.initializeService();
    }
    
    // 私有方法使用_前缀
    _initializeService() {
        // 初始化逻辑
    }
    
    // 公共方法
    processData(data) {
        try {
            return this._validateAndProcess(data);
        } catch (error) {
            this._handleError(error);
            throw error;
        }
    }
    
    // 错误处理
    _handleError(error) {
        const logger = window.OTA.getService('logger');
        logger.logError('ServiceName.processData', error);
    }
}
```

#### 3. 异步处理
```javascript
// ✅ 推荐 - 使用 async/await
async function fetchOrderData(orderId) {
    try {
        const response = await fetch(`/api/orders/${orderId}`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return await response.json();
    } catch (error) {
        logger.logError('fetchOrderData', error);
        throw error;
    }
}

// ❌ 避免 - 回调地狱
fetch('/api/orders')
    .then(response => response.json())
    .then(data => {
        processData(data, function(result) {
            // 嵌套回调
        });
    });
```

### CSS 编码规范

#### 1. 类名约定
```css
/* ✅ 推荐 - BEM 命名法 */
.order-form {}
.order-form__field {}
.order-form__field--required {}
.order-form__submit-button {}
.order-form__submit-button--disabled {}

/* ❌ 避免 - 过度嵌套 */
.container .form .field .input .label {}
```

#### 2. 响应式设计
```css
/* ✅ 推荐 - Mobile First */
.order-card {
    width: 100%;
    padding: 1rem;
}

@media (min-width: 768px) {
    .order-card {
        width: 48%;
        padding: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .order-card {
        width: 32%;
        padding: 2rem;
    }
}
```

## 🔧 开发工具和设置

### 开发环境要求
- **浏览器**: Chrome 80+, Firefox 75+, Safari 13+
- **编辑器**: VS Code 推荐（配置 ESLint 和 Prettier）
- **版本控制**: Git
- **Node.js**: v14+（如需构建工具）

### 推荐的 VS Code 扩展
```json
{
    "recommendations": [
        "esbenp.prettier-vscode",
        "dbaeumer.vscode-eslint",
        "bradlc.vscode-tailwindcss",
        "ms-vscode.vscode-json",
        "ritwickdey.liveserver"
    ]
}
```

### ESLint 配置
```javascript
// .eslintrc.js
module.exports = {
    env: {
        browser: true,
        es2021: true
    },
    extends: ['eslint:recommended'],
    parserOptions: {
        ecmaVersion: 12,
        sourceType: 'module'
    },
    rules: {
        'no-console': 'warn',
        'no-unused-vars': 'error',
        'prefer-const': 'error',
        'no-var': 'error'
    },
    globals: {
        'window': 'readonly',
        'document': 'readonly'
    }
};
```

## 🔄 开发流程

### Git 分支策略
```bash
# 主分支
main          # 生产环境
develop       # 开发环境

# 功能分支
feature/[feature-name]    # 新功能
hotfix/[issue-id]         # 紧急修复
refactor/[module-name]    # 代码重构
```

### 提交信息规范
```bash
# 格式: <类型>: <描述>
feat: 添加多订单批量处理功能
fix: 修复AI分析的日期识别问题
refactor: 重构依赖注入系统
docs: 更新API文档
style: 修复代码格式
perf: 优化懒加载性能
test: 添加单元测试
```

### 开发步骤
1. **创建功能分支**
   ```bash
   git checkout -b feature/new-feature
   ```

2. **开发和测试**
   - 编写功能代码
   - 添加单元测试
   - 本地测试验证

3. **代码审查**
   ```bash
   # 创建 Pull Request
   git push origin feature/new-feature
   ```

4. **合并和部署**
   - 审查通过后合并到 develop
   - 测试环境验证
   - 发布到生产环境

## 🧩 组件开发指南

### 新组件创建

#### 1. 组件基本结构
```javascript
// js/components/example-component.js
class ExampleComponent {
    constructor(options = {}) {
        this.options = {
            container: null,
            autoInit: true,
            ...options
        };
        
        this.state = {
            isInitialized: false,
            isLoading: false
        };
        
        if (this.options.autoInit) {
            this.init();
        }
    }
    
    init() {
        if (this.state.isInitialized) {
            console.warn('Component already initialized');
            return;
        }
        
        this._validateOptions();
        this._createElements();
        this._bindEvents();
        this._registerComponent();
        
        this.state.isInitialized = true;
    }
    
    _validateOptions() {
        if (!this.options.container) {
            throw new Error('Container is required');
        }
    }
    
    _createElements() {
        // DOM 元素创建
    }
    
    _bindEvents() {
        // 事件绑定
    }
    
    _registerComponent() {
        const lifecycleManager = window.OTA.getService('componentLifecycleManager');
        lifecycleManager.register(this.constructor.name, this);
    }
    
    destroy() {
        this._unbindEvents();
        this._removeElements();
        this.state.isInitialized = false;
    }
}

// 注册到全局
window.OTA = window.OTA || {};
window.OTA.ExampleComponent = ExampleComponent;
```

#### 2. 服务类创建
```javascript
// js/services/example-service.js
class ExampleService {
    constructor() {
        this.isInitialized = false;
        this.cache = new Map();
    }
    
    init() {
        if (this.isInitialized) return;
        
        this._setupConfiguration();
        this._initializeCache();
        
        this.isInitialized = true;
    }
    
    async processData(data) {
        const logger = window.OTA.getService('logger');
        
        try {
            const cacheKey = this._generateCacheKey(data);
            
            // 检查缓存
            if (this.cache.has(cacheKey)) {
                return this.cache.get(cacheKey);
            }
            
            // 处理数据
            const result = await this._performProcessing(data);
            
            // 缓存结果
            this.cache.set(cacheKey, result);
            
            return result;
        } catch (error) {
            logger.logError('ExampleService.processData', error);
            throw error;
        }
    }
    
    _setupConfiguration() {
        // 配置初始化
    }
    
    _initializeCache() {
        // 缓存初始化
    }
    
    _generateCacheKey(data) {
        return JSON.stringify(data);
    }
    
    async _performProcessing(data) {
        // 实际处理逻辑
        return data;
    }
}

// 注册服务
const container = window.OTA?.container;
if (container) {
    container.register('exampleService', () => {
        const service = new ExampleService();
        service.init();
        return service;
    }, true); // true 表示单例
}
```

### 组件通信模式

#### 1. 事件驱动通信
```javascript
// 发布事件
class PublisherComponent {
    publishEvent(eventName, data) {
        const eventCoordinator = window.OTA.getService('globalEventCoordinator');
        eventCoordinator.emit(eventName, data);
    }
}

// 订阅事件
class SubscriberComponent {
    init() {
        const eventCoordinator = window.OTA.getService('globalEventCoordinator');
        eventCoordinator.on('dataUpdated', this.handleDataUpdate.bind(this));
    }
    
    handleDataUpdate(data) {
        // 处理数据更新
    }
}
```

#### 2. 服务依赖注入
```javascript
class ComponentWithDependencies {
    constructor() {
        // 依赖注入
        this.apiService = window.OTA.getService('apiService');
        this.logger = window.OTA.getService('logger');
        this.uiManager = window.OTA.getService('uiManager');
    }
    
    async performAction() {
        try {
            const data = await this.apiService.fetchData();
            this.uiManager.updateUI(data);
        } catch (error) {
            this.logger.logError('ComponentWithDependencies.performAction', error);
            this.uiManager.showError('操作失败');
        }
    }
}
```

## 🤖 AI 服务集成

### Gemini API 集成
```javascript
class GeminiService {
    constructor() {
        this.apiKey = this._getApiKey();
        this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
    }
    
    async analyzeOrderText(text) {
        try {
            const prompt = this._buildPrompt(text);
            const response = await this._makeRequest(prompt);
            return this._parseResponse(response);
        } catch (error) {
            this._handleError(error);
            throw error;
        }
    }
    
    _buildPrompt(text) {
        return {
            contents: [{
                parts: [{
                    text: `分析订单文本：${text}`
                }]
            }]
        };
    }
    
    async _makeRequest(prompt) {
        const response = await fetch(`${this.baseUrl}/models/gemini-pro:generateContent?key=${this.apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(prompt)
        });
        
        if (!response.ok) {
            throw new Error(`Gemini API error: ${response.status}`);
        }
        
        return await response.json();
    }
    
    _parseResponse(response) {
        // 解析 Gemini 响应
        return response;
    }
    
    _getApiKey() {
        // 安全获取 API Key
        return 'your-api-key';
    }
    
    _handleError(error) {
        const logger = window.OTA.getService('logger');
        logger.logError('GeminiService', error);
    }
}
```

## 📊 性能优化

### 懒加载实现
```javascript
class LazyLoader {
    constructor() {
        this.loadedModules = new Set();
        this.loadingPromises = new Map();
        this.maxConcurrentLoads = 5;
        this.currentLoads = 0;
    }
    
    async loadModule(modulePath) {
        if (this.loadedModules.has(modulePath)) {
            return true;
        }
        
        if (this.loadingPromises.has(modulePath)) {
            return await this.loadingPromises.get(modulePath);
        }
        
        // 并发控制
        if (this.currentLoads >= this.maxConcurrentLoads) {
            await this._waitForSlot();
        }
        
        const loadPromise = this._performLoad(modulePath);
        this.loadingPromises.set(modulePath, loadPromise);
        
        try {
            await loadPromise;
            this.loadedModules.add(modulePath);
            return true;
        } finally {
            this.loadingPromises.delete(modulePath);
            this.currentLoads--;
        }
    }
    
    async _performLoad(modulePath) {
        this.currentLoads++;
        
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = modulePath;
            script.onload = () => resolve();
            script.onerror = () => reject(new Error(`Failed to load ${modulePath}`));
            document.head.appendChild(script);
        });
    }
    
    async _waitForSlot() {
        return new Promise(resolve => {
            const checkSlot = () => {
                if (this.currentLoads < this.maxConcurrentLoads) {
                    resolve();
                } else {
                    setTimeout(checkSlot, 50);
                }
            };
            checkSlot();
        });
    }
}
```

### 内存管理
```javascript
class MemoryManager {
    constructor() {
        this.componentPool = new Map();
        this.memoryThreshold = 50 * 1024 * 1024; // 50MB
        this.gcInterval = 60000; // 1分钟
        
        this.startMemoryMonitoring();
    }
    
    getFromPool(componentType) {
        const pool = this.componentPool.get(componentType);
        return pool && pool.length > 0 ? pool.pop() : null;
    }
    
    returnToPool(componentType, instance) {
        if (!this.componentPool.has(componentType)) {
            this.componentPool.set(componentType, []);
        }
        
        const pool = this.componentPool.get(componentType);
        if (pool.length < 10) { // 池大小限制
            instance.reset(); // 重置组件状态
            pool.push(instance);
        } else {
            instance.destroy(); // 销毁组件
        }
    }
    
    startMemoryMonitoring() {
        setInterval(() => {
            this.performGarbageCollection();
            this.checkMemoryUsage();
        }, this.gcInterval);
    }
    
    performGarbageCollection() {
        const now = Date.now();
        
        for (const [type, pool] of this.componentPool.entries()) {
            const activePool = pool.filter(instance => {
                const isExpired = now - instance._pooledAt > 300000; // 5分钟
                if (isExpired) {
                    instance.destroy();
                    return false;
                }
                return true;
            });
            this.componentPool.set(type, activePool);
        }
    }
    
    checkMemoryUsage() {
        if (performance.memory) {
            const used = performance.memory.usedJSHeapSize;
            if (used > this.memoryThreshold) {
                this.triggerAggressiveCleanup();
            }
        }
    }
    
    triggerAggressiveCleanup() {
        // 积极垃圾回收
        for (const pool of this.componentPool.values()) {
            pool.splice(0, Math.floor(pool.length / 2));
        }
    }
}
```

## 🧪 测试指南

### 单元测试
```javascript
// tests/unit/api-service.test.js
describe('APIService', () => {
    let apiService;
    
    beforeEach(() => {
        apiService = new APIService();
        apiService.init();
    });
    
    afterEach(() => {
        apiService.destroy();
    });
    
    describe('createOrder', () => {
        it('应该成功创建订单', async () => {
            const orderData = {
                customer_name: 'Test User',
                pickup_location: 'KLIA',
                dropoff_location: 'KL City'
            };
            
            const result = await apiService.createOrder(orderData);
            
            expect(result).toBeDefined();
            expect(result.status).toBe(true);
        });
        
        it('应该处理缺少必填字段的错误', async () => {
            const invalidData = {
                customer_name: 'Test User'
                // 缺少必填字段
            };
            
            await expect(apiService.createOrder(invalidData))
                .rejects.toThrow('必填字段缺失');
        });
    });
});
```

### 集成测试
```javascript
// tests/integration/order-flow.test.js
describe('订单处理流程', () => {
    beforeEach(() => {
        // 初始化测试环境
        window.OTA = {};
        initializeApplication();
    });
    
    it('应该完成完整的订单创建流程', async () => {
        const orderText = '从KLIA到KL City，明天下午2点，2个人';
        
        // AI分析
        const aiService = window.OTA.getService('geminiService');
        const analysisResult = await aiService.analyzeOrderText(orderText);
        
        // 表单填充
        const formManager = window.OTA.getService('formManager');
        formManager.fillFormFromAnalysis(analysisResult);
        
        // 订单提交
        const apiService = window.OTA.getService('apiService');
        const submitResult = await apiService.createOrder(formManager.getFormData());
        
        expect(submitResult.status).toBe(true);
    });
});
```

## 🛡️ 安全指南

### 输入验证
```javascript
class InputValidator {
    static validateOrderData(data) {
        const errors = [];
        
        // 必填字段检查
        const requiredFields = ['customer_name', 'pickup_location', 'dropoff_location'];
        for (const field of requiredFields) {
            if (!data[field] || data[field].trim() === '') {
                errors.push(`${field} 为必填字段`);
            }
        }
        
        // 数据类型检查
        if (data.passenger_number && !Number.isInteger(Number(data.passenger_number))) {
            errors.push('乘客人数必须为整数');
        }
        
        // 字符串长度检查
        if (data.customer_name && data.customer_name.length > 100) {
            errors.push('客户姓名不能超过100个字符');
        }
        
        // SQL注入防护
        const sqlInjectionPattern = /('|(\-\-)|(;)|(\||\|)|(\*|\*))/;
        for (const [key, value] of Object.entries(data)) {
            if (typeof value === 'string' && sqlInjectionPattern.test(value)) {
                errors.push(`${key} 包含非法字符`);
            }
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    
    static sanitizeInput(input) {
        if (typeof input !== 'string') return input;
        
        return input
            .replace(/[<>"'&]/g, (match) => {
                const entityMap = {
                    '<': '&lt;',
                    '>': '&gt;',
                    '"': '&quot;',
                    "'": '&#39;',
                    '&': '&amp;'
                };
                return entityMap[match];
            })
            .trim();
    }
}
```

### 错误处理
```javascript
class ErrorHandler {
    static handleError(error, context = {}) {
        const logger = window.OTA.getService('logger');
        
        // 错误分类
        let errorType = 'UNKNOWN';
        let userMessage = '系统错误，请稍后重试';
        
        if (error instanceof ValidationError) {
            errorType = 'VALIDATION';
            userMessage = error.message;
        } else if (error instanceof NetworkError) {
            errorType = 'NETWORK';
            userMessage = '网络连接失败，请检查网络连接';
        } else if (error instanceof APIError) {
            errorType = 'API';
            userMessage = '服务器错误，请稍后重试';
        }
        
        // 记录错误
        logger.logError(errorType, {
            message: error.message,
            stack: error.stack,
            context,
            timestamp: new Date().toISOString()
        });
        
        // 显示用户友好的错误信息
        const uiManager = window.OTA.getService('uiManager');
        uiManager.showError(userMessage);
        
        return {
            type: errorType,
            message: userMessage,
            originalError: error
        };
    }
}

// 全局错误捕获
window.addEventListener('error', (event) => {
    ErrorHandler.handleError(event.error, {
        source: 'global',
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
    });
});

window.addEventListener('unhandledrejection', (event) => {
    ErrorHandler.handleError(event.reason, {
        source: 'promise',
        type: 'unhandledrejection'
    });
});
```

## 📚 相关资源

### 文档链接
- [API参考文档](API-Reference.md)
- [架构设计指南](Architecture-Guide.md)
- [性能优化指南](Performance-Guide.md)
- [用户使用指南](User-Guide.md)

### 外部资源
- [Gemini API 文档](https://ai.google.dev/docs)
- [MDN Web Docs](https://developer.mozilla.org/)
- [ES6+ 特性指南](https://github.com/lukehoban/es6features)
- [JavaScript 性能优化](https://web.dev/performance/)

---
*开发指南版本: v1.0 | 最后更新: 2025-07-27*