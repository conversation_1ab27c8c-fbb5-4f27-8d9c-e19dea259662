# Gemini AI系统 API文档

## 📋 文档概述

本文档详细描述了重构后Gemini AI系统的API接口，包括主要服务类、方法签名、参数说明和使用示例。

**版本**: 2.0.0 (重构版本)  
**更新时间**: 2024-01-01  
**兼容性**: 完全向后兼容1.x版本

## 🚀 快速开始

### 基本使用
```javascript
// 获取Gemini服务实例
const geminiService = window.OTA.geminiService;

// 解析单个订单
const result = await geminiService.parseOrder(orderText);

// 解析多个订单
const results = await geminiService.parseMultipleOrders(multipleOrdersText);
```

### 高级配置
```javascript
// 配置实时分析
geminiService.configureRealtimeAnalysis({
    enabled: true,
    analysisDelay: 1500,
    minTextLength: 20
});

// 更新ID映射
geminiService.updateIdMappings({
    carTypes: [...],
    languages: [...]
});
```

## 📚 核心API接口

### 1. GeminiService 类

#### 1.1 parseOrder(orderText, isRealtime)
解析单个订单文本

**参数**:
- `orderText` (string): 订单文本内容
- `isRealtime` (boolean, 可选): 是否为实时分析模式，默认false

**返回值**: Promise<Object>
```javascript
{
    success: boolean,           // 解析是否成功
    data: {                    // 解析后的订单数据
        customerName: string,
        phoneNumber: string,
        pickupLocation: string,
        destination: string,
        // ... 其他字段
    },
    confidence: number,        // 置信度 (0-1)
    processingTime: number,    // 处理时间(毫秒)
    source: string,           // 处理来源 ('gemini' | 'fallback')
    error?: string            // 错误信息(如果有)
}
```

**使用示例**:
```javascript
const orderText = `
    客户姓名：张三
    联系电话：+60123456789
    接送地点：吉隆坡国际机场
    目的地：双子塔
    航班信息：MH123 15:30抵达
`;

const result = await geminiService.parseOrder(orderText);
if (result.success) {
    console.log('解析成功:', result.data);
} else {
    console.error('解析失败:', result.error);
}
```

#### 1.2 parseMultipleOrders(ordersText)
解析多个订单文本

**参数**:
- `ordersText` (string): 包含多个订单的文本

**返回值**: Promise<Array<Object>>
```javascript
[
    {
        orderIndex: number,    // 订单索引
        success: boolean,      // 解析是否成功
        data: Object,         // 订单数据
        confidence: number,   // 置信度
        // ... 其他字段
    },
    // ... 更多订单
]
```

#### 1.3 analyzeImage(imageData, options)
分析图片中的订单信息

**参数**:
- `imageData` (string): Base64编码的图片数据
- `options` (Object, 可选): 分析选项

**返回值**: Promise<Object>
```javascript
{
    success: boolean,
    extractedText: string,    // 提取的文本
    orderData: Object,        // 解析的订单数据
    confidence: number,       // 置信度
    processingTime: number
}
```

#### 1.4 getStatus()
获取服务状态

**返回值**: Object
```javascript
{
    version: string,          // 版本号
    isInitialized: boolean,   // 是否已初始化
    coordinatorStatus: string, // 协调器状态
    lastActivity: number,     // 最后活动时间
    statistics: {             // 统计信息
        totalRequests: number,
        successfulRequests: number,
        failedRequests: number,
        averageProcessingTime: number
    }
}
```

#### 1.5 configureRealtimeAnalysis(config)
配置实时分析功能

**参数**:
- `config` (Object): 配置对象
```javascript
{
    enabled: boolean,         // 是否启用
    analysisDelay: number,    // 分析延迟(毫秒)
    minTextLength: number,    // 最小文本长度
    maxConcurrent: number     // 最大并发数
}
```

#### 1.6 updateIdMappings(mappings)
更新ID映射数据

**参数**:
- `mappings` (Object): 映射数据
```javascript
{
    carTypes: Array,          // 车型数据
    languages: Array,         // 语言数据
    subCategories: Array,     // 子分类数据
    backendUsers: Array       // 后端用户数据
}
```

### 2. GeminiCoordinator 类

#### 2.1 processOrder(orderText, options)
处理订单（协调器直接接口）

**参数**:
- `orderText` (string): 订单文本
- `options` (Object): 处理选项
```javascript
{
    skipCache: boolean,       // 跳过缓存
    timeout: number,          // 超时时间
    priority: string,         // 优先级 ('high'|'normal'|'low')
    test: boolean            // 测试模式
}
```

#### 2.2 getMetrics()
获取性能指标

**返回值**: Object
```javascript
{
    processingStats: {
        totalRequests: number,
        successfulRequests: number,
        failedRequests: number,
        averageProcessingTime: number
    },
    cacheStats: {
        hits: number,
        misses: number,
        hitRate: number
    },
    concurrencyStats: {
        current: number,
        peak: number,
        queueLength: number
    }
}
```

## 🔧 高级功能

### 1. OTA渠道处理器

#### 获取特定渠道处理器
```javascript
const coordinator = window.OTA.gemini.getGeminiCoordinator();
const processor = await coordinator.getProcessor('agoda');
```

#### 支持的OTA渠道
- `agoda` - Agoda订单处理器
- `booking` - Booking.com订单处理器
- `chong-dealer` - Chong Dealer订单处理器
- `generic` - 通用订单处理器

### 2. 服务注册中心

#### 注册自定义服务
```javascript
const registry = window.OTA.Registry;
registry.registerService('customProcessor', processorInstance, 'PROCESSOR');
```

#### 获取服务
```javascript
const service = registry.getService('customProcessor');
```

### 3. 性能监控

#### 启用性能监控
```javascript
const monitor = window.OTA.gemini.performanceMonitor;
monitor.enable();
```

#### 获取性能报告
```javascript
const report = monitor.generateReport();
```

## ⚠️ 错误处理

### 常见错误类型
```javascript
// 输入验证错误
{
    error: 'INVALID_INPUT',
    message: '订单文本不能为空',
    code: 'E001'
}

// 处理超时错误
{
    error: 'TIMEOUT',
    message: '处理超时，请稍后重试',
    code: 'E002'
}

// 系统繁忙错误
{
    error: 'SYSTEM_BUSY',
    message: '系统繁忙，请稍后重试',
    code: 'E003'
}
```

### 错误处理最佳实践
```javascript
try {
    const result = await geminiService.parseOrder(orderText);
    if (!result.success) {
        // 处理业务逻辑错误
        console.error('解析失败:', result.error);
    }
} catch (error) {
    // 处理系统级错误
    console.error('系统错误:', error.message);
}
```

## 🧪 测试和调试

### 测试模式
```javascript
// 启用测试模式
const result = await geminiService.parseOrder(orderText, { test: true });
```

### 调试信息
```javascript
// 获取详细调试信息
const status = geminiService.getStatus();
console.log('调试信息:', status);
```

### 性能分析
```javascript
// 启用性能分析
const coordinator = window.OTA.gemini.getGeminiCoordinator();
coordinator.config.monitoring.enableProfiling = true;
```

## 📝 版本兼容性

### 2.0.0 新特性
- ✅ 模块化架构
- ✅ OTA渠道专业化处理
- ✅ 智能缓存机制
- ✅ 性能监控系统
- ✅ 增强的错误处理

### 向后兼容保证
- ✅ 所有1.x版本API接口保持不变
- ✅ 相同的方法签名和返回格式
- ✅ 配置参数完全兼容
- ✅ 事件系统保持一致

### 迁移指南
从1.x版本升级到2.0.0无需修改任何代码，所有现有功能将自动使用新的模块化架构。

---

**文档维护**: 随API更新同步维护  
**技术支持**: OTA系统开发团队  
**最后更新**: 2024-01-01
