/**
 * 服务定位器 - 统一服务获取接口
 * 解决当前系统中 window.OTA.xxx || window.xxx 的双重模式问题
 * 
 * 使用方式:
 * - 替换所有 getAppState() 为 getService('appState')
 * - 替换所有 getLogger() 为 getService('logger')
 * - 统一所有依赖获取方式
 */

// 确保OTA命名空间和依赖容器存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 服务定位器类
     * 提供统一的服务获取接口，兼容旧的获取方式
     */
    class ServiceLocator {
        constructor() {
            this.container = null;
            this.fallbackMap = new Map();
            this.migrationWarnings = new Set();
        }

        /**
         * 初始化服务定位器
         * @param {DependencyContainer} container - 依赖容器实例
         */
        init(container) {
            this.container = container;
            this.setupFallbackMap();
            this.registerCoreServices();
            console.log('✅ 服务定位器已初始化');
        }

        /**
         * 设置降级映射
         * 用于兼容旧的获取方式
         */
        setupFallbackMap() {
            // 核心服务
            this.fallbackMap.set('appState', () => window.OTA.appState || window.appState);
            this.fallbackMap.set('logger', () => window.OTA.logger || window.logger);
            this.fallbackMap.set('apiService', () => window.OTA.apiService || window.apiService);
            this.fallbackMap.set('geminiService', () => window.OTA.geminiService || window.geminiService);
            this.fallbackMap.set('uiManager', () => window.OTA.uiManager || window.uiManager);
            this.fallbackMap.set('utils', () => window.OTA.utils || window.utils);
            this.fallbackMap.set('i18nManager', () => window.OTA.i18nManager || window.i18nManager);
            
            // 功能管理器
            this.fallbackMap.set('formManager', () => window.OTA.formManager || window.formManager);
            this.fallbackMap.set('imageUploadManager', () => window.OTA.imageUploadManager || window.imageUploadManager);
            this.fallbackMap.set('currencyConverter', () => window.OTA.currencyConverter || window.currencyConverter);
            this.fallbackMap.set('multiOrderManager', () => window.OTA.multiOrderManager || window.multiOrderManager);
            this.fallbackMap.set('orderHistoryManager', () => window.OTA.orderHistoryManager || window.orderHistoryManager);
            this.fallbackMap.set('pagingServiceManager', () => window.OTA.pagingServiceManager || window.pagingServiceManager);
            this.fallbackMap.set('languageManager', () => window.OTA.languageManager || window.languageManager);
            this.fallbackMap.set('kimiService', () => window.OTA.kimiService || window.kimiService);
        }

        /**
         * 注册核心服务到依赖容器
         */
        registerCoreServices() {
            if (!this.container) return;

            // 注册核心服务工厂函数
            this.container.register('appState', () => {
                return window.OTA.appState || window.appState || this.createAppState();
            });

            this.container.register('logger', () => {
                return window.OTA.logger || window.logger || this.createLogger();
            });

            this.container.register('apiService', () => {
                return window.OTA.apiService || window.apiService || this.createApiService();
            });

            this.container.register('geminiService', () => {
                return window.OTA.geminiService || window.geminiService || this.createGeminiService();
            });

            this.container.register('uiManager', () => {
                return window.OTA.uiManager || window.uiManager || this.createUIManager();
            });

            this.container.register('utils', () => {
                return window.OTA.utils || window.utils || this.createUtils();
            });

            this.container.register('formManager', () => {
                return window.OTA.formManager || window.formManager || this.createFormManager();
            });

            this.container.register('languageManager', () => {
                return window.OTA.languageManager || window.getLanguageManager?.() || this.createLanguageManager();
            });

            this.container.register('kimiService', () => {
                return window.OTA.kimiService || window.getKimiService?.() || this.createKimiService();
            });
        }

        /**
         * 获取服务实例
         * @param {string} serviceName - 服务名称
         * @returns {any} 服务实例
         */
        getService(serviceName) {
            // 优先从依赖容器获取
            if (this.container && this.container.has(serviceName)) {
                try {
                    return this.container.get(serviceName);
                } catch (error) {
                    console.warn(`从依赖容器获取 ${serviceName} 失败，尝试降级方案:`, error.message);
                }
            }

            // 降级到旧的获取方式
            if (this.fallbackMap.has(serviceName)) {
                const service = this.fallbackMap.get(serviceName)();
                
                // 使用智能警告管理器发出迁移警告
                if (!this.migrationWarnings.has(serviceName)) {
                    const warningManager = window.OTA?.core?.warningManager;
                    if (warningManager) {
                        warningManager.warn(
                            'SERVICE_FALLBACK',
                            `服务 ${serviceName} 使用了降级获取方式，建议迁移到依赖容器`,
                            'WARNING',
                            { serviceName, fallbackUsed: true }
                        );
                    } else {
                        // 降级到原有警告方式
                        console.warn(`⚠️ 服务 ${serviceName} 使用了降级获取方式，建议迁移到依赖容器`);
                    }
                    this.migrationWarnings.add(serviceName);
                }
                
                return service;
            }

            // 最后尝试直接从全局获取
            let globalService = window.OTA[serviceName] || window[serviceName];

            // 🔧 修复重构遗留问题：支持嵌套路径查找
            // 处理 configManager 等服务的嵌套路径暴露
            if (!globalService && serviceName === 'configManager') {
                globalService = window.OTA?.gemini?.core?.configManager ||
                               window.OTA?.gemini?.core?.getConfigManager?.();
            }

            if (globalService) {
                const warningManager = window.OTA?.core?.warningManager;
                if (warningManager) {
                    warningManager.warn(
                        'SERVICE_GLOBAL_ACCESS',
                        `服务 ${serviceName} 从全局获取，建议注册到依赖容器`,
                        'WARNING',
                        { serviceName, globalAccess: true }
                    );
                } else {
                    // 降级到原有警告方式
                    console.warn(`⚠️ 服务 ${serviceName} 从全局获取，建议注册到依赖容器`);
                }
                return globalService;
            }

            throw new Error(`服务 ${serviceName} 未找到`);
        }

        /**
         * 检查服务是否可用
         * @param {string} serviceName - 服务名称
         * @returns {boolean}
         */
        hasService(serviceName) {
            try {
                const service = this.getService(serviceName);
                return !!service;
            } catch {
                return false;
            }
        }

        /**
         * 获取所有可用服务
         * @returns {string[]}
         */
        getAvailableServices() {
            const services = new Set();
            
            // 从依赖容器获取
            if (this.container) {
                this.container.getRegisteredServices().forEach(name => services.add(name));
            }
            
            // 从降级映射获取
            this.fallbackMap.forEach((_, name) => services.add(name));
            
            // 从全局获取
            Object.keys(window.OTA || {}).forEach(name => services.add(name));
            
            return Array.from(services);
        }

        /**
         * 创建默认的AppState实例
         */
        createAppState() {
            console.warn('创建默认AppState实例，建议使用正式的工厂函数');
            return new (window.AppState || class DefaultAppState {
                constructor() {
                    this.state = {};
                }
                get(path) { return path.split('.').reduce((obj, key) => obj?.[key], this.state); }
                set(path, value) { 
                    const keys = path.split('.');
                    const lastKey = keys.pop();
                    const target = keys.reduce((obj, key) => obj[key] = obj[key] || {}, this.state);
                    target[lastKey] = value;
                }
            })();
        }

        /**
         * 创建默认的Logger实例
         */
        createLogger() {
            // 移除警告日志：创建默认Logger实例的提示
            return {
                log: (message, level = 'info', data = null) => {
                    const levelStr = typeof level === 'string' ? level : 'info';
                    console.log(`[${levelStr.toUpperCase()}] ${message}`, data || '');
                },
                logError: (message, error) => {
                    console.error(`[ERROR] ${message}`, error);
                }
            };
        }

        /**
         * 创建默认的ApiService实例
         */
        createApiService() {
            // 移除警告日志：创建默认ApiService实例的提示
            return {
                isAvailable: () => false,
                createOrder: () => Promise.reject(new Error('ApiService not properly initialized'))
            };
        }

        /**
         * 创建默认的GeminiService实例
         */
        createGeminiService() {
            console.warn('创建默认GeminiService实例，建议使用正式的工厂函数');
            return {
                isAvailable: () => false,
                analyzeOrder: () => Promise.reject(new Error('GeminiService not properly initialized'))
            };
        }

        /**
         * 创建默认的UIManager实例
         */
        createUIManager() {
            console.warn('创建默认UIManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                getManager: () => null
            };
        }

        /**
         * 创建默认的Utils实例
         */
        createUtils() {
            console.warn('创建默认Utils实例，建议使用正式的工厂函数');
            return {
                debounce: (fn, delay) => fn,
                throttle: (fn, delay) => fn,
                formatDate: (date) => date.toString()
            };
        }

        /**
         * 创建默认的FormManager实例
         */
        createFormManager() {
            console.warn('创建默认FormManager实例，建议使用正式的工厂函数');
            return {
                setLanguageSelection: (languageIds) => {
                    console.log('FormManager: 设置语言选择', languageIds);
                },
                init: () => {},
                validate: () => true
            };
        }

        /**
         * 创建默认的LanguageManager实例
         */
        createLanguageManager() {
            console.warn('创建默认LanguageManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                getCurrentLanguage: () => 'en',
                setLanguage: (lang) => console.log('LanguageManager: 设置语言', lang),
                getAvailableLanguages: () => []
            };
        }

        /**
         * 创建默认的KimiService实例
         */
        createKimiService() {
            console.warn('创建默认KimiService实例，建议使用正式的工厂函数');
            return {
                isAvailable: () => false,
                analyzeOrder: () => Promise.reject(new Error('KimiService not properly initialized')),
                init: () => {}
            };
        }

        /**
         * 获取迁移状态报告
         * @returns {Object}
         */
        getMigrationReport() {
            return {
                totalServices: this.getAvailableServices().length,
                containerServices: this.container ? this.container.getRegisteredServices().length : 0,
                fallbackUsed: this.migrationWarnings.size,
                warnings: Array.from(this.migrationWarnings),
                recommendations: [
                    '将所有服务注册到依赖容器',
                    '替换直接的全局访问为 getService() 调用',
                    '移除双重获取模式 (window.OTA.xxx || window.xxx)'
                ]
            };
        }
    }

    // 创建全局服务定位器实例
    const serviceLocator = new ServiceLocator();

    // 等待依赖容器准备就绪后初始化
    if (window.OTA.container) {
        serviceLocator.init(window.OTA.container);
    } else {
        // 延迟初始化
        setTimeout(() => {
            if (window.OTA.container) {
                serviceLocator.init(window.OTA.container);
            }
        }, 100);
    }

    // 暴露到OTA命名空间
    window.OTA.serviceLocator = serviceLocator;

    // 提供统一的服务获取函数
    window.OTA.getService = function(serviceName) {
        return serviceLocator.getService(serviceName);
    };

    // 向后兼容的全局函数
    window.getService = window.OTA.getService;

    // 提供便捷的服务获取函数（逐步替换旧的获取方式）
    window.getAppState = () => {
        console.warn('[DEPRECATED] getAppState() 已废弃，请使用 window.OTA.getService("appState")');
        return serviceLocator.getService('appState');
    };
    window.getLogger = () => {
        console.warn('[DEPRECATED] getLogger() 已废弃，请使用 window.OTA.getService("logger")');
        return serviceLocator.getService('logger');
    };
    window.getAPIService = () => {
        console.warn('[DEPRECATED] getAPIService() 已废弃，请使用 window.OTA.getService("apiService")');
        return serviceLocator.getService('apiService');
    };
    window.getApiService = () => {
        console.warn('[DEPRECATED] getApiService() 已废弃，请使用 window.OTA.getService("apiService")');
        return serviceLocator.getService('apiService');
    };
    window.getGeminiService = () => {
        console.warn('[DEPRECATED] getGeminiService() 已废弃，请使用 window.OTA.getService("geminiService")');
        return serviceLocator.getService('geminiService');
    };
    window.getUIManager = () => {
        console.warn('[DEPRECATED] getUIManager() 已废弃，请使用 window.OTA.getService("uiManager")');
        return serviceLocator.getService('uiManager');
    };
    window.getUtils = () => {
        console.warn('[DEPRECATED] getUtils() 已废弃，请使用 window.OTA.getService("utils")');
        return serviceLocator.getService('utils');
    };
    window.getImageUploadManager = () => {
        console.warn('[DEPRECATED] getImageUploadManager() 已废弃，请使用 window.OTA.getService("imageUploadManager")');
        return serviceLocator.getService('imageUploadManager');
    };
    window.getCurrencyConverter = () => {
        console.warn('[DEPRECATED] getCurrencyConverter() 已废弃，请使用 window.OTA.getService("currencyConverter")');
        return serviceLocator.getService('currencyConverter');
    };
    window.getMultiOrderManager = () => {
        console.warn('[DEPRECATED] getMultiOrderManager() 已废弃，请使用 window.OTA.getService("multiOrderManager")');
        return serviceLocator.getService('multiOrderManager');
    };
    window.getOrderHistoryManager = () => {
        console.warn('[DEPRECATED] getOrderHistoryManager() 已废弃，请使用 window.OTA.getService("orderHistoryManager")');
        return serviceLocator.getService('orderHistoryManager');
    };
    window.getPagingServiceManager = () => {
        console.warn('[DEPRECATED] getPagingServiceManager() 已废弃，请使用 window.OTA.getService("pagingServiceManager")');
        return serviceLocator.getService('pagingServiceManager');
    };
    window.getI18nManager = () => {
        console.warn('[DEPRECATED] getI18nManager() 已废弃，请使用 window.OTA.getService("i18nManager")');
        return serviceLocator.getService('i18nManager');
    };
    window.getLanguageManager = () => {
        console.warn('[DEPRECATED] getLanguageManager() 已废弃，请使用 window.OTA.getService("languageManager")');
        return serviceLocator.getService('languageManager');
    };
    window.getKimiService = () => {
        console.warn('[DEPRECATED] getKimiService() 已废弃，请使用 window.OTA.getService("kimiService")');
        return serviceLocator.getService('kimiService');
    };

    console.log('✅ 服务定位器已加载');

})();
