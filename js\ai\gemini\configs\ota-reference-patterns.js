/**
 * @OTA_CONFIG OTA参考号规则配置
 * 🏷️ 标签: @OTA_REFERENCE_PATTERNS_CONFIG
 * 📝 说明: 统一管理所有OTA平台的参考号识别规则，支持配置热更新
 * 🎯 功能: 规则配置、优先级管理、排除规则、版本控制
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.configs = window.OTA.gemini.configs || {};

(function() {
    'use strict';

    /**
     * OTA参考号规则配置
     * 所有OTA平台的参考号识别规则集中管理
     */
    const otaReferencePatterns = {
        // 配置版本信息
        version: '1.0.0',
        lastUpdated: '2024-01-20',
        
        // 平台特定规则 (按优先级排序)
        platformRules: {
            'Chong Dealer': {
                patterns: [
                    // 高优先级：特定格式
                    /^CD[A-Z0-9]{6,12}$/i,
                    /^CHONG[A-Z0-9]{4,8}$/i,
                    /^崇[A-Z0-9]{4,8}$/,
                    
                    // 中优先级：带标识符的格式
                    /团号[:\s]*([A-Z0-9]{6,15})/i,
                    /确认号[:\s]*([A-Z0-9]{6,15})/i,
                    /崇德勒[:\s]*([A-Z0-9]{6,15})/i,
                    
                    // 低优先级：通用格式
                    /订单[:\s]*([A-Z0-9]{8,15})/i,
                    /编号[:\s]*([A-Z0-9]{8,15})/i
                ],
                priority: 10,
                description: 'Chong Dealer崇德勒专用格式',
                supportedLanguages: ['zh-CN', 'zh-TW', 'en'],
                confidenceBonus: 0.2, // 匹配成功时的置信度加成
                
                // 特殊处理规则
                specialRules: {
                    // 中文内容加成
                    chineseContentBonus: 0.1,
                    // 包含举牌服务关键词加成
                    signHoldingBonus: 0.1,
                    // 包车服务关键词加成
                    charterServiceBonus: 0.1
                }
            },

            'Klook': {
                patterns: [
                    /^KL[A-Z0-9]{8,12}$/i,
                    /^KLOOK[A-Z0-9]{4,8}$/i,
                    /^客路[A-Z0-9]{4,8}$/,
                    /Klook.*?([A-Z0-9]{8,15})/i,
                    /客路.*?([A-Z0-9]{8,15})/i,
                    /Booking\s*Reference[:\s]*([A-Z0-9]{8,15})/i,
                    /预订参考[:\s]*([A-Z0-9]{8,15})/i
                ],
                priority: 9,
                description: 'Klook客路专用格式',
                supportedLanguages: ['en', 'zh-CN', 'zh-TW', 'ja', 'ko'],
                confidenceBonus: 0.15,
                
                specialRules: {
                    englishContentBonus: 0.1,
                    productCodeBonus: 0.1,
                    emailFormatBonus: 0.05
                }
            },

            'Ctrip': {
                patterns: [
                    /CT[A-Z0-9]{8,15}/gi,
                    /CTRIP[A-Z0-9]{6,12}/gi,
                    /\d{10,18}/g,
                    /[A-Z]{2,4}\d{8,14}/gi,
                    /携程订单[：:]\s*([A-Z0-9]{8,20})/gi,
                    /订单号[：:]\s*([A-Z0-9]{8,20})/gi,
                    /确认号[：:]\s*([A-Z0-9]{8,20})/gi,
                    /产品订单号[：:]\s*([A-Z0-9]{8,20})/gi
                ],
                priority: 9,
                description: '携程Ctrip专用格式',
                supportedLanguages: ['zh-CN', 'en'],
                confidenceBonus: 0.15,

                specialRules: {
                    mixedLanguageBonus: 0.1,
                    ctripTermsBonus: 0.1,
                    longOrderNumberBonus: 0.05,
                    chineseLocationBonus: 0.1,
                    currencyConversionBonus: 0.05
                }
            },

            'KKday': {
                patterns: [
                    /^KK[A-Z0-9]{8,12}$/i,
                    /^KKDAY[A-Z0-9]{4,8}$/i,
                    /KKday.*?([A-Z0-9]{8,15})/i,
                    /訂單編號[:\s]*([A-Z0-9]{8,15})/i,
                    /Order\s*ID[:\s]*([A-Z0-9]{8,15})/i
                ],
                priority: 8,
                description: 'KKday专用格式',
                supportedLanguages: ['zh-TW', 'en', 'ja'],
                confidenceBonus: 0.12,
                
                specialRules: {
                    traditionalChineseBonus: 0.15,
                    taiwanRegionBonus: 0.1
                }
            },

            'Agoda': {
                patterns: [
                    /AG[A-Z0-9]{8,15}/gi,
                    /AGODA[A-Z0-9]{6,12}/gi,
                    /\d{8,16}/g,
                    /[A-Z]{2,4}\d{6,12}/gi,
                    /Agoda订单[：:]\s*([A-Z0-9]{8,20})/gi,
                    /预订号[：:]\s*([A-Z0-9]{8,20})/gi,
                    /确认号[：:]\s*([A-Z0-9]{8,20})/gi,
                    /Booking ID[：:]\s*([A-Z0-9]{8,20})/gi
                ],
                priority: 8,
                description: 'Agoda专用格式',
                supportedLanguages: ['en', 'zh-CN', 'th', 'vi', 'ms', 'ja', 'ko'],
                confidenceBonus: 0.12,

                specialRules: {
                    hotelBookingBonus: 0.1,
                    longStayBonus: 0.05,
                    regionSpecificBonus: 0.1,
                    multiCurrencyBonus: 0.05
                }
            },

            'Booking.com': {
                patterns: [
                    /BK[A-Z0-9]{8,15}/gi,
                    /BOOKING[A-Z0-9]{6,12}/gi,
                    /\d{9,18}/g,
                    /[A-Z]{2,4}\d{8,14}/gi,
                    /Booking\.com订单[：:]\s*([A-Z0-9]{8,20})/gi,
                    /预订确认号[：:]\s*([A-Z0-9]{8,20})/gi,
                    /Confirmation Number[：:]\s*([A-Z0-9]{8,20})/gi,
                    /Reservation Number[：:]\s*([A-Z0-9]{8,20})/gi
                ],
                priority: 8,
                description: 'Booking.com专用格式',
                supportedLanguages: ['en', 'zh-CN', 'de', 'fr', 'es', 'it', 'pt', 'nl'],
                confidenceBonus: 0.12,

                specialRules: {
                    accommodationBonus: 0.1,
                    pinCodeBonus: 0.15,
                    multiLanguageBonus: 0.05,
                    europeanLocationBonus: 0.1,
                    propertyTypeBonus: 0.05
                }
            },

            'Expedia': {
                patterns: [
                    /^EX[A-Z0-9]{8,12}$/i,
                    /^EXPEDIA[A-Z0-9]{4,8}$/i,
                    /Expedia.*?([A-Z0-9]{8,15})/i,
                    /Itinerary[:\s]*([A-Z0-9]{8,15})/i,
                    /行程编号[:\s]*([A-Z0-9]{8,15})/i
                ],
                priority: 7,
                description: 'Expedia专用格式',
                supportedLanguages: ['en', 'zh-CN'],
                confidenceBonus: 0.1,
                
                specialRules: {
                    itineraryBonus: 0.1,
                    flightHotelComboBonus: 0.05
                }
            }
        },

        // 通用目标模式 (优先级递减)
        genericPatterns: [
            {
                pattern: /^[A-Z]{2,4}[0-9]{6,10}$/,
                description: '字母前缀+数字组合',
                confidence: 0.7,
                minLength: 8,
                maxLength: 14
            },
            {
                pattern: /^[A-Z0-9]{8,15}$/,
                description: '字母数字混合',
                confidence: 0.6,
                minLength: 8,
                maxLength: 15
            },
            {
                pattern: /^[0-9]{8,12}$/,
                description: '纯数字',
                confidence: 0.5,
                minLength: 8,
                maxLength: 12
            },
            {
                pattern: /^[A-Z]{3,6}[0-9]{3,8}[A-Z]{0,3}$/,
                description: '复合字母数字',
                confidence: 0.65,
                minLength: 6,
                maxLength: 17
            }
        ],

        // 排除规则 - 这些内容不应被识别为参考号
        excludePatterns: [
            {
                pattern: /^[A-Za-z\s\u4e00-\u9fff]{2,50}$/,
                description: '客户姓名',
                reason: 'likely_customer_name'
            },
            {
                pattern: /^[\+]?[\d\s\-\(\)]{8,20}$/,
                description: '电话号码',
                reason: 'phone_number'
            },
            {
                pattern: /^[A-Z]{2,3}\d{3,4}$/,
                description: '航班号',
                reason: 'flight_number'
            },
            {
                pattern: /^\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}/,
                description: '日期格式',
                reason: 'date_format'
            },
            {
                pattern: /^\d{1,2}:\d{2}$/,
                description: '时间格式',
                reason: 'time_format'
            },
            {
                pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                description: '邮箱地址',
                reason: 'email_address'
            },
            {
                pattern: /^[A-Z]{3}?\s*\d+(\.\d{2})?$/,
                description: '价格金额',
                reason: 'price_amount'
            },
            {
                pattern: /^(接机|送机|包车|charter|pickup|dropoff)$/i,
                description: '服务类型',
                reason: 'service_type'
            },
            {
                pattern: /^\d+\s*(件|pcs|luggage)$/i,
                description: '行李数量',
                reason: 'luggage_count'
            },
            {
                pattern: /^\d+\s*(人|pax|passenger)$/i,
                description: '乘客数量',
                reason: 'passenger_count'
            },
            {
                pattern: /^(KLIA|KLIA2|SZB|BKI|JHB|PEN|TWU|SIN)$/i,
                description: '机场代码',
                reason: 'airport_code'
            }
        ],

        // 上下文增强规则
        contextRules: {
            // 前后文关键词增强识别
            contextKeywords: {
                before: ['订单', '确认', '参考', '编号', '团号', 'order', 'booking', 'reference', 'confirmation'],
                after: ['订单', '确认', '预订', 'booking', 'order', 'confirmation']
            },
            
            // 位置权重
            positionWeights: {
                beginning: 0.3,  // 文本开头
                middle: 0.1,     // 文本中间
                end: 0.2         // 文本结尾
            },
            
            // 格式权重
            formatWeights: {
                uppercase: 0.1,      // 全大写
                alphanumeric: 0.15,  // 字母数字混合
                withSeparator: 0.1,  // 包含分隔符
                inQuotes: 0.2        // 在引号内
            }
        },

        // 动态配置支持
        dynamicConfig: {
            enableHotReload: true,
            configUpdateInterval: 5 * 60 * 1000, // 5分钟检查一次配置更新
            fallbackToDefault: true,
            validateOnLoad: true
        },

        // 性能优化配置
        performanceConfig: {
            maxPatternsPerPlatform: 20,
            patternCacheSize: 100,
            enablePatternCache: true,
            timeoutMs: 5000
        },

        // 调试和监控配置
        debugConfig: {
            enableDetailedLogging: false,
            logMatchAttempts: false,
            collectStatistics: true,
            enablePerformanceMetrics: true
        }
    };

    /**
     * 配置验证函数
     * @param {Object} config - 配置对象
     * @returns {Object} 验证结果
     */
    function validateConfig(config) {
        const errors = [];
        const warnings = [];

        // 验证必需字段
        if (!config.platformRules || typeof config.platformRules !== 'object') {
            errors.push('缺少platformRules配置');
        }

        if (!config.genericPatterns || !Array.isArray(config.genericPatterns)) {
            errors.push('缺少genericPatterns配置');
        }

        // 验证平台规则
        for (const [platform, rules] of Object.entries(config.platformRules || {})) {
            if (!rules.patterns || !Array.isArray(rules.patterns)) {
                errors.push(`平台${platform}缺少patterns配置`);
            }

            if (typeof rules.priority !== 'number') {
                warnings.push(`平台${platform}缺少priority配置`);
            }

            // 验证正则表达式
            for (const pattern of rules.patterns || []) {
                try {
                    new RegExp(pattern);
                } catch (e) {
                    errors.push(`平台${platform}包含无效的正则表达式: ${pattern}`);
                }
            }
        }

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }

    /**
     * 获取配置的公共接口
     * @returns {Object} 配置对象
     */
    function getOTAReferencePatterns() {
        return JSON.parse(JSON.stringify(otaReferencePatterns)); // 深拷贝防止意外修改
    }

    /**
     * 更新配置
     * @param {Object} newConfig - 新配置
     * @returns {boolean} 更新是否成功
     */
    function updateOTAReferencePatterns(newConfig) {
        const validation = validateConfig(newConfig);
        
        if (!validation.isValid) {
            console.error('配置验证失败:', validation.errors);
            return false;
        }

        if (validation.warnings.length > 0) {
            console.warn('配置警告:', validation.warnings);
        }

        // 更新配置
        Object.assign(otaReferencePatterns, newConfig);
        otaReferencePatterns.lastUpdated = new Date().toISOString();
        
        console.log('OTA参考号规则配置已更新');
        return true;
    }

    // 暴露到全局命名空间
    window.OTA.gemini.configs.otaReferencePatterns = otaReferencePatterns;
    window.OTA.gemini.configs.getOTAReferencePatterns = getOTAReferencePatterns;
    window.OTA.gemini.configs.updateOTAReferencePatterns = updateOTAReferencePatterns;
    window.OTA.gemini.configs.validateOTAReferenceConfig = validateConfig;

    // 向后兼容
    window.getOTAReferencePatterns = getOTAReferencePatterns;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerConfig('otaReferencePatterns', otaReferencePatterns, '@OTA_REFERENCE_PATTERNS_CONFIG');
        window.OTA.Registry.registerFactory('getOTAReferencePatterns', getOTAReferencePatterns, '@OTA_REFERENCE_PATTERNS_FACTORY');
    }

    // 配置验证
    const validation = validateConfig(otaReferencePatterns);
    if (validation.isValid) {
        console.log('✅ OTA参考号规则配置已加载并验证通过');
        if (validation.warnings.length > 0) {
            console.warn('⚠️ 配置警告:', validation.warnings);
        }
    } else {
        console.error('❌ OTA参考号规则配置验证失败:', validation.errors);
    }

})();
