/**
 * @SERVICE 统一服务注册中心
 * 🏷️ 标签: @SERVICE_REGISTRY
 * 📝 说明: 统一管理所有OTA Gemini组件的注册、访问和生命周期管理
 * 🎯 功能: 服务注册、依赖注入、生命周期管理、服务发现
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.core = window.OTA.gemini.core || {};

(function() {
    'use strict';

    /**
     * 服务注册中心类
     * 提供统一的服务注册、发现和管理功能
     */
    class ServiceRegistry {
        constructor() {
            this.logger = window.getLogger?.() || console;
            
            // 服务注册表
            this.services = new Map();
            
            // 工厂函数注册表
            this.factories = new Map();
            
            // 服务实例缓存
            this.instances = new Map();
            
            // 服务依赖关系图
            this.dependencies = new Map();
            
            // 服务标签索引
            this.tagIndex = new Map();
            
            // 服务状态跟踪
            this.serviceStates = new Map();
            
            // 注册中心配置
            this.config = {
                // 是否启用自动依赖注入
                autoInject: true,
                
                // 是否启用循环依赖检测
                circularDependencyCheck: true,
                
                // 服务实例缓存策略
                cacheStrategy: 'singleton', // singleton | prototype | session
                
                // 是否启用服务健康检查
                healthCheck: true,
                
                // 健康检查间隔（毫秒）
                healthCheckInterval: 30000,
                
                // 是否启用性能监控
                performanceMonitoring: true
            };
            
            // 性能统计
            this.stats = {
                totalServices: 0,
                totalFactories: 0,
                totalInstances: 0,
                registrationTime: new Map(),
                accessCount: new Map(),
                errorCount: new Map(),
                lastHealthCheck: null
            };
            
            // 服务状态枚举
            this.ServiceState = {
                REGISTERED: 'registered',
                INITIALIZING: 'initializing',
                READY: 'ready',
                ERROR: 'error',
                DESTROYED: 'destroyed'
            };
            
            // 初始化注册中心
            this.initialize();
        }

        /**
         * 初始化注册中心
         */
        initialize() {
            this.logger.log('服务注册中心初始化开始', 'info');
            
            // 启动健康检查
            if (this.config.healthCheck) {
                this.startHealthCheck();
            }
            
            // 注册核心服务
            this.registerCoreServices();
            
            this.logger.log('服务注册中心初始化完成', 'info');
        }

        /**
         * 注册服务实例
         * @param {string} name - 服务名称
         * @param {Object} instance - 服务实例
         * @param {string} tag - 服务标签
         * @param {Object} options - 注册选项
         * @returns {boolean} 注册是否成功
         */
        registerService(name, instance, tag = '', options = {}) {
            try {
                const startTime = Date.now();
                
                // 验证参数
                if (!name || typeof name !== 'string') {
                    throw new Error('服务名称必须是非空字符串');
                }
                
                if (!instance) {
                    throw new Error('服务实例不能为空');
                }
                
                // 检查是否已注册
                if (this.services.has(name) && !options.override) {
                    this.logger.logWarning(`服务 ${name} 已存在，跳过注册`);
                    return false;
                }
                
                // 创建服务描述符
                const serviceDescriptor = {
                    name,
                    instance,
                    tag,
                    registeredAt: new Date().toISOString(),
                    options: { ...options },
                    dependencies: options.dependencies || [],
                    metadata: {
                        version: instance.version || '1.0.0',
                        type: typeof instance,
                        constructor: instance.constructor?.name || 'Unknown'
                    }
                };
                
                // 注册服务
                this.services.set(name, serviceDescriptor);
                this.instances.set(name, instance);
                this.serviceStates.set(name, this.ServiceState.REGISTERED);
                
                // 更新标签索引
                if (tag) {
                    this.updateTagIndex(tag, name);
                }
                
                // 记录依赖关系
                if (serviceDescriptor.dependencies.length > 0) {
                    this.dependencies.set(name, serviceDescriptor.dependencies);
                }
                
                // 更新统计
                this.stats.totalServices++;
                this.stats.totalInstances++;
                this.stats.registrationTime.set(name, Date.now() - startTime);
                this.stats.accessCount.set(name, 0);
                this.stats.errorCount.set(name, 0);
                
                this.logger.log(`服务 ${name} 注册成功 [标签: ${tag}]`, 'info');
                
                // 触发服务注册事件
                this.emitEvent('serviceRegistered', { name, tag, instance });
                
                return true;
                
            } catch (error) {
                this.logger.logError(`服务 ${name} 注册失败`, error);
                return false;
            }
        }

        /**
         * 注册工厂函数
         * @param {string} name - 工厂名称
         * @param {Function} factory - 工厂函数
         * @param {string} tag - 工厂标签
         * @param {Object} options - 注册选项
         * @returns {boolean} 注册是否成功
         */
        registerFactory(name, factory, tag = '', options = {}) {
            try {
                // 验证参数
                if (!name || typeof name !== 'string') {
                    throw new Error('工厂名称必须是非空字符串');
                }
                
                if (!factory || typeof factory !== 'function') {
                    throw new Error('工厂必须是函数');
                }
                
                // 检查是否已注册
                if (this.factories.has(name) && !options.override) {
                    this.logger.logWarning(`工厂 ${name} 已存在，跳过注册`);
                    return false;
                }
                
                // 创建工厂描述符
                const factoryDescriptor = {
                    name,
                    factory,
                    tag,
                    registeredAt: new Date().toISOString(),
                    options: { ...options },
                    metadata: {
                        type: 'factory',
                        parameters: factory.length
                    }
                };
                
                // 注册工厂
                this.factories.set(name, factoryDescriptor);
                
                // 更新标签索引
                if (tag) {
                    this.updateTagIndex(tag, name, 'factory');
                }
                
                // 更新统计
                this.stats.totalFactories++;
                
                this.logger.log(`工厂 ${name} 注册成功 [标签: ${tag}]`, 'info');
                
                // 触发工厂注册事件
                this.emitEvent('factoryRegistered', { name, tag, factory });
                
                return true;
                
            } catch (error) {
                this.logger.logError(`工厂 ${name} 注册失败`, error);
                return false;
            }
        }

        /**
         * 获取服务实例
         * @param {string} name - 服务名称
         * @param {Object} options - 获取选项
         * @returns {Object|null} 服务实例
         */
        getService(name, options = {}) {
            try {
                const startTime = Date.now();
                
                // 检查服务是否存在
                if (!this.services.has(name)) {
                    // 尝试通过工厂创建
                    if (this.factories.has(name)) {
                        return this.createFromFactory(name, options);
                    }
                    
                    this.logger.logWarning(`服务 ${name} 未找到`);
                    return null;
                }
                
                const serviceDescriptor = this.services.get(name);
                const instance = this.instances.get(name);
                
                // 更新访问统计
                const currentCount = this.stats.accessCount.get(name) || 0;
                this.stats.accessCount.set(name, currentCount + 1);
                
                // 检查服务状态
                const state = this.serviceStates.get(name);
                if (state === this.ServiceState.ERROR) {
                    this.logger.logError(`服务 ${name} 处于错误状态`);
                    return null;
                }
                
                // 性能监控
                if (this.config.performanceMonitoring) {
                    const accessTime = Date.now() - startTime;
                    this.recordPerformance(name, 'access', accessTime);
                }
                
                // 触发服务访问事件
                this.emitEvent('serviceAccessed', { name, instance, options });
                
                return instance;
                
            } catch (error) {
                this.logger.logError(`获取服务 ${name} 失败`, error);
                
                // 更新错误统计
                const currentErrorCount = this.stats.errorCount.get(name) || 0;
                this.stats.errorCount.set(name, currentErrorCount + 1);
                
                return null;
            }
        }

        /**
         * 通过工厂创建服务实例
         * @param {string} name - 工厂名称
         * @param {Object} options - 创建选项
         * @returns {Object|null} 创建的实例
         */
        createFromFactory(name, options = {}) {
            try {
                const factoryDescriptor = this.factories.get(name);
                if (!factoryDescriptor) {
                    return null;
                }
                
                const startTime = Date.now();
                
                // 调用工厂函数创建实例
                const instance = factoryDescriptor.factory(options);
                
                // 根据缓存策略决定是否缓存实例
                if (this.config.cacheStrategy === 'singleton') {
                    // 将创建的实例注册为服务
                    this.registerService(name, instance, factoryDescriptor.tag, {
                        createdFromFactory: true,
                        factoryOptions: options
                    });
                }
                
                // 性能监控
                if (this.config.performanceMonitoring) {
                    const creationTime = Date.now() - startTime;
                    this.recordPerformance(name, 'factory_creation', creationTime);
                }
                
                this.logger.log(`通过工厂创建服务实例 ${name}`, 'info');
                
                return instance;
                
            } catch (error) {
                this.logger.logError(`工厂创建服务 ${name} 失败`, error);
                return null;
            }
        }

        /**
         * 根据标签获取服务列表
         * @param {string} tag - 服务标签
         * @returns {Array} 服务列表
         */
        getServicesByTag(tag) {
            if (!this.tagIndex.has(tag)) {
                return [];
            }
            
            const serviceNames = this.tagIndex.get(tag);
            const services = [];
            
            for (const serviceName of serviceNames) {
                const instance = this.getService(serviceName);
                if (instance) {
                    services.push({
                        name: serviceName,
                        instance,
                        descriptor: this.services.get(serviceName)
                    });
                }
            }
            
            return services;
        }

        /**
         * 注销服务
         * @param {string} name - 服务名称
         * @returns {boolean} 注销是否成功
         */
        unregisterService(name) {
            try {
                if (!this.services.has(name)) {
                    this.logger.logWarning(`服务 ${name} 不存在，无法注销`);
                    return false;
                }
                
                const serviceDescriptor = this.services.get(name);
                
                // 检查依赖关系
                const dependents = this.findDependents(name);
                if (dependents.length > 0) {
                    this.logger.logWarning(`服务 ${name} 被其他服务依赖，无法注销: ${dependents.join(', ')}`);
                    return false;
                }
                
                // 调用服务的销毁方法（如果存在）
                const instance = this.instances.get(name);
                if (instance && typeof instance.destroy === 'function') {
                    instance.destroy();
                }
                
                // 从注册表中移除
                this.services.delete(name);
                this.instances.delete(name);
                this.serviceStates.delete(name);
                this.dependencies.delete(name);
                
                // 更新标签索引
                if (serviceDescriptor.tag) {
                    this.removeFromTagIndex(serviceDescriptor.tag, name);
                }
                
                // 更新统计
                this.stats.totalServices--;
                this.stats.totalInstances--;
                this.stats.accessCount.delete(name);
                this.stats.errorCount.delete(name);
                this.stats.registrationTime.delete(name);
                
                this.logger.log(`服务 ${name} 注销成功`, 'info');
                
                // 触发服务注销事件
                this.emitEvent('serviceUnregistered', { name, instance });
                
                return true;
                
            } catch (error) {
                this.logger.logError(`服务 ${name} 注销失败`, error);
                return false;
            }
        }

        /**
         * 检查循环依赖
         * @param {string} serviceName - 服务名称
         * @param {Set} visited - 已访问的服务
         * @param {Set} recursionStack - 递归栈
         * @returns {boolean} 是否存在循环依赖
         */
        checkCircularDependency(serviceName, visited = new Set(), recursionStack = new Set()) {
            if (recursionStack.has(serviceName)) {
                return true; // 发现循环依赖
            }
            
            if (visited.has(serviceName)) {
                return false; // 已经检查过，无循环依赖
            }
            
            visited.add(serviceName);
            recursionStack.add(serviceName);
            
            const dependencies = this.dependencies.get(serviceName) || [];
            for (const dependency of dependencies) {
                if (this.checkCircularDependency(dependency, visited, recursionStack)) {
                    return true;
                }
            }
            
            recursionStack.delete(serviceName);
            return false;
        }

        /**
         * 查找依赖某个服务的其他服务
         * @param {string} serviceName - 服务名称
         * @returns {Array} 依赖者列表
         */
        findDependents(serviceName) {
            const dependents = [];

            for (const [name, deps] of this.dependencies.entries()) {
                if (deps.includes(serviceName)) {
                    dependents.push(name);
                }
            }

            return dependents;
        }

        /**
         * 智能依赖解析（新增功能）
         * @param {string} serviceName - 服务名称
         * @param {Set} resolving - 正在解析的服务集合（用于循环依赖检测）
         * @returns {Array} 解析后的依赖实例列表
         */
        resolveDependencies(serviceName, resolving = new Set()) {
            // 循环依赖检测
            if (resolving.has(serviceName)) {
                const cycle = Array.from(resolving).concat(serviceName).join(' -> ');
                this.logger.logError(`检测到循环依赖: ${cycle}`);
                throw new Error(`循环依赖: ${cycle}`);
            }

            const dependencies = this.dependencies.get(serviceName) || [];
            const resolved = [];
            resolving.add(serviceName);

            try {
                for (const depName of dependencies) {
                    if (this.services.has(depName)) {
                        // 递归解析依赖的依赖
                        this.resolveDependencies(depName, new Set(resolving));
                        const depInstance = this.getService(depName);
                        if (depInstance) {
                            resolved.push({
                                name: depName,
                                instance: depInstance,
                                metadata: this.services.get(depName)
                            });
                        }
                    } else {
                        this.logger.logWarning(`依赖服务未找到: ${depName} (被${serviceName}依赖)`);
                    }
                }
            } finally {
                resolving.delete(serviceName);
            }

            return resolved;
        }

        /**
         * 更新标签索引
         * @param {string} tag - 标签
         * @param {string} name - 服务名称
         * @param {string} type - 类型
         */
        updateTagIndex(tag, name, type = 'service') {
            if (!this.tagIndex.has(tag)) {
                this.tagIndex.set(tag, new Set());
            }
            
            this.tagIndex.get(tag).add(name);
        }

        /**
         * 从标签索引中移除
         * @param {string} tag - 标签
         * @param {string} name - 服务名称
         */
        removeFromTagIndex(tag, name) {
            if (this.tagIndex.has(tag)) {
                this.tagIndex.get(tag).delete(name);
                
                // 如果标签下没有服务了，删除标签
                if (this.tagIndex.get(tag).size === 0) {
                    this.tagIndex.delete(tag);
                }
            }
        }

        /**
         * 启动健康检查
         */
        startHealthCheck() {
            setInterval(() => {
                this.performHealthCheck();
            }, this.config.healthCheckInterval);
        }

        /**
         * 执行健康检查
         */
        performHealthCheck() {
            this.stats.lastHealthCheck = new Date().toISOString();
            
            for (const [name, instance] of this.instances.entries()) {
                try {
                    // 如果服务有健康检查方法，调用它
                    if (typeof instance.healthCheck === 'function') {
                        const isHealthy = instance.healthCheck();
                        const currentState = isHealthy ? this.ServiceState.READY : this.ServiceState.ERROR;
                        this.serviceStates.set(name, currentState);
                    } else {
                        // 简单检查：服务实例是否存在
                        this.serviceStates.set(name, instance ? this.ServiceState.READY : this.ServiceState.ERROR);
                    }
                } catch (error) {
                    this.logger.logError(`服务 ${name} 健康检查失败`, error);
                    this.serviceStates.set(name, this.ServiceState.ERROR);
                }
            }
        }

        /**
         * 记录性能数据
         * @param {string} serviceName - 服务名称
         * @param {string} operation - 操作类型
         * @param {number} duration - 持续时间
         */
        recordPerformance(serviceName, operation, duration) {
            // 这里可以集成到性能监控系统
            this.logger.log(`性能记录: ${serviceName}.${operation} = ${duration}ms`, 'debug');
        }

        /**
         * 触发事件
         * @param {string} eventName - 事件名称
         * @param {Object} eventData - 事件数据
         */
        emitEvent(eventName, eventData) {
            // 简单的事件触发机制
            if (window.dispatchEvent) {
                const event = new CustomEvent(`ota.registry.${eventName}`, {
                    detail: eventData
                });
                window.dispatchEvent(event);
            }
        }

        /**
         * 注册核心服务
         */
        registerCoreServices() {
            // 注册注册中心自身
            this.registerService('serviceRegistry', this, '@SERVICE_REGISTRY', {
                core: true,
                singleton: true
            });
        }

        /**
         * 获取所有已注册的服务列表
         * @returns {Array} 服务列表
         */
        getAllServices() {
            const services = [];
            
            for (const [name, descriptor] of this.services.entries()) {
                services.push({
                    name,
                    tag: descriptor.tag,
                    state: this.serviceStates.get(name),
                    registeredAt: descriptor.registeredAt,
                    accessCount: this.stats.accessCount.get(name) || 0,
                    errorCount: this.stats.errorCount.get(name) || 0
                });
            }
            
            return services;
        }

        /**
         * 获取所有已注册的工厂列表
         * @returns {Array} 工厂列表
         */
        getAllFactories() {
            const factories = [];
            
            for (const [name, descriptor] of this.factories.entries()) {
                factories.push({
                    name,
                    tag: descriptor.tag,
                    registeredAt: descriptor.registeredAt,
                    parameters: descriptor.metadata.parameters
                });
            }
            
            return factories;
        }

        /**
         * 获取注册中心统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                ...this.stats,
                serviceStates: Object.fromEntries(this.serviceStates),
                tagCount: this.tagIndex.size,
                dependencyCount: this.dependencies.size,
                healthCheckEnabled: this.config.healthCheck,
                lastHealthCheck: this.stats.lastHealthCheck
            };
        }

        /**
         * 重置注册中心
         */
        reset() {
            // 注销所有服务（除了核心服务）
            const serviceNames = Array.from(this.services.keys());
            for (const name of serviceNames) {
                const descriptor = this.services.get(name);
                if (!descriptor.options.core) {
                    this.unregisterService(name);
                }
            }
            
            // 清理工厂
            this.factories.clear();
            
            // 重置统计
            this.stats = {
                totalServices: 1, // 保留注册中心自身
                totalFactories: 0,
                totalInstances: 1,
                registrationTime: new Map(),
                accessCount: new Map(),
                errorCount: new Map(),
                lastHealthCheck: null
            };
            
            this.logger.log('服务注册中心已重置', 'info');
        }
    }

    // 创建全局单例实例
    function getServiceRegistry() {
        if (!window.OTA.Registry) {
            window.OTA.Registry = new ServiceRegistry();
        }
        return window.OTA.Registry;
    }

    // 暴露到全局命名空间
    window.OTA.gemini.core.ServiceRegistry = ServiceRegistry;
    window.OTA.gemini.core.getServiceRegistry = getServiceRegistry;

    // 创建全局实例
    window.OTA.Registry = getServiceRegistry();

    // 向后兼容
    window.getServiceRegistry = getServiceRegistry;

    console.log('✅ 统一服务注册中心已加载');

})();
