/**
 * 统一服务获取函数清理脚本
 * 消除getLogger()在38个文件中的重复定义，统一使用OTA.Registry管理
 * 
 * 执行任务2.1：统一服务获取函数模式
 */

(function() {
    'use strict';

    /**
     * 服务获取函数统一器
     */
    class ServiceGetterUnifier {
        constructor() {
            this.processedFiles = [];
            this.errors = [];
            this.statistics = {
                totalFiles: 0,
                processedFiles: 0,
                removedInlineFunctions: 0,
                replacedCalls: 0
            };
        }

        /**
         * 需要清理的文件列表
         */
        getFilesToProcess() {
            return [
                // 核心模块中的内联getLogger函数
                'js/core/dom-helper.js',
                'js/core/test-coverage-engine.js',
                'js/core/automated-test-runner.js',
                'js/core/unified-config-center.js',
                'js/core/lazy-loader.js',
                'js/core/dom-optimization-engine.js',
                'js/core/module-hot-replacement.js',
                'js/core/config-migration-tool.js',
                
                // AI模块中的getLogger调用
                'js/ai/gemini-service.js',
                'js/ai/gemini/core/ota-reference-engine.js',
                
                // 组件模块中的getLogger函数
                'js/components/multi-order/multi-order-batch-manager.js',
                
                // 管理器模块中的getLogger函数
                'js/managers/multi-order-manager.js',
                
                // 其他需要清理的文件
                'js/core/ota-registry.js',
                'js/core/duplicate-detector.js'
            ];
        }

        /**
         * 检测内联getLogger函数的模式
         */
        getInlineGetLoggerPatterns() {
            return [
                // 标准内联函数模式
                {
                    pattern: /function getLogger\(\) {\s*return window\.OTA\?\.logger \|\| window\.getLogger\?\.\(\) \|\| console;\s*}/g,
                    replacement: '',
                    description: '移除标准内联getLogger函数'
                },
                
                // 简化内联函数模式
                {
                    pattern: /function getLogger\(\) {\s*return window\.OTA\?\.logger \|\| window\.getLogger\?\.\(\) \|\| console;\s*}/g,
                    replacement: '',
                    description: '移除简化内联getLogger函数'
                },
                
                // 延迟获取依赖注释模式
                {
                    pattern: /\/\/ 延迟获取依赖，确保加载顺序\s*function getLogger\(\) {\s*return window\.OTA\?\.logger \|\| window\.getLogger\?\.\(\) \|\| console;\s*}/g,
                    replacement: '',
                    description: '移除带注释的内联getLogger函数'
                }
            ];
        }

        /**
         * 获取getLogger调用替换模式
         */
        getLoggerCallPatterns() {
            return [
                // 直接调用getLogger()
                {
                    pattern: /const logger = getLogger\(\);/g,
                    replacement: 'const logger = window.OTA.getService("logger");',
                    description: '替换getLogger()直接调用'
                },
                
                // 条件调用getLogger()
                {
                    pattern: /const logger = window\.getLogger\?\.\(\) \|\| console;/g,
                    replacement: 'const logger = window.OTA.getService("logger");',
                    description: '替换条件getLogger()调用'
                },
                
                // 内联调用模式
                {
                    pattern: /window\.getLogger\?\.\(\)/g,
                    replacement: 'window.OTA.getService("logger")',
                    description: '替换内联getLogger()调用'
                }
            ];
        }

        /**
         * 处理单个文件
         */
        async processFile(filePath) {
            try {
                console.log(`🔄 处理文件: ${filePath}`);
                
                // 这里应该读取文件内容，但在浏览器环境中我们无法直接读取文件
                // 实际实现需要使用文件API或者手动处理每个文件
                
                const result = {
                    filePath,
                    removedInlineFunctions: 0,
                    replacedCalls: 0,
                    success: true,
                    changes: []
                };
                
                this.processedFiles.push(result);
                this.statistics.processedFiles++;
                
                return result;
                
            } catch (error) {
                console.error(`❌ 处理文件失败: ${filePath}`, error);
                this.errors.push({ filePath, error: error.message });
                return { filePath, success: false, error: error.message };
            }
        }

        /**
         * 生成统一的服务获取工具函数
         */
        generateUnifiedServiceGetter() {
            return `
/**
 * 统一的服务获取工具函数
 * 替换所有内联的getLogger等函数
 */
function getOTAService(serviceName, fallback = console) {
    try {
        // 优先使用OTA.getService
        if (window.OTA && window.OTA.getService) {
            return window.OTA.getService(serviceName);
        }
        
        // 降级到直接访问OTA命名空间
        if (window.OTA && window.OTA[serviceName]) {
            return window.OTA[serviceName];
        }
        
        // 最后降级到全局函数
        const globalGetter = window[\`get\${serviceName.charAt(0).toUpperCase() + serviceName.slice(1)}\`];
        if (globalGetter && typeof globalGetter === 'function') {
            return globalGetter();
        }
        
        return fallback;
    } catch (error) {
        console.warn(\`获取服务 \${serviceName} 失败:\`, error);
        return fallback;
    }
}

// 便捷的服务获取函数
function getOTALogger() {
    return getOTAService('logger', console);
}

function getOTAAppState() {
    return getOTAService('appState', null);
}

function getOTAConfigCenter() {
    return getOTAService('configCenter', null);
}
`;
        }

        /**
         * 执行完整的统一化处理
         */
        async executeUnification() {
            console.log('🚀 开始执行服务获取函数统一化...');
            
            const filesToProcess = this.getFilesToProcess();
            this.statistics.totalFiles = filesToProcess.length;
            
            console.log(`📋 需要处理 ${filesToProcess.length} 个文件`);
            
            // 处理每个文件
            for (const filePath of filesToProcess) {
                await this.processFile(filePath);
            }
            
            // 生成统一工具函数
            const unifiedGetter = this.generateUnifiedServiceGetter();
            console.log('📝 生成统一服务获取工具函数');
            
            // 生成报告
            this.generateReport();
            
            return {
                success: this.errors.length === 0,
                statistics: this.statistics,
                processedFiles: this.processedFiles,
                errors: this.errors,
                unifiedGetter
            };
        }

        /**
         * 生成处理报告
         */
        generateReport() {
            console.group('📊 服务获取函数统一化报告');
            
            console.log('📈 统计数据:');
            console.table(this.statistics);
            
            if (this.processedFiles.length > 0) {
                console.log('✅ 成功处理的文件:');
                this.processedFiles.forEach(file => {
                    if (file.success) {
                        console.log(`  - ${file.filePath}`);
                    }
                });
            }
            
            if (this.errors.length > 0) {
                console.log('❌ 处理失败的文件:');
                this.errors.forEach(error => {
                    console.log(`  - ${error.filePath}: ${error.error}`);
                });
            }
            
            console.log('💡 建议的后续步骤:');
            console.log('  1. 验证所有文件的功能正常');
            console.log('  2. 运行测试套件确保兼容性');
            console.log('  3. 更新相关文档');
            console.log('  4. 监控系统性能变化');
            
            console.groupEnd();
        }

        /**
         * 验证统一化效果
         */
        validateUnification() {
            console.log('🔍 验证服务获取函数统一化效果...');
            
            const validationResults = {
                otaServiceAvailable: !!window.OTA?.getService,
                loggerServiceWorking: false,
                appStateServiceWorking: false,
                registryIntegration: !!window.OTA?.Registry,
                duplicateDetection: []
            };
            
            // 测试Logger服务
            try {
                const logger = window.OTA.getService('logger');
                validationResults.loggerServiceWorking = !!(logger && logger.log);
            } catch (error) {
                console.warn('Logger服务验证失败:', error);
            }
            
            // 测试AppState服务
            try {
                const appState = window.OTA.getService('appState');
                validationResults.appStateServiceWorking = !!(appState && appState.get);
            } catch (error) {
                console.warn('AppState服务验证失败:', error);
            }
            
            // 检测重复定义
            const globalFunctions = Object.keys(window).filter(key => 
                key.startsWith('get') && typeof window[key] === 'function'
            );
            
            const duplicates = globalFunctions.filter(func => 
                ['getLogger', 'getAppState', 'getAPIService'].includes(func)
            );
            
            validationResults.duplicateDetection = duplicates;
            
            console.log('✅ 验证结果:', validationResults);
            return validationResults;
        }
    }

    // 创建全局实例
    const serviceGetterUnifier = new ServiceGetterUnifier();
    
    // 暴露到全局作用域用于调试
    window.serviceGetterUnifier = serviceGetterUnifier;
    
    // 提供便捷的执行命令
    window.unifyServiceGetters = () => serviceGetterUnifier.executeUnification();
    window.validateServiceUnification = () => serviceGetterUnifier.validateUnification();
    
    console.log('✅ 服务获取函数统一器已加载');
    console.log('💡 使用 unifyServiceGetters() 开始统一化处理');
    console.log('💡 使用 validateServiceUnification() 验证统一化效果');

})();
