/**
 * @OTA_SERVICE OTA参考号识别引擎
 * 🏷️ 标签: @OTA_REFERENCE_ENGINE
 * 📝 说明: 本地参考号识别系统，支持文本和图片输入，为每个OTA渠道定义特定的参考号格式规则
 * 🎯 功能: 智能识别、优先级匹配、多格式支持、降级处理
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.core = window.OTA.gemini.core || {};

(function() {
    'use strict';

    /**
     * OTA参考号识别引擎类
     * 负责智能识别各种OTA平台的参考号格式
     */
    class OTAReferenceEngine {
        constructor() {
            this.logger = window.getLogger?.() || console;
            
            // 加载参考号规则配置
            this.loadReferencePatterns();
            
            // 识别统计
            this.stats = {
                totalAttempts: 0,
                successfulMatches: 0,
                platformMatches: new Map(),
                fallbackUsed: 0
            };
        }

        /**
         * 加载参考号规则配置
         */
        loadReferencePatterns() {
            // 从配置文件加载，如果不存在则使用默认配置
            this.patterns = window.OTA?.gemini?.configs?.otaReferencePatterns || this.getDefaultPatterns();
        }

        /**
         * 获取默认参考号规则配置
         * @returns {Object} 默认规则配置
         */
        getDefaultPatterns() {
            return {
                // 平台特定规则 (按优先级排序)
                platformRules: {
                    'Chong Dealer': {
                        patterns: [
                            /^CD[A-Z0-9]{6,12}$/i,
                            /^CHONG[A-Z0-9]{4,8}$/i,
                            /^崇[A-Z0-9]{4,8}$/,
                            /团号[:\s]*([A-Z0-9]{6,15})/i,
                            /确认号[:\s]*([A-Z0-9]{6,15})/i
                        ],
                        priority: 10,
                        description: 'Chong Dealer专用格式'
                    },
                    'Klook': {
                        patterns: [
                            /^KL[A-Z0-9]{8,12}$/i,
                            /^KLOOK[A-Z0-9]{4,8}$/i,
                            /^客路[A-Z0-9]{4,8}$/,
                            /Klook.*?([A-Z0-9]{8,15})/i
                        ],
                        priority: 9,
                        description: 'Klook客路专用格式'
                    },
                    'Ctrip': {
                        patterns: [
                            /^CT[A-Z0-9]{8,12}$/i,
                            /^CTRIP[A-Z0-9]{4,8}$/i,
                            /^携程[A-Z0-9]{6,10}$/,
                            /订单号[:\s]*([A-Z0-9]{8,15})/i
                        ],
                        priority: 9,
                        description: '携程专用格式'
                    },
                    'KKday': {
                        patterns: [
                            /^KK[A-Z0-9]{8,12}$/i,
                            /^KKDAY[A-Z0-9]{4,8}$/i,
                            /KKday.*?([A-Z0-9]{8,15})/i
                        ],
                        priority: 8,
                        description: 'KKday专用格式'
                    },
                    'Agoda': {
                        patterns: [
                            /^AG[A-Z0-9]{8,12}$/i,
                            /^AGODA[A-Z0-9]{4,8}$/i,
                            /Agoda.*?([A-Z0-9]{8,15})/i
                        ],
                        priority: 8,
                        description: 'Agoda专用格式'
                    },
                    'Booking.com': {
                        patterns: [
                            /^BK[A-Z0-9]{8,12}$/i,
                            /^BOOKING[A-Z0-9]{4,8}$/i,
                            /Booking.*?([A-Z0-9]{8,15})/i
                        ],
                        priority: 8,
                        description: 'Booking.com专用格式'
                    }
                },

                // 通用目标模式
                genericPatterns: [
                    /^[A-Z]{2,4}[0-9]{6,10}$/,  // 字母+数字组合
                    /^[A-Z0-9]{8,15}$/,         // 字母数字混合
                    /^[0-9]{8,12}$/             // 纯数字
                ],

                // 排除规则 - 这些内容不应被识别为参考号
                excludePatterns: [
                    /^[A-Za-z\s\u4e00-\u9fff]{2,50}$/,  // 客户姓名
                    /^[\+]?[\d\s\-\(\)]{8,20}$/,        // 电话号码
                    /^[A-Z]{2,3}\d{3,4}$/,              // 航班号
                    /^\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}/, // 日期
                    /^\d{1,2}:\d{2}$/,                  // 时间
                    /^[^\s@]+@[^\s@]+\.[^\s@]+$/,       // 邮箱
                    /^[A-Z]{3}?\s*\d+(\.\d{2})?$/,      // 价格
                    /^(接机|送机|包车|charter|pickup|dropoff)$/i, // 服务类型
                    /^\d+\s*(件|pcs|luggage)$/i,        // 行李数量
                    /^\d+\s*(人|pax|passenger)$/i       // 乘客数量
                ]
            };
        }

        /**
         * 智能识别OTA参考号
         * @param {string} text - 输入文本
         * @param {string} [preferredPlatform] - 首选平台
         * @returns {Object} 识别结果
         */
        identifyReference(text, preferredPlatform = null) {
            this.stats.totalAttempts++;
            
            try {
                const cleanText = this.preprocessText(text);
                
                // 步骤1: 如果指定了首选平台，优先使用平台特定规则
                if (preferredPlatform && this.patterns.platformRules[preferredPlatform]) {
                    const platformResult = this.matchPlatformRules(cleanText, preferredPlatform);
                    if (platformResult.found) {
                        this.updateStats(preferredPlatform, true);
                        return platformResult;
                    }
                }

                // 步骤2: 遍历所有平台规则（按优先级排序）
                const sortedPlatforms = this.getSortedPlatforms();
                for (const platform of sortedPlatforms) {
                    const platformResult = this.matchPlatformRules(cleanText, platform);
                    if (platformResult.found) {
                        this.updateStats(platform, true);
                        return platformResult;
                    }
                }

                // 步骤3: 使用通用规则
                const genericResult = this.matchGenericRules(cleanText);
                if (genericResult.found) {
                    this.updateStats('generic', true);
                    return genericResult;
                }

                // 步骤4: 智能提取候选项
                const candidateResult = this.extractCandidates(cleanText);
                if (candidateResult.found) {
                    this.updateStats('candidate', true);
                    return candidateResult;
                }

                // 未找到匹配
                this.stats.fallbackUsed++;
                return {
                    found: false,
                    reference: null,
                    platform: null,
                    confidence: 0,
                    method: 'none',
                    message: '未找到有效的OTA参考号'
                };

            } catch (error) {
                this.logger.logError('OTA参考号识别失败', error);
                return {
                    found: false,
                    reference: null,
                    platform: null,
                    confidence: 0,
                    method: 'error',
                    error: error.message
                };
            }
        }

        /**
         * 预处理输入文本
         * @param {string} text - 原始文本
         * @returns {string} 清理后的文本
         */
        preprocessText(text) {
            if (!text || typeof text !== 'string') {
                return '';
            }

            return text
                .trim()
                .replace(/\s+/g, ' ')  // 标准化空格
                .replace(/[""'']/g, '"') // 标准化引号
                .replace(/[：]/g, ':');  // 标准化冒号
        }

        /**
         * 匹配平台特定规则
         * @param {string} text - 文本
         * @param {string} platform - 平台名称
         * @returns {Object} 匹配结果
         */
        matchPlatformRules(text, platform) {
            const platformRule = this.patterns.platformRules[platform];
            if (!platformRule) {
                return { found: false };
            }

            for (let i = 0; i < platformRule.patterns.length; i++) {
                const pattern = platformRule.patterns[i];
                const match = text.match(pattern);
                
                if (match) {
                    const reference = match[1] || match[0];
                    
                    // 验证候选项
                    if (this.isValidReference(reference)) {
                        return {
                            found: true,
                            reference: reference.trim().toUpperCase(),
                            platform: platform,
                            confidence: this.calculateConfidence(reference, platform, i),
                            method: 'platform_rule',
                            patternIndex: i,
                            description: platformRule.description
                        };
                    }
                }
            }

            return { found: false };
        }

        /**
         * 匹配通用规则
         * @param {string} text - 文本
         * @returns {Object} 匹配结果
         */
        matchGenericRules(text) {
            for (let i = 0; i < this.patterns.genericPatterns.length; i++) {
                const pattern = this.patterns.genericPatterns[i];
                const matches = text.match(new RegExp(pattern.source, 'g'));
                
                if (matches) {
                    for (const match of matches) {
                        if (this.isValidReference(match)) {
                            return {
                                found: true,
                                reference: match.trim().toUpperCase(),
                                platform: 'generic',
                                confidence: 0.6 - (i * 0.1),
                                method: 'generic_rule',
                                patternIndex: i
                            };
                        }
                    }
                }
            }

            return { found: false };
        }

        /**
         * 提取候选项
         * @param {string} text - 文本
         * @returns {Object} 提取结果
         */
        extractCandidates(text) {
            // 提取所有可能的候选项
            const candidates = [];
            
            // 提取字母数字组合
            const alphanumericMatches = text.match(/[A-Z0-9]{6,15}/gi) || [];
            candidates.push(...alphanumericMatches);
            
            // 提取冒号后的内容
            const colonMatches = text.match(/[:：]\s*([A-Z0-9]{6,15})/gi) || [];
            candidates.push(...colonMatches.map(m => m.replace(/[:：]\s*/, '')));

            // 评分和排序
            const scoredCandidates = candidates
                .map(candidate => ({
                    text: candidate.trim().toUpperCase(),
                    score: this.scoreCandidate(candidate)
                }))
                .filter(item => item.score > 0)
                .sort((a, b) => b.score - a.score);

            if (scoredCandidates.length > 0) {
                const best = scoredCandidates[0];
                return {
                    found: true,
                    reference: best.text,
                    platform: 'candidate',
                    confidence: Math.min(best.score / 100, 0.8),
                    method: 'candidate_extraction',
                    allCandidates: scoredCandidates.slice(0, 5)
                };
            }

            return { found: false };
        }

        /**
         * 验证参考号是否有效
         * @param {string} reference - 参考号
         * @returns {boolean} 是否有效
         */
        isValidReference(reference) {
            if (!reference || typeof reference !== 'string') {
                return false;
            }

            const cleanRef = reference.trim();
            
            // 长度检查
            if (cleanRef.length < 4 || cleanRef.length > 20) {
                return false;
            }

            // 排除规则检查
            for (const excludePattern of this.patterns.excludePatterns) {
                if (excludePattern.test(cleanRef)) {
                    return false;
                }
            }

            return true;
        }

        /**
         * 计算置信度
         * @param {string} reference - 参考号
         * @param {string} platform - 平台
         * @param {number} patternIndex - 模式索引
         * @returns {number} 置信度 (0-1)
         */
        calculateConfidence(reference, platform, patternIndex = 0) {
            let confidence = 0.5; // 基础置信度

            // 平台优先级加成
            const platformRule = this.patterns.platformRules[platform];
            if (platformRule) {
                confidence += (platformRule.priority / 10) * 0.3;
            }

            // 模式匹配加成 (越早匹配的模式置信度越高)
            confidence += (1 - patternIndex * 0.1) * 0.2;

            // 长度合理性加成
            const length = reference.length;
            if (length >= 8 && length <= 12) {
                confidence += 0.1;
            }

            // 格式规范性加成
            if (/^[A-Z0-9\-_]+$/.test(reference)) {
                confidence += 0.1;
            }

            return Math.min(confidence, 1.0);
        }

        /**
         * 候选项评分
         * @param {string} candidate - 候选项
         * @returns {number} 分数
         */
        scoreCandidate(candidate) {
            let score = 0;
            const cleanCandidate = candidate.trim().toUpperCase();

            // 基础分数
            if (cleanCandidate.length >= 6 && cleanCandidate.length <= 15) {
                score += 30;
            }

            // 格式分数
            if (/^[A-Z0-9\-_]+$/.test(cleanCandidate)) {
                score += 25;
            }

            // 字母数字组合分数
            if (/^[A-Z]{2,4}[0-9]{4,}$/.test(cleanCandidate)) {
                score += 20;
            }

            // 排除无效候选项
            if (this.isValidReference(cleanCandidate)) {
                score += 25;
            } else {
                score = 0;
            }

            return score;
        }

        /**
         * 获取按优先级排序的平台列表
         * @returns {Array} 排序后的平台列表
         */
        getSortedPlatforms() {
            return Object.keys(this.patterns.platformRules)
                .sort((a, b) => {
                    const priorityA = this.patterns.platformRules[a].priority || 0;
                    const priorityB = this.patterns.platformRules[b].priority || 0;
                    return priorityB - priorityA;
                });
        }

        /**
         * 更新统计信息
         * @param {string} platform - 平台名称
         * @param {boolean} success - 是否成功
         */
        updateStats(platform, success) {
            if (success) {
                this.stats.successfulMatches++;
                const count = this.stats.platformMatches.get(platform) || 0;
                this.stats.platformMatches.set(platform, count + 1);
            }
        }

        /**
         * 获取识别统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                ...this.stats,
                successRate: this.stats.totalAttempts > 0 ? 
                    (this.stats.successfulMatches / this.stats.totalAttempts * 100).toFixed(2) + '%' : '0%',
                platformDistribution: Object.fromEntries(this.stats.platformMatches)
            };
        }

        /**
         * 重置统计信息
         */
        resetStats() {
            this.stats = {
                totalAttempts: 0,
                successfulMatches: 0,
                platformMatches: new Map(),
                fallbackUsed: 0
            };
        }

        /**
         * 增强的OTA参考号提取器（从原gemini-service.js迁移）
         * @param {string} text - 输入文本
         * @param {string} otaType - OTA类型（可选）
         * @returns {string|null} 提取的参考号
         */
        enhancedOtaReferenceExtractor(text, otaType = null) {
            if (!text || typeof text !== 'string') {
                return null;
            }

            const cleanText = text.replace(/\s+/g, ' ').trim();
            this.logger.log(`开始OTA参考号提取: ${cleanText.substring(0, 100)}...`, 'info');

            try {
                // 步骤1: 使用平台特定规则（如果指定了平台）
                if (otaType && this.patterns.platformRules[otaType]) {
                    const result = this.matchPlatformRules(cleanText, otaType);
                    if (result.found) {
                        this.logger.log(`✅ 平台特定规则匹配: ${result.reference} (${otaType})`, 'info');
                        return result.reference;
                    }
                }

                // 步骤2: 使用通用目标模式
                const generalResult = this.matchGeneralRules(cleanText);
                if (generalResult.found) {
                    this.logger.log(`✅ 通用规则匹配: ${generalResult.reference}`, 'info');
                    return generalResult.reference;
                }

                // 步骤3: 智能候选项提取
                const potentialReferences = this.extractPotentialReferences(cleanText);
                const validCandidates = [];

                for (const word of potentialReferences) {
                    // 排除明显不是参考号的内容
                    if (this.shouldExcludeAsReference(word)) {
                        continue;
                    }

                    // 检查是否匹配目标模式
                    if (this.matchesTargetPattern(word)) {
                        const score = this.calculateReferenceScore(word, otaType);
                        validCandidates.push({ reference: word, score });
                    }
                }

                // 步骤4: 选择最佳候选项
                if (validCandidates.length > 0) {
                    validCandidates.sort((a, b) => b.score - a.score);
                    const bestCandidate = validCandidates[0];

                    if (bestCandidate.score >= 10) { // 最低置信度阈值
                        this.logger.log(`✅ 智能提取成功: ${bestCandidate.reference} (分数: ${bestCandidate.score})`, 'info');
                        return bestCandidate.reference;
                    }
                }

                this.logger.log('❌ 未找到有效的OTA参考号', 'warning');
                return null;

            } catch (error) {
                this.logger.logError('OTA参考号提取失败', error);
                return null;
            }
        }

        /**
         * 提取潜在的参考号候选项
         * @param {string} text - 输入文本
         * @returns {Array} 候选参考号数组
         */
        extractPotentialReferences(text) {
            // 使用多种分割方式提取候选项
            const separators = /[\s,，。；;：:\n\r\t]+/;
            const words = text.split(separators).filter(word => word.trim().length >= 4);

            // 额外提取：查找连续的字母数字组合
            const alphanumericPattern = /[A-Z0-9]{4,}/gi;
            const alphanumericMatches = text.match(alphanumericPattern) || [];

            // 合并并去重
            const allCandidates = [...new Set([...words, ...alphanumericMatches])];

            return allCandidates.filter(candidate =>
                candidate &&
                candidate.trim().length >= 4 &&
                candidate.trim().length <= 30
            );
        }

        /**
         * 检查是否应该排除某个候选项
         * @param {string} candidate - 候选参考号
         * @returns {boolean} 是否应该排除
         */
        shouldExcludeAsReference(candidate) {
            const cleanCandidate = candidate.trim();

            for (const [key, pattern] of Object.entries(this.patterns.excludePatterns)) {
                if (pattern.test(cleanCandidate)) {
                    this.logger.log(`排除候选项: ${cleanCandidate} (匹配排除规则: ${key})`, 'debug');
                    return true;
                }
            }

            return false;
        }

        /**
         * 检查候选项是否匹配目标模式
         * @param {string} candidate - 候选参考号
         * @returns {boolean} 是否匹配
         */
        matchesTargetPattern(candidate) {
            const cleanCandidate = candidate.trim().toUpperCase();

            for (const pattern of Object.values(this.patterns.targetPatterns)) {
                if (pattern.test(cleanCandidate)) {
                    return true;
                }
            }

            return false;
        }

        /**
         * 计算参考号候选项的分数
         * @param {string} candidate - 候选参考号
         * @param {string} otaType - OTA类型
         * @returns {number} 分数
         */
        calculateReferenceScore(candidate, otaType) {
            let score = 0;
            const cleanCandidate = candidate.trim().toUpperCase();

            // 基础得分：长度合理性
            if (cleanCandidate.length >= 6 && cleanCandidate.length <= 20) {
                score += 10;
            }

            // 字符组成得分
            const letterCount = (cleanCandidate.match(/[A-Z]/g) || []).length;
            const digitCount = (cleanCandidate.match(/\d/g) || []).length;

            if (letterCount >= 2 && digitCount >= 2) {
                score += 5; // 字母数字混合
            }

            // 平台特定得分
            if (otaType && this.patterns.platformRules[otaType]) {
                const platformRule = this.patterns.platformRules[otaType];
                for (const pattern of platformRule.patterns) {
                    if (pattern.test(cleanCandidate)) {
                        score += platformRule.priority * 5;
                        break;
                    }
                }
            }

            // 目标模式匹配得分
            for (const [key, pattern] of Object.entries(this.patterns.targetPatterns)) {
                if (pattern.test(cleanCandidate)) {
                    score += 8;
                    break;
                }
            }

            return score;
        }

        /**
         * 验证OTA参考号是否有效（从原gemini-service.js迁移）
         * @param {string} reference - 参考号
         * @returns {boolean} 是否有效
         */
        isValidOtaReference(reference) {
            if (!reference || typeof reference !== 'string') {
                return false;
            }

            const cleanRef = reference.trim().toUpperCase();

            // 基本长度检查
            if (cleanRef.length < 4 || cleanRef.length > 30) {
                return false;
            }

            // 检查是否匹配任何目标模式
            return this.matchesTargetPattern(cleanRef);
        }
    }

    // 创建全局实例
    function getOTAReferenceEngine() {
        if (!window.OTA.gemini.core.otaReferenceEngine) {
            window.OTA.gemini.core.otaReferenceEngine = new OTAReferenceEngine();
        }
        return window.OTA.gemini.core.otaReferenceEngine;
    }

    // 暴露到全局命名空间
    window.OTA.gemini.core.OTAReferenceEngine = OTAReferenceEngine;
    window.OTA.gemini.core.getOTAReferenceEngine = getOTAReferenceEngine;

    // 向后兼容
    window.getOTAReferenceEngine = getOTAReferenceEngine;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('otaReferenceEngine', getOTAReferenceEngine(), '@OTA_REFERENCE_ENGINE');
        window.OTA.Registry.registerFactory('getOTAReferenceEngine', getOTAReferenceEngine, '@OTA_REFERENCE_ENGINE_FACTORY');
    }

    console.log('✅ OTA参考号识别引擎已加载');

})();
