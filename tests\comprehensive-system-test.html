<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA系统综合测试 - 阶段3优化验证</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #4a5568;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .test-section {
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            background: #f7fafc;
        }
        .test-section h3 {
            color: #2d3748;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #cbd5e0;
        }
        .status-indicator.success {
            background: #48bb78;
        }
        .status-indicator.error {
            background: #f56565;
        }
        .status-indicator.warning {
            background: #ed8936;
        }
        .test-item {
            margin-bottom: 8px;
            padding: 8px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #4299e1;
            font-size: 14px;
        }
        .result {
            font-weight: bold;
            color: #2b6cb0;
        }
        .error {
            color: #e53e3e;
        }
        .success {
            color: #38a169;
        }
        .warning {
            color: #d69e2e;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .summary {
            background: #edf2f7;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .summary h3 {
            color: #2d3748;
            margin-bottom: 15px;
        }
        .metric {
            display: inline-block;
            margin: 5px 15px 5px 0;
            padding: 5px 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #4299e1;
        }
        .console-output {
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 OTA系统综合测试 - 阶段3优化验证</h1>
        
        <div class="summary">
            <h3>📊 测试概览</h3>
            <div class="metric">
                <strong>测试模块:</strong> <span id="totalModules">0</span>
            </div>
            <div class="metric">
                <strong>通过测试:</strong> <span id="passedTests">0</span>
            </div>
            <div class="metric">
                <strong>失败测试:</strong> <span id="failedTests">0</span>
            </div>
            <div class="metric">
                <strong>成功率:</strong> <span id="successRate">0%</span>
            </div>
        </div>

        <div class="test-grid">
            <div class="test-section">
                <h3>
                    <div class="status-indicator" id="utilsStatus"></div>
                    🔧 统一工具函数测试
                </h3>
                <div id="utilsTests"></div>
            </div>

            <div class="test-section">
                <h3>
                    <div class="status-indicator" id="namespaceStatus"></div>
                    🌐 命名空间测试
                </h3>
                <div id="namespaceTests"></div>
            </div>

            <div class="test-section">
                <h3>
                    <div class="status-indicator" id="compatibilityStatus"></div>
                    🔄 向后兼容性测试
                </h3>
                <div id="compatibilityTests"></div>
            </div>

            <div class="test-section">
                <h3>
                    <div class="status-indicator" id="performanceStatus"></div>
                    ⚡ 性能基线测试
                </h3>
                <div id="performanceTests"></div>
            </div>

            <div class="test-section">
                <h3>
                    <div class="status-indicator" id="loggingStatus"></div>
                    📝 日志系统测试
                </h3>
                <div id="loggingTests"></div>
            </div>

            <div class="test-section">
                <h3>
                    <div class="status-indicator" id="integrationStatus"></div>
                    🔗 集成功能测试
                </h3>
                <div id="integrationTests"></div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button onclick="runComprehensiveTest()">🚀 运行综合测试</button>
            <button onclick="runPerformanceTest()">⚡ 性能测试</button>
            <button onclick="runCompatibilityTest()">🔄 兼容性测试</button>
            <button onclick="clearResults()">🗑️ 清空结果</button>
        </div>

        <div class="console-output" id="consoleOutput" style="display: none;">
            <div style="margin-bottom: 10px;"><strong>控制台输出监控:</strong></div>
            <div id="consoleLog"></div>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="../js/services/logger.js"></script>
    <script src="../js/utils/utils.js"></script>
    <script src="../js/core/api-key-manager.js"></script>
    <script src="../js/components/multi-order/multi-order-utils.js"></script>

    <script>
        let testResults = {
            total: 0,
            passed: 0,
            failed: 0
        };

        let consoleMessages = [];
        let originalConsole = {
            log: console.log,
            warn: console.warn,
            error: console.error
        };

        // 拦截控制台输出
        function interceptConsole() {
            console.log = (...args) => {
                consoleMessages.push({ type: 'log', message: args.join(' '), time: new Date().toLocaleTimeString() });
                originalConsole.log.apply(console, args);
                updateConsoleOutput();
            };
            console.warn = (...args) => {
                consoleMessages.push({ type: 'warn', message: args.join(' '), time: new Date().toLocaleTimeString() });
                originalConsole.warn.apply(console, args);
                updateConsoleOutput();
            };
            console.error = (...args) => {
                consoleMessages.push({ type: 'error', message: args.join(' '), time: new Date().toLocaleTimeString() });
                originalConsole.error.apply(console, args);
                updateConsoleOutput();
            };
        }

        function updateConsoleOutput() {
            const consoleLog = document.getElementById('consoleLog');
            const output = document.getElementById('consoleOutput');
            
            if (consoleMessages.length > 0) {
                output.style.display = 'block';
                consoleLog.innerHTML = consoleMessages.slice(-20).map(msg => 
                    `<div style="color: ${msg.type === 'error' ? '#f56565' : msg.type === 'warn' ? '#ed8936' : '#e2e8f0'}">
                        [${msg.time}] ${msg.type.toUpperCase()}: ${msg.message}
                    </div>`
                ).join('');
                consoleLog.scrollTop = consoleLog.scrollHeight;
            }
        }

        function addTestResult(containerId, title, result, isSuccess = true) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = 'test-item';
            div.innerHTML = `
                <strong>${title}:</strong> 
                <span class="result ${isSuccess ? 'success' : 'error'}">${result}</span>
            `;
            container.appendChild(div);

            testResults.total++;
            if (isSuccess) {
                testResults.passed++;
            } else {
                testResults.failed++;
            }
            updateSummary();
        }

        function updateSummary() {
            document.getElementById('totalModules').textContent = 6;
            document.getElementById('passedTests').textContent = testResults.passed;
            document.getElementById('failedTests').textContent = testResults.failed;
            const rate = testResults.total > 0 ? Math.round((testResults.passed / testResults.total) * 100) : 0;
            document.getElementById('successRate').textContent = rate + '%';
        }

        function updateStatus(statusId, success) {
            const indicator = document.getElementById(statusId);
            indicator.className = `status-indicator ${success ? 'success' : 'error'}`;
        }

        function testUtilsFunctions() {
            const container = document.getElementById('utilsTests');
            container.innerHTML = '';
            let allSuccess = true;

            try {
                // 测试formatPrice
                const priceResult = window.OTA?.utils?.formatPrice(100, 'MYR');
                const priceSuccess = priceResult && priceResult.includes('RM');
                addTestResult('utilsTests', 'formatPrice函数', priceSuccess ? '✅ 正常' : '❌ 失败', priceSuccess);
                if (!priceSuccess) allSuccess = false;

                // 测试formatPhoneDisplay
                const phoneResult = window.OTA?.utils?.formatPhoneDisplay('+60123456789');
                const phoneSuccess = phoneResult && phoneResult.includes('***');
                addTestResult('utilsTests', 'formatPhoneDisplay函数', phoneSuccess ? '✅ 正常' : '❌ 失败', phoneSuccess);
                if (!phoneSuccess) allSuccess = false;

                // 测试日期格式化
                const dateResult = window.OTA?.utils?.formatDateForAPI('2024-01-15');
                const dateSuccess = dateResult && dateResult.includes('-');
                addTestResult('utilsTests', 'formatDateForAPI函数', dateSuccess ? '✅ 正常' : '❌ 失败', dateSuccess);
                if (!dateSuccess) allSuccess = false;

                // 测试验证函数
                const emailValid = window.OTA?.utils?.isValidEmail('<EMAIL>');
                addTestResult('utilsTests', 'isValidEmail函数', emailValid ? '✅ 正常' : '❌ 失败', emailValid);
                if (!emailValid) allSuccess = false;

            } catch (error) {
                addTestResult('utilsTests', '工具函数测试', `❌ 错误: ${error.message}`, false);
                allSuccess = false;
            }

            updateStatus('utilsStatus', allSuccess);
        }

        function testNamespace() {
            const container = document.getElementById('namespaceTests');
            container.innerHTML = '';
            let allSuccess = true;

            try {
                // 测试OTA命名空间
                const otaExists = typeof window.OTA === 'object';
                addTestResult('namespaceTests', 'OTA命名空间', otaExists ? '✅ 存在' : '❌ 缺失', otaExists);
                if (!otaExists) allSuccess = false;

                // 测试utils命名空间
                const utilsExists = window.OTA?.utils && typeof window.OTA.utils === 'object';
                addTestResult('namespaceTests', 'OTA.utils命名空间', utilsExists ? '✅ 存在' : '❌ 缺失', utilsExists);
                if (!utilsExists) allSuccess = false;

                // 测试API密钥管理器
                const apiKeyExists = window.OTA?.apiKeyManager;
                addTestResult('namespaceTests', 'API密钥管理器', apiKeyExists ? '✅ 存在' : '❌ 缺失', !!apiKeyExists);
                if (!apiKeyExists) allSuccess = false;

                // 测试多订单工具
                const multiOrderExists = window.OTA?.getMultiOrderUtils;
                addTestResult('namespaceTests', '多订单工具函数', multiOrderExists ? '✅ 存在' : '❌ 缺失', !!multiOrderExists);
                if (!multiOrderExists) allSuccess = false;

            } catch (error) {
                addTestResult('namespaceTests', '命名空间测试', `❌ 错误: ${error.message}`, false);
                allSuccess = false;
            }

            updateStatus('namespaceStatus', allSuccess);
        }

        function testCompatibility() {
            const container = document.getElementById('compatibilityTests');
            container.innerHTML = '';
            let allSuccess = true;

            try {
                // 测试向后兼容的utils访问
                const legacyUtils = window.utils;
                const hasDeprecationWarning = legacyUtils !== undefined;
                addTestResult('compatibilityTests', '向后兼容utils访问', hasDeprecationWarning ? '✅ 支持' : '❌ 不支持', hasDeprecationWarning);

                // 测试降级机制
                const originalUtils = window.OTA?.utils;
                window.OTA.utils = null; // 临时禁用
                
                const multiOrderUtils = window.OTA?.getMultiOrderUtils?.();
                const fallbackWorks = multiOrderUtils?.formatPrice(100, 'MYR');
                addTestResult('compatibilityTests', '降级机制', fallbackWorks ? '✅ 正常' : '❌ 失败', !!fallbackWorks);
                
                // 恢复
                window.OTA.utils = originalUtils;

                // 测试废弃警告
                let warningCaught = false;
                const originalWarn = console.warn;
                console.warn = (msg) => {
                    if (msg.includes('DEPRECATED')) {
                        warningCaught = true;
                    }
                    originalWarn(msg);
                };
                
                // 触发废弃警告
                try {
                    const _ = window.utils;
                } catch (e) {}
                
                console.warn = originalWarn;
                addTestResult('compatibilityTests', '废弃警告机制', warningCaught ? '✅ 正常' : '⚠️ 未触发', warningCaught);

            } catch (error) {
                addTestResult('compatibilityTests', '兼容性测试', `❌ 错误: ${error.message}`, false);
                allSuccess = false;
            }

            updateStatus('compatibilityStatus', allSuccess);
        }

        function testPerformance() {
            const container = document.getElementById('performanceTests');
            container.innerHTML = '';
            let allSuccess = true;

            try {
                // 测试函数调用性能
                const start = performance.now();
                for (let i = 0; i < 1000; i++) {
                    window.OTA?.utils?.formatPrice(100, 'MYR');
                }
                const end = performance.now();
                const duration = end - start;
                
                addTestResult('performanceTests', '1000次价格格式化', `${duration.toFixed(2)}ms`, duration < 100);
                if (duration >= 100) allSuccess = false;

                // 测试内存使用
                const memoryInfo = performance.memory;
                if (memoryInfo) {
                    const usedMB = Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024);
                    addTestResult('performanceTests', 'JS堆内存使用', `${usedMB}MB`, usedMB < 50);
                    if (usedMB >= 50) allSuccess = false;
                }

                // 测试脚本加载数量
                const scriptCount = document.querySelectorAll('script[src]').length;
                addTestResult('performanceTests', '加载脚本数量', `${scriptCount}个`, scriptCount < 20);
                if (scriptCount >= 20) allSuccess = false;

            } catch (error) {
                addTestResult('performanceTests', '性能测试', `❌ 错误: ${error.message}`, false);
                allSuccess = false;
            }

            updateStatus('performanceStatus', allSuccess);
        }

        function testLogging() {
            const container = document.getElementById('loggingTests');
            container.innerHTML = '';
            let allSuccess = true;

            try {
                // 清空之前的消息
                consoleMessages = [];

                // 测试Logger系统
                const logger = window.getLogger?.();
                const loggerExists = !!logger;
                addTestResult('loggingTests', 'Logger系统', loggerExists ? '✅ 存在' : '❌ 缺失', loggerExists);
                if (!loggerExists) allSuccess = false;

                // 测试日志级别控制
                if (logger) {
                    logger.log('测试信息', 'info');
                    logger.log('测试警告', 'warning');
                    logger.logError('测试错误', new Error('测试'));
                }

                // 检查控制台输出数量
                setTimeout(() => {
                    const logCount = consoleMessages.length;
                    addTestResult('loggingTests', '日志输出控制', `${logCount}条消息`, logCount < 10);
                    if (logCount >= 10) allSuccess = false;
                    updateStatus('loggingStatus', allSuccess);
                }, 100);

            } catch (error) {
                addTestResult('loggingTests', '日志系统测试', `❌ 错误: ${error.message}`, false);
                allSuccess = false;
                updateStatus('loggingStatus', allSuccess);
            }
        }

        function testIntegration() {
            const container = document.getElementById('integrationTests');
            container.innerHTML = '';
            let allSuccess = true;

            try {
                // 测试工具函数集成
                const multiOrderUtils = window.OTA?.getMultiOrderUtils?.();
                if (multiOrderUtils) {
                    const priceResult = multiOrderUtils.formatPrice(100, 'MYR');
                    const priceSuccess = priceResult && priceResult.includes('RM');
                    addTestResult('integrationTests', '多订单工具集成', priceSuccess ? '✅ 正常' : '❌ 失败', priceSuccess);
                    if (!priceSuccess) allSuccess = false;
                }

                // 测试API密钥管理集成
                const apiKeyManager = window.OTA?.apiKeyManager;
                if (apiKeyManager) {
                    const geminiKey = apiKeyManager.getApiKey('gemini');
                    const keyExists = !!geminiKey;
                    addTestResult('integrationTests', 'API密钥管理集成', keyExists ? '✅ 正常' : '❌ 失败', keyExists);
                    if (!keyExists) allSuccess = false;
                }

                // 测试全局函数访问
                const globalLogger = window.getLogger?.();
                const globalAccess = !!globalLogger;
                addTestResult('integrationTests', '全局函数访问', globalAccess ? '✅ 正常' : '❌ 失败', globalAccess);
                if (!globalAccess) allSuccess = false;

            } catch (error) {
                addTestResult('integrationTests', '集成测试', `❌ 错误: ${error.message}`, false);
                allSuccess = false;
            }

            updateStatus('integrationStatus', allSuccess);
        }

        function runComprehensiveTest() {
            // 重置测试结果
            testResults = { total: 0, passed: 0, failed: 0 };
            consoleMessages = [];
            
            // 启动控制台拦截
            interceptConsole();
            
            // 运行所有测试
            testUtilsFunctions();
            testNamespace();
            testCompatibility();
            testPerformance();
            testLogging();
            testIntegration();
            
            // 显示控制台输出
            setTimeout(() => {
                if (consoleMessages.length > 0) {
                    document.getElementById('consoleOutput').style.display = 'block';
                }
            }, 500);
        }

        function runPerformanceTest() {
            testResults = { total: 0, passed: 0, failed: 0 };
            testPerformance();
        }

        function runCompatibilityTest() {
            testResults = { total: 0, passed: 0, failed: 0 };
            testCompatibility();
        }

        function clearResults() {
            ['utilsTests', 'namespaceTests', 'compatibilityTests', 'performanceTests', 'loggingTests', 'integrationTests'].forEach(id => {
                document.getElementById(id).innerHTML = '';
            });
            
            ['utilsStatus', 'namespaceStatus', 'compatibilityStatus', 'performanceStatus', 'loggingStatus', 'integrationStatus'].forEach(id => {
                document.getElementById(id).className = 'status-indicator';
            });
            
            testResults = { total: 0, passed: 0, failed: 0 };
            consoleMessages = [];
            updateSummary();
            document.getElementById('consoleOutput').style.display = 'none';
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            setTimeout(() => {
                updateSummary();
            }, 1000);
        });
    </script>
</body>
</html>
