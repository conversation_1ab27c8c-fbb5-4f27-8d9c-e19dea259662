/**
 * @OTA_CORE 模块懒加载系统
 * 🏷️ 标签: @LAZY_LOADER @MODULE_LOADING @PERFORMANCE_OPTIMIZATION
 * 📝 说明: 实现按需加载的模块系统，提升初始加载性能和用户体验
 * ⚠️ 警告: 核心性能优化基础设施，请勿重复开发
 */

(function() {
    'use strict';

    // 延迟获取依赖，确保加载顺序
    function getConfigCenter() {
        return window.OTA?.configCenter || window.getConfigCenter?.();
    }

    function getDependencyResolver() {
        return window.OTA?.dependencyResolver || window.getDependencyResolver?.();
    }

    /**
     * @OTA_CORE 模块懒加载器类
     * 提供按需加载、依赖管理和性能优化功能
     */
    class LazyLoader {
        constructor() {
            this.logger = window.OTA.getService('logger');
            this.configCenter = getConfigCenter();
            this.dependencyResolver = getDependencyResolver();
            
            // 模块注册表
            this.moduleRegistry = new Map();
            
            // 加载状态跟踪
            this.loadingStates = new Map();
            
            // 加载队列
            this.loadingQueue = [];
            
            // 缓存已加载的模块
            this.loadedModules = new Map();
            
            // 加载配置
            this.loadConfig = this.loadLazyLoadConfig();
            
            // 性能监控
            this.performanceMetrics = {
                totalRequests: 0,
                cacheHits: 0,
                loadTimes: [],
                failedLoads: 0,
                concurrentLoads: 0,
                maxConcurrentLoads: 0
            };
            
            // 加载策略
            this.loadingStrategies = new Map();
            
            // 预加载队列
            this.preloadQueue = [];
            
            // 错误处理器
            this.errorHandlers = new Map();
            
            this.initialize();
            
            this.logger.log('✅ 模块懒加载系统已初始化', 'info', {
                registeredModules: this.moduleRegistry.size,
                loadingStrategies: this.loadingStrategies.size,
                preloadEnabled: this.loadConfig.preload.enabled
            });
        }

        /**
         * 加载懒加载配置
         * @returns {Object} 懒加载配置
         */
        loadLazyLoadConfig() {
            const defaultConfig = {
                // 基础配置
                enabled: true,
                timeout: 30000, // 30秒超时
                retryAttempts: 3,
                retryDelay: 1000,
                
                // 并发控制
                maxConcurrentLoads: 5,
                loadingDelay: 0, // 加载延迟
                
                // 缓存配置
                cache: {
                    enabled: true,
                    maxSize: 100,
                    ttl: 3600000 // 1小时
                },
                
                // 预加载配置
                preload: {
                    enabled: true,
                    idleTimeout: 2000, // 空闲2秒后开始预加载
                    maxPreloadItems: 10
                },
                
                // 性能优化
                performance: {
                    monitoring: true,
                    compression: false,
                    bundling: false
                },
                
                // 错误处理
                errorHandling: {
                    fallbackToSync: true,
                    logErrors: true,
                    showUserErrors: false
                }
            };

            if (this.configCenter) {
                const lazyConfig = this.configCenter.getConfig('lazyLoading') || {};
                return {
                    ...defaultConfig,
                    ...lazyConfig
                };
            }

            return defaultConfig;
        }

        /**
         * 初始化懒加载系统
         */
        initialize() {
            // 注册默认加载策略
            this.registerLoadingStrategies();
            
            // 设置预加载机制
            if (this.loadConfig.preload.enabled) {
                this.setupPreloading();
            }
            
            // 设置性能监控
            if (this.loadConfig.performance.monitoring) {
                this.setupPerformanceMonitoring();
            }
            
            // 注册默认错误处理器
            this.setupDefaultErrorHandlers();
        }

        /**
         * 注册模块
         * @param {string} moduleId - 模块ID
         * @param {Object} moduleConfig - 模块配置
         */
        registerModule(moduleId, moduleConfig) {
            const config = {
                id: moduleId,
                path: moduleConfig.path,
                dependencies: moduleConfig.dependencies || [],
                priority: moduleConfig.priority || 'normal',
                strategy: moduleConfig.strategy || 'default',
                preload: moduleConfig.preload || false,
                cache: moduleConfig.cache !== false,
                timeout: moduleConfig.timeout || this.loadConfig.timeout,
                retryAttempts: moduleConfig.retryAttempts || this.loadConfig.retryAttempts,
                ...moduleConfig
            };

            this.moduleRegistry.set(moduleId, config);
            
            // 如果标记为预加载，添加到预加载队列
            if (config.preload) {
                this.preloadQueue.push(moduleId);
            }
            
            this.logger.log(`模块已注册: ${moduleId}`, 'debug', config);
        }

        /**
         * 懒加载模块
         * @param {string} moduleId - 模块ID
         * @param {Object} options - 加载选项
         * @returns {Promise<any>} 加载的模块
         */
        async loadModule(moduleId, options = {}) {
            this.performanceMetrics.totalRequests++;
            
            // 检查缓存
            if (this.loadedModules.has(moduleId)) {
                this.performanceMetrics.cacheHits++;
                this.logger.log(`模块缓存命中: ${moduleId}`, 'debug');
                return this.loadedModules.get(moduleId);
            }
            
            // 检查是否正在加载
            if (this.loadingStates.has(moduleId)) {
                this.logger.log(`模块正在加载，等待完成: ${moduleId}`, 'debug');
                return this.loadingStates.get(moduleId);
            }
            
            // 获取模块配置
            const moduleConfig = this.moduleRegistry.get(moduleId);
            if (!moduleConfig) {
                throw new Error(`未注册的模块: ${moduleId}`);
            }
            
            // 检查并发限制
            if (this.performanceMetrics.concurrentLoads >= this.loadConfig.maxConcurrentLoads) {
                this.logger.log(`达到并发限制，模块加入队列: ${moduleId}`, 'debug');
                return this.queueModuleLoad(moduleId, options);
            }
            
            // 开始加载
            const loadPromise = this.performModuleLoad(moduleId, moduleConfig, options);
            this.loadingStates.set(moduleId, loadPromise);
            
            try {
                const module = await loadPromise;
                
                // 缓存模块
                if (moduleConfig.cache) {
                    this.loadedModules.set(moduleId, module);
                }
                
                // 清理加载状态
                this.loadingStates.delete(moduleId);
                
                // 处理队列中的下一个模块
                this.processLoadingQueue();
                
                return module;
                
            } catch (error) {
                this.loadingStates.delete(moduleId);
                this.performanceMetrics.failedLoads++;
                
                // 处理队列中的下一个模块
                this.processLoadingQueue();
                
                throw error;
            }
        }

        /**
         * 执行模块加载
         * @param {string} moduleId - 模块ID
         * @param {Object} moduleConfig - 模块配置
         * @param {Object} options - 加载选项
         * @returns {Promise<any>} 加载的模块
         */
        async performModuleLoad(moduleId, moduleConfig, options) {
            const startTime = performance.now();
            this.performanceMetrics.concurrentLoads++;
            this.performanceMetrics.maxConcurrentLoads = Math.max(
                this.performanceMetrics.maxConcurrentLoads,
                this.performanceMetrics.concurrentLoads
            );
            
            try {
                this.logger.log(`开始加载模块: ${moduleId}`, 'info');
                
                // 加载依赖
                await this.loadDependencies(moduleConfig.dependencies);
                
                // 获取加载策略
                const strategy = this.loadingStrategies.get(moduleConfig.strategy) || 
                                this.loadingStrategies.get('default');
                
                // 执行加载
                const module = await strategy.load(moduleConfig, options);
                
                const endTime = performance.now();
                const loadTime = endTime - startTime;
                this.performanceMetrics.loadTimes.push(loadTime);
                
                this.logger.log(`模块加载完成: ${moduleId} (${loadTime.toFixed(2)}ms)`, 'info');
                
                return module;
                
            } catch (error) {
                const errorHandler = this.errorHandlers.get(moduleConfig.strategy) || 
                                   this.errorHandlers.get('default');
                
                return await errorHandler.handle(moduleId, moduleConfig, error, options);
                
            } finally {
                this.performanceMetrics.concurrentLoads--;
            }
        }

        /**
         * 加载依赖模块
         * @param {Array} dependencies - 依赖列表
         * @returns {Promise<Array>} 加载的依赖
         */
        async loadDependencies(dependencies) {
            if (!dependencies || dependencies.length === 0) {
                return [];
            }
            
            this.logger.log(`加载依赖: ${dependencies.join(', ')}`, 'debug');
            
            // 并行加载依赖
            const dependencyPromises = dependencies.map(dep => this.loadModule(dep));
            
            try {
                return await Promise.all(dependencyPromises);
            } catch (error) {
                this.logger.logError('依赖加载失败', error);
                throw new Error(`依赖加载失败: ${error.message}`);
            }
        }

        /**
         * 将模块加载加入队列
         * @param {string} moduleId - 模块ID
         * @param {Object} options - 加载选项
         * @returns {Promise<any>} 加载Promise
         */
        queueModuleLoad(moduleId, options) {
            return new Promise((resolve, reject) => {
                this.loadingQueue.push({
                    moduleId,
                    options,
                    resolve,
                    reject,
                    timestamp: Date.now()
                });
            });
        }

        /**
         * 处理加载队列
         */
        async processLoadingQueue() {
            if (this.loadingQueue.length === 0 || 
                this.performanceMetrics.concurrentLoads >= this.loadConfig.maxConcurrentLoads) {
                return;
            }
            
            const queueItem = this.loadingQueue.shift();
            if (!queueItem) return;
            
            try {
                const module = await this.loadModule(queueItem.moduleId, queueItem.options);
                queueItem.resolve(module);
            } catch (error) {
                queueItem.reject(error);
            }
        }

        /**
         * 注册加载策略
         */
        registerLoadingStrategies() {
            // 默认策略：动态脚本加载
            this.loadingStrategies.set('default', {
                load: async (moduleConfig, options) => {
                    return this.loadScriptModule(moduleConfig.path, options);
                }
            });
            
            // ES模块策略
            this.loadingStrategies.set('esmodule', {
                load: async (moduleConfig, options) => {
                    return this.loadESModule(moduleConfig.path, options);
                }
            });
            
            // JSON数据策略
            this.loadingStrategies.set('json', {
                load: async (moduleConfig, options) => {
                    return this.loadJSONModule(moduleConfig.path, options);
                }
            });
            
            // CSS样式策略
            this.loadingStrategies.set('css', {
                load: async (moduleConfig, options) => {
                    return this.loadCSSModule(moduleConfig.path, options);
                }
            });
        }

        /**
         * 加载脚本模块
         * @param {string} path - 脚本路径
         * @param {Object} options - 加载选项
         * @returns {Promise<any>} 加载的模块
         */
        loadScriptModule(path, options = {}) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = path;
                script.async = true;
                
                // 设置超时
                const timeout = setTimeout(() => {
                    reject(new Error(`脚本加载超时: ${path}`));
                }, options.timeout || this.loadConfig.timeout);
                
                script.onload = () => {
                    clearTimeout(timeout);
                    resolve(window); // 返回全局对象，让调用者自行获取模块
                };
                
                script.onerror = () => {
                    clearTimeout(timeout);
                    reject(new Error(`脚本加载失败: ${path}`));
                };
                
                document.head.appendChild(script);
            });
        }

        /**
         * 加载ES模块
         * @param {string} path - 模块路径
         * @param {Object} options - 加载选项
         * @returns {Promise<any>} 加载的模块
         */
        async loadESModule(path, options = {}) {
            try {
                return await import(path);
            } catch (error) {
                throw new Error(`ES模块加载失败: ${path} - ${error.message}`);
            }
        }

        /**
         * 加载JSON模块
         * @param {string} path - JSON路径
         * @param {Object} options - 加载选项
         * @returns {Promise<any>} 加载的JSON数据
         */
        async loadJSONModule(path, options = {}) {
            try {
                const response = await fetch(path);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return await response.json();
            } catch (error) {
                throw new Error(`JSON模块加载失败: ${path} - ${error.message}`);
            }
        }

        /**
         * 加载CSS模块
         * @param {string} path - CSS路径
         * @param {Object} options - 加载选项
         * @returns {Promise<HTMLLinkElement>} 加载的CSS链接元素
         */
        loadCSSModule(path, options = {}) {
            return new Promise((resolve, reject) => {
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = path;
                
                const timeout = setTimeout(() => {
                    reject(new Error(`CSS加载超时: ${path}`));
                }, options.timeout || this.loadConfig.timeout);
                
                link.onload = () => {
                    clearTimeout(timeout);
                    resolve(link);
                };
                
                link.onerror = () => {
                    clearTimeout(timeout);
                    reject(new Error(`CSS加载失败: ${path}`));
                };
                
                document.head.appendChild(link);
            });
        }

        /**
         * 设置预加载机制
         */
        setupPreloading() {
            // 空闲时预加载
            let idleTimer = null;
            
            const resetIdleTimer = () => {
                if (idleTimer) {
                    clearTimeout(idleTimer);
                }
                
                idleTimer = setTimeout(() => {
                    this.startPreloading();
                }, this.loadConfig.preload.idleTimeout);
            };
            
            // 监听用户活动
            ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
                document.addEventListener(event, resetIdleTimer, { passive: true });
            });
            
            // 初始设置
            resetIdleTimer();
        }

        /**
         * 开始预加载
         */
        async startPreloading() {
            if (this.preloadQueue.length === 0) return;
            
            this.logger.log('开始预加载模块', 'debug', {
                queueLength: this.preloadQueue.length
            });
            
            const preloadItems = this.preloadQueue.splice(0, this.loadConfig.preload.maxPreloadItems);
            
            // 并行预加载，但不等待完成
            preloadItems.forEach(moduleId => {
                this.loadModule(moduleId).catch(error => {
                    this.logger.logError(`预加载失败: ${moduleId}`, error);
                });
            });
        }

        /**
         * 设置性能监控
         */
        setupPerformanceMonitoring() {
            // 定期输出性能指标
            setInterval(() => {
                const avgLoadTime = this.performanceMetrics.loadTimes.length > 0 ?
                    this.performanceMetrics.loadTimes.reduce((a, b) => a + b, 0) / this.performanceMetrics.loadTimes.length :
                    0;
                
                this.logger.log('懒加载性能指标', 'debug', {
                    totalRequests: this.performanceMetrics.totalRequests,
                    cacheHitRate: this.performanceMetrics.totalRequests > 0 ? 
                        (this.performanceMetrics.cacheHits / this.performanceMetrics.totalRequests * 100).toFixed(1) + '%' : '0%',
                    avgLoadTime: avgLoadTime.toFixed(2) + 'ms',
                    failedLoads: this.performanceMetrics.failedLoads,
                    maxConcurrentLoads: this.performanceMetrics.maxConcurrentLoads,
                    loadedModules: this.loadedModules.size,
                    queueLength: this.loadingQueue.length
                });
            }, 60000); // 每分钟输出一次
        }

        /**
         * 设置默认错误处理器
         */
        setupDefaultErrorHandlers() {
            // 默认错误处理器
            this.errorHandlers.set('default', {
                handle: async (moduleId, moduleConfig, error, options) => {
                    this.logger.logError(`模块加载失败: ${moduleId}`, error);
                    
                    // 重试机制
                    if (options.retryCount < moduleConfig.retryAttempts) {
                        this.logger.log(`重试加载模块: ${moduleId} (${options.retryCount + 1}/${moduleConfig.retryAttempts})`, 'warning');
                        
                        await new Promise(resolve => setTimeout(resolve, this.loadConfig.retryDelay));
                        
                        return this.loadModule(moduleId, {
                            ...options,
                            retryCount: (options.retryCount || 0) + 1
                        });
                    }
                    
                    // 降级处理
                    if (this.loadConfig.errorHandling.fallbackToSync && moduleConfig.fallbackPath) {
                        this.logger.log(`使用降级路径: ${moduleConfig.fallbackPath}`, 'warning');
                        return this.loadScriptModule(moduleConfig.fallbackPath, options);
                    }
                    
                    throw error;
                }
            });
        }

        /**
         * 预加载模块
         * @param {string} moduleId - 模块ID
         */
        preloadModule(moduleId) {
            if (!this.preloadQueue.includes(moduleId)) {
                this.preloadQueue.push(moduleId);
            }
        }

        /**
         * 卸载模块
         * @param {string} moduleId - 模块ID
         */
        unloadModule(moduleId) {
            this.loadedModules.delete(moduleId);
            this.loadingStates.delete(moduleId);
            
            this.logger.log(`模块已卸载: ${moduleId}`, 'debug');
        }

        /**
         * 清理缓存
         */
        clearCache() {
            this.loadedModules.clear();
            this.logger.log('模块缓存已清理', 'info');
        }

        /**
         * 获取性能指标
         * @returns {Object} 性能指标
         */
        getPerformanceMetrics() {
            const avgLoadTime = this.performanceMetrics.loadTimes.length > 0 ?
                this.performanceMetrics.loadTimes.reduce((a, b) => a + b, 0) / this.performanceMetrics.loadTimes.length :
                0;
            
            return {
                ...this.performanceMetrics,
                avgLoadTime,
                cacheHitRate: this.performanceMetrics.totalRequests > 0 ? 
                    (this.performanceMetrics.cacheHits / this.performanceMetrics.totalRequests) : 0,
                loadedModulesCount: this.loadedModules.size,
                registeredModulesCount: this.moduleRegistry.size,
                queueLength: this.loadingQueue.length,
                preloadQueueLength: this.preloadQueue.length
            };
        }

        /**
         * 获取加载状态
         * @returns {Object} 加载状态
         */
        getLoadingStatus() {
            return {
                isLoading: this.loadingStates.size > 0,
                loadingModules: Array.from(this.loadingStates.keys()),
                queuedModules: this.loadingQueue.map(item => item.moduleId),
                loadedModules: Array.from(this.loadedModules.keys()),
                registeredModules: Array.from(this.moduleRegistry.keys())
            };
        }

        /**
         * 获取系统状态
         * @returns {Object} 系统状态
         */
        getStatus() {
            return {
                enabled: this.loadConfig.enabled,
                performanceMetrics: this.getPerformanceMetrics(),
                loadingStatus: this.getLoadingStatus(),
                config: this.loadConfig
            };
        }
    }

    // 创建全局实例
    const lazyLoader = new LazyLoader();

    // 导出到全局作用域
    window.OTA = window.OTA || {};
    window.OTA.lazyLoader = lazyLoader;
    window.OTA.getLazyLoader = () => lazyLoader;

    // 向后兼容
    window.getLazyLoader = () => lazyLoader;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('lazyLoader', lazyLoader, '@OTA_LAZY_LOADER');
        window.OTA.Registry.registerFactory('getLazyLoader', () => lazyLoader, '@OTA_LAZY_LOADER_FACTORY');
    }

})();
