/**
 * 酒店知识库内联数据模块
 * 使用减法开发原则：最小化数据集，按需扩展
 * <AUTHOR> Assistant
 * @date 2025-01-24
 */

(function() {
    'use strict';

    /**
     * 核心酒店数据 - 精选常用酒店
     * 数据结构优化：[中文名, 英文名, 区域索引]
     */
    const REGION_INDEX = ['吉隆坡', '槟城', '新山', '亚庇', '其他'];
    
    const CORE_HOTELS = [
        // 吉隆坡地区 - 主要国际连锁酒店
        ['香格里拉酒店', 'Shangri-La Hotel', 0],
        ['希尔顿酒店', 'Hilton Hotel', 0],
        ['万豪酒店', 'Marriott Hotel', 0],
        ['丽思卡尔顿酒店', 'The Ritz-Carlton', 0],
        ['洲际酒店', 'InterContinental Hotel', 0],
        ['凯悦酒店', 'Hyatt Hotel', 0],
        ['喜来登酒店', 'Sheraton Hotel', 0],
        ['威斯汀酒店', 'The Westin', 0],
        ['文华东方酒店', 'Mandarin Oriental', 0],
        ['四季酒店', 'Four Seasons Hotel', 0],
        
        // 槟城地区
        ['槟城香格里拉酒店', 'Shangri-La Hotel Penang', 1],
        ['槟城希尔顿酒店', 'Hilton Penang', 1],
        ['槟城万豪酒店', 'Marriott Penang', 1],
        
        // 新山地区
        ['新山希尔顿酒店', 'Hilton Johor Bahru', 2],
        ['新山万豪酒店', 'Marriott Johor Bahru', 2],
        
        // 亚庇地区
        ['亚庇香格里拉酒店', 'Shangri-La Tanjung Aru Resort', 3],
        ['亚庇凯悦酒店', 'Hyatt Regency Kinabalu', 3],
        
        // 其他常用酒店
        ['1 City酒店', '1 City Hotel', 4],
        ['白金酒店', 'Platinum Hotel', 4],
        ['太平洋丽晶酒店', 'Pacific Regency Hotel', 4],
        ['吉隆坡塔酒店', 'KL Tower Hotel', 0],
        ['双子塔酒店', 'Petronas Twin Towers Hotel', 0]
    ];

    /**
     * 酒店品牌关键词映射
     * 用于模糊匹配和智能识别
     */
    const BRAND_KEYWORDS = {
        '香格里拉': 'Shangri-La',
        '希尔顿': 'Hilton',
        '万豪': 'Marriott',
        '丽思卡尔顿': 'Ritz-Carlton',
        '洲际': 'InterContinental',
        '凯悦': 'Hyatt',
        '喜来登': 'Sheraton',
        '威斯汀': 'Westin',
        '文华东方': 'Mandarin Oriental',
        '四季': 'Four Seasons'
    };

    /**
     * 内联酒店数据管理器
     * 使用减法开发：最小功能集，高效实现
     */
    class InlineHotelDataManager {
        constructor() {
            this.exactMatchMap = new Map();
            this.fuzzySearchIndex = new Map();
            this.initialized = false;
            this.totalHotels = CORE_HOTELS.length;
        }

        /**
         * 初始化数据索引
         * 只在首次查询时执行，避免启动延迟
         */
        initialize() {
            if (this.initialized) return;

            // 构建精确匹配索引
            CORE_HOTELS.forEach(([chinese, english, regionIndex]) => {
                const region = REGION_INDEX[regionIndex];
                this.exactMatchMap.set(chinese, {
                    english: english,
                    malay: this.generateMalayName(english),
                    region: region,
                    source: 'inline_data'
                });

                // 构建模糊匹配索引
                this.addToFuzzyIndex(chinese, english, region);
            });

            this.initialized = true;
            console.log(`✅ 内联酒店数据初始化完成，共 ${this.totalHotels} 家酒店`);
        }

        /**
         * 添加到模糊搜索索引
         */
        addToFuzzyIndex(chineseName, englishName, region) {
            const keywords = this.extractKeywords(chineseName);
            
            keywords.forEach(keyword => {
                if (!this.fuzzySearchIndex.has(keyword)) {
                    this.fuzzySearchIndex.set(keyword, []);
                }
                this.fuzzySearchIndex.get(keyword).push({
                    chinese: chineseName,
                    english: englishName,
                    malay: this.generateMalayName(englishName),
                    region: region,
                    source: 'inline_data'
                });
            });
        }

        /**
         * 提取酒店关键词
         */
        extractKeywords(hotelName) {
            // 移除常见后缀
            const suffixes = ['酒店', '宾馆', '旅馆', '度假村', '度假屋', '民宿', '客栈', '饭店'];
            let cleanName = hotelName;
            
            suffixes.forEach(suffix => {
                cleanName = cleanName.replace(new RegExp(suffix + '$'), '');
            });

            const keywords = [cleanName];
            
            // 添加品牌关键词
            Object.keys(BRAND_KEYWORDS).forEach(brand => {
                if (hotelName.includes(brand)) {
                    keywords.push(brand);
                }
            });
            
            return keywords.filter(k => k.length > 0);
        }

        /**
         * 生成马来文酒店名称
         */
        generateMalayName(englishName) {
            const malayTranslations = {
                'Hotel': 'Hotel',
                'Resort': 'Resort',
                'Shangri-La': 'Shangri-La',
                'Hilton': 'Hilton',
                'Marriott': 'Marriott',
                'Ritz-Carlton': 'Ritz-Carlton',
                'InterContinental': 'InterContinental',
                'Hyatt': 'Hyatt',
                'Sheraton': 'Sheraton',
                'Westin': 'Westin',
                'International': 'Antarabangsa',
                'City': 'Bandar',
                'Tower': 'Menara',
                'Regency': 'Regency'
            };
            
            let malayName = englishName;
            Object.entries(malayTranslations).forEach(([english, malay]) => {
                malayName = malayName.replace(new RegExp(`\\b${english}\\b`, 'gi'), malay);
            });
            
            return malayName;
        }

        /**
         * 查询酒店信息
         * 懒加载：首次调用时初始化
         */
        queryHotel(address) {
            // 懒加载初始化
            if (!this.initialized) {
                this.initialize();
            }

            // 精确匹配
            if (this.exactMatchMap.has(address)) {
                const result = this.exactMatchMap.get(address);
                console.log(`🎯 内联数据精确匹配: ${address} → ${result.english}`);
                return result;
            }
            
            // 模糊匹配
            const fuzzyResult = this.performFuzzySearch(address);
            if (fuzzyResult) {
                console.log(`🔍 内联数据模糊匹配: ${address} → ${fuzzyResult.english}`);
                return fuzzyResult;
            }
            
            return null;
        }

        /**
         * 执行模糊搜索
         */
        performFuzzySearch(address) {
            const keywords = this.extractKeywords(address);
            
            let bestMatch = null;
            let bestScore = 0;
            
            keywords.forEach(keyword => {
                if (this.fuzzySearchIndex.has(keyword)) {
                    const candidates = this.fuzzySearchIndex.get(keyword);
                    candidates.forEach(candidate => {
                        const score = this.calculateMatchScore(address, candidate.chinese);
                        if (score > bestScore && score > 0.6) {
                            bestScore = score;
                            bestMatch = candidate;
                        }
                    });
                }
            });
            
            return bestMatch;
        }

        /**
         * 计算匹配分数
         */
        calculateMatchScore(input, candidate) {
            const inputClean = input.replace(/[酒店宾馆旅馆度假村民宿客栈饭店]/g, '');
            const candidateClean = candidate.replace(/[酒店宾馆旅馆度假村民宿客栈饭店]/g, '');
            
            if (inputClean.includes(candidateClean) || candidateClean.includes(inputClean)) {
                return 0.8;
            }
            
            let commonChars = 0;
            const minLength = Math.min(inputClean.length, candidateClean.length);
            
            for (let i = 0; i < minLength; i++) {
                if (inputClean[i] === candidateClean[i]) {
                    commonChars++;
                }
            }
            
            return commonChars / Math.max(inputClean.length, candidateClean.length);
        }

        /**
         * 获取统计信息
         */
        getStats() {
            return {
                totalHotels: this.totalHotels,
                exactMatches: this.exactMatchMap.size,
                fuzzyMatches: this.fuzzySearchIndex.size,
                initialized: this.initialized
            };
        }
    }

    // 创建全局实例
    window.OTA = window.OTA || {};
    window.OTA.inlineHotelDataManager = new InlineHotelDataManager();

    console.log('✅ 内联酒店数据模块已加载');

})();
