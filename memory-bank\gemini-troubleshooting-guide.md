# Gemini AI系统故障排查指南

## 📋 指南概述

本指南提供了Gemini AI系统常见问题的诊断和解决方案，帮助开发者快速定位和修复系统问题。

**适用版本**: 2.0.0 (重构版本)  
**目标用户**: 开发者、系统管理员  
**更新时间**: 2024-01-01

## 🚨 紧急问题快速诊断

### 系统完全无响应
```javascript
// 快速诊断脚本
function emergencyDiagnosis() {
    console.log('=== 紧急诊断开始 ===');
    
    // 1. 检查基础服务
    console.log('OTA对象:', !!window.OTA);
    console.log('Gemini服务:', !!window.OTA?.geminiService);
    console.log('协调器:', !!window.OTA?.gemini?.getGeminiCoordinator);
    
    // 2. 检查API配置
    console.log('API配置:', !!window.GEMINI_CONFIG?.apiKey);
    
    // 3. 检查网络连接
    fetch('https://generativelanguage.googleapis.com/v1beta/models')
        .then(() => console.log('网络连接: 正常'))
        .catch(() => console.log('网络连接: 异常'));
    
    console.log('=== 诊断完成 ===');
}

// 运行诊断
emergencyDiagnosis();
```

## 🔍 常见问题分类

### 1. 初始化问题

#### 问题1.1: 系统初始化失败
**症状**: 页面加载后无法使用Gemini功能
**可能原因**:
- 脚本加载顺序错误
- API密钥未配置
- 网络连接问题

**诊断步骤**:
```javascript
// 检查初始化状态
function checkInitialization() {
    const checks = {
        otaObject: !!window.OTA,
        geminiService: !!window.OTA?.geminiService,
        coordinator: !!window.OTA?.gemini?.getGeminiCoordinator,
        apiConfig: !!window.GEMINI_CONFIG?.apiKey
    };
    
    console.table(checks);
    
    // 找出失败的环节
    Object.entries(checks).forEach(([key, value]) => {
        if (!value) {
            console.error(`❌ ${key} 初始化失败`);
        }
    });
}
```

**解决方案**:
```javascript
// 方案1: 延迟初始化
setTimeout(() => {
    if (!window.OTA?.geminiService) {
        console.log('尝试重新初始化...');
        // 重新加载必要脚本
        location.reload();
    }
}, 3000);

// 方案2: 手动初始化
if (window.OTA && !window.OTA.geminiService) {
    // 检查是否有备用初始化方法
    if (typeof initializeGeminiService === 'function') {
        initializeGeminiService();
    }
}
```

#### 问题1.2: 协调器创建失败
**症状**: geminiService存在但协调器无法创建
**诊断**:
```javascript
function diagnoseCoordinator() {
    try {
        const coordinator = window.OTA.gemini.getGeminiCoordinator();
        if (!coordinator) {
            console.error('协调器创建失败');
            
            // 检查依赖
            const dependencies = [
                'window.OTA.Registry',
                'window.OTA.gemini.GeminiCoordinator'
            ];
            
            dependencies.forEach(dep => {
                const exists = eval(`typeof ${dep} !== 'undefined'`);
                console.log(`${dep}: ${exists ? '✅' : '❌'}`);
            });
        }
    } catch (error) {
        console.error('协调器诊断失败:', error);
    }
}
```

### 2. API调用问题

#### 问题2.1: API密钥错误
**症状**: 返回401或403错误
**诊断**:
```javascript
function checkApiKey() {
    const apiKey = window.GEMINI_CONFIG?.apiKey;
    
    if (!apiKey) {
        console.error('❌ API密钥未配置');
        return false;
    }
    
    if (apiKey.length < 30) {
        console.error('❌ API密钥格式可能错误');
        return false;
    }
    
    if (apiKey.startsWith('YOUR_')) {
        console.error('❌ 使用的是示例API密钥');
        return false;
    }
    
    console.log('✅ API密钥格式正确');
    return true;
}
```

**解决方案**:
```javascript
// 更新API密钥
window.GEMINI_CONFIG = {
    ...window.GEMINI_CONFIG,
    apiKey: 'YOUR_ACTUAL_API_KEY'
};

// 重新初始化服务
if (window.OTA?.geminiService) {
    window.OTA.geminiService.updateConfig(window.GEMINI_CONFIG);
}
```

#### 问题2.2: 请求超时
**症状**: 处理时间过长或超时错误
**诊断**:
```javascript
function diagnoseTimeout() {
    const coordinator = window.OTA.gemini.getGeminiCoordinator();
    const metrics = coordinator?.getMetrics();
    
    if (metrics) {
        console.log('平均响应时间:', metrics.processingStats.averageProcessingTime);
        console.log('当前并发数:', metrics.concurrencyStats.current);
        console.log('队列长度:', metrics.concurrencyStats.queueLength);
        
        if (metrics.processingStats.averageProcessingTime > 15000) {
            console.warn('⚠️ 响应时间过长，可能的原因:');
            console.log('- 网络连接慢');
            console.log('- API服务器负载高');
            console.log('- 请求内容过于复杂');
        }
    }
}
```

**解决方案**:
```javascript
// 调整超时配置
const coordinator = window.OTA.gemini.getGeminiCoordinator();
if (coordinator) {
    coordinator.config.processing.timeout = 30000; // 增加到30秒
    coordinator.config.processing.maxConcurrent = 10; // 减少并发数
}
```

### 3. 解析问题

#### 问题3.1: 解析结果不准确
**症状**: 订单信息提取错误或不完整
**诊断**:
```javascript
async function diagnoseParsingAccuracy(orderText) {
    console.log('=== 解析诊断开始 ===');
    
    // 1. 检查输入文本
    console.log('文本长度:', orderText.length);
    console.log('包含中文:', /[\u4e00-\u9fa5]/.test(orderText));
    console.log('包含英文:', /[a-zA-Z]/.test(orderText));
    console.log('包含数字:', /\d/.test(orderText));
    
    // 2. 尝试解析
    try {
        const result = await window.OTA.geminiService.parseOrder(orderText);
        console.log('解析结果:', result);
        console.log('置信度:', result.confidence);
        console.log('处理来源:', result.source);
        
        if (result.confidence < 0.7) {
            console.warn('⚠️ 置信度较低，可能原因:');
            console.log('- 文本格式不清晰');
            console.log('- 关键信息缺失');
            console.log('- 语言混合使用');
        }
    } catch (error) {
        console.error('解析失败:', error);
    }
}
```

**解决方案**:
```javascript
// 优化输入文本
function optimizeOrderText(text) {
    return text
        .replace(/\s+/g, ' ')           // 规范化空格
        .replace(/[，。]/g, ', ')        // 统一标点符号
        .trim();                        // 去除首尾空格
}

// 使用优化后的文本
const optimizedText = optimizeOrderText(originalText);
const result = await geminiService.parseOrder(optimizedText);
```

#### 问题3.2: OTA渠道识别错误
**症状**: 系统识别错误的OTA渠道
**诊断**:
```javascript
function diagnoseChannelIdentification(orderText) {
    const coordinator = window.OTA.gemini.getGeminiCoordinator();
    
    // 获取渠道识别详情
    coordinator.identifyChannel(orderText).then(result => {
        console.log('渠道识别结果:', result);
        console.log('候选渠道:', result.candidates);
        console.log('置信度分布:', result.confidenceScores);
        
        // 分析识别逻辑
        result.candidates.forEach(candidate => {
            console.log(`${candidate.channel}: ${candidate.confidence} (${candidate.reason})`);
        });
    });
}
```

### 4. 性能问题

#### 问题4.1: 响应速度慢
**症状**: 处理时间超过预期
**诊断**:
```javascript
function diagnosePerformance() {
    const coordinator = window.OTA.gemini.getGeminiCoordinator();
    const metrics = coordinator.getMetrics();
    
    console.log('=== 性能诊断 ===');
    console.log('缓存命中率:', (metrics.cacheStats.hitRate * 100).toFixed(2) + '%');
    console.log('平均处理时间:', metrics.processingStats.averageProcessingTime + 'ms');
    console.log('当前并发数:', metrics.concurrencyStats.current);
    console.log('队列长度:', metrics.concurrencyStats.queueLength);
    
    // 性能建议
    if (metrics.cacheStats.hitRate < 0.5) {
        console.warn('⚠️ 缓存命中率低，建议:');
        console.log('- 增加缓存大小');
        console.log('- 延长缓存时间');
        console.log('- 检查缓存键生成逻辑');
    }
    
    if (metrics.processingStats.averageProcessingTime > 10000) {
        console.warn('⚠️ 处理时间过长，建议:');
        console.log('- 检查网络连接');
        console.log('- 优化请求内容');
        console.log('- 增加超时时间');
    }
}
```

**解决方案**:
```javascript
// 性能优化配置
function optimizePerformance() {
    const coordinator = window.OTA.gemini.getGeminiCoordinator();
    
    // 优化缓存
    coordinator.config.cache = {
        ...coordinator.config.cache,
        maxSize: 10000,                 // 增加缓存大小
        maxAge: 30 * 60 * 1000,        // 延长缓存时间
        enableLRU: true                 // 启用LRU策略
    };
    
    // 优化并发
    coordinator.config.processing = {
        ...coordinator.config.processing,
        maxConcurrent: 15,              // 适中的并发数
        queueSize: 300,                 // 增加队列大小
        enableBatching: true            // 启用批处理
    };
    
    console.log('✅ 性能优化配置已应用');
}
```

#### 问题4.2: 内存使用过高
**症状**: 浏览器内存占用持续增长
**诊断**:
```javascript
function diagnoseMemoryUsage() {
    // 检查内存使用
    if (performance.memory) {
        const memory = performance.memory;
        console.log('=== 内存使用情况 ===');
        console.log('已使用:', (memory.usedJSHeapSize / 1024 / 1024).toFixed(2) + 'MB');
        console.log('总分配:', (memory.totalJSHeapSize / 1024 / 1024).toFixed(2) + 'MB');
        console.log('限制:', (memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2) + 'MB');
        
        if (memory.usedJSHeapSize > 100 * 1024 * 1024) {
            console.warn('⚠️ 内存使用过高');
        }
    }
    
    // 检查缓存大小
    const coordinator = window.OTA.gemini.getGeminiCoordinator();
    const cacheSize = coordinator?.cache?.size || 0;
    console.log('缓存条目数:', cacheSize);
}
```

**解决方案**:
```javascript
// 内存清理
function cleanupMemory() {
    const coordinator = window.OTA.gemini.getGeminiCoordinator();
    
    // 清理缓存
    if (coordinator?.cache) {
        coordinator.cache.clear();
        console.log('✅ 缓存已清理');
    }
    
    // 强制垃圾回收 (仅在开发环境)
    if (window.gc && typeof window.gc === 'function') {
        window.gc();
        console.log('✅ 垃圾回收已执行');
    }
}
```

## 🛠️ 调试工具

### 1. 系统状态检查器
```javascript
function systemHealthCheck() {
    console.log('=== 系统健康检查 ===');
    
    const checks = [
        {
            name: '基础服务',
            check: () => !!window.OTA?.geminiService,
            fix: '检查脚本加载顺序'
        },
        {
            name: 'API配置',
            check: () => !!window.GEMINI_CONFIG?.apiKey,
            fix: '配置正确的API密钥'
        },
        {
            name: '协调器',
            check: () => !!window.OTA?.gemini?.getGeminiCoordinator(),
            fix: '检查协调器初始化'
        }
    ];
    
    checks.forEach(({ name, check, fix }) => {
        const status = check() ? '✅' : '❌';
        console.log(`${status} ${name}`);
        if (!check()) {
            console.log(`   修复建议: ${fix}`);
        }
    });
}
```

### 2. 性能监控器
```javascript
function startPerformanceMonitoring() {
    setInterval(() => {
        const coordinator = window.OTA.gemini.getGeminiCoordinator();
        if (coordinator) {
            const metrics = coordinator.getMetrics();
            
            // 检查关键指标
            if (metrics.processingStats.averageProcessingTime > 15000) {
                console.warn('⚠️ 响应时间告警:', metrics.processingStats.averageProcessingTime + 'ms');
            }
            
            if (metrics.cacheStats.hitRate < 0.3) {
                console.warn('⚠️ 缓存命中率告警:', (metrics.cacheStats.hitRate * 100).toFixed(2) + '%');
            }
        }
    }, 30000); // 每30秒检查一次
}
```

### 3. 错误收集器
```javascript
function setupErrorCollection() {
    window.addEventListener('error', (event) => {
        if (event.filename?.includes('gemini')) {
            console.error('=== Gemini错误 ===');
            console.error('文件:', event.filename);
            console.error('行号:', event.lineno);
            console.error('错误:', event.message);
            console.error('堆栈:', event.error?.stack);
        }
    });
    
    // 监听未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
        if (event.reason?.message?.includes('gemini')) {
            console.error('=== Gemini Promise错误 ===');
            console.error('原因:', event.reason);
        }
    });
}
```

## 📞 获取帮助

### 自助诊断流程
1. **运行紧急诊断**: `emergencyDiagnosis()`
2. **检查系统健康**: `systemHealthCheck()`
3. **分析性能指标**: `diagnosePerformance()`
4. **查看错误日志**: 检查浏览器控制台

### 联系支持
- **技术文档**: 查看完整的API文档和使用指南
- **测试页面**: 使用 `/test-*.html` 页面验证功能
- **状态页面**: 访问 `/status.html` 查看系统状态
- **开发团队**: OTA系统开发组

---

**指南版本**: 2.0.0  
**最后更新**: 2024-01-01  
**维护状态**: 活跃维护中
