# Gemini AI系统扩展开发指南

## 📋 指南概述

本指南详细说明如何为Gemini AI系统开发新的OTA平台处理器，包括开发模板、最佳实践和集成步骤。

**适用版本**: 2.0.0 (重构版本)  
**目标用户**: 扩展开发者  
**更新时间**: 2024-01-01

## 🚀 快速开始

### 开发环境准备
```bash
# 项目结构
create job/
├── js/gemini/
│   ├── processors/           # 处理器目录
│   ├── core/                # 核心组件
│   └── configs/             # 配置文件
└── tests/                   # 测试文件
```

### 基本开发流程
1. **创建处理器类** - 继承BaseProcessor
2. **实现必需方法** - 参考号识别、数据处理等
3. **配置注册** - 在服务注册中心注册
4. **编写测试** - 单元测试和集成测试
5. **更新配置** - 添加到路由配置

## 📝 处理器开发模板

### 1. 基础处理器模板
```javascript
/**
 * @OTA_PROCESSOR [平台名称]订单处理器
 * 🏷️ 标签: @PROCESSOR @OTA_[平台名称]
 * 📝 说明: 处理[平台名称]平台的订单解析和数据转换
 * 🎯 功能: 参考号识别、字段映射、数据验证
 */

(function() {
    'use strict';
    
    // 延迟获取依赖，确保加载顺序
    function getBaseProcessor() {
        return window.OTA.gemini.BaseProcessor;
    }
    
    function getLogger() {
        return window.OTA.logger || console;
    }
    
    /**
     * [平台名称]处理器类
     * @extends BaseProcessor
     */
    class [PlatformName]Processor extends getBaseProcessor() {
        constructor() {
            super();
            this.platform = '[platform-name]';
            this.version = '1.0.0';
            this.logger = getLogger();
            
            // 平台特定配置
            this.config = {
                // 参考号模式
                referencePatterns: [
                    /[平台特定的正则表达式]/,
                    // 添加更多模式
                ],
                
                // 字段映射
                fieldMapping: {
                    // 平台字段 -> 标准字段
                    'platform_field': 'standard_field'
                },
                
                // 预设值
                presetValues: {
                    // 平台特定的默认值
                },
                
                // 置信度权重
                confidenceWeights: {
                    referenceNumber: 0.4,
                    contentAnalysis: 0.3,
                    fieldMapping: 0.3
                }
            };
        }
        
        /**
         * 识别参考号
         * @param {string} text - 订单文本
         * @returns {Object} 识别结果
         */
        async identifyReferenceNumber(text) {
            try {
                const results = [];
                
                // 使用配置的模式进行匹配
                for (const pattern of this.config.referencePatterns) {
                    const matches = text.match(pattern);
                    if (matches) {
                        results.push({
                            value: matches[0],
                            confidence: 0.8,
                            pattern: pattern.toString(),
                            position: text.indexOf(matches[0])
                        });
                    }
                }
                
                // 返回最佳匹配
                if (results.length > 0) {
                    const best = results.reduce((prev, current) => 
                        current.confidence > prev.confidence ? current : prev
                    );
                    
                    return {
                        success: true,
                        referenceNumber: best.value,
                        confidence: best.confidence,
                        metadata: {
                            pattern: best.pattern,
                            position: best.position,
                            allMatches: results
                        }
                    };
                }
                
                return {
                    success: false,
                    confidence: 0,
                    reason: '未找到匹配的参考号模式'
                };
                
            } catch (error) {
                this.logger.logError(`[${this.platform}] 参考号识别失败`, error);
                return {
                    success: false,
                    confidence: 0,
                    error: error.message
                };
            }
        }
        
        /**
         * 处理订单数据
         * @param {string} orderText - 订单文本
         * @param {Object} options - 处理选项
         * @returns {Object} 处理结果
         */
        async processOrder(orderText, options = {}) {
            try {
                this.logger.log(`[${this.platform}] 开始处理订单`, 'info');
                
                // 1. 预处理文本
                const cleanText = this.preprocessText(orderText);
                
                // 2. 识别参考号
                const refResult = await this.identifyReferenceNumber(cleanText);
                
                // 3. 提取基础信息
                const baseData = await this.extractBaseInformation(cleanText);
                
                // 4. 应用字段映射
                const mappedData = this.applyFieldMapping(baseData);
                
                // 5. 应用预设值
                const finalData = this.applyPresetValues(mappedData);
                
                // 6. 计算置信度
                const confidence = this.calculateConfidence(refResult, baseData);
                
                return {
                    success: true,
                    data: finalData,
                    confidence: confidence,
                    referenceNumber: refResult.referenceNumber,
                    platform: this.platform,
                    processingTime: Date.now() - (options.startTime || Date.now()),
                    metadata: {
                        referenceResult: refResult,
                        fieldMappingApplied: Object.keys(this.config.fieldMapping).length,
                        presetValuesApplied: Object.keys(this.config.presetValues).length
                    }
                };
                
            } catch (error) {
                this.logger.logError(`[${this.platform}] 订单处理失败`, error);
                return {
                    success: false,
                    error: error.message,
                    platform: this.platform
                };
            }
        }
        
        /**
         * 预处理文本
         * @param {string} text - 原始文本
         * @returns {string} 清理后的文本
         */
        preprocessText(text) {
            return text
                .replace(/\s+/g, ' ')           // 规范化空格
                .replace(/[，。]/g, ', ')        // 统一标点符号
                .trim();                        // 去除首尾空格
        }
        
        /**
         * 提取基础信息
         * @param {string} text - 清理后的文本
         * @returns {Object} 提取的信息
         */
        async extractBaseInformation(text) {
            // 实现平台特定的信息提取逻辑
            const extractedData = {};
            
            // 示例：提取客户姓名
            const nameMatch = text.match(/(?:客户|姓名|name)[：:]\s*([^\s,，]+)/i);
            if (nameMatch) {
                extractedData.customerName = nameMatch[1];
            }
            
            // 示例：提取电话号码
            const phoneMatch = text.match(/(?:电话|手机|phone)[：:]\s*([\+\d\s\-]+)/i);
            if (phoneMatch) {
                extractedData.phoneNumber = phoneMatch[1].replace(/\s/g, '');
            }
            
            // 添加更多提取逻辑...
            
            return extractedData;
        }
        
        /**
         * 应用字段映射
         * @param {Object} data - 原始数据
         * @returns {Object} 映射后的数据
         */
        applyFieldMapping(data) {
            const mappedData = { ...data };
            
            Object.entries(this.config.fieldMapping).forEach(([platformField, standardField]) => {
                if (data[platformField] !== undefined) {
                    mappedData[standardField] = data[platformField];
                    delete mappedData[platformField];
                }
            });
            
            return mappedData;
        }
        
        /**
         * 应用预设值
         * @param {Object} data - 映射后的数据
         * @returns {Object} 应用预设值后的数据
         */
        applyPresetValues(data) {
            const finalData = { ...data };
            
            Object.entries(this.config.presetValues).forEach(([field, value]) => {
                if (finalData[field] === undefined || finalData[field] === '') {
                    finalData[field] = value;
                }
            });
            
            return finalData;
        }
        
        /**
         * 计算置信度
         * @param {Object} refResult - 参考号识别结果
         * @param {Object} baseData - 基础数据
         * @returns {number} 置信度 (0-1)
         */
        calculateConfidence(refResult, baseData) {
            const weights = this.config.confidenceWeights;
            let totalConfidence = 0;
            
            // 参考号置信度
            totalConfidence += (refResult.confidence || 0) * weights.referenceNumber;
            
            // 内容分析置信度
            const dataFields = Object.keys(baseData).length;
            const contentConfidence = Math.min(dataFields / 5, 1); // 假设5个字段为满分
            totalConfidence += contentConfidence * weights.contentAnalysis;
            
            // 字段映射置信度
            const mappingConfidence = Object.keys(this.config.fieldMapping).length > 0 ? 0.8 : 0.5;
            totalConfidence += mappingConfidence * weights.fieldMapping;
            
            return Math.min(totalConfidence, 1);
        }
        
        /**
         * 获取处理器信息
         * @returns {Object} 处理器信息
         */
        getInfo() {
            return {
                platform: this.platform,
                version: this.version,
                supportedFeatures: [
                    'referenceNumberIdentification',
                    'fieldMapping',
                    'presetValues',
                    'confidenceCalculation'
                ],
                configuredPatterns: this.config.referencePatterns.length,
                fieldMappings: Object.keys(this.config.fieldMapping).length,
                presetValues: Object.keys(this.config.presetValues).length
            };
        }
    }
    
    // 注册处理器
    if (typeof window !== 'undefined') {
        window.OTA = window.OTA || {};
        window.OTA.gemini = window.OTA.gemini || {};
        window.OTA.gemini.[PlatformName]Processor = [PlatformName]Processor;
        
        // 自动注册到服务注册中心
        if (window.OTA.Registry) {
            window.OTA.Registry.registerService(
                '[platform-name]-processor',
                new [PlatformName]Processor(),
                'PROCESSOR'
            );
        }
    }
    
})();
```

### 2. 配置文件模板
```javascript
// js/gemini/configs/[platform-name]-config.js

/**
 * @CONFIG_FILE [平台名称]配置文件
 * 🏷️ 标签: @CONFIG_FILE @OTA_[平台名称]
 */

(function() {
    'use strict';
    
    const [PLATFORM_NAME]_CONFIG = {
        // 平台标识
        platform: '[platform-name]',
        displayName: '[平台显示名称]',
        
        // 参考号识别模式
        referencePatterns: [
            {
                pattern: /[平台特定正则]/,
                description: '主要参考号格式',
                confidence: 0.9
            },
            {
                pattern: /[备用正则]/,
                description: '备用参考号格式',
                confidence: 0.7
            }
        ],
        
        // 字段映射规则
        fieldMapping: {
            // 平台字段名 -> 标准字段名
            'guest_name': 'customerName',
            'contact_phone': 'phoneNumber',
            'pickup_address': 'pickupLocation',
            'dropoff_address': 'destination'
        },
        
        // 预设值配置
        presetValues: {
            // 根据平台特点设置默认值
            'serviceType': 'transfer',
            'language': 'english',
            'carType': 'comfort'
        },
        
        // 验证规则
        validationRules: {
            required: ['customerName', 'phoneNumber'],
            optional: ['email', 'specialRequests'],
            formats: {
                phoneNumber: /^[\+]?[\d\s\-\(\)]+$/,
                email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
            }
        },
        
        // 处理选项
        processingOptions: {
            enableCache: true,
            cacheTimeout: 10 * 60 * 1000, // 10分钟
            maxRetries: 3,
            retryDelay: 1000
        }
    };
    
    // 导出配置
    if (typeof window !== 'undefined') {
        window.OTA = window.OTA || {};
        window.OTA.gemini = window.OTA.gemini || {};
        window.OTA.gemini.configs = window.OTA.gemini.configs || {};
        window.OTA.gemini.configs['[platform-name]'] = [PLATFORM_NAME]_CONFIG;
    }
    
})();
```

## 🧪 测试开发

### 1. 单元测试模板
```javascript
// tests/processors/[platform-name]-processor.test.js

/**
 * [平台名称]处理器单元测试
 */

(function() {
    'use strict';
    
    // 测试套件
    const [PlatformName]ProcessorTests = {
        name: '[PlatformName]Processor Tests',
        
        async runAll() {
            console.log(`开始执行 ${this.name}`);
            
            const tests = [
                this.testReferenceNumberIdentification,
                this.testOrderProcessing,
                this.testFieldMapping,
                this.testPresetValues,
                this.testConfidenceCalculation
            ];
            
            let passed = 0;
            let failed = 0;
            
            for (const test of tests) {
                try {
                    await test.call(this);
                    console.log(`✅ ${test.name}`);
                    passed++;
                } catch (error) {
                    console.error(`❌ ${test.name}:`, error.message);
                    failed++;
                }
            }
            
            console.log(`测试完成: ${passed} 通过, ${failed} 失败`);
            return { passed, failed };
        },
        
        async testReferenceNumberIdentification() {
            const processor = new window.OTA.gemini.[PlatformName]Processor();
            
            // 测试用例
            const testCases = [
                {
                    text: '订单号：[示例参考号]',
                    expected: true,
                    description: '标准格式参考号'
                },
                {
                    text: '没有参考号的文本',
                    expected: false,
                    description: '无参考号文本'
                }
            ];
            
            for (const testCase of testCases) {
                const result = await processor.identifyReferenceNumber(testCase.text);
                
                if (result.success !== testCase.expected) {
                    throw new Error(`${testCase.description} 失败: 期望 ${testCase.expected}, 实际 ${result.success}`);
                }
            }
        },
        
        async testOrderProcessing() {
            const processor = new window.OTA.gemini.[PlatformName]Processor();
            
            const sampleOrder = `
                客户姓名：张三
                联系电话：+60123456789
                接送地点：吉隆坡机场
                目的地：双子塔
                订单号：[示例参考号]
            `;
            
            const result = await processor.processOrder(sampleOrder);
            
            if (!result.success) {
                throw new Error('订单处理失败: ' + result.error);
            }
            
            if (!result.data.customerName) {
                throw new Error('未提取到客户姓名');
            }
            
            if (result.confidence < 0.5) {
                throw new Error('置信度过低: ' + result.confidence);
            }
        },
        
        // 添加更多测试方法...
    };
    
    // 导出测试套件
    if (typeof window !== 'undefined') {
        window.OTA = window.OTA || {};
        window.OTA.tests = window.OTA.tests || {};
        window.OTA.tests.[PlatformName]ProcessorTests = [PlatformName]ProcessorTests;
    }
    
})();
```

## 📚 最佳实践

### 1. 代码规范
- **命名约定**: 使用PascalCase命名处理器类
- **注释规范**: 使用JSDoc格式的中文注释
- **错误处理**: 所有方法都应有适当的错误处理
- **日志记录**: 使用统一的日志系统记录关键操作

### 2. 性能优化
- **缓存策略**: 合理使用缓存减少重复计算
- **异步处理**: 使用async/await处理异步操作
- **内存管理**: 避免内存泄漏，及时清理不需要的对象
- **批量处理**: 支持批量处理提高效率

### 3. 扩展性设计
- **配置驱动**: 将规则和配置分离到配置文件
- **插件架构**: 支持功能插件扩展
- **版本兼容**: 考虑向后兼容性
- **国际化**: 支持多语言处理

## 🔧 集成步骤

### 1. 注册处理器
```javascript
// 在处理器文件末尾添加注册代码
if (window.OTA.Registry) {
    window.OTA.Registry.registerService(
        '[platform-name]-processor',
        new [PlatformName]Processor(),
        'PROCESSOR'
    );
}
```

### 2. 更新路由配置
```javascript
// 在 js/gemini/core/processor-router.js 中添加路由规则
const routingRules = {
    // 现有规则...
    '[platform-name]': {
        processor: '[platform-name]-processor',
        priority: 1,
        patterns: [
            /[平台特定识别模式]/
        ]
    }
};
```

### 3. 添加到测试套件
```javascript
// 在测试运行器中添加新的测试套件
const testSuites = [
    // 现有测试...
    window.OTA.tests.[PlatformName]ProcessorTests
];
```

## 📋 开发检查清单

### 开发阶段
- [ ] 创建处理器类文件
- [ ] 实现所有必需方法
- [ ] 创建配置文件
- [ ] 编写单元测试
- [ ] 测试参考号识别
- [ ] 测试数据处理
- [ ] 验证字段映射
- [ ] 检查预设值应用

### 集成阶段
- [ ] 注册到服务注册中心
- [ ] 更新路由配置
- [ ] 添加到测试套件
- [ ] 运行集成测试
- [ ] 验证与现有系统兼容
- [ ] 性能测试
- [ ] 文档更新

### 部署阶段
- [ ] 代码审查
- [ ] 最终测试
- [ ] 部署到测试环境
- [ ] 用户验收测试
- [ ] 部署到生产环境
- [ ] 监控和维护

---

**指南版本**: 2.0.0  
**最后更新**: 2024-01-01  
**技术支持**: OTA系统开发团队
