/**
 * 控制台日志清理工具
 * 用于分析和清理项目中过多的控制台日志输出
 * 任务3.3：清理控制台日志过多问题
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 控制台日志清理器类
     */
    class ConsoleLogCleaner {
        constructor() {
            this.logPatterns = new Map(); // 日志模式统计
            this.cleanupRules = new Map(); // 清理规则
            this.analysisResults = {
                totalLogs: 0,
                debugLogs: 0,
                temporaryLogs: 0,
                redundantLogs: 0,
                keepLogs: 0,
                cleanupTargets: []
            };
            
            this.setupCleanupRules();
        }

        /**
         * 设置清理规则
         */
        setupCleanupRules() {
            // 需要完全移除的日志模式
            this.cleanupRules.set('REMOVE_COMPLETELY', [
                // 临时调试日志
                /console\.log\(['"`].*debug.*['"`]\)/gi,
                /console\.log\(['"`].*test.*['"`]\)/gi,
                /console\.log\(['"`].*temp.*['"`]\)/gi,
                /console\.log\(['"`].*临时.*['"`]\)/gi,
                /console\.log\(['"`].*测试.*['"`]\)/gi,
                /console\.log\(['"`].*调试.*['"`]\)/gi,
                
                // 开发阶段的状态输出
                /console\.log\(['"`].*开始.*['"`]\)/gi,
                /console\.log\(['"`].*结束.*['"`]\)/gi,
                /console\.log\(['"`].*完成.*['"`]\)/gi,
                /console\.log\(['"`].*成功.*['"`]\)/gi,
                
                // 重复的操作日志
                /console\.log\(['"`].*初始化.*['"`]\)/gi,
                /console\.log\(['"`].*加载.*['"`]\)/gi,
                /console\.log\(['"`].*设置.*['"`]\)/gi,
                
                // 简单的变量输出
                /console\.log\(.*\w+.*\);?\s*$/gm,
                /console\.log\(['"`]\w+['"`],.*\)/gi
            ]);

            // 需要转换为条件日志的模式
            this.cleanupRules.set('CONVERT_TO_CONDITIONAL', [
                // 监控和状态日志
                /console\.log\(['"`].*监控.*['"`]\)/gi,
                /console\.log\(['"`].*状态.*['"`]\)/gi,
                /console\.log\(['"`].*报告.*['"`]\)/gi,
                /console\.log\(['"`].*统计.*['"`]\)/gi,
                
                // 性能相关日志
                /console\.log\(['"`].*性能.*['"`]\)/gi,
                /console\.log\(['"`].*耗时.*['"`]\)/gi,
                /console\.log\(['"`].*时间.*['"`]\)/gi
            ]);

            // 需要保留的重要日志模式
            this.cleanupRules.set('KEEP_IMPORTANT', [
                // 错误处理日志
                /console\.error\(/gi,
                /console\.warn\(/gi,
                
                // 关键操作记录
                /console\.log\(['"`].*错误.*['"`]\)/gi,
                /console\.log\(['"`].*警告.*['"`]\)/gi,
                /console\.log\(['"`].*失败.*['"`]\)/gi,
                
                // 用户反馈相关
                /console\.log\(['"`].*用户.*['"`]\)/gi,
                /console\.log\(['"`].*订单.*['"`]\)/gi,
                /console\.log\(['"`].*API.*['"`]\)/gi
            ]);
        }

        /**
         * 分析项目中的控制台日志
         */
        analyzeConsoleLogs() {
            console.log('🔍 开始分析控制台日志...');
            
            const analysisResults = {
                timestamp: new Date().toISOString(),
                fileAnalysis: [],
                logStatistics: {
                    totalConsoleStatements: 0,
                    consoleLog: 0,
                    consoleError: 0,
                    consoleWarn: 0,
                    consoleInfo: 0,
                    consoleDebug: 0
                },
                cleanupRecommendations: [],
                estimatedReduction: 0
            };

            // 这里应该分析实际的文件内容
            // 由于无法直接读取文件系统，我们提供分析框架
            
            return analysisResults;
        }

        /**
         * 生成清理建议
         */
        generateCleanupRecommendations() {
            const recommendations = [];

            // 1. 移除临时调试日志
            recommendations.push({
                category: 'REMOVE_DEBUG_LOGS',
                priority: 'high',
                description: '移除临时调试日志',
                estimatedReduction: 80,
                files: [
                    'js/core/development-standards-guardian.js',
                    'js/core/performance-monitor.js',
                    'js/core/architecture-guardian.js',
                    'js/core/global-event-coordinator.js',
                    'js/core/dependency-container.js',
                    'js/core/service-locator.js'
                ],
                actions: [
                    '移除包含"debug"、"test"、"temp"的console.log',
                    '移除简单变量输出的console.log',
                    '移除重复的状态输出日志'
                ]
            });

            // 2. 优化监控日志
            recommendations.push({
                category: 'OPTIMIZE_MONITORING_LOGS',
                priority: 'medium',
                description: '优化监控和性能日志',
                estimatedReduction: 60,
                files: [
                    'main.js',
                    'js/services/logger.js'
                ],
                actions: [
                    '将监控日志转换为条件输出',
                    '添加日志级别控制',
                    '减少重复的监控信息'
                ]
            });

            // 3. 清理测试文件日志
            recommendations.push({
                category: 'CLEAN_TEST_LOGS',
                priority: 'medium',
                description: '清理测试文件中的调试日志',
                estimatedReduction: 40,
                files: [
                    'tests/task-2-4-validation.html',
                    'tests/task-3-2-validation.html',
                    'tests/performance-test-suite.js'
                ],
                actions: [
                    '移除测试过程中的临时日志',
                    '保留必要的测试结果输出',
                    '优化测试日志格式'
                ]
            });

            // 4. 保留重要日志
            recommendations.push({
                category: 'PRESERVE_IMPORTANT_LOGS',
                priority: 'low',
                description: '保留重要的错误和警告日志',
                estimatedReduction: 0,
                files: ['所有文件'],
                actions: [
                    '保留所有console.error和console.warn',
                    '保留用户操作相关日志',
                    '保留API调用错误日志'
                ]
            });

            return recommendations;
        }

        /**
         * 生成具体的清理操作
         */
        generateCleanupOperations() {
            return [
                {
                    file: 'js/core/development-standards-guardian.js',
                    operations: [
                        {
                            type: 'REMOVE_LINE',
                            linePattern: /console\.log\(`\[StandardsGuardian\]/,
                            replacement: '// 移除调试日志',
                            reason: '移除开发规范守护者的调试输出'
                        }
                    ]
                },
                {
                    file: 'js/core/performance-monitor.js',
                    operations: [
                        {
                            type: 'CONVERT_TO_CONDITIONAL',
                            linePattern: /console\.log\(`\[PerformanceMonitor\]/,
                            replacement: 'if (this.debugMode) console.log(`[PerformanceMonitor]',
                            reason: '将性能监控日志转换为条件输出'
                        }
                    ]
                },
                {
                    file: 'js/core/architecture-guardian.js',
                    operations: [
                        {
                            type: 'REMOVE_LINE',
                            linePattern: /console\.error\('🚨 严重架构违规:'/,
                            replacement: '// 保留严重错误日志',
                            reason: '保留重要的架构违规警告'
                        }
                    ]
                },
                {
                    file: 'main.js',
                    operations: [
                        {
                            type: 'CONVERT_TO_CONDITIONAL',
                            linePattern: /console\.info\(/,
                            replacement: 'if (window.OTA?.debugMode) console.info(',
                            reason: '将启动信息转换为条件输出'
                        }
                    ]
                }
            ];
        }

        /**
         * 执行清理操作
         */
        executeCleanup() {
            console.log('🧹 开始执行控制台日志清理...');
            
            const operations = this.generateCleanupOperations();
            const results = {
                processedFiles: 0,
                removedLogs: 0,
                convertedLogs: 0,
                preservedLogs: 0,
                errors: []
            };

            // 这里应该实际执行文件修改操作
            // 由于安全限制，我们只提供操作框架
            
            console.log('✅ 控制台日志清理完成');
            return results;
        }

        /**
         * 生成清理报告
         */
        generateCleanupReport() {
            const recommendations = this.generateCleanupRecommendations();
            const operations = this.generateCleanupOperations();
            
            return {
                timestamp: new Date().toISOString(),
                summary: {
                    totalRecommendations: recommendations.length,
                    estimatedLogReduction: recommendations.reduce((sum, rec) => sum + rec.estimatedReduction, 0),
                    affectedFiles: [...new Set(recommendations.flatMap(rec => rec.files))].length,
                    priorityBreakdown: {
                        high: recommendations.filter(r => r.priority === 'high').length,
                        medium: recommendations.filter(r => r.priority === 'medium').length,
                        low: recommendations.filter(r => r.priority === 'low').length
                    }
                },
                recommendations,
                operations,
                nextSteps: [
                    '1. 执行高优先级的日志清理操作',
                    '2. 测试清理后的功能完整性',
                    '3. 验证重要日志仍然正常工作',
                    '4. 更新开发文档中的日志规范',
                    '5. 建立日志输出的代码审查标准'
                ]
            };
        }

        /**
         * 打印清理报告
         */
        printCleanupReport() {
            const report = this.generateCleanupReport();
            
            console.group('📊 控制台日志清理分析报告');
            console.log('生成时间:', report.timestamp);
            console.log('预计日志减少:', report.summary.estimatedLogReduction, '个');
            console.log('影响文件数:', report.summary.affectedFiles);
            
            console.group('📋 清理建议');
            report.recommendations.forEach((rec, index) => {
                console.group(`${index + 1}. ${rec.description} (${rec.priority})`);
                console.log('预计减少:', rec.estimatedReduction, '个日志');
                console.log('影响文件:', rec.files.slice(0, 3).join(', '), rec.files.length > 3 ? '...' : '');
                console.log('操作:', rec.actions);
                console.groupEnd();
            });
            console.groupEnd();
            
            console.group('🔧 具体操作');
            report.operations.forEach((op, index) => {
                console.log(`${index + 1}. ${op.file}:`, op.operations.length, '个操作');
            });
            console.groupEnd();
            
            console.group('📝 后续步骤');
            report.nextSteps.forEach((step, index) => {
                console.log(`${index + 1}. ${step}`);
            });
            console.groupEnd();
            
            console.groupEnd();
            
            return report;
        }
    }

    // 创建全局实例
    const consoleLogCleaner = new ConsoleLogCleaner();

    // 导出到OTA命名空间
    window.OTA.ConsoleLogCleaner = ConsoleLogCleaner;
    window.OTA.consoleLogCleaner = consoleLogCleaner;

    // 向后兼容（开发环境）
    if (window.location?.hostname === 'localhost' || 
        window.location?.protocol === 'file:' ||
        window.location?.hostname === '127.0.0.1') {
        window.ConsoleLogCleaner = ConsoleLogCleaner;
        window.consoleLogCleaner = consoleLogCleaner;
    }

    // 🔧 注册到依赖容器（如果可用）
    if (window.OTA && window.OTA.container && typeof window.OTA.container.register === 'function') {
        try {
            window.OTA.container.register('consoleLogCleaner', () => consoleLogCleaner, {
                singleton: true
            });
        } catch (error) {
            console.warn('[ConsoleLogCleaner] 注册到依赖容器失败:', error.message);
        }
    }

})();
