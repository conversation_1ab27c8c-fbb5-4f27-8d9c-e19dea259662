/**
 * @TEST API密钥管理器验证脚本
 * 🏷️ 标签: @API_KEY_MANAGER_TEST @VALIDATION_SCRIPT
 * 📝 说明: 验证API密钥管理器的核心功能和服务集成
 * 🎯 目标: 确保统一密钥管理系统正常工作
 */

(function() {
    'use strict';

    /**
     * API密钥管理器验证器
     */
    class ApiKeyManagerValidator {
        constructor() {
            this.testResults = [];
            this.logger = window.getLogger?.() || console;
        }

        /**
         * 记录测试结果
         */
        recordTest(testName, success, message, details = null) {
            const result = {
                testName,
                success,
                message,
                details,
                timestamp: new Date().toISOString()
            };
            
            this.testResults.push(result);
            
            const status = success ? '✅ PASS' : '❌ FAIL';
            this.logger.log(`${status} ${testName}: ${message}`, success ? 'info' : 'error', details);
            
            return result;
        }

        /**
         * 验证管理器基础功能
         */
        validateBasicFunctionality() {
            this.logger.log('开始验证API密钥管理器基础功能...', 'info');
            
            try {
                // 1. 检查管理器存在性
                const manager = window.OTA?.apiKeyManager;
                if (!manager) {
                    this.recordTest('管理器存在性', false, 'API密钥管理器未找到');
                    return false;
                }
                this.recordTest('管理器存在性', true, 'API密钥管理器已正确加载');

                // 2. 验证核心方法存在
                const requiredMethods = ['getApiKey', 'setApiKey', 'hasApiKey', 'getAllApiKeys', 'getApiKeyInfo'];
                const missingMethods = requiredMethods.filter(method => typeof manager[method] !== 'function');
                
                if (missingMethods.length > 0) {
                    this.recordTest('核心方法', false, `缺少方法: ${missingMethods.join(', ')}`);
                    return false;
                }
                this.recordTest('核心方法', true, '所有核心方法都存在');

                // 3. 测试密钥获取
                const geminiKey = manager.getApiKey('gemini');
                const kimiKey = manager.getApiKey('kimi');
                
                this.recordTest('Gemini密钥获取', !!geminiKey, 
                    geminiKey ? `成功获取密钥 (长度: ${geminiKey.length})` : '未能获取密钥');
                
                this.recordTest('Kimi密钥获取', !!kimiKey, 
                    kimiKey ? `成功获取密钥 (长度: ${kimiKey.length})` : '未能获取密钥');

                // 4. 测试密钥存在性检查
                const hasGemini = manager.hasApiKey('gemini');
                const hasKimi = manager.hasApiKey('kimi');
                
                this.recordTest('密钥存在性检查', hasGemini && hasKimi, 
                    `Gemini: ${hasGemini}, Kimi: ${hasKimi}`);

                // 5. 测试运行时密钥设置
                const testKey = 'test-runtime-key-' + Date.now();
                const setResult = manager.setApiKey('gemini', testKey);
                const retrievedKey = manager.getApiKey('gemini');
                
                this.recordTest('运行时密钥设置', setResult && retrievedKey === testKey, 
                    setResult ? '运行时密钥设置和获取成功' : '运行时密钥设置失败');

                // 6. 测试获取所有密钥信息
                const allKeys = manager.getAllApiKeys();
                const keyCount = Object.keys(allKeys || {}).length;
                
                this.recordTest('获取所有密钥信息', keyCount > 0, 
                    `获取到 ${keyCount} 个服务的密钥信息`);

                return true;

            } catch (error) {
                this.recordTest('基础功能验证', false, `验证过程中发生错误: ${error.message}`, {
                    stack: error.stack
                });
                return false;
            }
        }

        /**
         * 验证服务集成
         */
        validateServiceIntegration() {
            this.logger.log('开始验证服务集成...', 'info');
            
            try {
                // 1. 验证Kimi服务集成
                if (window.KimiService) {
                    const kimiService = new window.KimiService();
                    const hasApiKey = !!kimiService.apiKey;
                    
                    this.recordTest('Kimi服务集成', hasApiKey, 
                        hasApiKey ? `Kimi服务成功获取API密钥 (长度: ${kimiService.apiKey.length})` : 'Kimi服务未能获取API密钥');
                } else {
                    this.recordTest('Kimi服务集成', false, 'KimiService类未找到');
                }

                // 2. 验证OTA Registry集成
                const registry = window.OTA?.Registry;
                if (registry) {
                    const managerFromRegistry = registry.getService('apiKeyManager');
                    this.recordTest('Registry集成', !!managerFromRegistry, 
                        managerFromRegistry ? 'API密钥管理器已正确注册到Registry' : 'Registry中未找到API密钥管理器');
                    
                    // 验证工厂函数
                    const factory = registry.getFactory('getApiKeyManager');
                    if (factory) {
                        const managerInstance = factory();
                        this.recordTest('工厂函数', !!managerInstance, 
                            managerInstance ? '工厂函数正常工作' : '工厂函数返回空值');
                    } else {
                        this.recordTest('工厂函数', false, '工厂函数未注册');
                    }
                } else {
                    this.recordTest('Registry集成', false, 'OTA Registry未找到');
                }

                // 3. 验证全局访问
                const globalManager = window.OTA?.apiKeyManager;
                this.recordTest('全局访问', !!globalManager, 
                    globalManager ? '可通过window.OTA.apiKeyManager访问' : '全局访问失败');

                return true;

            } catch (error) {
                this.recordTest('服务集成验证', false, `验证过程中发生错误: ${error.message}`, {
                    stack: error.stack
                });
                return false;
            }
        }

        /**
         * 验证向后兼容性
         */
        validateBackwardCompatibility() {
            this.logger.log('开始验证向后兼容性...', 'info');
            
            try {
                const manager = window.OTA?.apiKeyManager;
                if (!manager) {
                    this.recordTest('向后兼容性', false, 'API密钥管理器未找到');
                    return false;
                }

                // 1. 测试降级机制 - 清除运行时密钥，应该降级到默认值
                if (manager.runtimeKeys) {
                    const originalSize = manager.runtimeKeys.size;
                    manager.runtimeKeys.clear();
                    
                    const defaultKey = manager.getApiKey('gemini');
                    this.recordTest('降级机制', !!defaultKey, 
                        defaultKey ? '成功降级到默认密钥' : '降级机制失败',
                        { originalRuntimeKeys: originalSize });
                }

                // 2. 测试错误处理
                const invalidKey = manager.getApiKey('nonexistent-service');
                this.recordTest('错误处理', invalidKey === null, 
                    invalidKey === null ? '正确处理无效服务名' : '错误处理异常');

                // 3. 测试统计功能
                const stats = manager.getAccessStats();
                this.recordTest('统计功能', !!stats && typeof stats.totalAccess === 'number', 
                    stats ? `统计功能正常 (总访问: ${stats.totalAccess})` : '统计功能异常');

                return true;

            } catch (error) {
                this.recordTest('向后兼容性验证', false, `验证过程中发生错误: ${error.message}`, {
                    stack: error.stack
                });
                return false;
            }
        }

        /**
         * 运行完整验证
         */
        runFullValidation() {
            this.logger.log('🔑 开始API密钥管理器完整验证...', 'info');
            
            const startTime = Date.now();
            this.testResults = [];

            // 运行所有验证
            const basicResult = this.validateBasicFunctionality();
            const integrationResult = this.validateServiceIntegration();
            const compatibilityResult = this.validateBackwardCompatibility();

            const endTime = Date.now();
            const duration = endTime - startTime;

            // 生成验证报告
            const report = this.generateValidationReport(duration);
            
            // 输出报告
            this.logger.log('🔑 API密钥管理器验证完成', 'info', report);
            
            return {
                success: basicResult && integrationResult && compatibilityResult,
                report,
                testResults: this.testResults,
                duration
            };
        }

        /**
         * 生成验证报告
         */
        generateValidationReport(duration) {
            const totalTests = this.testResults.length;
            const passedTests = this.testResults.filter(r => r.success).length;
            const failedTests = totalTests - passedTests;
            const successRate = totalTests > 0 ? (passedTests / totalTests * 100).toFixed(1) : 0;

            const report = {
                summary: {
                    totalTests,
                    passedTests,
                    failedTests,
                    successRate: `${successRate}%`,
                    duration: `${duration}ms`
                },
                categories: {
                    basicFunctionality: this.testResults.filter(r => 
                        ['管理器存在性', '核心方法', 'Gemini密钥获取', 'Kimi密钥获取', '密钥存在性检查', '运行时密钥设置', '获取所有密钥信息'].includes(r.testName)
                    ).length,
                    serviceIntegration: this.testResults.filter(r => 
                        ['Kimi服务集成', 'Registry集成', '工厂函数', '全局访问'].includes(r.testName)
                    ).length,
                    backwardCompatibility: this.testResults.filter(r => 
                        ['降级机制', '错误处理', '统计功能'].includes(r.testName)
                    ).length
                },
                failedTests: this.testResults.filter(r => !r.success).map(r => ({
                    name: r.testName,
                    message: r.message
                }))
            };

            return report;
        }
    }

    // 创建全局验证器实例
    window.apiKeyManagerValidator = new ApiKeyManagerValidator();

    // 如果在浏览器环境中，自动运行验证
    if (typeof window !== 'undefined' && window.document) {
        // 等待页面加载完成后运行验证
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => window.apiKeyManagerValidator.runFullValidation(), 1000);
            });
        } else {
            setTimeout(() => window.apiKeyManagerValidator.runFullValidation(), 1000);
        }
    }

    // Node.js环境支持
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = ApiKeyManagerValidator;
    }

})();
