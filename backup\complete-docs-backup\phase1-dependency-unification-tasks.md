# 阶段一：依赖统一化任务清单

## 📋 阶段概要

**目标**：消除双重依赖模式，建立统一的依赖获取机制  
**时间**：第1-2周  
**优先级**：高  

## 🎯 核心任务

### 任务组A：传统获取函数迁移（47个位置）

#### A1: 迁移 getAppState()（12个位置）
**文件清单**：
- [ ] `js/api-service.js` - 第143, 180, 222, 291, 339, 378, 413行
- [ ] `js/gemini-service.js` - 第89, 156行  
- [ ] `js/ui-manager.js` - 第234行
- [ ] `main.js` - 第208, 463行

**迁移模式**：
```javascript
// 替换前
const appState = getAppState();

// 替换后  
const appState = window.OTA.getService('appState');
```

**验证标准**：
- [ ] 所有getAppState()调用已替换
- [ ] 功能测试通过
- [ ] 无控制台错误

#### A2: 迁移 getLogger()（8个位置）  
**文件清单**：
- [ ] `js/api-service.js` - 第148, 338, 377, 414行
- [ ] `js/gemini-service.js` - 第67行
- [ ] `js/ui-manager.js` - 第123行
- [ ] `main.js` - 第291行

**验证标准**：
- [ ] 日志功能正常工作
- [ ] 性能监控数据正确

#### A3: 迁移 getAPIService()（7个位置）
**文件清单**：
- [ ] `js/multi-order-manager.js` - 第234, 567行
- [ ] `js/order-history-manager.js` - 第89行
- [ ] `js/managers/form-manager.js` - 第156行
- [ ] `js/ui-manager.js` - 第345行

**验证标准**：
- [ ] API调用功能正常
- [ ] 登录/登出功能正常

#### A4: 迁移 getGeminiService()（6个位置）
**文件清单**：
- [ ] `js/api-service.js` - 第415行
- [ ] `js/managers/event-manager.js` - 第123行
- [ ] `js/multi-order-manager.js` - 第890行
- [ ] `js/ui-manager.js` - 第456行

**验证标准**：
- [ ] AI解析功能正常
- [ ] 订单处理流程无误

#### A5: 迁移 getUIManager()（14个位置）
**文件清单**：
- [ ] `js/multi-order-manager.js` - 第123, 234, 345, 456, 567, 678, 789, 890, 991行
- [ ] `js/managers/event-manager.js` - 第234行
- [ ] `js/order-history-manager.js` - 第156行
- [ ] `js/image-upload-manager.js` - 第89行
- [ ] `main.js` - 第325, 569行

**验证标准**：
- [ ] UI更新功能正常
- [ ] 弹窗和提示正常显示

### 任务组B：循环依赖解决（3个主要循环）

#### B1: 解决 UIManager ↔ EventManager ↔ FormManager 循环依赖
**问题分析**：
- UIManager在创建时传递自身引用给EventManager
- EventManager需要调用UIManager的showAlert等方法
- FormManager通过UIManager获取，形成循环

**解决方案**：
```javascript
// 实现事件驱动架构
class UIManager {
    constructor() {
        this.eventBus = window.OTA.getService('eventCoordinator');
        // 移除直接依赖，使用事件通信
    }
    
    showAlert(message) {
        this.eventBus.emit('ui:alert', { message });
    }
}

class EventManager {
    constructor() {
        this.eventBus = window.OTA.getService('eventCoordinator');
        // 监听UI事件而非直接调用
        this.eventBus.on('ui:alert', this.handleAlert.bind(this));
    }
}
```

**实施步骤**：
- [ ] 重构UIManager构造函数，移除EventManager直接创建
- [ ] 实现事件驱动的通信机制
- [ ] 修改EventManager使用事件而非直接调用UIManager
- [ ] 重构FormManager获取UIManager的方式
- [ ] 测试循环依赖已解决

#### B2: 解决 MultiOrderManager ↔ UIManager 循环依赖
**问题分析**：
- MultiOrderManager在多处获取UIManager实例
- UIManager初始化可能触发MultiOrderManager的初始化

**解决方案**：
```javascript
// 使用延迟初始化
class MultiOrderManager {
    constructor() {
        this._uiManager = null;
    }
    
    get uiManager() {
        if (!this._uiManager) {
            this._uiManager = window.OTA.getService('uiManager');
        }
        return this._uiManager;
    }
}
```

**实施步骤**：
- [ ] 实现UIManager的延迟获取模式
- [ ] 修改MultiOrderManager的15处UIManager获取
- [ ] 确保初始化顺序正确
- [ ] 验证循环依赖已消除

#### B3: 优化 ServiceLocator ↔ DependencyContainer 依赖链
**问题分析**：
- ServiceLocator依赖DependencyContainer
- 各服务注册时可能互相依赖

**解决方案**：
```javascript
// 分阶段初始化
class ApplicationBootstrap {
    async initializeDependencies() {
        // 第一阶段：注册无依赖的服务
        this.registerCoreDependencies();
        
        // 第二阶段：注册有依赖的服务
        this.registerBusinessServices();
        
        // 第三阶段：初始化所有服务
        this.initializeAllServices();
    }
}
```

**实施步骤**：
- [ ] 分析所有服务的依赖关系
- [ ] 实现分阶段初始化机制
- [ ] 确保无循环依赖
- [ ] 测试服务获取正常

### 任务组C：统一服务注册（89个服务）

#### C1: 核心服务注册
**服务清单**：
- [ ] appState
- [ ] logger  
- [ ] utils
- [ ] eventCoordinator
- [ ] apiService
- [ ] geminiService
- [ ] i18nManager

**注册模式**：
```javascript
// 在dependency-container.js中注册
container.register('appState', () => {
    return window.OTA.appState || new AppState();
});

container.register('logger', () => {
    return window.OTA.logger || new Logger();
});
```

#### C2: 管理器服务注册
**服务清单**：
- [ ] uiManager
- [ ] formManager
- [ ] eventManager
- [ ] multiOrderManager
- [ ] orderHistoryManager
- [ ] imageUploadManager
- [ ] currencyConverter
- [ ] pagingServiceManager
- [ ] priceManager
- [ ] realtimeAnalysisManager

#### C3: 工具类服务注册
**服务清单**：
- [ ] gridResizer
- [ ] hotelDataInline
- [ ] otaChannelMapping
- [ ] kimiService

**验证标准**：
- [ ] 所有服务可通过container.get()获取
- [ ] 服务实例为单例模式
- [ ] 无重复注册警告

### 任务组D：兼容性保证

#### D1: 创建兼容性包装器
**实施内容**：
```javascript
// 在service-locator.js中添加
window.getAppState = function() {
    console.warn('[DEPRECATED] getAppState() 已废弃，请使用 window.OTA.getService("appState")');
    return window.OTA.getService('appState');
};

window.getLogger = function() {
    console.warn('[DEPRECATED] getLogger() 已废弃，请使用 window.OTA.getService("logger")');  
    return window.OTA.getService('logger');
};
```

**包装器清单**：
- [ ] getAppState()
- [ ] getLogger()
- [ ] getAPIService()
- [ ] getApiService()
- [ ] getGeminiService()
- [ ] getUIManager()
- [ ] getUtils()
- [ ] getImageUploadManager()
- [ ] getCurrencyConverter()
- [ ] getMultiOrderManager()
- [ ] getOrderHistoryManager()
- [ ] getPagingServiceManager()
- [ ] getI18nManager()

#### D2: 迁移监控机制
**实施内容**：
```javascript
// 在service-locator.js中添加监控
class MigrationMonitor {
    constructor() {
        this.deprecatedCalls = new Map();
    }
    
    recordDeprecatedCall(functionName) {
        const count = this.deprecatedCalls.get(functionName) || 0;
        this.deprecatedCalls.set(functionName, count + 1);
    }
    
    generateReport() {
        return {
            totalCalls: Array.from(this.deprecatedCalls.values()).reduce((a, b) => a + b, 0),
            byFunction: Object.fromEntries(this.deprecatedCalls)
        };
    }
}
```

**监控指标**：
- [ ] 废弃函数调用次数
- [ ] 调用位置统计
- [ ] 迁移进度百分比

## 🧪 测试验证

### 单元测试
**测试用例**：
- [ ] 依赖容器服务注册测试
- [ ] 服务单例模式测试
- [ ] 循环依赖检测测试
- [ ] 兼容性包装器测试

### 集成测试
**测试场景**：
- [ ] 系统启动流程测试
- [ ] 核心功能集成测试
- [ ] 用户登录流程测试
- [ ] 订单创建流程测试
- [ ] 多订单管理测试

### 性能测试
**测试指标**：
- [ ] 启动时间对比
- [ ] 内存使用对比
- [ ] 服务获取性能
- [ ] 循环依赖消除验证

## 📊 成功标准

### 功能指标
- [ ] 所有现有功能正常运行
- [ ] 47个传统获取函数已迁移
- [ ] 3个循环依赖已解决
- [ ] 89个服务已注册到容器

### 性能指标  
- [ ] 启动时间无显著增加
- [ ] 内存使用稳定或减少
- [ ] 服务获取延迟<5ms
- [ ] 无内存泄漏

### 质量指标
- [ ] ESLint检查通过
- [ ] 无控制台错误或警告
- [ ] 测试覆盖率>90%
- [ ] 代码审查通过

## 🗓️ 时间规划

### 第1周
**周一-周二**：任务组A（迁移传统获取函数）
**周三-周四**：任务组B（解决循环依赖）  
**周五**：任务组C第一部分（核心服务注册）

### 第2周
**周一-周二**：任务组C剩余部分（管理器和工具类服务）
**周三-周四**：任务组D（兼容性保证）
**周五**：测试验证和问题修复

## 🚨 风险提醒

### 高风险操作
- [ ] 修改core/application-bootstrap.js时需格外小心
- [ ] UIManager的重构可能影响所有UI功能
- [ ] MultiOrderManager的修改需要全面测试

### 回滚预案
- [ ] 每次修改前创建Git分支
- [ ] 保留原始获取函数备份
- [ ] 准备快速回滚脚本

### 质量保证
- [ ] 每个任务完成后立即测试
- [ ] 关键修改需要代码审查
- [ ] 定期备份和提交代码

---

**任务清单版本**：v1.0  
**创建日期**：2025-01-27  
**预计完成**：2025-02-10  
**负责人**：前端架构组