<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>🧪 完整修复验证测试</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f8f9fa; 
            line-height: 1.6;
        }
        .test-container { 
            background: white; 
            padding: 25px; 
            border-radius: 10px; 
            box-shadow: 0 4px 6px rgba(0,0,0,0.1); 
            max-width: 1000px;
            margin: 0 auto;
        }
        .result { 
            margin: 8px 0; 
            padding: 12px; 
            border-radius: 6px; 
            border-left: 4px solid;
        }
        .success { 
            background: #d4edda; 
            color: #155724; 
            border-left-color: #28a745;
        }
        .error { 
            background: #f8d7da; 
            color: #721c24; 
            border-left-color: #dc3545;
        }
        .warning { 
            background: #fff3cd; 
            color: #856404; 
            border-left-color: #ffc107;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border-left-color: #17a2b8;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #495057;
        }
        .stat-label {
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
        }
        h1 { color: #343a40; text-align: center; }
        h2 { color: #495057; border-bottom: 2px solid #dee2e6; padding-bottom: 10px; }
        h3 { color: #6c757d; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 完整修复验证测试</h1>
        <p style="text-align: center; color: #6c757d;">
            验证所有CORS错误修复和功能完整性
        </p>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="errorCount">0</div>
                <div class="stat-label">启动错误</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="successCount">0</div>
                <div class="stat-label">通过测试</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="healthScore">0%</div>
                <div class="stat-label">健康评分</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="startupTime">-</div>
                <div class="stat-label">启动时间</div>
            </div>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progressFill" style="width: 0%"></div>
        </div>
        
        <h2>📊 实时启动监控</h2>
        <div id="startup-monitor">🔄 等待应用启动...</div>
        
        <h2>🔧 核心模块测试</h2>
        <div id="module-tests">📋 准备测试核心模块...</div>
        
        <h2>🎯 功能验证测试</h2>
        <div id="function-tests">⏳ 等待功能测试...</div>
        
        <h2>📈 最终报告</h2>
        <div id="final-report">📊 测试进行中...</div>
    </div>

    <script>
        // 测试状态
        let testState = {
            errors: 0,
            successes: 0,
            totalTests: 12,
            startTime: Date.now(),
            startupTime: null
        };
        
        // DOM元素
        const elements = {
            startupMonitor: document.getElementById('startup-monitor'),
            moduleTests: document.getElementById('module-tests'),
            functionTests: document.getElementById('function-tests'),
            finalReport: document.getElementById('final-report'),
            errorCount: document.getElementById('errorCount'),
            successCount: document.getElementById('successCount'),
            healthScore: document.getElementById('healthScore'),
            startupTimeEl: document.getElementById('startupTime'),
            progressFill: document.getElementById('progressFill')
        };
        
        function updateStats() {
            elements.errorCount.textContent = testState.errors;
            elements.successCount.textContent = testState.successes;
            const progress = Math.round((testState.successes / testState.totalTests) * 100);
            elements.healthScore.textContent = progress + '%';
            elements.progressFill.style.width = progress + '%';
            
            if (testState.startupTime) {
                elements.startupTimeEl.textContent = testState.startupTime + 'ms';
            }
        }
        
        function addResult(container, message, type = 'success') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = message;
            container.appendChild(div);
            
            if (type === 'success') testState.successes++;
            if (type === 'error') testState.errors++;
            updateStats();
        }
        
        // 监控启动错误
        const originalError = console.error;
        console.error = function(...args) {
            const message = args.join(' ');
            if (message.includes('configManager') || 
                message.includes('handlePanelBlur') || 
                message.includes('handlePanelFocus') ||
                message.includes('api-key-manager')) {
                addResult(elements.startupMonitor, `❌ 启动错误: ${message}`, 'error');
            }
            originalError.apply(console, args);
        };
        
        // 主测试流程
        setTimeout(() => {
            testState.startupTime = Date.now() - testState.startTime;
            updateStats();
            
            addResult(elements.startupMonitor, '✅ 应用启动监控完成', 'success');
            runModuleTests();
        }, 3000);
        
        function runModuleTests() {
            addResult(elements.moduleTests, '🔍 开始核心模块测试...', 'info');
            
            // 测试1: ConfigManager
            try {
                const configManager = window.OTA?.gemini?.core?.ConfigManager || 
                                    window.OTA?.gemini?.core?.getConfigManager?.();
                if (configManager || window.OTA?.gemini?.core?.ConfigManager) {
                    addResult(elements.moduleTests, '✅ ConfigManager: 已修复并可用', 'success');
                } else {
                    addResult(elements.moduleTests, '❌ ConfigManager: 仍然不可用', 'error');
                }
            } catch (error) {
                addResult(elements.moduleTests, `❌ ConfigManager测试失败: ${error.message}`, 'error');
            }
            
            // 测试2: API密钥管理器
            try {
                const apiKeyManager = window.OTA?.apiKeyManager || window.getApiKeyManager?.();
                if (apiKeyManager) {
                    addResult(elements.moduleTests, '✅ API密钥管理器: 正常运行', 'success');
                    
                    // 测试logger兼容性
                    const testKey = apiKeyManager.getApiKey('gemini');
                    if (testKey) {
                        addResult(elements.moduleTests, '✅ API密钥获取: 功能正常', 'success');
                    } else {
                        addResult(elements.moduleTests, '⚠️ API密钥获取: 无可用密钥', 'warning');
                    }
                } else {
                    addResult(elements.moduleTests, '❌ API密钥管理器: 不可用', 'error');
                }
            } catch (error) {
                addResult(elements.moduleTests, `❌ API密钥管理器测试失败: ${error.message}`, 'error');
            }
            
            // 测试3: 多订单事件管理器
            try {
                const eventManager = window.OTA?.multiOrder?.getEventManager?.();
                if (eventManager) {
                    addResult(elements.moduleTests, '✅ 多订单事件管理器: 可用', 'success');
                    
                    // 测试缺失的方法是否已修复
                    if (typeof eventManager.handlePanelBlur === 'function') {
                        addResult(elements.moduleTests, '✅ handlePanelBlur方法: 已修复', 'success');
                    } else {
                        addResult(elements.moduleTests, '❌ handlePanelBlur方法: 仍然缺失', 'error');
                    }
                    
                    if (typeof eventManager.handlePanelFocus === 'function') {
                        addResult(elements.moduleTests, '✅ handlePanelFocus方法: 已修复', 'success');
                    } else {
                        addResult(elements.moduleTests, '❌ handlePanelFocus方法: 仍然缺失', 'error');
                    }
                } else {
                    addResult(elements.moduleTests, '❌ 多订单事件管理器: 不可用', 'error');
                }
            } catch (error) {
                addResult(elements.moduleTests, `❌ 多订单事件管理器测试失败: ${error.message}`, 'error');
            }
            
            setTimeout(runFunctionTests, 1000);
        }
        
        function runFunctionTests() {
            addResult(elements.functionTests, '🎯 开始功能验证测试...', 'info');
            
            // 测试系统启动状态
            if (window.OTA?.app) {
                addResult(elements.functionTests, '✅ 系统启动: 成功', 'success');
            } else {
                addResult(elements.functionTests, '❌ 系统启动: 失败', 'error');
            }
            
            // 测试Gemini服务
            try {
                const geminiService = window.OTA?.getService?.('geminiService');
                if (geminiService) {
                    addResult(elements.functionTests, '✅ Gemini AI服务: 可用', 'success');
                } else {
                    addResult(elements.functionTests, '❌ Gemini AI服务: 不可用', 'error');
                }
            } catch (error) {
                addResult(elements.functionTests, `❌ Gemini服务测试失败: ${error.message}`, 'error');
            }
            
            // 测试多订单管理器
            try {
                const multiOrderManager = window.OTA?.getService?.('multiOrderManager');
                if (multiOrderManager) {
                    addResult(elements.functionTests, '✅ 多订单管理器: 可用', 'success');
                } else {
                    addResult(elements.functionTests, '❌ 多订单管理器: 不可用', 'error');
                }
            } catch (error) {
                addResult(elements.functionTests, `❌ 多订单管理器测试失败: ${error.message}`, 'error');
            }
            
            setTimeout(generateFinalReport, 1000);
        }
        
        function generateFinalReport() {
            const successRate = Math.round((testState.successes / testState.totalTests) * 100);
            const totalTime = Date.now() - testState.startTime;
            
            let reportClass = 'success';
            let reportIcon = '🎉';
            let reportTitle = '完美修复！';
            
            if (testState.errors > 0) {
                reportClass = testState.errors > 2 ? 'error' : 'warning';
                reportIcon = testState.errors > 2 ? '❌' : '⚠️';
                reportTitle = testState.errors > 2 ? '仍有问题需要处理' : '基本修复完成';
            }
            
            addResult(elements.finalReport, `${reportIcon} ${reportTitle}`, reportClass);
            addResult(elements.finalReport, `✅ 成功测试: ${testState.successes}/${testState.totalTests}`, 'info');
            addResult(elements.finalReport, `❌ 失败测试: ${testState.errors}/${testState.totalTests}`, 'info');
            addResult(elements.finalReport, `📊 成功率: ${successRate}%`, 'info');
            addResult(elements.finalReport, `⏱️ 总测试时间: ${totalTime}ms`, 'info');
            
            if (testState.errors === 0) {
                addResult(elements.finalReport, '🚀 所有CORS错误已修复！应用可以完美运行！', 'success');
                addResult(elements.finalReport, '💡 现在可以正常双击 index.html 使用应用了', 'info');
            } else {
                addResult(elements.finalReport, `🔧 还有 ${testState.errors} 个问题需要进一步处理`, 'warning');
            }
        }
    </script>
    
    <!-- 加载完整的应用来进行测试 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/core/api-key-manager.js"></script>
    <script src="js/core/ota-registry.js"></script>
    <script src="js/ai/gemini/core/config-manager.js"></script>
    <script src="js/components/multi-order/multi-order-event-manager.js"></script>
    <script src="js/bootstrap/application-bootstrap.js"></script>
    <script src="main.js"></script>
</body>
</html>