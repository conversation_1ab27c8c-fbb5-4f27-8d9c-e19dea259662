/**
 * @CORE 错误恢复引擎
 * 🏷️ 标签: @ERROR_RECOVERY_ENGINE
 * 📝 说明: 负责Gemini AI响应的错误检测、修复和数据恢复
 * 🎯 功能: JSON格式修复、部分数据提取、智能错误恢复
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.core = window.OTA.gemini.core || {};

(function() {
    'use strict';

    /**
     * 错误恢复引擎类
     * 负责处理Gemini AI响应中的各种错误和异常情况
     */
    class ErrorRecoveryEngine {
        constructor() {
            this.logger = window.getLogger?.() || console;
            
            // 初始化恢复策略
            this.initializeRecoveryStrategies();
            
            // 初始化错误模式
            this.initializeErrorPatterns();
            
            // 统计信息
            this.stats = {
                totalRecoveryAttempts: 0,
                successfulRecoveries: 0,
                jsonFixAttempts: 0,
                partialExtractionAttempts: 0,
                failedRecoveries: 0
            };
        }

        /**
         * 初始化恢复策略
         */
        initializeRecoveryStrategies() {
            // JSON修复策略优先级
            this.jsonFixStrategies = [
                'removeTrailingCommas',
                'fixQuotes',
                'addMissingBrackets',
                'removeComments',
                'fixEscapeCharacters',
                'extractJsonBlock'
            ];

            // 部分数据提取策略
            this.partialExtractionStrategies = [
                'extractKeyValuePairs',
                'extractStructuredData',
                'extractBasicInfo',
                'extractFromText'
            ];
        }

        /**
         * 初始化错误模式
         */
        initializeErrorPatterns() {
            // 常见JSON错误模式
            this.jsonErrorPatterns = {
                trailingComma: /,(\s*[}\]])/g,
                unquotedKeys: /(\w+)(\s*:\s*)/g,
                singleQuotes: /'/g,
                missingCommas: /}(\s*)"/g,
                extraCommas: /,,+/g,
                unclosedStrings: /"([^"\\]*(\\.[^"\\]*)*)/g
            };

            // 数据提取模式
            this.dataExtractionPatterns = {
                customerName: /(?:customer_name|姓名|客户)["\s]*[:：]\s*["']?([^"',\n]+)["']?/i,
                customerContact: /(?:customer_contact|联系方式|电话)["\s]*[:：]\s*["']?([^"',\n]+)["']?/i,
                pickupDate: /(?:pickup_date|接送日期|日期)["\s]*[:：]\s*["']?([^"',\n]+)["']?/i,
                pickupTime: /(?:pickup_time|接送时间|时间)["\s]*[:：]\s*["']?([^"',\n]+)["']?/i,
                pickup: /(?:pickup|接送地点|出发地)["\s]*[:：]\s*["']?([^"',\n]+)["']?/i,
                dropoff: /(?:dropoff|目的地|到达地)["\s]*[:：]\s*["']?([^"',\n]+)["']?/i,
                otaPrice: /(?:ota_price|价格|费用)["\s]*[:：]\s*["']?([^"',\n]+)["']?/i,
                otaReferenceNumber: /(?:ota_reference_number|订单号|参考号)["\s]*[:：]\s*["']?([^"',\n]+)["']?/i
            };
        }

        /**
         * 智能错误恢复（从原gemini-service.js迁移）
         * @param {string} responseText - Gemini响应文本
         * @param {string} originalText - 原始订单文本
         * @returns {Object} 恢复结果
         */
        intelligentErrorRecovery(responseText, originalText = '') {
            this.stats.totalRecoveryAttempts++;
            
            try {
                this.logger.log('开始智能错误恢复', 'info');

                // 步骤1: 尝试直接解析JSON
                const directParseResult = this.tryDirectJsonParse(responseText);
                if (directParseResult.success) {
                    this.stats.successfulRecoveries++;
                    return {
                        success: true,
                        data: directParseResult.data,
                        method: 'direct_parse',
                        confidence: 0.9
                    };
                }

                // 步骤2: 尝试修复JSON格式
                const jsonFixResult = this.tryFixJsonFormat(responseText);
                if (jsonFixResult.success) {
                    this.stats.successfulRecoveries++;
                    this.stats.jsonFixAttempts++;
                    return {
                        success: true,
                        data: jsonFixResult.data,
                        method: 'json_fix',
                        confidence: 0.7,
                        fixedIssues: jsonFixResult.fixedIssues
                    };
                }

                // 步骤3: 尝试提取部分数据
                const partialResult = this.tryExtractPartialData(responseText, originalText);
                if (partialResult.success) {
                    this.stats.successfulRecoveries++;
                    this.stats.partialExtractionAttempts++;
                    return {
                        success: true,
                        data: partialResult.data,
                        method: 'partial_extraction',
                        confidence: 0.5,
                        extractedFields: partialResult.extractedFields
                    };
                }

                // 步骤4: 创建默认结构
                const defaultResult = this.createDefaultStructure(originalText);
                this.stats.failedRecoveries++;
                return {
                    success: false,
                    data: defaultResult,
                    method: 'default_fallback',
                    confidence: 0.1,
                    error: 'All recovery strategies failed'
                };

            } catch (error) {
                this.logger.logError('智能错误恢复失败', error);
                this.stats.failedRecoveries++;
                return {
                    success: false,
                    data: this.createDefaultStructure(originalText),
                    method: 'error_fallback',
                    confidence: 0.0,
                    error: error.message
                };
            }
        }

        /**
         * 尝试直接解析JSON
         * @param {string} text - 响应文本
         * @returns {Object} 解析结果
         */
        tryDirectJsonParse(text) {
            try {
                const cleanText = text.trim();
                const parsed = JSON.parse(cleanText);
                
                if (typeof parsed === 'object' && parsed !== null) {
                    return { success: true, data: parsed };
                }
                
                return { success: false, error: 'Not a valid object' };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        /**
         * 尝试修复JSON格式（从原gemini-service.js迁移）
         * @param {string} text - 响应文本
         * @returns {Object} 修复结果
         */
        tryFixJsonFormat(text) {
            let fixedText = text.trim();
            const fixedIssues = [];

            try {
                // 提取JSON块
                const jsonMatch = fixedText.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    fixedText = jsonMatch[0];
                    fixedIssues.push('extracted_json_block');
                }

                // 应用修复策略
                for (const strategy of this.jsonFixStrategies) {
                    const result = this.applyFixStrategy(fixedText, strategy);
                    if (result.modified) {
                        fixedText = result.text;
                        fixedIssues.push(strategy);
                    }
                }

                // 尝试解析修复后的JSON
                const parsed = JSON.parse(fixedText);
                
                return {
                    success: true,
                    data: parsed,
                    fixedIssues: fixedIssues
                };

            } catch (error) {
                return {
                    success: false,
                    error: error.message,
                    fixedIssues: fixedIssues
                };
            }
        }

        /**
         * 应用修复策略
         * @param {string} text - 文本
         * @param {string} strategy - 策略名称
         * @returns {Object} 修复结果
         */
        applyFixStrategy(text, strategy) {
            let modifiedText = text;
            let modified = false;

            switch (strategy) {
                case 'removeTrailingCommas':
                    const beforeCommas = modifiedText;
                    modifiedText = modifiedText.replace(this.jsonErrorPatterns.trailingComma, '$1');
                    modified = beforeCommas !== modifiedText;
                    break;

                case 'fixQuotes':
                    const beforeQuotes = modifiedText;
                    modifiedText = modifiedText.replace(this.jsonErrorPatterns.singleQuotes, '"');
                    modified = beforeQuotes !== modifiedText;
                    break;

                case 'addMissingBrackets':
                    if (!modifiedText.startsWith('{')) {
                        modifiedText = '{' + modifiedText;
                        modified = true;
                    }
                    if (!modifiedText.endsWith('}')) {
                        modifiedText = modifiedText + '}';
                        modified = true;
                    }
                    break;

                case 'removeComments':
                    const beforeComments = modifiedText;
                    modifiedText = modifiedText.replace(/\/\*[\s\S]*?\*\//g, '');
                    modifiedText = modifiedText.replace(/\/\/.*$/gm, '');
                    modified = beforeComments !== modifiedText;
                    break;

                case 'fixEscapeCharacters':
                    const beforeEscape = modifiedText;
                    modifiedText = modifiedText.replace(/\\(?!["\\/bfnrt])/g, '\\\\');
                    modified = beforeEscape !== modifiedText;
                    break;

                case 'extractJsonBlock':
                    const jsonMatch = modifiedText.match(/\{[\s\S]*\}/);
                    if (jsonMatch && jsonMatch[0] !== modifiedText) {
                        modifiedText = jsonMatch[0];
                        modified = true;
                    }
                    break;
            }

            return { text: modifiedText, modified };
        }

        /**
         * 尝试提取部分数据（从原gemini-service.js迁移）
         * @param {string} responseText - 响应文本
         * @param {string} originalText - 原始文本
         * @returns {Object} 提取结果
         */
        tryExtractPartialData(responseText, originalText = '') {
            try {
                const extractedData = {};
                const extractedFields = [];

                // 使用正则表达式提取关键字段
                for (const [field, pattern] of Object.entries(this.dataExtractionPatterns)) {
                    const match = responseText.match(pattern);
                    if (match && match[1]) {
                        extractedData[field] = match[1].trim();
                        extractedFields.push(field);
                    }
                }

                // 如果从响应中提取的数据不足，尝试从原始文本提取
                if (extractedFields.length < 3 && originalText) {
                    for (const [field, pattern] of Object.entries(this.dataExtractionPatterns)) {
                        if (!extractedData[field]) {
                            const match = originalText.match(pattern);
                            if (match && match[1]) {
                                extractedData[field] = match[1].trim();
                                extractedFields.push(field + '_from_original');
                            }
                        }
                    }
                }

                // 添加默认值
                const defaultData = this.createDefaultStructure(originalText);
                const mergedData = { ...defaultData, ...extractedData };

                return {
                    success: extractedFields.length > 0,
                    data: mergedData,
                    extractedFields: extractedFields
                };

            } catch (error) {
                this.logger.logError('部分数据提取失败', error);
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        /**
         * 创建默认数据结构
         * @param {string} originalText - 原始文本
         * @returns {Object} 默认数据结构
         */
        createDefaultStructure(originalText = '') {
            return {
                customer_name: null,
                customer_contact: null,
                customer_email: null,
                pickup: null,
                dropoff: null,
                pickup_date: null,
                pickup_time: null,
                passenger_count: null,
                luggage_count: null,
                car_type_id: 1, // 默认Comfort 5 Seater
                sub_category_id: 2, // 默认接机
                driving_region_id: 1, // 默认吉隆坡
                backend_user_id: null,
                languages_id_array: [2], // 默认英语
                ota_price: null,
                ota_currency: 'MYR',
                ota_reference_number: null,
                special_requests: null,
                flight_info: null,
                meet_and_greet: false,
                child_seat_required: false,
                wheelchair_accessible: false,
                raw_text: originalText,
                _recovery_method: 'default_structure',
                _confidence: 0.1
            };
        }

        /**
         * 验证恢复的数据
         * @param {Object} data - 恢复的数据
         * @returns {Object} 验证结果
         */
        validateRecoveredData(data) {
            const validation = {
                isValid: true,
                errors: [],
                warnings: [],
                score: 0
            };

            try {
                // 检查必需字段
                const requiredFields = ['customer_name', 'pickup', 'pickup_date'];
                for (const field of requiredFields) {
                    if (!data[field]) {
                        validation.errors.push(`Missing required field: ${field}`);
                        validation.isValid = false;
                    } else {
                        validation.score += 20;
                    }
                }

                // 检查数据类型
                if (data.passenger_count && isNaN(parseInt(data.passenger_count))) {
                    validation.warnings.push('passenger_count is not a valid number');
                } else if (data.passenger_count) {
                    validation.score += 10;
                }

                if (data.ota_price && isNaN(parseFloat(data.ota_price))) {
                    validation.warnings.push('ota_price is not a valid number');
                } else if (data.ota_price) {
                    validation.score += 10;
                }

                // 检查日期格式
                if (data.pickup_date && !/^\d{2}-\d{2}-\d{4}$/.test(data.pickup_date)) {
                    validation.warnings.push('pickup_date format should be DD-MM-YYYY');
                } else if (data.pickup_date) {
                    validation.score += 15;
                }

                // 检查时间格式
                if (data.pickup_time && !/^\d{2}:\d{2}$/.test(data.pickup_time)) {
                    validation.warnings.push('pickup_time format should be HH:MM');
                } else if (data.pickup_time) {
                    validation.score += 15;
                }

                // 计算最终分数
                validation.score = Math.min(validation.score, 100);

            } catch (error) {
                validation.isValid = false;
                validation.errors.push(`Validation error: ${error.message}`);
            }

            return validation;
        }

        /**
         * 获取恢复统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            const total = this.stats.totalRecoveryAttempts;
            return {
                ...this.stats,
                successRate: total > 0 ? ((this.stats.successfulRecoveries / total) * 100).toFixed(2) + '%' : '0%',
                jsonFixRate: this.stats.jsonFixAttempts > 0 ? 
                    ((this.stats.jsonFixAttempts / total) * 100).toFixed(2) + '%' : '0%',
                partialExtractionRate: this.stats.partialExtractionAttempts > 0 ? 
                    ((this.stats.partialExtractionAttempts / total) * 100).toFixed(2) + '%' : '0%'
            };
        }

        /**
         * 重置统计信息
         */
        resetStats() {
            this.stats = {
                totalRecoveryAttempts: 0,
                successfulRecoveries: 0,
                jsonFixAttempts: 0,
                partialExtractionAttempts: 0,
                failedRecoveries: 0
            };
        }
    }

    // 暴露到全局命名空间
    window.OTA.gemini.core.ErrorRecoveryEngine = ErrorRecoveryEngine;

    // 创建单例实例
    let errorRecoveryEngineInstance = null;

    /**
     * 获取错误恢复引擎单例实例
     * @returns {ErrorRecoveryEngine} 错误恢复引擎实例
     */
    function getErrorRecoveryEngine() {
        if (!errorRecoveryEngineInstance) {
            errorRecoveryEngineInstance = new ErrorRecoveryEngine();
        }
        return errorRecoveryEngineInstance;
    }

    // 暴露工厂函数
    window.OTA.gemini.core.getErrorRecoveryEngine = getErrorRecoveryEngine;

    // 注册到服务注册中心
    if (window.OTA?.gemini?.core?.ServiceRegistry) {
        window.OTA.gemini.core.ServiceRegistry.register('errorRecoveryEngine', getErrorRecoveryEngine, '@ERROR_RECOVERY_ENGINE');
    }

    console.log('✅ 错误恢复引擎模块已加载');

})();
