/**
 * 🎯 多订单快捷编辑管理器
 * 负责处理多订单系统的快捷编辑功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-20
 */

(function() {
    'use strict';

    /**
     * 多订单快捷编辑管理器类
     * 专门处理快捷编辑面板、字段编辑、事件处理等功能
     */
    class MultiOrderQuickEditManager {
        /**
         * 构造函数
         * @param {Object} dependencies - 依赖注入对象
         * @param {Object} dependencies.logger - 日志服务
         * @param {Object} dependencies.config - 配置对象
         * @param {Object} dependencies.state - 状态管理器
         * @param {Object} dependencies.utils - 工具函数
         * @param {Object} dependencies.validationManager - 验证管理器
         * @param {Object} dependencies.cleanupManager - 清理管理器
         */
        constructor(dependencies = {}) {
            this.logger = dependencies.logger || this.getLogger();
            this.config = {
                // 快捷编辑配置
                quickEditFields: [
                    { name: 'customerName', label: '客户姓名', type: 'text', required: true },
                    { name: 'customerContact', label: '联系电话', type: 'tel', required: true },
                    { name: 'pickup', label: '上车地点', type: 'text', required: true },
                    { name: 'dropoff', label: '下车地点', type: 'text', required: true },
                    { name: 'pickupDate', label: '日期', type: 'date', required: true },
                    { name: 'pickupTime', label: '时间', type: 'time', required: true },
                    { name: 'passengerCount', label: '乘客数', type: 'number', min: 1, max: 20 },
                    { name: 'luggageCount', label: '行李数', type: 'number', min: 0, max: 50 },
                    { name: 'price', label: '价格', type: 'number', step: '0.01' },
                    { name: 'otaReferenceNumber', label: 'OTA参考号', type: 'text', required: true }
                ],
                animationDuration: 300,
                autoSaveDelay: 1000,
                ...dependencies.config
            };

            // 依赖注入
            this.state = dependencies.state;
            this.utils = dependencies.utils;
            this.validationManager = dependencies.validationManager;
            this.cleanupManager = dependencies.cleanupManager;

            // 编辑状态
            this.currentEditingIndex = -1;
            this.boundEventHandlers = new Map();

            // 初始化
            this.init();
        }

        /**
         * 初始化快捷编辑管理器
         */
        init() {
            this.logger?.log('🎯 快捷编辑管理器初始化开始', 'info');
            
            try {
                // 绑定全局事件处理器
                this.bindGlobalEventHandlers();
                
                this.logger?.log('✅ 快捷编辑管理器初始化完成', 'success');
            } catch (error) {
                this.logger?.logError('快捷编辑管理器初始化失败', error);
                throw error;
            }
        }

        /**
         * 绑定全局事件处理器
         */
        bindGlobalEventHandlers() {
            // 绑定ESC键处理器
            this.boundEventHandlers.set('quickEditEscape', this.handleQuickEditEscape.bind(this));
            document.addEventListener('keydown', this.boundEventHandlers.get('quickEditEscape'));
        }

        /**
         * 快捷编辑订单
         * @param {number} index - 订单索引
         */
        quickEditOrder(index) {
            if (!this.state?.parsedOrders || index >= this.state.parsedOrders.length) {
                this.logger?.log(`无效的订单索引: ${index}`, 'warn');
                return;
            }

            const orderItem = document.querySelector(`.order-card[data-order-index="${index}"]`);
            if (!orderItem) {
                this.logger?.log(`找不到订单卡片: ${index}`, 'warn');
                return;
            }

            // 检查是否已经在编辑模式
            if (orderItem.classList.contains('editing')) {
                this.exitQuickEdit(index);
                return;
            }

            // 进入编辑模式
            orderItem.classList.add('editing');
            this.currentEditingIndex = index;
            
            // 创建快捷编辑面板
            this.createQuickEditPanel(index);

            this.logger?.log(`启动订单 ${index + 1} 快捷编辑模式`, 'info');
        }

        /**
         * 创建快捷编辑面板
         * @param {number} index - 订单索引
         */
        createQuickEditPanel(index) {
            // 先清理现有的快捷编辑面板
            this.cleanupQuickEditPanels();

            const order = this.state.parsedOrders[index];
            if (!order) {
                this.logger?.logError(`订单数据不存在: ${index}`);
                return;
            }

            // 创建覆盖层
            const overlay = document.createElement('div');
            overlay.className = 'quick-edit-overlay';
            overlay.setAttribute('data-order-index', index.toString());
            
            // 创建编辑面板
            const panel = document.createElement('div');
            panel.className = 'quick-edit-panel';
            
            // 生成字段HTML
            const fieldsHTML = this.config.quickEditFields.map(field => {
                const value = order[field.name] || '';
                const isRequired = field.required ? 'required' : '';
                const isOtaRef = field.name === 'otaReferenceNumber';
                const hasValue = value && value.trim() !== '';

                return `
                    <div class="quick-edit-field">
                        <label for="edit-${field.name}-${index}" class="field-label ${field.required ? 'required' : ''}">
                            ${field.label}
                        </label>
                        <div class="field-input-wrapper">
                            <input type="${field.type}" 
                                   id="edit-${field.name}-${index}"
                                   name="${field.name}"
                                   value="${value}"
                                   ${isRequired}
                                   ${field.min ? `min="${field.min}"` : ''}
                                   ${field.max ? `max="${field.max}"` : ''}
                                   ${field.step ? `step="${field.step}"` : ''}
                                   class="field-input"
                                   data-field="${field.name}"
                                   data-index="${index}"
                                   onblur="window.OTA.multiOrderManager.handleQuickEditBlur(event, ${index}, '${field.name}')"
                                   oninput="window.OTA.multiOrderManager.handleQuickEditInput(event, ${index}, '${field.name}')"
                                   onkeypress="window.OTA.multiOrderManager.handleQuickEditKeypress(event)">
                            ${isOtaRef && !hasValue ? `
                                <button type="button" class="ota-reference-generate-btn" 
                                        onclick="window.OTA.multiOrderManager.generateOtaReference(${index})" 
                                        title="生成随机参考号">🎲</button>
                            ` : ''}
                        </div>
                    </div>
                `;
            }).join('');

            panel.innerHTML = `
                <div class="quick-edit-header">
                    <h3 class="quick-edit-title">快速编辑订单 ${index + 1}</h3>
                    <button class="quick-edit-close" onclick="window.OTA.multiOrderManager.exitQuickEdit(${index})">✕</button>
                </div>
                <div class="quick-edit-fields">
                    ${fieldsHTML}
                </div>
                <div class="quick-edit-actions">
                    <button class="btn btn-outline" onclick="window.OTA.multiOrderManager.exitQuickEdit(${index})">取消</button>
                    <button class="btn btn-primary" onclick="window.OTA.multiOrderManager.saveQuickEdit(${index})">保存</button>
                </div>
            `;

            overlay.appendChild(panel);
            document.body.appendChild(overlay);

            // 聚焦第一个输入框
            const firstInput = panel.querySelector('input');
            if (firstInput) firstInput.focus();

            // 显示面板动画
            requestAnimationFrame(() => {
                panel.classList.add('show');
            });

            // 为输入框添加事件监听器
            const inputs = panel.querySelectorAll('input');
            inputs.forEach(input => {
                input.addEventListener('focus', () => {
                    input.select(); // 聚焦时选中所有文本
                });
            });

            // 点击覆盖层关闭
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    this.exitQuickEdit(index);
                }
            });

            this.logger?.log(`创建快捷编辑面板完成: 订单 ${index + 1}`, 'info');
        }

        /**
         * 退出快捷编辑
         * @param {number} index - 订单索引
         */
        exitQuickEdit(index) {
            if (this.cleanupManager && typeof this.cleanupManager.exitQuickEdit === 'function') {
                this.cleanupManager.exitQuickEdit(index);
            } else {
                // 降级方案
                this.fallbackExitQuickEdit(index);
            }
            
            this.currentEditingIndex = -1;
        }

        /**
         * 降级方案：退出快捷编辑
         * @param {number} index - 订单索引
         */
        fallbackExitQuickEdit(index) {
            // 移除编辑状态
            const orderItem = document.querySelector(`.order-card[data-order-index="${index}"]`);
            if (orderItem) {
                orderItem.classList.remove('editing');
            }

            // 移除快捷编辑面板
            const overlay = document.querySelector(`.quick-edit-overlay[data-order-index="${index}"]`);
            if (overlay) {
                overlay.remove();
            }

            this.logger?.log(`退出订单 ${index + 1} 快捷编辑模式`, 'info');
        }

        /**
         * 保存快捷编辑
         * @param {number} index - 订单索引
         */
        saveQuickEdit(index) {
            // 验证所有必填字段
            const requiredFields = ['customerName', 'pickup', 'dropoff', 'pickupDate', 'otaReferenceNumber'];
            const panel = document.querySelector(`.quick-edit-overlay[data-order-index="${index}"] .quick-edit-panel`);
            
            if (!panel) {
                this.logger?.logError('快捷编辑面板不存在');
                return;
            }

            // 收集所有字段值
            const fieldValues = {};
            const inputs = panel.querySelectorAll('input');
            
            inputs.forEach(input => {
                const fieldName = input.getAttribute('data-field');
                fieldValues[fieldName] = input.value;
            });

            // 验证必填字段
            const missingFields = requiredFields.filter(field => !fieldValues[field] || fieldValues[field].trim() === '');
            
            if (missingFields.length > 0) {
                const uiManager = window.getService?.('uiManager') || window.getUIManager?.();
                uiManager?.showError(`请填写必填字段: ${missingFields.join(', ')}`);
                return;
            }

            // 更新订单数据
            Object.keys(fieldValues).forEach(fieldName => {
                this.updateOrderField(index, fieldName, fieldValues[fieldName]);
            });

            // 保存成功，退出编辑模式
            this.exitQuickEdit(index);
            this.logger?.log(`订单 ${index + 1} 快捷编辑已保存`, 'success');
        }

        /**
         * 更新订单字段
         * @param {number} index - 订单索引
         * @param {string} fieldName - 字段名
         * @param {string} value - 新值
         */
        updateOrderField(index, fieldName, value) {
            if (this.state?.parsedOrders && this.state.parsedOrders[index]) {
                this.state.parsedOrders[index][fieldName] = value;
                
                // 触发验证
                if (this.validationManager && typeof this.validationManager.validateField === 'function') {
                    this.validationManager.validateField(index, fieldName, value);
                }
                
                this.logger?.log(`订单 ${index + 1} 字段 ${fieldName} 已更新`, 'info', { value });
            }
        }

        /**
         * 清理快捷编辑面板
         */
        cleanupQuickEditPanels() {
            document.querySelectorAll('.quick-edit-overlay').forEach(el => el.remove());
        }

        /**
         * 处理快捷编辑ESC键
         * @param {KeyboardEvent} event - 键盘事件
         */
        handleQuickEditEscape(event) {
            if (event.key === 'Escape') {
                const overlay = document.querySelector('.quick-edit-overlay');
                if (overlay) {
                    const index = overlay.getAttribute('data-order-index');
                    if (index) {
                        this.exitQuickEdit(parseInt(index));
                    }
                }
            }
        }

        /**
         * 获取日志服务
         * @returns {Object} 日志服务对象
         */
        getLogger() {
            return window.getLogger?.() || {
                log: console.log.bind(console),
                logError: console.error.bind(console)
            };
        }

        /**
         * 销毁管理器
         */
        destroy() {
            // 移除事件监听器
            this.boundEventHandlers.forEach((handler, key) => {
                if (key === 'quickEditEscape') {
                    document.removeEventListener('keydown', handler);
                }
            });
            
            // 清理快捷编辑面板
            this.cleanupQuickEditPanels();
            
            // 清空状态
            this.currentEditingIndex = -1;
            this.boundEventHandlers.clear();
            
            this.logger?.log('🎯 快捷编辑管理器已销毁', 'info');
        }
    }

    /**
     * 创建快捷编辑管理器实例的工厂函数
     * @param {Object} dependencies - 依赖注入对象
     * @returns {MultiOrderQuickEditManager} 快捷编辑管理器实例
     */
    function createMultiOrderQuickEditManager(dependencies = {}) {
        return new MultiOrderQuickEditManager(dependencies);
    }

    // 导出到全局作用域
    window.getMultiOrderQuickEditManager = createMultiOrderQuickEditManager;
    window.MultiOrderQuickEditManager = MultiOrderQuickEditManager;

    // 确保OTA命名空间存在
    if (typeof window.OTA === 'undefined') {
        window.OTA = {};
    }
    if (typeof window.OTA.multiOrder === 'undefined') {
        window.OTA.multiOrder = {};
    }

    // 注册到OTA命名空间
    window.OTA.multiOrder.QuickEditManager = MultiOrderQuickEditManager;
    window.OTA.multiOrder.getQuickEditManager = createMultiOrderQuickEditManager;

})();
