/**
 * @ERROR_HANDLER 错误处理中心
 * 🏷️ 标签: @ERROR_HANDLING_CENTER
 * 📝 说明: 统一的错误处理中心，提供错误分类、恢复策略和错误报告功能
 * 🎯 功能: 错误分类、错误恢复、错误报告、错误监控
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.core = window.OTA.gemini.core || {};

(function() {
    'use strict';

    /**
     * 错误处理中心类
     * 提供统一的错误处理和恢复机制
     */
    class ErrorHandlingCenter {
        constructor() {
            this.logger = window.getLogger?.() || console;
            this.performanceMonitor = null;
            this.errorHistory = [];
            this.errorStats = new Map();
            this.recoveryStrategies = new Map();
            this.errorListeners = new Map();
            
            // 错误配置
            this.config = {
                // 错误历史保留数量
                maxErrorHistory: 1000,
                
                // 错误重试配置
                retryConfig: {
                    maxRetries: 3,
                    retryDelay: 1000,
                    backoffMultiplier: 2,
                    maxRetryDelay: 10000
                },
                
                // 错误报告配置
                reportingConfig: {
                    enableAutoReporting: true,
                    reportThreshold: 5, // 同类错误超过5次自动报告
                    reportInterval: 300000 // 5分钟报告间隔
                },
                
                // 错误恢复配置
                recoveryConfig: {
                    enableAutoRecovery: true,
                    recoveryTimeout: 30000, // 30秒恢复超时
                    maxRecoveryAttempts: 2
                }
            };
            
            // 错误类型定义
            this.errorTypes = {
                VALIDATION_ERROR: 'validation_error',
                PROCESSING_ERROR: 'processing_error',
                NETWORK_ERROR: 'network_error',
                TIMEOUT_ERROR: 'timeout_error',
                CONFIGURATION_ERROR: 'configuration_error',
                PROCESSOR_ERROR: 'processor_error',
                PARSING_ERROR: 'parsing_error',
                AUTHENTICATION_ERROR: 'authentication_error',
                RATE_LIMIT_ERROR: 'rate_limit_error',
                UNKNOWN_ERROR: 'unknown_error'
            };
            
            // 错误严重级别
            this.errorSeverity = {
                LOW: 'low',
                MEDIUM: 'medium',
                HIGH: 'high',
                CRITICAL: 'critical'
            };
            
            this.init();
        }

        /**
         * 初始化错误处理中心
         */
        init() {
            try {
                // 初始化性能监控器引用
                this.initializePerformanceMonitor();
                
                // 注册默认恢复策略
                this.registerDefaultRecoveryStrategies();
                
                // 启动错误报告定时器
                this.startErrorReporting();
                
                // 注册全局错误处理器
                this.registerGlobalErrorHandlers();
                
                this.logger.log('错误处理中心初始化完成', 'info');
                
            } catch (error) {
                this.logger.logError('错误处理中心初始化失败', error);
            }
        }

        /**
         * 初始化性能监控器
         */
        initializePerformanceMonitor() {
            try {
                this.performanceMonitor = window.OTA.gemini.monitoring.getPerformanceMonitor?.();
            } catch (error) {
                this.logger.log('无法获取性能监控器引用', 'warning');
            }
        }

        /**
         * 处理错误
         * @param {Error|string} error - 错误对象或错误消息
         * @param {Object} context - 错误上下文
         * @returns {Promise<Object>} 错误处理结果
         */
        async handleError(error, context = {}) {
            try {
                // 标准化错误对象
                const standardizedError = this.standardizeError(error, context);
                
                // 记录错误
                this.recordError(standardizedError);
                
                // 分类错误
                const errorType = this.classifyError(standardizedError);
                const severity = this.determineSeverity(standardizedError, errorType);
                
                // 更新错误统计
                this.updateErrorStats(errorType, severity);
                
                // 尝试错误恢复
                const recoveryResult = await this.attemptRecovery(standardizedError, errorType, context);
                
                // 创建错误报告
                const errorReport = this.createErrorReport(standardizedError, errorType, severity, recoveryResult);
                
                // 触发错误事件
                this.emitErrorEvent('errorHandled', errorReport);
                
                // 检查是否需要告警
                this.checkErrorAlerts(errorType, severity);
                
                return {
                    success: recoveryResult.success,
                    error: standardizedError,
                    errorType,
                    severity,
                    recovery: recoveryResult,
                    report: errorReport
                };
                
            } catch (handlingError) {
                this.logger.logError('错误处理过程中发生异常', handlingError);
                return {
                    success: false,
                    error: error,
                    errorType: this.errorTypes.UNKNOWN_ERROR,
                    severity: this.errorSeverity.CRITICAL,
                    recovery: { success: false, strategy: null, error: handlingError.message },
                    report: null
                };
            }
        }

        /**
         * 标准化错误对象
         * @param {Error|string} error - 原始错误
         * @param {Object} context - 上下文
         * @returns {Object} 标准化错误对象
         */
        standardizeError(error, context) {
            const timestamp = new Date().toISOString();
            const errorId = this.generateErrorId();
            
            let standardizedError = {
                id: errorId,
                timestamp,
                context: { ...context }
            };
            
            if (error instanceof Error) {
                standardizedError = {
                    ...standardizedError,
                    name: error.name,
                    message: error.message,
                    stack: error.stack,
                    originalError: error
                };
            } else if (typeof error === 'string') {
                standardizedError = {
                    ...standardizedError,
                    name: 'StringError',
                    message: error,
                    stack: null,
                    originalError: null
                };
            } else if (typeof error === 'object' && error !== null) {
                standardizedError = {
                    ...standardizedError,
                    name: error.name || 'ObjectError',
                    message: error.message || JSON.stringify(error),
                    stack: error.stack || null,
                    originalError: error
                };
            } else {
                standardizedError = {
                    ...standardizedError,
                    name: 'UnknownError',
                    message: String(error),
                    stack: null,
                    originalError: error
                };
            }
            
            return standardizedError;
        }

        /**
         * 分类错误
         * @param {Object} error - 标准化错误对象
         * @returns {string} 错误类型
         */
        classifyError(error) {
            const message = error.message.toLowerCase();
            const name = error.name.toLowerCase();
            
            // 验证错误
            if (message.includes('validation') || message.includes('invalid') || 
                message.includes('required') || message.includes('missing')) {
                return this.errorTypes.VALIDATION_ERROR;
            }
            
            // 网络错误
            if (message.includes('network') || message.includes('fetch') || 
                message.includes('connection') || name.includes('networkerror')) {
                return this.errorTypes.NETWORK_ERROR;
            }
            
            // 超时错误
            if (message.includes('timeout') || message.includes('timed out') || 
                name.includes('timeouterror')) {
                return this.errorTypes.TIMEOUT_ERROR;
            }
            
            // 认证错误
            if (message.includes('auth') || message.includes('unauthorized') || 
                message.includes('forbidden') || message.includes('token')) {
                return this.errorTypes.AUTHENTICATION_ERROR;
            }
            
            // 限流错误
            if (message.includes('rate limit') || message.includes('too many requests') || 
                message.includes('quota exceeded')) {
                return this.errorTypes.RATE_LIMIT_ERROR;
            }
            
            // 配置错误
            if (message.includes('config') || message.includes('configuration') || 
                message.includes('setting')) {
                return this.errorTypes.CONFIGURATION_ERROR;
            }
            
            // 处理器错误
            if (message.includes('processor') || message.includes('handler') || 
                error.context.processorName) {
                return this.errorTypes.PROCESSOR_ERROR;
            }
            
            // 解析错误
            if (message.includes('parse') || message.includes('parsing') || 
                message.includes('syntax') || name.includes('syntaxerror')) {
                return this.errorTypes.PARSING_ERROR;
            }
            
            // 处理错误
            if (message.includes('process') || message.includes('processing') || 
                message.includes('execution')) {
                return this.errorTypes.PROCESSING_ERROR;
            }
            
            return this.errorTypes.UNKNOWN_ERROR;
        }

        /**
         * 确定错误严重级别
         * @param {Object} error - 错误对象
         * @param {string} errorType - 错误类型
         * @returns {string} 严重级别
         */
        determineSeverity(error, errorType) {
            // 关键错误类型
            const criticalTypes = [
                this.errorTypes.AUTHENTICATION_ERROR,
                this.errorTypes.CONFIGURATION_ERROR
            ];
            
            // 高严重级别错误类型
            const highSeverityTypes = [
                this.errorTypes.PROCESSOR_ERROR,
                this.errorTypes.NETWORK_ERROR
            ];
            
            // 中等严重级别错误类型
            const mediumSeverityTypes = [
                this.errorTypes.PROCESSING_ERROR,
                this.errorTypes.TIMEOUT_ERROR,
                this.errorTypes.RATE_LIMIT_ERROR
            ];
            
            if (criticalTypes.includes(errorType)) {
                return this.errorSeverity.CRITICAL;
            }
            
            if (highSeverityTypes.includes(errorType)) {
                return this.errorSeverity.HIGH;
            }
            
            if (mediumSeverityTypes.includes(errorType)) {
                return this.errorSeverity.MEDIUM;
            }
            
            // 检查错误消息中的关键词
            const message = error.message.toLowerCase();
            if (message.includes('critical') || message.includes('fatal') || 
                message.includes('system') || message.includes('crash')) {
                return this.errorSeverity.CRITICAL;
            }
            
            return this.errorSeverity.LOW;
        }

        /**
         * 尝试错误恢复
         * @param {Object} error - 错误对象
         * @param {string} errorType - 错误类型
         * @param {Object} context - 上下文
         * @returns {Promise<Object>} 恢复结果
         */
        async attemptRecovery(error, errorType, context) {
            if (!this.config.recoveryConfig.enableAutoRecovery) {
                return { success: false, strategy: null, message: '自动恢复已禁用' };
            }
            
            const recoveryStrategy = this.recoveryStrategies.get(errorType);
            if (!recoveryStrategy) {
                return { success: false, strategy: null, message: '没有可用的恢复策略' };
            }
            
            try {
                this.logger.log(`尝试错误恢复: ${errorType}`, 'info');
                
                const recoveryResult = await Promise.race([
                    recoveryStrategy.recover(error, context),
                    new Promise((_, reject) => 
                        setTimeout(() => reject(new Error('恢复超时')), this.config.recoveryConfig.recoveryTimeout)
                    )
                ]);
                
                if (recoveryResult.success) {
                    this.logger.log(`错误恢复成功: ${errorType}`, 'info');
                    return {
                        success: true,
                        strategy: recoveryStrategy.name,
                        message: recoveryResult.message || '恢复成功',
                        data: recoveryResult.data
                    };
                } else {
                    this.logger.log(`错误恢复失败: ${errorType} - ${recoveryResult.message}`, 'warning');
                    return {
                        success: false,
                        strategy: recoveryStrategy.name,
                        message: recoveryResult.message || '恢复失败'
                    };
                }
                
            } catch (recoveryError) {
                this.logger.logError(`错误恢复异常: ${errorType}`, recoveryError);
                return {
                    success: false,
                    strategy: recoveryStrategy.name,
                    message: `恢复异常: ${recoveryError.message}`
                };
            }
        }

        /**
         * 记录错误
         * @param {Object} error - 错误对象
         */
        recordError(error) {
            // 添加到错误历史
            this.errorHistory.push(error);
            
            // 保持历史记录数量限制
            if (this.errorHistory.length > this.config.maxErrorHistory) {
                this.errorHistory.shift();
            }
            
            // 记录到性能监控器
            if (this.performanceMonitor) {
                this.performanceMonitor.recordFailedProcessing(
                    error.context.processorName || 'unknown',
                    error.context.processingTime || 0,
                    error.message
                );
            }
        }

        /**
         * 更新错误统计
         * @param {string} errorType - 错误类型
         * @param {string} severity - 严重级别
         */
        updateErrorStats(errorType, severity) {
            const key = `${errorType}_${severity}`;
            const currentCount = this.errorStats.get(key) || 0;
            this.errorStats.set(key, currentCount + 1);
        }

        /**
         * 创建错误报告
         * @param {Object} error - 错误对象
         * @param {string} errorType - 错误类型
         * @param {string} severity - 严重级别
         * @param {Object} recoveryResult - 恢复结果
         * @returns {Object} 错误报告
         */
        createErrorReport(error, errorType, severity, recoveryResult) {
            return {
                errorId: error.id,
                timestamp: error.timestamp,
                errorType,
                severity,
                message: error.message,
                context: error.context,
                stack: error.stack,
                recovery: recoveryResult,
                environment: {
                    userAgent: navigator.userAgent,
                    url: window.location.href,
                    timestamp: new Date().toISOString()
                }
            };
        }

        /**
         * 注册默认恢复策略
         */
        registerDefaultRecoveryStrategies() {
            // 网络错误恢复策略
            this.registerRecoveryStrategy(this.errorTypes.NETWORK_ERROR, {
                name: 'NetworkErrorRecovery',
                recover: async (error, context) => {
                    // 重试网络请求
                    if (context.retryFunction && context.retryCount < this.config.retryConfig.maxRetries) {
                        await this.delay(this.config.retryConfig.retryDelay * Math.pow(this.config.retryConfig.backoffMultiplier, context.retryCount));
                        
                        try {
                            const result = await context.retryFunction();
                            return { success: true, message: '网络重试成功', data: result };
                        } catch (retryError) {
                            return { success: false, message: `网络重试失败: ${retryError.message}` };
                        }
                    }
                    return { success: false, message: '无法恢复网络错误' };
                }
            });

            // 超时错误恢复策略
            this.registerRecoveryStrategy(this.errorTypes.TIMEOUT_ERROR, {
                name: 'TimeoutErrorRecovery',
                recover: async (error, context) => {
                    // 使用降级处理
                    if (context.fallbackFunction) {
                        try {
                            const result = await context.fallbackFunction();
                            return { success: true, message: '超时降级处理成功', data: result };
                        } catch (fallbackError) {
                            return { success: false, message: `降级处理失败: ${fallbackError.message}` };
                        }
                    }
                    return { success: false, message: '无法恢复超时错误' };
                }
            });

            // 处理器错误恢复策略
            this.registerRecoveryStrategy(this.errorTypes.PROCESSOR_ERROR, {
                name: 'ProcessorErrorRecovery',
                recover: async (error, context) => {
                    // 尝试使用降级处理器
                    if (context.fallbackProcessor) {
                        try {
                            const result = await context.fallbackProcessor.processOrder(context.orderText);
                            return { success: true, message: '处理器降级成功', data: result };
                        } catch (fallbackError) {
                            return { success: false, message: `处理器降级失败: ${fallbackError.message}` };
                        }
                    }
                    return { success: false, message: '无法恢复处理器错误' };
                }
            });

            // 验证错误恢复策略
            this.registerRecoveryStrategy(this.errorTypes.VALIDATION_ERROR, {
                name: 'ValidationErrorRecovery',
                recover: async (error, context) => {
                    // 尝试数据清理和重新验证
                    if (context.cleanupFunction && context.validateFunction) {
                        try {
                            const cleanedData = await context.cleanupFunction(context.data);
                            const validationResult = await context.validateFunction(cleanedData);
                            
                            if (validationResult.isValid) {
                                return { success: true, message: '数据清理后验证成功', data: cleanedData };
                            } else {
                                return { success: false, message: `清理后仍验证失败: ${validationResult.errors.join(', ')}` };
                            }
                        } catch (cleanupError) {
                            return { success: false, message: `数据清理失败: ${cleanupError.message}` };
                        }
                    }
                    return { success: false, message: '无法恢复验证错误' };
                }
            });

            // 限流错误恢复策略
            this.registerRecoveryStrategy(this.errorTypes.RATE_LIMIT_ERROR, {
                name: 'RateLimitErrorRecovery',
                recover: async (error, context) => {
                    // 等待并重试
                    const waitTime = context.retryAfter || 60000; // 默认等待1分钟
                    await this.delay(waitTime);
                    
                    if (context.retryFunction) {
                        try {
                            const result = await context.retryFunction();
                            return { success: true, message: '限流等待后重试成功', data: result };
                        } catch (retryError) {
                            return { success: false, message: `限流重试失败: ${retryError.message}` };
                        }
                    }
                    return { success: false, message: '无法恢复限流错误' };
                }
            });
        }

        /**
         * 注册恢复策略
         * @param {string} errorType - 错误类型
         * @param {Object} strategy - 恢复策略
         */
        registerRecoveryStrategy(errorType, strategy) {
            this.recoveryStrategies.set(errorType, strategy);
            this.logger.log(`已注册恢复策略: ${errorType} -> ${strategy.name}`, 'info');
        }

        /**
         * 启动错误报告
         */
        startErrorReporting() {
            if (!this.config.reportingConfig.enableAutoReporting) {
                return;
            }

            setInterval(() => {
                this.generatePeriodicErrorReport();
            }, this.config.reportingConfig.reportInterval);
        }

        /**
         * 生成定期错误报告
         */
        generatePeriodicErrorReport() {
            const recentErrors = this.errorHistory.filter(
                error => Date.now() - new Date(error.timestamp).getTime() < this.config.reportingConfig.reportInterval
            );

            if (recentErrors.length > 0) {
                const report = {
                    timestamp: new Date().toISOString(),
                    period: this.config.reportingConfig.reportInterval,
                    errorCount: recentErrors.length,
                    errorsByType: this.groupErrorsByType(recentErrors),
                    errorsBySeverity: this.groupErrorsBySeverity(recentErrors),
                    topErrors: this.getTopErrors(recentErrors)
                };

                this.emitErrorEvent('periodicReport', report);
                this.logger.log(`生成定期错误报告: ${recentErrors.length}个错误`, 'info');
            }
        }

        /**
         * 检查错误告警
         * @param {string} errorType - 错误类型
         * @param {string} severity - 严重级别
         */
        checkErrorAlerts(errorType, severity) {
            const key = `${errorType}_${severity}`;
            const count = this.errorStats.get(key) || 0;

            if (count >= this.config.reportingConfig.reportThreshold) {
                this.createErrorAlert(errorType, severity, count);
            }
        }

        /**
         * 创建错误告警
         * @param {string} errorType - 错误类型
         * @param {string} severity - 严重级别
         * @param {number} count - 错误数量
         */
        createErrorAlert(errorType, severity, count) {
            const alert = {
                id: this.generateErrorId(),
                type: 'error_threshold_exceeded',
                errorType,
                severity,
                count,
                threshold: this.config.reportingConfig.reportThreshold,
                timestamp: new Date().toISOString()
            };

            this.emitErrorEvent('alert', alert);
            this.logger.log(`🚨 错误告警: ${errorType}(${severity}) 错误数量达到 ${count}`, 'warning');
        }

        /**
         * 注册全局错误处理器
         */
        registerGlobalErrorHandlers() {
            // 捕获未处理的Promise拒绝
            window.addEventListener('unhandledrejection', (event) => {
                this.handleError(event.reason, {
                    type: 'unhandledPromiseRejection',
                    promise: event.promise
                });
            });

            // 捕获全局JavaScript错误
            window.addEventListener('error', (event) => {
                this.handleError(event.error || event.message, {
                    type: 'globalError',
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno
                });
            });
        }

        /**
         * 按类型分组错误
         * @param {Array} errors - 错误列表
         * @returns {Object} 分组结果
         */
        groupErrorsByType(errors) {
            const groups = {};
            errors.forEach(error => {
                const type = this.classifyError(error);
                groups[type] = (groups[type] || 0) + 1;
            });
            return groups;
        }

        /**
         * 按严重级别分组错误
         * @param {Array} errors - 错误列表
         * @returns {Object} 分组结果
         */
        groupErrorsBySeverity(errors) {
            const groups = {};
            errors.forEach(error => {
                const type = this.classifyError(error);
                const severity = this.determineSeverity(error, type);
                groups[severity] = (groups[severity] || 0) + 1;
            });
            return groups;
        }

        /**
         * 获取最常见错误
         * @param {Array} errors - 错误列表
         * @param {number} limit - 限制数量
         * @returns {Array} 最常见错误
         */
        getTopErrors(errors, limit = 5) {
            const errorCounts = {};
            
            errors.forEach(error => {
                const key = `${error.name}: ${error.message}`;
                errorCounts[key] = (errorCounts[key] || 0) + 1;
            });

            return Object.entries(errorCounts)
                .sort(([,a], [,b]) => b - a)
                .slice(0, limit)
                .map(([error, count]) => ({ error, count }));
        }

        /**
         * 触发错误事件
         * @param {string} eventType - 事件类型
         * @param {Object} data - 事件数据
         */
        emitErrorEvent(eventType, data) {
            const listeners = this.errorListeners.get(eventType) || [];
            listeners.forEach(listener => {
                try {
                    listener(data);
                } catch (error) {
                    this.logger.logError('错误事件监听器执行失败', error);
                }
            });
        }

        /**
         * 添加错误事件监听器
         * @param {string} eventType - 事件类型
         * @param {Function} listener - 监听器函数
         */
        addEventListener(eventType, listener) {
            if (!this.errorListeners.has(eventType)) {
                this.errorListeners.set(eventType, []);
            }
            this.errorListeners.get(eventType).push(listener);
        }

        /**
         * 移除错误事件监听器
         * @param {string} eventType - 事件类型
         * @param {Function} listener - 监听器函数
         */
        removeEventListener(eventType, listener) {
            const listeners = this.errorListeners.get(eventType) || [];
            const index = listeners.indexOf(listener);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }

        /**
         * 延迟函数
         * @param {number} ms - 延迟毫秒数
         * @returns {Promise} Promise对象
         */
        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        /**
         * 生成错误ID
         * @returns {string} 错误ID
         */
        generateErrorId() {
            return 'error_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        /**
         * 获取错误统计
         * @returns {Object} 错误统计
         */
        getErrorStats() {
            return {
                totalErrors: this.errorHistory.length,
                errorsByType: this.groupErrorsByType(this.errorHistory),
                errorsBySeverity: this.groupErrorsBySeverity(this.errorHistory),
                recentErrors: this.errorHistory.slice(-10),
                errorStats: Object.fromEntries(this.errorStats),
                recoveryStrategies: Array.from(this.recoveryStrategies.keys())
            };
        }

        /**
         * 清理错误历史
         */
        clearErrorHistory() {
            this.errorHistory = [];
            this.errorStats.clear();
            this.logger.log('错误历史已清理', 'info');
        }

        /**
         * 设置配置
         * @param {Object} newConfig - 新配置
         */
        setConfig(newConfig) {
            Object.assign(this.config, newConfig);
            this.logger.log('错误处理中心配置已更新', 'info');
        }
    }

    // 创建全局单例实例
    function getErrorHandlingCenter() {
        if (!window.OTA.gemini.core.errorHandlingCenter) {
            window.OTA.gemini.core.errorHandlingCenter = new ErrorHandlingCenter();
        }
        return window.OTA.gemini.core.errorHandlingCenter;
    }

    // 暴露到全局命名空间
    window.OTA.gemini.core.ErrorHandlingCenter = ErrorHandlingCenter;
    window.OTA.gemini.core.getErrorHandlingCenter = getErrorHandlingCenter;

    // 向后兼容
    window.getErrorHandlingCenter = getErrorHandlingCenter;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('errorHandlingCenter', getErrorHandlingCenter(), '@ERROR_HANDLING_CENTER');
        window.OTA.Registry.registerFactory('getErrorHandlingCenter', getErrorHandlingCenter, '@ERROR_HANDLING_CENTER_FACTORY');
    }

    console.log('✅ 错误处理中心已加载');

})();
