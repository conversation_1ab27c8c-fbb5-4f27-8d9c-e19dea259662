<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务3.4：工具函数整合验证</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #4a5568;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            background: #f7fafc;
        }
        .test-section h3 {
            color: #2d3748;
            margin-bottom: 15px;
        }
        .test-item {
            margin-bottom: 10px;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #4299e1;
        }
        .result {
            font-weight: bold;
            color: #2b6cb0;
        }
        .error {
            color: #e53e3e;
        }
        .success {
            color: #38a169;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .status {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .status.loading {
            background: #fef5e7;
            color: #744210;
        }
        .status.success {
            background: #f0fff4;
            color: #22543d;
        }
        .status.error {
            background: #fed7d7;
            color: #742a2a;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 任务3.4：工具函数整合验证</h1>
        
        <div id="status" class="status loading">
            正在加载工具函数...
        </div>

        <div class="test-section">
            <h3>📊 价格格式化函数测试</h3>
            <div id="priceTests"></div>
        </div>

        <div class="test-section">
            <h3>📱 电话号码格式化函数测试</h3>
            <div id="phoneTests"></div>
        </div>

        <div class="test-section">
            <h3>📅 日期格式化函数测试</h3>
            <div id="dateTests"></div>
        </div>

        <div class="test-section">
            <h3>✅ 验证函数测试</h3>
            <div id="validationTests"></div>
        </div>

        <div class="test-section">
            <h3>🔄 重复函数调用对比测试</h3>
            <div id="comparisonTests"></div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button onclick="runAllTests()">🚀 运行所有测试</button>
            <button onclick="testUnifiedFunctions()">🔧 测试统一函数</button>
            <button onclick="testFallbackFunctions()">⚠️ 测试降级函数</button>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="../js/services/logger.js"></script>
    <script src="../js/utils/utils.js"></script>
    <script src="../js/components/multi-order/multi-order-utils.js"></script>
    <script src="../js/managers/currency-converter.js"></script>

    <script>
        // 测试数据
        const testData = {
            prices: [
                { value: 100, currency: 'MYR' },
                { value: 50.75, currency: 'USD' },
                { value: 200, currency: 'SGD' },
                { value: 300.5, currency: 'CNY' },
                { value: null, currency: 'MYR' },
                { value: 'invalid', currency: 'MYR' }
            ],
            phones: [
                '+60123456789',
                '0123456789',
                '+8613812345678',
                '+6591234567',
                '',
                null
            ],
            dates: [
                '2024-01-15',
                '15-01-2024',
                '15/01/2024',
                '2024/01/15',
                'invalid-date',
                ''
            ],
            validationTests: [
                { type: 'email', value: '<EMAIL>' },
                { type: 'email', value: 'invalid-email' },
                { type: 'phone', value: '+60123456789' },
                { type: 'phone', value: 'invalid-phone' },
                { type: 'date', value: '2024-01-15' },
                { type: 'date', value: 'invalid-date' },
                { type: 'time', value: '14:30' },
                { type: 'time', value: '25:70' },
                { type: 'price', value: 100.50 },
                { type: 'price', value: -10 }
            ]
        };

        function updateStatus(message, type = 'loading') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function addTestResult(containerId, title, result, isSuccess = true) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = 'test-item';
            div.innerHTML = `
                <strong>${title}:</strong> 
                <span class="result ${isSuccess ? 'success' : 'error'}">${result}</span>
            `;
            container.appendChild(div);
        }

        function testPriceFormatting() {
            const container = document.getElementById('priceTests');
            container.innerHTML = '';

            testData.prices.forEach((test, index) => {
                try {
                    const result = window.OTA?.utils?.formatPrice(test.value, test.currency);
                    addTestResult('priceTests', 
                        `价格 ${test.value} ${test.currency}`, 
                        result || '未定义', 
                        !!result
                    );
                } catch (error) {
                    addTestResult('priceTests', 
                        `价格 ${test.value} ${test.currency}`, 
                        `错误: ${error.message}`, 
                        false
                    );
                }
            });
        }

        function testPhoneFormatting() {
            const container = document.getElementById('phoneTests');
            container.innerHTML = '';

            testData.phones.forEach((phone, index) => {
                try {
                    const result = window.OTA?.utils?.formatPhoneDisplay(phone);
                    addTestResult('phoneTests', 
                        `电话 ${phone || 'null'}`, 
                        result || '未定义', 
                        !!result
                    );
                } catch (error) {
                    addTestResult('phoneTests', 
                        `电话 ${phone || 'null'}`, 
                        `错误: ${error.message}`, 
                        false
                    );
                }
            });
        }

        function testDateFormatting() {
            const container = document.getElementById('dateTests');
            container.innerHTML = '';

            testData.dates.forEach((date, index) => {
                try {
                    const apiResult = window.OTA?.utils?.formatDateForAPI(date);
                    const inputResult = window.OTA?.utils?.formatDateForInput(date);
                    
                    addTestResult('dateTests', 
                        `日期 ${date || 'empty'} (API格式)`, 
                        apiResult || '未定义', 
                        !!apiResult
                    );
                    
                    addTestResult('dateTests', 
                        `日期 ${date || 'empty'} (Input格式)`, 
                        inputResult || '未定义', 
                        !!inputResult
                    );
                } catch (error) {
                    addTestResult('dateTests', 
                        `日期 ${date || 'empty'}`, 
                        `错误: ${error.message}`, 
                        false
                    );
                }
            });
        }

        function testValidationFunctions() {
            const container = document.getElementById('validationTests');
            container.innerHTML = '';

            testData.validationTests.forEach((test, index) => {
                try {
                    let result;
                    switch (test.type) {
                        case 'email':
                            result = window.OTA?.utils?.isValidEmail(test.value);
                            break;
                        case 'phone':
                            result = window.OTA?.utils?.isValidPhone(test.value);
                            break;
                        case 'date':
                            result = window.OTA?.utils?.isValidDate(test.value);
                            break;
                        case 'time':
                            result = window.OTA?.utils?.isValidTime(test.value);
                            break;
                        case 'price':
                            result = window.OTA?.utils?.isValidPrice(test.value);
                            break;
                    }
                    
                    addTestResult('validationTests', 
                        `${test.type} 验证 "${test.value}"`, 
                        result ? '有效' : '无效', 
                        result !== undefined
                    );
                } catch (error) {
                    addTestResult('validationTests', 
                        `${test.type} 验证 "${test.value}"`, 
                        `错误: ${error.message}`, 
                        false
                    );
                }
            });
        }

        function testUnifiedFunctions() {
            updateStatus('测试统一工具函数...', 'loading');
            
            setTimeout(() => {
                testPriceFormatting();
                testPhoneFormatting();
                testDateFormatting();
                testValidationFunctions();
                
                updateStatus('✅ 统一工具函数测试完成', 'success');
            }, 500);
        }

        function testFallbackFunctions() {
            updateStatus('测试降级函数...', 'loading');
            
            // 临时禁用统一函数来测试降级
            const originalUtils = window.OTA?.utils;
            if (window.OTA) {
                window.OTA.utils = null;
            }
            
            setTimeout(() => {
                try {
                    // 测试multi-order-utils的降级函数
                    if (window.OTA?.getMultiOrderUtils) {
                        const utils = window.OTA.getMultiOrderUtils();
                        const container = document.getElementById('comparisonTests');
                        container.innerHTML = '';
                        
                        addTestResult('comparisonTests', 
                            '降级价格格式化', 
                            utils.formatPrice(100, 'MYR'), 
                            true
                        );
                        
                        addTestResult('comparisonTests', 
                            '降级电话格式化', 
                            utils.formatPhone('+60123456789'), 
                            true
                        );
                    }
                    
                    updateStatus('✅ 降级函数测试完成', 'success');
                } catch (error) {
                    updateStatus(`❌ 降级函数测试失败: ${error.message}`, 'error');
                } finally {
                    // 恢复统一函数
                    if (window.OTA && originalUtils) {
                        window.OTA.utils = originalUtils;
                    }
                }
            }, 500);
        }

        function runAllTests() {
            updateStatus('运行所有测试...', 'loading');
            
            // 清空所有测试结果
            ['priceTests', 'phoneTests', 'dateTests', 'validationTests', 'comparisonTests'].forEach(id => {
                document.getElementById(id).innerHTML = '';
            });
            
            setTimeout(() => {
                testUnifiedFunctions();
                setTimeout(() => {
                    testFallbackFunctions();
                }, 1000);
            }, 500);
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            setTimeout(() => {
                if (window.OTA?.utils) {
                    updateStatus('✅ 工具函数加载成功，可以开始测试', 'success');
                } else {
                    updateStatus('❌ 工具函数加载失败', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>
