/**
 * @MIGRATION_MANAGER 渐进式重构管理器
 * 🏷️ 标签: @MIGRATION_MANAGER
 * 📝 说明: 管理从旧版本到新版本的渐进式迁移过程，包括兼容性检查、功能切换、回滚机制
 * 🎯 功能: 迁移管理、兼容性检查、功能切换、回滚机制、风险控制
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.migration = window.OTA.gemini.migration || {};

(function() {
    'use strict';

    /**
     * 渐进式重构管理器类
     * 负责管理整个重构过程的安全迁移
     */
    class RefactorManager {
        constructor() {
            this.logger = window.getLogger?.() || console;
            this.version = '1.0.0';
            this.managerName = 'RefactorManager';
            
            // 迁移状态
            this.migrationState = {
                phase: 'PREPARATION',     // PREPARATION, TESTING, MIGRATION, COMPLETE, ROLLBACK
                progress: 0,              // 0-100
                startTime: null,
                lastUpdate: null,
                errors: [],
                warnings: []
            };
            
            // 迁移阶段定义
            this.migrationPhases = {
                PREPARATION: {
                    name: '准备阶段',
                    description: '检查环境、验证依赖、准备迁移',
                    weight: 10,
                    tasks: [
                        'checkEnvironment',
                        'validateDependencies', 
                        'backupCurrentState',
                        'prepareNewComponents'
                    ]
                },
                TESTING: {
                    name: '测试阶段',
                    description: '并行测试新旧系统，验证功能一致性',
                    weight: 30,
                    tasks: [
                        'runCompatibilityTests',
                        'performanceComparison',
                        'functionalValidation',
                        'errorHandlingTests'
                    ]
                },
                MIGRATION: {
                    name: '迁移阶段',
                    description: '逐步切换到新系统',
                    weight: 50,
                    tasks: [
                        'enableFeatureToggle',
                        'gradualTrafficShift',
                        'monitorPerformance',
                        'validateResults'
                    ]
                },
                COMPLETE: {
                    name: '完成阶段',
                    description: '清理旧代码，完成迁移',
                    weight: 10,
                    tasks: [
                        'cleanupOldCode',
                        'updateDocumentation',
                        'finalValidation',
                        'celebrateSuccess'
                    ]
                }
            };
            
            // 兼容性检查配置
            this.compatibilityConfig = {
                // 关键接口检查
                criticalInterfaces: [
                    'parseOrderText',
                    'analyzeOrder', 
                    'extractOrderData',
                    'processGeminiRequest'
                ],
                
                // 数据格式检查
                dataFormats: [
                    'orderDataStructure',
                    'responseFormat',
                    'errorFormat',
                    'metadataStructure'
                ],
                
                // 性能基准
                performanceBenchmarks: {
                    maxProcessingTime: 10000,    // 10秒
                    maxMemoryUsage: 100 * 1024 * 1024, // 100MB
                    minSuccessRate: 0.95,        // 95%成功率
                    maxErrorRate: 0.05           // 5%错误率
                }
            };
            
            // 回滚配置
            this.rollbackConfig = {
                enabled: true,
                autoRollbackThreshold: {
                    errorRate: 0.1,              // 10%错误率触发自动回滚
                    performanceDegradation: 0.5, // 50%性能下降触发回滚
                    criticalErrors: 3            // 3个关键错误触发回滚
                },
                rollbackTimeout: 30000,          // 30秒回滚超时
                preserveData: true               // 回滚时保留数据
            };
            
            // 监控指标
            this.metrics = {
                migration: {
                    startTime: null,
                    endTime: null,
                    duration: 0,
                    tasksCompleted: 0,
                    tasksTotal: 0,
                    errorsEncountered: 0,
                    warningsGenerated: 0
                },
                performance: {
                    oldSystemMetrics: {},
                    newSystemMetrics: {},
                    comparisonResults: {}
                },
                compatibility: {
                    testsRun: 0,
                    testsPassed: 0,
                    testsFailed: 0,
                    compatibilityScore: 0
                }
            };
            
            // 组件引用
            this.components = {
                featureToggle: null,
                compatibilityAdapter: null,
                oldGeminiService: null,
                newGeminiCoordinator: null
            };
            
            // 初始化管理器
            this.initialize();
        }

        /**
         * 初始化重构管理器
         */
        async initialize() {
            this.logger.log('渐进式重构管理器初始化开始', 'info');
            
            try {
                // 获取组件引用
                await this.initializeComponents();
                
                // 设置监控
                this.setupMonitoring();
                
                // 加载迁移状态
                this.loadMigrationState();
                
                this.logger.log('渐进式重构管理器初始化完成', 'info');
                
            } catch (error) {
                this.logger.logError('渐进式重构管理器初始化失败', error);
                throw error;
            }
        }

        /**
         * 初始化组件引用
         */
        async initializeComponents() {
            // 获取功能切换器
            this.components.featureToggle = window.OTA?.gemini?.migration?.getFeatureToggle?.() ||
                                          window.OTA?.Registry?.getService('featureToggle');
            
            // 获取兼容性适配器
            this.components.compatibilityAdapter = window.OTA?.gemini?.migration?.getCompatibilityAdapter?.() ||
                                                  window.OTA?.Registry?.getService('compatibilityAdapter');
            
            // 获取旧系统引用
            this.components.oldGeminiService = window.GeminiService || 
                                             window.OTA?.gemini?.service ||
                                             window.geminiService;
            
            // 获取新系统引用
            this.components.newGeminiCoordinator = window.OTA?.gemini?.getGeminiCoordinator?.() ||
                                                 window.getGeminiCoordinator?.();
        }

        /**
         * 设置监控
         */
        setupMonitoring() {
            // 监控迁移进度
            setInterval(() => {
                this.updateMigrationMetrics();
            }, 5000); // 5秒更新一次
            
            // 监控系统健康状态
            setInterval(() => {
                this.checkSystemHealth();
            }, 10000); // 10秒检查一次
        }

        /**
         * 开始迁移过程
         * @param {Object} options - 迁移选项
         * @returns {Promise<Object>} 迁移结果
         */
        async startMigration(options = {}) {
            if (this.migrationState.phase !== 'PREPARATION' && 
                this.migrationState.phase !== 'COMPLETE') {
                throw new Error(`无法在当前阶段 ${this.migrationState.phase} 开始迁移`);
            }
            
            this.logger.log('开始渐进式重构迁移', 'info');
            
            try {
                // 重置状态
                this.resetMigrationState();
                
                // 执行迁移阶段
                for (const [phaseName, phaseConfig] of Object.entries(this.migrationPhases)) {
                    if (phaseName === 'COMPLETE') continue; // 跳过完成阶段，由其他方法触发
                    
                    await this.executePhase(phaseName, phaseConfig, options);
                    
                    // 检查是否需要回滚
                    if (await this.shouldRollback()) {
                        await this.rollback();
                        throw new Error('迁移过程中检测到问题，已自动回滚');
                    }
                }
                
                // 标记迁移完成
                await this.completeMigration();
                
                return {
                    success: true,
                    phase: this.migrationState.phase,
                    progress: this.migrationState.progress,
                    metrics: this.metrics
                };
                
            } catch (error) {
                this.migrationState.errors.push({
                    error: error.message,
                    timestamp: new Date().toISOString(),
                    phase: this.migrationState.phase
                });
                
                this.logger.logError('迁移过程失败', error);
                throw error;
            }
        }

        /**
         * 执行迁移阶段
         * @param {string} phaseName - 阶段名称
         * @param {Object} phaseConfig - 阶段配置
         * @param {Object} options - 选项
         */
        async executePhase(phaseName, phaseConfig, options) {
            this.logger.log(`开始执行阶段: ${phaseConfig.name}`, 'info');
            
            this.migrationState.phase = phaseName;
            this.migrationState.lastUpdate = new Date().toISOString();
            
            const phaseStartProgress = this.migrationState.progress;
            const phaseWeight = phaseConfig.weight;
            
            for (let i = 0; i < phaseConfig.tasks.length; i++) {
                const taskName = phaseConfig.tasks[i];
                
                try {
                    await this.executeTask(taskName, options);
                    
                    // 更新进度
                    const taskProgress = (phaseWeight / phaseConfig.tasks.length);
                    this.migrationState.progress = phaseStartProgress + (taskProgress * (i + 1));
                    
                    this.metrics.migration.tasksCompleted++;
                    
                } catch (error) {
                    this.logger.logError(`任务执行失败: ${taskName}`, error);
                    this.migrationState.errors.push({
                        task: taskName,
                        error: error.message,
                        timestamp: new Date().toISOString(),
                        phase: phaseName
                    });
                    
                    this.metrics.migration.errorsEncountered++;
                    
                    // 根据错误严重程度决定是否继续
                    if (this.isCriticalError(error)) {
                        throw error;
                    }
                }
            }
            
            this.logger.log(`阶段完成: ${phaseConfig.name}`, 'info');
        }

        /**
         * 执行具体任务
         * @param {string} taskName - 任务名称
         * @param {Object} options - 选项
         */
        async executeTask(taskName, options) {
            this.logger.log(`执行任务: ${taskName}`, 'info');
            
            switch (taskName) {
                case 'checkEnvironment':
                    await this.checkEnvironment();
                    break;
                    
                case 'validateDependencies':
                    await this.validateDependencies();
                    break;
                    
                case 'backupCurrentState':
                    await this.backupCurrentState();
                    break;
                    
                case 'prepareNewComponents':
                    await this.prepareNewComponents();
                    break;
                    
                case 'runCompatibilityTests':
                    await this.runCompatibilityTests();
                    break;
                    
                case 'performanceComparison':
                    await this.performanceComparison();
                    break;
                    
                case 'functionalValidation':
                    await this.functionalValidation();
                    break;
                    
                case 'errorHandlingTests':
                    await this.errorHandlingTests();
                    break;
                    
                case 'enableFeatureToggle':
                    await this.enableFeatureToggle(options);
                    break;
                    
                case 'gradualTrafficShift':
                    await this.gradualTrafficShift(options);
                    break;
                    
                case 'monitorPerformance':
                    await this.monitorPerformance();
                    break;
                    
                case 'validateResults':
                    await this.validateResults();
                    break;
                    
                case 'cleanupOldCode':
                    await this.cleanupOldCode();
                    break;
                    
                case 'updateDocumentation':
                    await this.updateDocumentation();
                    break;
                    
                case 'finalValidation':
                    await this.finalValidation();
                    break;
                    
                case 'celebrateSuccess':
                    await this.celebrateSuccess();
                    break;
                    
                default:
                    this.logger.logWarning(`未知任务: ${taskName}`);
            }
        }

        /**
         * 检查环境
         */
        async checkEnvironment() {
            // 检查浏览器兼容性
            if (!window.Promise || !window.fetch) {
                throw new Error('浏览器不支持必要的现代JavaScript特性');
            }
            
            // 检查必要的全局对象
            if (!window.OTA) {
                throw new Error('OTA命名空间未初始化');
            }
            
            // 检查日志系统
            if (!window.getLogger) {
                this.migrationState.warnings.push({
                    warning: '日志系统未找到，将使用console',
                    timestamp: new Date().toISOString()
                });
            }
            
            this.logger.log('环境检查完成', 'info');
        }

        /**
         * 验证依赖
         */
        async validateDependencies() {
            const requiredDependencies = [
                'window.OTA.Registry',
                'window.OTA.gemini',
                'window.getLogger'
            ];
            
            const missingDependencies = [];
            
            for (const dep of requiredDependencies) {
                if (!this.getNestedProperty(window, dep)) {
                    missingDependencies.push(dep);
                }
            }
            
            if (missingDependencies.length > 0) {
                this.migrationState.warnings.push({
                    warning: `缺少依赖: ${missingDependencies.join(', ')}`,
                    timestamp: new Date().toISOString()
                });
            }
            
            this.logger.log('依赖验证完成', 'info');
        }

        /**
         * 备份当前状态
         */
        async backupCurrentState() {
            const backup = {
                timestamp: new Date().toISOString(),
                version: this.version,
                oldGeminiService: this.components.oldGeminiService ? 'available' : 'not_found',
                configuration: {
                    // 备份当前配置
                },
                state: {
                    // 备份当前状态
                }
            };
            
            // 保存到localStorage
            try {
                localStorage.setItem('ota_gemini_migration_backup', JSON.stringify(backup));
                this.logger.log('状态备份完成', 'info');
            } catch (error) {
                this.logger.logWarning('状态备份失败，但迁移将继续', error);
            }
        }

        /**
         * 准备新组件
         */
        async prepareNewComponents() {
            // 确保新组件已加载
            if (!this.components.newGeminiCoordinator) {
                // 尝试创建新协调器
                if (window.OTA?.gemini?.getGeminiCoordinator) {
                    this.components.newGeminiCoordinator = window.OTA.gemini.getGeminiCoordinator();
                } else {
                    throw new Error('新的Gemini协调器未找到');
                }
            }
            
            // 等待新组件初始化
            if (this.components.newGeminiCoordinator.initializationState) {
                let attempts = 0;
                while (!this.components.newGeminiCoordinator.initializationState.isInitialized && attempts < 10) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    attempts++;
                }
                
                if (!this.components.newGeminiCoordinator.initializationState.isInitialized) {
                    throw new Error('新组件初始化超时');
                }
            }
            
            this.logger.log('新组件准备完成', 'info');
        }

        /**
         * 运行兼容性测试
         */
        async runCompatibilityTests() {
            if (!this.components.compatibilityAdapter) {
                this.logger.logWarning('兼容性适配器未找到，跳过兼容性测试');
                return;
            }
            
            const testResults = await this.components.compatibilityAdapter.runCompatibilityTests();
            
            this.metrics.compatibility.testsRun = testResults.total;
            this.metrics.compatibility.testsPassed = testResults.passed;
            this.metrics.compatibility.testsFailed = testResults.failed;
            this.metrics.compatibility.compatibilityScore = testResults.score;
            
            if (testResults.score < 0.8) {
                throw new Error(`兼容性测试分数过低: ${testResults.score}`);
            }
            
            this.logger.log(`兼容性测试完成，分数: ${testResults.score}`, 'info');
        }

        /**
         * 性能比较
         */
        async performanceComparison() {
            const testCases = [
                'Test Order: ABC123456789',
                '订单号：CD789012345 乘客：张三 电话：13800138000',
                'Order: KL456789012 Passenger: John Smith Phone: +60123456789'
            ];
            
            // 测试旧系统
            if (this.components.oldGeminiService) {
                this.metrics.performance.oldSystemMetrics = await this.benchmarkSystem(
                    this.components.oldGeminiService, 
                    testCases
                );
            }
            
            // 测试新系统
            if (this.components.newGeminiCoordinator) {
                this.metrics.performance.newSystemMetrics = await this.benchmarkSystem(
                    this.components.newGeminiCoordinator, 
                    testCases
                );
            }
            
            // 比较结果
            this.metrics.performance.comparisonResults = this.comparePerformance(
                this.metrics.performance.oldSystemMetrics,
                this.metrics.performance.newSystemMetrics
            );
            
            this.logger.log('性能比较完成', 'info');
        }

        /**
         * 系统基准测试
         * @param {Object} system - 系统对象
         * @param {Array} testCases - 测试用例
         * @returns {Object} 基准测试结果
         */
        async benchmarkSystem(system, testCases) {
            const results = {
                totalTests: testCases.length,
                successfulTests: 0,
                failedTests: 0,
                averageTime: 0,
                minTime: Infinity,
                maxTime: 0,
                totalTime: 0
            };
            
            for (const testCase of testCases) {
                const startTime = Date.now();
                
                try {
                    let result;
                    if (system.processOrder) {
                        result = await system.processOrder(testCase, { test: true });
                    } else if (system.parseOrder) {
                        result = await system.parseOrder(testCase, { test: true });
                    } else {
                        throw new Error('系统不支持测试方法');
                    }
                    
                    const endTime = Date.now();
                    const duration = endTime - startTime;
                    
                    results.successfulTests++;
                    results.totalTime += duration;
                    results.minTime = Math.min(results.minTime, duration);
                    results.maxTime = Math.max(results.maxTime, duration);
                    
                } catch (error) {
                    results.failedTests++;
                    this.logger.logWarning(`基准测试失败: ${testCase}`, error);
                }
            }
            
            results.averageTime = results.totalTime / results.totalTests;
            results.successRate = results.successfulTests / results.totalTests;
            
            return results;
        }

        /**
         * 比较性能
         * @param {Object} oldMetrics - 旧系统指标
         * @param {Object} newMetrics - 新系统指标
         * @returns {Object} 比较结果
         */
        comparePerformance(oldMetrics, newMetrics) {
            if (!oldMetrics || !newMetrics) {
                return { comparison: 'incomplete' };
            }
            
            return {
                timeImprovement: (oldMetrics.averageTime - newMetrics.averageTime) / oldMetrics.averageTime,
                successRateChange: newMetrics.successRate - oldMetrics.successRate,
                recommendation: newMetrics.averageTime < oldMetrics.averageTime && 
                               newMetrics.successRate >= oldMetrics.successRate ? 
                               'proceed' : 'review'
            };
        }

        /**
         * 功能验证
         */
        async functionalValidation() {
            // 这里实现功能验证逻辑
            this.logger.log('功能验证完成', 'info');
        }

        /**
         * 错误处理测试
         */
        async errorHandlingTests() {
            // 这里实现错误处理测试逻辑
            this.logger.log('错误处理测试完成', 'info');
        }

        /**
         * 启用功能切换
         * @param {Object} options - 选项
         */
        async enableFeatureToggle(options) {
            if (!this.components.featureToggle) {
                throw new Error('功能切换器未找到');
            }
            
            await this.components.featureToggle.enableGradualMigration(options);
            this.logger.log('功能切换已启用', 'info');
        }

        /**
         * 渐进式流量切换
         * @param {Object} options - 选项
         */
        async gradualTrafficShift(options) {
            if (!this.components.featureToggle) {
                throw new Error('功能切换器未找到');
            }
            
            const trafficPercentages = options.trafficPercentages || [10, 25, 50, 75, 100];
            
            for (const percentage of trafficPercentages) {
                await this.components.featureToggle.setTrafficPercentage(percentage);
                
                // 等待一段时间观察效果
                await new Promise(resolve => setTimeout(resolve, options.waitTime || 30000));
                
                // 检查系统健康状态
                if (await this.shouldRollback()) {
                    throw new Error(`流量切换到 ${percentage}% 时检测到问题`);
                }
                
                this.logger.log(`流量已切换到新系统: ${percentage}%`, 'info');
            }
        }

        /**
         * 监控性能
         */
        async monitorPerformance() {
            // 这里实现性能监控逻辑
            this.logger.log('性能监控完成', 'info');
        }

        /**
         * 验证结果
         */
        async validateResults() {
            // 这里实现结果验证逻辑
            this.logger.log('结果验证完成', 'info');
        }

        /**
         * 清理旧代码
         */
        async cleanupOldCode() {
            // 这里实现旧代码清理逻辑
            this.logger.log('旧代码清理完成', 'info');
        }

        /**
         * 更新文档
         */
        async updateDocumentation() {
            // 这里实现文档更新逻辑
            this.logger.log('文档更新完成', 'info');
        }

        /**
         * 最终验证
         */
        async finalValidation() {
            // 这里实现最终验证逻辑
            this.logger.log('最终验证完成', 'info');
        }

        /**
         * 庆祝成功
         */
        async celebrateSuccess() {
            this.logger.log('🎉 渐进式重构迁移成功完成！', 'info');
        }

        /**
         * 检查是否应该回滚
         * @returns {boolean} 是否应该回滚
         */
        async shouldRollback() {
            if (!this.rollbackConfig.enabled) {
                return false;
            }
            
            // 检查错误率
            const errorRate = this.metrics.migration.errorsEncountered / 
                            Math.max(this.metrics.migration.tasksCompleted, 1);
            
            if (errorRate > this.rollbackConfig.autoRollbackThreshold.errorRate) {
                this.logger.logWarning(`错误率过高 (${errorRate})，触发自动回滚`);
                return true;
            }
            
            // 检查关键错误数量
            const criticalErrors = this.migrationState.errors.filter(e => 
                this.isCriticalError({ message: e.error })
            ).length;
            
            if (criticalErrors >= this.rollbackConfig.autoRollbackThreshold.criticalErrors) {
                this.logger.logWarning(`关键错误过多 (${criticalErrors})，触发自动回滚`);
                return true;
            }
            
            return false;
        }

        /**
         * 执行回滚
         */
        async rollback() {
            this.logger.log('开始执行回滚', 'info');
            
            this.migrationState.phase = 'ROLLBACK';
            
            try {
                // 禁用功能切换
                if (this.components.featureToggle) {
                    await this.components.featureToggle.disableNewSystem();
                }
                
                // 恢复备份状态
                await this.restoreBackup();
                
                this.logger.log('回滚完成', 'info');
                
            } catch (error) {
                this.logger.logError('回滚失败', error);
                throw error;
            }
        }

        /**
         * 恢复备份
         */
        async restoreBackup() {
            try {
                const backup = localStorage.getItem('ota_gemini_migration_backup');
                if (backup) {
                    const backupData = JSON.parse(backup);
                    // 这里实现备份恢复逻辑
                    this.logger.log('备份恢复完成', 'info');
                } else {
                    this.logger.logWarning('未找到备份数据');
                }
            } catch (error) {
                this.logger.logError('备份恢复失败', error);
            }
        }

        /**
         * 完成迁移
         */
        async completeMigration() {
            this.migrationState.phase = 'COMPLETE';
            this.migrationState.progress = 100;
            this.metrics.migration.endTime = Date.now();
            this.metrics.migration.duration = this.metrics.migration.endTime - this.metrics.migration.startTime;
            
            // 保存迁移状态
            this.saveMigrationState();
            
            this.logger.log('迁移过程完成', 'info');
        }

        /**
         * 判断是否为关键错误
         * @param {Error} error - 错误对象
         * @returns {boolean} 是否为关键错误
         */
        isCriticalError(error) {
            const criticalKeywords = [
                'initialization failed',
                'component not found',
                'compatibility test failed',
                'performance degradation'
            ];
            
            return criticalKeywords.some(keyword => 
                error.message.toLowerCase().includes(keyword)
            );
        }

        /**
         * 获取嵌套属性
         * @param {Object} obj - 对象
         * @param {string} path - 属性路径
         * @returns {*} 属性值
         */
        getNestedProperty(obj, path) {
            return path.split('.').reduce((current, key) => {
                return current && current[key] !== undefined ? current[key] : null;
            }, obj);
        }

        /**
         * 重置迁移状态
         */
        resetMigrationState() {
            this.migrationState = {
                phase: 'PREPARATION',
                progress: 0,
                startTime: new Date().toISOString(),
                lastUpdate: new Date().toISOString(),
                errors: [],
                warnings: []
            };
            
            this.metrics.migration.startTime = Date.now();
            this.metrics.migration.tasksTotal = Object.values(this.migrationPhases)
                .reduce((total, phase) => total + phase.tasks.length, 0);
        }

        /**
         * 更新迁移指标
         */
        updateMigrationMetrics() {
            this.migrationState.lastUpdate = new Date().toISOString();
            
            // 这里可以添加更多的指标更新逻辑
        }

        /**
         * 检查系统健康状态
         */
        checkSystemHealth() {
            // 这里实现系统健康检查逻辑
        }

        /**
         * 加载迁移状态
         */
        loadMigrationState() {
            try {
                const savedState = localStorage.getItem('ota_gemini_migration_state');
                if (savedState) {
                    const state = JSON.parse(savedState);
                    this.migrationState = { ...this.migrationState, ...state };
                }
            } catch (error) {
                this.logger.logWarning('加载迁移状态失败', error);
            }
        }

        /**
         * 保存迁移状态
         */
        saveMigrationState() {
            try {
                localStorage.setItem('ota_gemini_migration_state', JSON.stringify(this.migrationState));
            } catch (error) {
                this.logger.logWarning('保存迁移状态失败', error);
            }
        }

        /**
         * 获取迁移状态
         * @returns {Object} 迁移状态
         */
        getMigrationState() {
            return {
                ...this.migrationState,
                metrics: this.metrics
            };
        }

        /**
         * 获取迁移报告
         * @returns {Object} 迁移报告
         */
        getMigrationReport() {
            return {
                summary: {
                    phase: this.migrationState.phase,
                    progress: this.migrationState.progress,
                    duration: this.metrics.migration.duration,
                    success: this.migrationState.phase === 'COMPLETE'
                },
                details: {
                    tasksCompleted: this.metrics.migration.tasksCompleted,
                    tasksTotal: this.metrics.migration.tasksTotal,
                    errorsEncountered: this.metrics.migration.errorsEncountered,
                    warningsGenerated: this.metrics.migration.warningsGenerated
                },
                performance: this.metrics.performance,
                compatibility: this.metrics.compatibility,
                errors: this.migrationState.errors,
                warnings: this.migrationState.warnings
            };
        }
    }

    // 创建全局单例实例
    function getRefactorManager() {
        if (!window.OTA.gemini.migration.refactorManager) {
            window.OTA.gemini.migration.refactorManager = new RefactorManager();
        }
        return window.OTA.gemini.migration.refactorManager;
    }

    // 暴露到全局命名空间
    window.OTA.gemini.migration.RefactorManager = RefactorManager;
    window.OTA.gemini.migration.getRefactorManager = getRefactorManager;

    // 向后兼容
    window.getRefactorManager = getRefactorManager;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('refactorManager', getRefactorManager(), '@MIGRATION_MANAGER');
        window.OTA.Registry.registerFactory('getRefactorManager', getRefactorManager, '@MIGRATION_MANAGER_FACTORY');
    }

    console.log('✅ 渐进式重构管理器已加载');

})();
