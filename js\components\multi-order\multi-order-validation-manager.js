/**
 * @OTA_VALIDATION_MANAGER 多订单验证管理器
 * 🏷️ 标签: @VALIDATION_MANAGER
 * 📝 说明: 专门负责多订单系统的字段验证、数据验证和OTA参考号处理
 * ⚠️ 警告: 模块化重构产物，请勿重复开发
 * 重构特点：统一验证逻辑、字段验证、数据完整性检查
 * <AUTHOR>
 * @version 1.0.0-modular
 */

// 防止重复加载
if (window.OTA && window.OTA.MultiOrderValidationManager) {
    console.log('多订单验证管理器已存在，跳过重复加载');
} else {

/**
 * 多订单验证管理器类
 * 负责所有验证相关的操作
 */
class MultiOrderValidationManager {
    constructor(dependencies = {}) {
        // 🏗️ 依赖注入
        this.logger = dependencies.logger || this.getLogger();
        this.config = {
            requiredFields: ['customerName', 'pickup', 'dropoff', 'pickupDate', 'otaReferenceNumber'],
            validationRules: {
                email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                phone: /^[\+]?[0-9\-\(\)\s]+$/,
                date: /^\d{2}-\d{2}-\d{4}$/
            },
            ...dependencies.config
        };

        // 🏗️ 验证状态管理
        this.validationCache = new Map();
        this.fieldValidators = new Map();

        // 🏗️ 初始化
        this.init();
    }

    /**
     * 初始化验证管理器
     */
    init() {
        this.logger?.log('🔍 多订单验证管理器初始化', 'info');
        this.setupFieldValidators();
    }

    /**
     * 获取日志服务
     * @returns {Object} 日志服务实例
     */
    getLogger() {
        return window.getLogger?.() || {
            log: console.log.bind(console),
            logError: console.error.bind(console)
        };
    }

    /**
     * 设置字段验证器
     */
    setupFieldValidators() {
        // 邮箱验证器
        this.fieldValidators.set('email', (value) => {
            if (!value) return { isValid: true, message: '' };
            const isValid = this.config.validationRules.email.test(value);
            return {
                isValid,
                message: isValid ? '邮箱格式正确' : '邮箱格式不正确'
            };
        });

        // 电话验证器
        this.fieldValidators.set('phone', (value) => {
            if (!value) return { isValid: true, message: '' };
            const isValid = this.config.validationRules.phone.test(value);
            return {
                isValid,
                message: isValid ? '电话格式正确' : '电话格式不正确'
            };
        });

        // 日期验证器
        this.fieldValidators.set('date', (value) => {
            if (!value) return { isValid: true, message: '' };
            const isValid = this.config.validationRules.date.test(value);
            return {
                isValid,
                message: isValid ? '日期格式正确' : '日期格式应为DD-MM-YYYY'
            };
        });

        // OTA参考号验证器
        this.fieldValidators.set('otaReferenceNumber', (value) => {
            if (!value) return { isValid: false, message: 'OTA参考号为必填项' };
            const isValid = value.length >= 3;
            return {
                isValid,
                message: isValid ? 'OTA参考号格式正确' : 'OTA参考号至少需要3个字符'
            };
        });
    }

    /**
     * 验证字段
     * @param {number} index - 订单索引
     * @param {string} fieldName - 字段名
     * @param {*} value - 字段值
     */
    validateField(index, fieldName, value) {
        // 根据当前编辑模式选择正确的DOM选择器
        let fieldGroup;
        
        // 首先尝试快捷编辑模式的选择器
        fieldGroup = document.querySelector(`.quick-edit-panel .field-group[data-field="${fieldName}"]`);
        
        // 如果没找到，尝试网格编辑模式的选择器
        if (!fieldGroup) {
            fieldGroup = document.querySelector(`.order-item[data-order-index="${index}"] .field-group[data-field="${fieldName}"]`);
        }
        
        // 如果还没找到，尝试通用选择器
        if (!fieldGroup) {
            fieldGroup = document.querySelector(`[data-field="${fieldName}"][data-order-index="${index}"]`);
        }
        
        if (!fieldGroup) {
            this.logger?.log(`未找到字段组: ${fieldName} (订单${index})`, 'warn');
            return;
        }

        const validationContainer = fieldGroup.querySelector('.field-validation-container');
        if (!validationContainer) return;

        // 必填字段列表
        const requiredFields = this.config.requiredFields;
        const isRequired = requiredFields.includes(fieldName);
        const isEmpty = !value || value.toString().trim() === '';

        // 清除之前的验证状态
        fieldGroup.classList.remove('invalid', 'valid');
        validationContainer.innerHTML = '';

        if (isRequired && isEmpty) {
            // 必填字段为空
            fieldGroup.classList.add('invalid');
            validationContainer.innerHTML = `<div class="field-validation-message">此字段为必填项</div>`;
        } else if (!isEmpty) {
            // 字段有值，进行特定验证
            let isValid = true;
            let message = '';

            // 使用字段验证器
            const validator = this.fieldValidators.get(fieldName);
            if (validator) {
                const result = validator(value);
                isValid = result.isValid;
                message = result.message;
            } else {
                // 通用验证逻辑
                switch (fieldName) {
                    case 'customerEmail':
                        const emailResult = this.fieldValidators.get('email')(value);
                        isValid = emailResult.isValid;
                        message = emailResult.message;
                        break;
                    case 'customerPhone':
                        const phoneResult = this.fieldValidators.get('phone')(value);
                        isValid = phoneResult.isValid;
                        message = phoneResult.message;
                        break;
                    case 'pickupDate':
                        const dateResult = this.fieldValidators.get('date')(value);
                        isValid = dateResult.isValid;
                        message = dateResult.message;
                        break;
                    case 'pickup':
                    case 'dropoff':
                        isValid = value.length >= 3;
                        message = isValid ? '地址格式正确' : '地址至少需要3个字符';
                        break;
                    case 'customerName':
                        isValid = value.length >= 2;
                        message = isValid ? '客户姓名格式正确' : '客户姓名至少需要2个字符';
                        break;
                    default:
                        isValid = true;
                        message = '字段格式正确';
                        break;
                }
            }

            if (isValid && message) {
                fieldGroup.classList.add('valid');
                validationContainer.innerHTML = `<div class="field-success-message">${message}</div>`;
            } else if (!isValid) {
                fieldGroup.classList.add('invalid');
                validationContainer.innerHTML = `<div class="field-validation-message">${message}</div>`;
            }
        }

        // 缓存验证结果
        const cacheKey = `${index}-${fieldName}`;
        this.validationCache.set(cacheKey, {
            isValid: !fieldGroup.classList.contains('invalid'),
            message: validationContainer.textContent,
            timestamp: Date.now()
        });
    }

    /**
     * 验证和增强OTA参考号
     * @param {number} index - 订单索引
     * @param {Object} state - 状态对象
     */
    validateAndEnhanceOtaReference(index, state) {
        if (!state.parsedOrders || index >= state.parsedOrders.length) {
            this.logger?.log(`无效的订单索引: ${index}`, 'warn');
            return;
        }

        const order = state.parsedOrders[index];
        if (!order) {
            this.logger?.log(`订单 ${index + 1} 不存在`, 'warn');
            return;
        }

        // 检查是否已有OTA参考号
        if (order.otaReferenceNumber && order.otaReferenceNumber.trim() !== '') {
            this.logger?.log(`订单 ${index + 1} 已有OTA参考号: ${order.otaReferenceNumber}`, 'info');
            return;
        }

        // 生成OTA参考号
        const otaReference = this.generateOtaReferenceNumber(order, index);
        
        // 更新订单数据
        order.otaReferenceNumber = otaReference;
        
        // 更新UI显示
        const input = document.querySelector(`.order-item[data-order-index="${index}"] input[name="otaReferenceNumber"]`);
        if (input) {
            input.value = otaReference;
            input.classList.remove('ota-reference-missing');
        }

        this.logger?.log(`为订单 ${index + 1} 生成OTA参考号: ${otaReference}`, 'success');
    }

    /**
     * 生成OTA参考号
     * @param {Object} order - 订单对象
     * @param {number} index - 订单索引
     * @returns {string} 生成的OTA参考号
     */
    generateOtaReferenceNumber(order, index) {
        try {
            // 基于客户名生成前缀
            let prefix = '';
            if (order.customerName) {
                // 提取客户名字的前几个字符
                const cleanName = order.customerName.replace(/[^a-zA-Z0-9\u4e00-\u9fff]/g, '');
                prefix = cleanName.substring(0, 3).toUpperCase();
            } else {
                prefix = 'OTA';
            }

            // 生成时间戳后缀
            const timestamp = Date.now().toString().slice(-6);
            
            // 生成订单序号
            const orderNum = (index + 1).toString().padStart(2, '0');
            
            return `${prefix}${orderNum}${timestamp}`;
        } catch (error) {
            this.logger?.logError('生成OTA参考号失败', error);
            return `OTA${Date.now().toString().slice(-8)}`;
        }
    }

    /**
     * 验证和格式化订单字段
     * @param {Object} order - 订单对象
     */
    validateAndFormatOrderFields(order) {
        // 1. 处理备用字段名映射
        this.applyAlternativeFieldMapping(order);

        // 2. 格式化日期字段
        if (order.pickupDate) {
            order.pickupDate = this.formatDateField(order.pickupDate);
        }

        // 3. 格式化电话字段
        if (order.customerPhone) {
            order.customerPhone = this.formatPhoneField(order.customerPhone);
        }

        // 4. 清理和验证邮箱字段
        if (order.customerEmail) {
            order.customerEmail = this.formatEmailField(order.customerEmail);
        }

        // 5. 确保必要字段存在
        this.ensureRequiredFields(order);
    }

    /**
     * 处理备用字段名映射
     * @param {Object} order - 订单对象
     */
    applyAlternativeFieldMapping(order) {
        // 处理常见的字段名变体
        const fieldMappings = {
            'customer_name': 'customerName',
            'customer_email': 'customerEmail',
            'customer_phone': 'customerPhone',
            'pickup_location': 'pickup',
            'dropoff_location': 'dropoff',
            'pickup_date': 'pickupDate',
            'pickup_time': 'pickupTime',
            'ota_reference': 'otaReferenceNumber'
        };

        for (const [oldField, newField] of Object.entries(fieldMappings)) {
            if (order[oldField] && !order[newField]) {
                order[newField] = order[oldField];
                delete order[oldField];
            }
        }
    }

    /**
     * 格式化日期字段
     * @param {string} dateValue - 日期值
     * @returns {string} 格式化后的日期
     */
    formatDateField(dateValue) {
        // 使用统一的工具函数
        if (window.OTA?.utils?.formatDateForAPI) {
            return window.OTA.utils.formatDateForAPI(dateValue);
        }

        // 降级方案
        if (!dateValue) return '';

        try {
            const date = new Date(dateValue);
            if (isNaN(date.getTime())) return dateValue;

            const day = date.getDate().toString().padStart(2, '0');
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const year = date.getFullYear();

            return `${day}-${month}-${year}`;
        } catch (error) {
            this.logger?.logError('日期格式化失败', error);
            return dateValue;
        }
    }

    /**
     * 格式化电话字段
     * @param {string} phoneValue - 电话值
     * @returns {string} 格式化后的电话
     */
    formatPhoneField(phoneValue) {
        // 使用统一的工具函数进行清理
        if (window.OTA?.utils?.normalizePhoneNumber) {
            return window.OTA.utils.normalizePhoneNumber(phoneValue);
        }

        // 降级方案
        if (!phoneValue) return '';
        return phoneValue.replace(/[^\d\+\-\(\)\s]/g, '').trim();
    }

    /**
     * 格式化邮箱字段
     * @param {string} emailValue - 邮箱值
     * @returns {string} 格式化后的邮箱
     */
    formatEmailField(emailValue) {
        if (!emailValue) return '';

        // 转换为小写并去除首尾空格
        return emailValue.toLowerCase().trim();
    }

    /**
     * 确保必要字段存在
     * @param {Object} order - 订单对象
     */
    ensureRequiredFields(order) {
        // 确保所有必填字段都有默认值
        const defaults = {
            customerName: '',
            customerEmail: '',
            customerPhone: '',
            pickup: '',
            dropoff: '',
            pickupDate: '',
            pickupTime: '',
            otaReferenceNumber: '',
            serviceTypeId: 2, // 默认接机
            carTypeId: 1,     // 默认Comfort 5 Seater
            languagesIdArray: [2] // 默认英文
        };

        for (const [field, defaultValue] of Object.entries(defaults)) {
            if (order[field] === undefined || order[field] === null) {
                order[field] = defaultValue;
            }
        }
    }

    /**
     * 检查常见字段显示问题
     * @param {Array} orders - 订单数组
     * @returns {Object} 问题分析报告
     */
    checkCommonFieldDisplayIssues(orders) {
        console.group('🔍 常见字段显示问题检查');

        const issues = {
            missingRequiredFields: [],
            invalidEmailFormats: [],
            invalidPhoneFormats: [],
            invalidDateFormats: [],
            duplicateOtaReferences: [],
            emptyAddresses: []
        };

        orders.forEach((order, index) => {
            // 检查必填字段
            this.config.requiredFields.forEach(field => {
                if (!order[field] || order[field].toString().trim() === '') {
                    issues.missingRequiredFields.push({
                        orderIndex: index,
                        field: field,
                        message: `订单${index + 1}缺少必填字段: ${field}`
                    });
                }
            });

            // 检查邮箱格式
            if (order.customerEmail && !this.config.validationRules.email.test(order.customerEmail)) {
                issues.invalidEmailFormats.push({
                    orderIndex: index,
                    value: order.customerEmail,
                    message: `订单${index + 1}邮箱格式不正确`
                });
            }

            // 检查电话格式
            if (order.customerPhone && !this.config.validationRules.phone.test(order.customerPhone)) {
                issues.invalidPhoneFormats.push({
                    orderIndex: index,
                    value: order.customerPhone,
                    message: `订单${index + 1}电话格式不正确`
                });
            }

            // 检查日期格式
            if (order.pickupDate && !this.config.validationRules.date.test(order.pickupDate)) {
                issues.invalidDateFormats.push({
                    orderIndex: index,
                    value: order.pickupDate,
                    message: `订单${index + 1}日期格式不正确`
                });
            }

            // 检查地址是否为空
            if (!order.pickup || order.pickup.trim().length < 3) {
                issues.emptyAddresses.push({
                    orderIndex: index,
                    field: 'pickup',
                    message: `订单${index + 1}接送地址过短或为空`
                });
            }

            if (!order.dropoff || order.dropoff.trim().length < 3) {
                issues.emptyAddresses.push({
                    orderIndex: index,
                    field: 'dropoff',
                    message: `订单${index + 1}目的地址过短或为空`
                });
            }
        });

        // 检查重复的OTA参考号
        const otaReferences = new Map();
        orders.forEach((order, index) => {
            if (order.otaReferenceNumber) {
                if (otaReferences.has(order.otaReferenceNumber)) {
                    issues.duplicateOtaReferences.push({
                        orderIndex: index,
                        duplicateWith: otaReferences.get(order.otaReferenceNumber),
                        value: order.otaReferenceNumber,
                        message: `订单${index + 1}的OTA参考号与订单${otaReferences.get(order.otaReferenceNumber) + 1}重复`
                    });
                } else {
                    otaReferences.set(order.otaReferenceNumber, index);
                }
            }
        });

        console.log('🔍 问题检查完成:', issues);
        console.groupEnd();

        return issues;
    }

    /**
     * 获取选中订单的索引数组
     * @returns {Array<number>} 选中订单的索引数组
     */
    getSelectedOrderIndexes() {
        const checkboxes = document.querySelectorAll('.order-checkbox:checked');
        return Array.from(checkboxes).map(checkbox => {
            const orderItem = checkbox.closest('.order-card');
            return parseInt(orderItem.getAttribute('data-order-index'));
        }).filter(index => !isNaN(index));
    }

    /**
     * 验证选中的订单
     * @returns {Object} 验证结果
     */
    validateSelectedOrders() {
        const selectedIndexes = this.getSelectedOrderIndexes();

        if (selectedIndexes.length === 0) {
            return {
                isValid: false,
                message: '没有选中的订单',
                errors: ['请至少选择一个订单']
            };
        }

        const errors = [];
        const warnings = [];

        selectedIndexes.forEach(index => {
            // 检查缓存的验证结果
            this.config.requiredFields.forEach(field => {
                const cacheKey = `${index}-${field}`;
                const cached = this.validationCache.get(cacheKey);

                if (cached && !cached.isValid) {
                    errors.push(`订单${index + 1}的${field}字段验证失败: ${cached.message}`);
                }
            });
        });

        return {
            isValid: errors.length === 0,
            message: errors.length === 0 ? '所有选中订单验证通过' : '部分订单存在验证错误',
            errors,
            warnings,
            selectedCount: selectedIndexes.length
        };
    }

    /**
     * 清理验证缓存
     */
    clearValidationCache() {
        this.validationCache.clear();
        this.logger?.log('验证缓存已清理', 'info');
    }

    /**
     * 销毁验证管理器
     */
    destroy() {
        this.clearValidationCache();
        this.fieldValidators.clear();
        this.logger?.log('🔍 验证管理器已销毁', 'info');
    }
}

// 工厂函数
function getMultiOrderValidationManager(dependencies = {}) {
    return new MultiOrderValidationManager(dependencies);
}

window.getMultiOrderValidationManager = getMultiOrderValidationManager;

// 结束防重复加载检查
}
