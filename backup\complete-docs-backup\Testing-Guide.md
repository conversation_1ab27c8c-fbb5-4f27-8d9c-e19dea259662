# OTA系统测试指南

## 🧪 概述

本文档为OTA订单处理系统提供全面的测试策略和实施指南，包括单元测试、集成测试、性能测试和用户验收测试。

### 测试目标
- 🎯 **功能正确性**：确保所有功能按预期工作
- 🛡️ **稳定性**：验证系统在各种情况下的稳定运行
- ⚡ **性能**：确保系统满足性能要求
- 📱 **兼容性**：验证多浏览器和设备兼容性
- 🔒 **安全性**：检查安全漏洞和数据保护

## 📅 测试策略

### 测试金字塔
```
    ⬆️ 用户验收测试 (5%)
   ┌───────────────────┐
   │  手动测试、用户体验  │
   └───────────────────┘
    ┌──────────────────────────────┐
    │     集成测试 (25%)      │
    │  模块间交互、API测试   │
    └──────────────────────────────┘
    ┌────────────────────────────────────────┐
    │          单元测试 (70%)           │
    │   单个函数、类、组件测试      │
    └────────────────────────────────────────┘
```

### 测试环境

#### 本地开发环境
- **目的**：开发者日常测试
- **覆盖范围**：单元测试 + 部分集成测试
- **数据**：模拟数据

#### 测试环境
- **目的**：完整功能验证
- **覆盖范围**：全面集成测试 + 性能测试
- **数据**：仿真数据

#### 预发布环境
- **目的**：生产环境最终验证
- **覆盖范围**：用户验收测试 + 压力测试
- **数据**：真实数据子集

## 🔬 单元测试

### 测试框架设置

#### Jest 配置
```javascript
// jest.config.js
module.exports = {
    testEnvironment: 'jsdom',
    roots: ['<rootDir>/tests'],
    testMatch: [
        '**/tests/**/*.test.js',
        '**/tests/**/*.spec.js'
    ],
    setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
    collectCoverageFrom: [
        'js/**/*.js',
        '!js/vendor/**',
        '!js/**/*.min.js'
    ],
    coverageThreshold: {
        global: {
            branches: 80,
            functions: 80,
            lines: 80,
            statements: 80
        }
    },
    moduleNameMapping: {
        '^@/(.*)$': '<rootDir>/js/$1'
    }
};
```

#### 测试设置文件
```javascript
// tests/setup.js
// 全局测试设置

// 模拟LocalStorage
const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn()
};
Object.defineProperty(window, 'localStorage', {
    value: localStorageMock
});

// 模拟 fetch API
global.fetch = jest.fn();

// 模拟 OTA 全局对象
global.window.OTA = {
    container: {
        register: jest.fn(),
        get: jest.fn()
    },
    getService: jest.fn()
};

// 测试工具函数
global.createMockComponent = (overrides = {}) => {
    return {
        init: jest.fn(),
        destroy: jest.fn(),
        reset: jest.fn(),
        ...overrides
    };
};

global.createMockService = (overrides = {}) => {
    return {
        isInitialized: false,
        init: jest.fn(),
        ...overrides
    };
};
```

### 核心模块测试

#### API 服务测试
```javascript
// tests/unit/services/api-service.test.js
describe('APIService', () => {
    let apiService;
    
    beforeEach(() => {
        fetch.mockClear();
        apiService = new APIService();
        apiService.init();
    });
    
    describe('createOrder', () => {
        it('应该成功创建订单', async () => {
            const mockResponse = {
                status: true,
                order_id: '12345'
            };
            
            fetch.mockResolvedValueOnce({
                ok: true,
                json: () => Promise.resolve(mockResponse)
            });
            
            const orderData = {
                customer_name: 'John Doe',
                pickup_location: 'KLIA',
                dropoff_location: 'KL City',
                pickup_date: '2025-01-20',
                pickup_time: '14:30',
                passenger_number: 2
            };
            
            const result = await apiService.createOrder(orderData);
            
            expect(fetch).toHaveBeenCalledWith(
                expect.stringContaining('/create_order'),
                expect.objectContaining({
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(orderData)
                })
            );
            
            expect(result).toEqual(mockResponse);
        });
        
        it('应该处理网络错误', async () => {
            fetch.mockRejectedValueOnce(new Error('Network error'));
            
            await expect(apiService.createOrder({})).rejects.toThrow('Network error');
        });
        
        it('应该处理HTTP错误', async () => {
            fetch.mockResolvedValueOnce({
                ok: false,
                status: 422,
                statusText: 'Validation Failed'
            });
            
            await expect(apiService.createOrder({})).rejects.toThrow();
        });
    });
    
    describe('validateOrderData', () => {
        it('应该验证必填字段', () => {
            const invalidData = {
                customer_name: 'John Doe'
                // 缺少必填字段
            };
            
            const result = apiService.validateOrderData(invalidData);
            
            expect(result.isValid).toBe(false);
            expect(result.errors).toContain('pickup_location 为必填字段');
        });
        
        it('应该通过有效数据验证', () => {
            const validData = {
                customer_name: 'John Doe',
                pickup_location: 'KLIA',
                dropoff_location: 'KL City',
                pickup_date: '2025-01-20',
                pickup_time: '14:30',
                passenger_number: 2
            };
            
            const result = apiService.validateOrderData(validData);
            
            expect(result.isValid).toBe(true);
            expect(result.errors).toHaveLength(0);
        });
    });
});
```

#### 依赖容器测试
```javascript
// tests/unit/core/dependency-container.test.js
describe('DependencyContainer', () => {
    let container;
    
    beforeEach(() => {
        container = new DependencyContainer();
    });
    
    describe('服务注册', () => {
        it('应该注册单例服务', () => {
            const factory = jest.fn(() => ({ test: true }));
            
            container.register('testService', factory, true);
            
            const instance1 = container.get('testService');
            const instance2 = container.get('testService');
            
            expect(factory).toHaveBeenCalledTimes(1);
            expect(instance1).toBe(instance2);
        });
        
        it('应该注册非单例服务', () => {
            const factory = jest.fn(() => ({ test: true }));
            
            container.register('testService', factory, false);
            
            const instance1 = container.get('testService');
            const instance2 = container.get('testService');
            
            expect(factory).toHaveBeenCalledTimes(2);
            expect(instance1).not.toBe(instance2);
        });
    });
    
    describe('循环依赖检测', () => {
        it('应该检测循环依赖', () => {
            container.register('serviceA', () => container.get('serviceB'));
            container.register('serviceB', () => container.get('serviceA'));
            
            expect(() => container.get('serviceA')).toThrow('Circular dependency detected');
        });
    });
});
```

#### AI 服务测试
```javascript
// tests/unit/ai/gemini-service.test.js
describe('GeminiService', () => {
    let geminiService;
    
    beforeEach(() => {
        fetch.mockClear();
        geminiService = new GeminiService();
        geminiService.init();
    });
    
    describe('analyzeOrderText', () => {
        it('应该成功分析订单文本', async () => {
            const mockResponse = {
                candidates: [{
                    content: {
                        parts: [{
                            text: JSON.stringify({
                                customer_name: 'John Doe',
                                pickup_location: 'KLIA',
                                dropoff_location: 'KL City'
                            })
                        }]
                    }
                }]
            };
            
            fetch.mockResolvedValueOnce({
                ok: true,
                json: () => Promise.resolve(mockResponse)
            });
            
            const orderText = '从KLIA机场到吉隆坡市中心，乘客John Doe';
            const result = await geminiService.analyzeOrderText(orderText);
            
            expect(result).toHaveProperty('customer_name', 'John Doe');
            expect(result).toHaveProperty('pickup_location', 'KLIA');
            expect(result).toHaveProperty('dropoff_location', 'KL City');
        });
        
        it('应该处理API错误', async () => {
            fetch.mockResolvedValueOnce({
                ok: false,
                status: 429,
                statusText: 'Too Many Requests'
            });
            
            await expect(geminiService.analyzeOrderText('test'))
                .rejects.toThrow('Gemini API error: 429');
        });
    });
    
    describe('_parseResponse', () => {
        it('应该解析有效响应', () => {
            const response = {
                candidates: [{
                    content: {
                        parts: [{
                            text: '{"customer_name": "John"}'
                        }]
                    }
                }]
            };
            
            const result = geminiService._parseResponse(response);
            
            expect(result).toHaveProperty('customer_name', 'John');
        });
        
        it('应该处理无效JSON', () => {
            const response = {
                candidates: [{
                    content: {
                        parts: [{
                            text: 'invalid json'
                        }]
                    }
                }]
            };
            
            expect(() => geminiService._parseResponse(response))
                .toThrow('无法解析Gemini响应');
        });
    });
});
```

### 测试工具和辅助

#### Mock 工具
```javascript
// tests/utils/mock-helpers.js
export class MockGenerator {
    static createOrderData(overrides = {}) {
        return {
            customer_name: 'Test Customer',
            customer_contact: '+60123456789',
            pickup_location: 'KLIA Terminal 1',
            dropoff_location: 'KL City Center',
            pickup_date: '2025-01-20',
            pickup_time: '14:30',
            passenger_number: 2,
            luggage_count: 2,
            car_type_id: 1,
            sub_category_id: 1,
            driving_region_id: 1,
            incharge_by_backend_user_id: 1,
            ...overrides
        };
    }
    
    static createGeminiResponse(data) {
        return {
            candidates: [{
                content: {
                    parts: [{
                        text: JSON.stringify(data)
                    }]
                }
            }]
        };
    }
    
    static createApiSuccessResponse(data) {
        return {
            status: true,
            data,
            message: 'Success'
        };
    }
    
    static createApiErrorResponse(errors) {
        return {
            status: false,
            errors,
            message: 'Validation failed'
        };
    }
}
```

## 🔗 集成测试

### 模块集成测试

#### 订单处理流程测试
```javascript
// tests/integration/order-processing.test.js
describe('订单处理集成测试', () => {
    let testEnvironment;
    
    beforeEach(async () => {
        // 初始化测试环境
        testEnvironment = new TestEnvironment();
        await testEnvironment.setup();
    });
    
    afterEach(async () => {
        await testEnvironment.teardown();
    });
    
    it('应该完成完整的订单创建流程', async () => {
        const orderText = '从KLIA到KL City，明天下午2点，2个人，客户：John Doe +60123456789';
        
        // 1. AI分析
        const aiService = testEnvironment.getService('geminiService');
        const analysisResult = await aiService.analyzeOrderText(orderText);
        
        // 2. 表单填充
        const formManager = testEnvironment.getService('formManager');
        formManager.fillFormFromAnalysis(analysisResult);
        
        // 3. 数据验证
        const formData = formManager.getFormData();
        expect(formData.customer_name).toBe('John Doe');
        expect(formData.pickup_location).toBe('KLIA');
        expect(formData.dropoff_location).toBe('KL City');
        
        // 4. 订单提交
        const apiService = testEnvironment.getService('apiService');
        const submitResult = await apiService.createOrder(formData);
        
        expect(submitResult.status).toBe(true);
        expect(submitResult).toHaveProperty('order_id');
    });
    
    it('应该处理多订单场景', async () => {
        const multiOrderText = `
            订单1: 从KLIA到KL City, 明天上午9点, John Doe
            订单2: 从KL City到KLIA, 后天下午3点, Jane Smith
        `;
        
        // 1. 多订单检测
        const multiOrderDetector = testEnvironment.getService('multiOrderDetector');
        const detectionResult = multiOrderDetector.detectMultipleOrders(multiOrderText);
        
        expect(detectionResult.isMultiOrder).toBe(true);
        expect(detectionResult.orderCount).toBe(2);
        
        // 2. 订单分割
        const orders = detectionResult.orders;
        expect(orders).toHaveLength(2);
        
        // 3. 批量处理
        const multiOrderManager = testEnvironment.getService('multiOrderManager');
        const processResults = await multiOrderManager.processBatch(orders);
        
        expect(processResults).toHaveLength(2);
        expect(processResults.every(result => result.status === true)).toBe(true);
    });
});
```

#### API 集成测试
```javascript
// tests/integration/api-integration.test.js
describe('API集成测试', () => {
    const API_BASE_URL = 'http://localhost:3000/api';
    
    beforeEach(() => {
        // 设置测试API服务器
        fetch.mockClear();
    });
    
    describe('订单API', () => {
        it('应该成功创建订单', async () => {
            const orderData = MockGenerator.createOrderData();
            const expectedResponse = MockGenerator.createApiSuccessResponse({
                order_id: 'ORD-12345'
            });
            
            fetch.mockResolvedValueOnce({
                ok: true,
                json: () => Promise.resolve(expectedResponse)
            });
            
            const apiService = new APIService();
            apiService.init();
            
            const result = await apiService.createOrder(orderData);
            
            expect(fetch).toHaveBeenCalledWith(
                `${API_BASE_URL}/create_order`,
                expect.objectContaining({
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(orderData)
                })
            );
            
            expect(result).toEqual(expectedResponse);
        });
    });
    
    describe('错误处理', () => {
        it('应该正确处理422验证错误', async () => {
            const invalidData = { customer_name: 'Test' }; // 缺少必填字段
            const errorResponse = MockGenerator.createApiErrorResponse({
                pickup_location: ['The pickup location field is required.']
            });
            
            fetch.mockResolvedValueOnce({
                ok: false,
                status: 422,
                json: () => Promise.resolve(errorResponse)
            });
            
            const apiService = new APIService();
            apiService.init();
            
            await expect(apiService.createOrder(invalidData))
                .rejects.toThrow('验证失败');
        });
    });
});
```

### 测试环境设置

#### 测试环境类
```javascript
// tests/utils/test-environment.js
export class TestEnvironment {
    constructor() {
        this.services = new Map();
        this.components = new Map();
        this.eventListeners = [];
    }
    
    async setup() {
        // 初始化全局对象
        this.setupGlobalOTA();
        
        // 模拟DOM环境
        this.setupDOMEnvironment();
        
        // 初始化核心服务
        await this.initializeCoreServices();
        
        // 设置Mock响应
        this.setupMockResponses();
    }
    
    async teardown() {
        // 清理事件监听器
        this.eventListeners.forEach(({ element, event, handler }) => {
            element.removeEventListener(event, handler);
        });
        
        // 销毁组件
        for (const component of this.components.values()) {
            if (component.destroy) {
                component.destroy();
            }
        }
        
        // 清理服务
        this.services.clear();
        this.components.clear();
        this.eventListeners = [];
    }
    
    setupGlobalOTA() {
        global.window.OTA = {
            container: {
                register: (name, factory, singleton) => {
                    this.services.set(name, { factory, singleton, instance: null });
                },
                get: (name) => this.getService(name)
            },
            getService: (name) => this.getService(name)
        };
    }
    
    getService(name) {
        const service = this.services.get(name);
        if (!service) {
            throw new Error(`Service '${name}' not found`);
        }
        
        if (service.singleton && service.instance) {
            return service.instance;
        }
        
        const instance = service.factory();
        if (service.singleton) {
            service.instance = instance;
        }
        
        return instance;
    }
    
    setupDOMEnvironment() {
        // 模拟DOM元素
        document.body.innerHTML = `
            <div id="app">
                <form id="order-form">
                    <input id="customer-name" type="text" />
                    <input id="pickup-location" type="text" />
                    <input id="dropoff-location" type="text" />
                    <button id="submit-btn" type="submit">提交</button>
                </form>
                <div id="multi-order-container"></div>
            </div>
        `;
    }
    
    async initializeCoreServices() {
        // 注册模拟服务
        const container = window.OTA.container;
        
        container.register('logger', () => createMockService({
            logError: jest.fn(),
            logInfo: jest.fn()
        }));
        
        container.register('apiService', () => {
            const service = new APIService();
            service.init();
            return service;
        });
        
        container.register('geminiService', () => {
            const service = new GeminiService();
            service.init();
            return service;
        });
        
        container.register('formManager', () => {
            const service = new FormManager();
            service.init();
            return service;
        });
    }
    
    setupMockResponses() {
        // 设置默认模拟API响应
        fetch.mockImplementation((url) => {
            if (url.includes('/create_order')) {
                return Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve({
                        status: true,
                        order_id: 'TEST-' + Date.now()
                    })
                });
            }
            
            if (url.includes('generativelanguage.googleapis.com')) {
                return Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve({
                        candidates: [{
                            content: {
                                parts: [{ text: '{}' }]
                            }
                        }]
                    })
                });
            }
            
            return Promise.reject(new Error(`Unmocked URL: ${url}`));
        });
    }
}
```

## ⚡ 性能测试

### 性能测试框架

#### Lighthouse CI 配置
```javascript
// lighthouserc.js
module.exports = {
    ci: {
        collect: {
            url: ['http://localhost:3000'],
            numberOfRuns: 3
        },
        assert: {
            assertions: {
                'categories:performance': ['error', { minScore: 0.8 }],
                'categories:accessibility': ['error', { minScore: 0.9 }],
                'categories:best-practices': ['error', { minScore: 0.9 }],
                'categories:seo': ['error', { minScore: 0.8 }],
                'first-contentful-paint': ['error', { maxNumericValue: 2000 }],
                'largest-contentful-paint': ['error', { maxNumericValue: 3000 }],
                'cumulative-layout-shift': ['error', { maxNumericValue: 0.1 }]
            }
        },
        upload: {
            target: 'temporary-public-storage'
        }
    }
};
```

#### 启动性能测试
```javascript
// tests/performance/startup-performance.test.js
describe('启动性能测试', () => {
    let performanceMonitor;
    
    beforeEach(() => {
        performanceMonitor = new PerformanceMonitor();
        performance.clearMarks();
        performance.clearMeasures();
    });
    
    it('应该在规定时间内完成初始化', async () => {
        performance.mark('startup-begin');
        
        // 模拟应用启动
        await initializeApplication();
        
        performance.mark('startup-end');
        performance.measure('startup-duration', 'startup-begin', 'startup-end');
        
        const measure = performance.getEntriesByName('startup-duration')[0];
        
        // 启动时间应少于2秒
        expect(measure.duration).toBeLessThan(2000);
    });
    
    it('应该在关键路径上加载最少模块', async () => {
        const initialScripts = document.querySelectorAll('script').length;
        
        await initializeApplication();
        
        const afterScripts = document.querySelectorAll('script').length;
        const loadedScripts = afterScripts - initialScripts;
        
        // 初始加载应少于10个脚本
        expect(loadedScripts).toBeLessThan(10);
    });
    
    it('应该在规定时间内加载懒加载模块', async () => {
        const lazyLoader = window.OTA.lazyLoader;
        
        const startTime = performance.now();
        await lazyLoader.loadModule('js/ai/gemini-service.js');
        const endTime = performance.now();
        
        const loadTime = endTime - startTime;
        
        // 懒加载应在500ms内完成
        expect(loadTime).toBeLessThan(500);
    });
});
```

#### 内存性能测试
```javascript
// tests/performance/memory-performance.test.js
describe('内存性能测试', () => {
    let memoryBaseline;
    
    beforeEach(() => {
        // 设置内存基线
        if (performance.memory) {
            memoryBaseline = performance.memory.usedJSHeapSize;
        }
    });
    
    it('应该在组件销毁后释放内存', async () => {
        const components = [];
        
        // 创建多个组件
        for (let i = 0; i < 100; i++) {
            const component = new TestComponent();
            components.push(component);
        }
        
        const memoryAfterCreation = performance.memory.usedJSHeapSize;
        
        // 销毁所有组件
        components.forEach(component => component.destroy());
        
        // 强制垃圾回收
        if (window.gc) {
            window.gc();
        }
        
        await new Promise(resolve => setTimeout(resolve, 100));
        
        const memoryAfterDestroy = performance.memory.usedJSHeapSize;
        const memoryFreed = memoryAfterCreation - memoryAfterDestroy;
        
        // 应该释放至少80%的内存
        expect(memoryFreed / (memoryAfterCreation - memoryBaseline)).toBeGreaterThan(0.8);
    });
    
    it('应该防止内存泄漏', async () => {
        const initialMemory = performance.memory.usedJSHeapSize;
        
        // 模拟多次操作
        for (let i = 0; i < 50; i++) {
            const component = new TestComponent();
            component.performOperation();
            component.destroy();
        }
        
        // 强制垃圾回收
        if (window.gc) {
            window.gc();
        }
        
        await new Promise(resolve => setTimeout(resolve, 100));
        
        const finalMemory = performance.memory.usedJSHeapSize;
        const memoryIncrease = finalMemory - initialMemory;
        
        // 内存增长应小于5MB
        expect(memoryIncrease).toBeLessThan(5 * 1024 * 1024);
    });
});
```

### 压力测试

#### 并发订单处理测试
```javascript
// tests/performance/stress-test.test.js
describe('压力测试', () => {
    it('应该处理并发订单创建', async () => {
        const concurrentOrders = 50;
        const promises = [];
        
        for (let i = 0; i < concurrentOrders; i++) {
            const orderData = MockGenerator.createOrderData({
                customer_name: `Customer ${i}`
            });
            
            promises.push(
                window.OTA.getService('apiService').createOrder(orderData)
            );
        }
        
        const startTime = performance.now();
        const results = await Promise.allSettled(promises);
        const endTime = performance.now();
        
        const successCount = results.filter(r => r.status === 'fulfilled').length;
        const duration = endTime - startTime;
        
        // 成功率应大于90%
        expect(successCount / concurrentOrders).toBeGreaterThan(0.9);
        
        // 平均响应时间应少于100ms
        expect(duration / concurrentOrders).toBeLessThan(100);
    });
    
    it('应该在大量数据下保持性能', async () => {
        const largeDataSet = Array.from({ length: 1000 }, (_, i) => 
            MockGenerator.createOrderData({ customer_name: `Customer ${i}` })
        );
        
        const startTime = performance.now();
        
        // 模拟数据处理
        const processor = window.OTA.getService('dataProcessor');
        const results = await processor.processBatch(largeDataSet);
        
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        expect(results).toHaveLength(1000);
        expect(duration).toBeLessThan(5000); // 5秒内完成
    });
});
```

## 👥 用户验收测试

### E2E 测试框架

#### Playwright 配置
```javascript
// playwright.config.js
module.exports = {
    testDir: './tests/e2e',
    timeout: 30000,
    use: {
        baseURL: 'http://localhost:3000',
        screenshot: 'only-on-failure',
        video: 'retain-on-failure'
    },
    projects: [
        {
            name: 'chromium',
            use: { ...devices['Desktop Chrome'] }
        },
        {
            name: 'firefox',
            use: { ...devices['Desktop Firefox'] }
        },
        {
            name: 'webkit',
            use: { ...devices['Desktop Safari'] }
        },
        {
            name: 'mobile-chrome',
            use: { ...devices['Pixel 5'] }
        }
    ]
};
```

#### 用户操作测试
```javascript
// tests/e2e/order-creation.spec.js
const { test, expect } = require('@playwright/test');

test.describe('订单创建流程', () => {
    test.beforeEach(async ({ page }) => {
        await page.goto('/');
        await page.waitForLoadState('networkidle');
    });
    
    test('用户应该能够创建基本订单', async ({ page }) => {
        // 填写订单文本
        await page.fill('#order-text-input', '从KLIA到KL City，明天下午2点，2个人');
        
        // 点击AI分析
        await page.click('#analyze-btn');
        
        // 等待分析结果
        await page.waitForSelector('#analysis-result', { state: 'visible' });
        
        // 验证表单填充
        await expect(page.locator('#pickup-location')).toHaveValue(/KLIA/);
        await expect(page.locator('#dropoff-location')).toHaveValue(/KL City/);
        await expect(page.locator('#passenger-number')).toHaveValue('2');
        
        // 填写必填字段
        await page.fill('#customer-name', 'John Doe');
        await page.fill('#customer-contact', '+60123456789');
        await page.fill('#pickup-date', '2025-01-20');
        await page.fill('#pickup-time', '14:30');
        
        // 提交订单
        await page.click('#submit-btn');
        
        // 验证成功消息
        await expect(page.locator('#success-message')).toBeVisible();
        await expect(page.locator('#success-message')).toContainText('订单创建成功');
    });
    
    test('用户应该能够处理多订单', async ({ page }) => {
        const multiOrderText = `
            订单1: 从KLIA到KL City, 明天上午9点, John Doe +60123456789
            订单2: 从KL City到KLIA, 后天下午3点, Jane Smith +60123456788
        `;
        
        await page.fill('#order-text-input', multiOrderText);
        await page.click('#analyze-btn');
        
        // 等待多订单模式激活
        await page.waitForSelector('#multi-order-container', { state: 'visible' });
        
        // 验证订单卡片
        const orderCards = page.locator('.order-card');
        await expect(orderCards).toHaveCount(2);
        
        // 验证第一个订单
        const firstCard = orderCards.nth(0);
        await expect(firstCard.locator('.customer-name')).toHaveValue('John Doe');
        
        // 验证第二个订单
        const secondCard = orderCards.nth(1);
        await expect(secondCard.locator('.customer-name')).toHaveValue('Jane Smith');
        
        // 批量提交
        await page.click('#submit-all-btn');
        
        // 验证成功消息
        await expect(page.locator('#batch-success-message')).toBeVisible();
        await expect(page.locator('#batch-success-message')).toContainText('2个订单全部提交成功');
    });
    
    test('用户应该能够切换语言', async ({ page }) => {
        // 点击语言切换按钮
        await page.click('#language-toggle');
        
        // 选择英文
        await page.click('[data-lang="en"]');
        
        // 验证界面语言更改
        await expect(page.locator('#submit-btn')).toContainText('Submit');
        await expect(page.locator('#analyze-btn')).toContainText('Analyze');
        
        // 选择中文
        await page.click('#language-toggle');
        await page.click('[data-lang="zh"]');
        
        // 验证界面语言更改
        await expect(page.locator('#submit-btn')).toContainText('提交');
        await expect(page.locator('#analyze-btn')).toContainText('分析');
    });
});
```

### 视觉回归测试

#### 屏幕截图对比
```javascript
// tests/e2e/visual-regression.spec.js
test.describe('视觉回归测试', () => {
    test('主页面视觉检查', async ({ page }) => {
        await page.goto('/');
        await page.waitForLoadState('networkidle');
        
        // 等待关键元素加载
        await page.waitForSelector('#order-form');
        
        // 屏幕截图对比
        await expect(page).toHaveScreenshot('homepage.png');
    });
    
    test('多订单界面视觉检查', async ({ page }) => {
        await page.goto('/');
        
        // 触发多订单模式
        await page.fill('#order-text-input', '订单1: 从KLIA到KL\n订单2: 从KL到KLIA');
        await page.click('#analyze-btn');
        await page.waitForSelector('#multi-order-container');
        
        // 多订单界面截图
        await expect(page.locator('#multi-order-container')).toHaveScreenshot('multi-order.png');
    });
    
    test('移动端响应式检查', async ({ page }) => {
        // 设置移动端视口
        await page.setViewportSize({ width: 375, height: 667 });
        await page.goto('/');
        
        await expect(page).toHaveScreenshot('mobile-homepage.png');
    });
});
```

## 📊 测试覆盖率和报告

### 覆盖率目标
- **整体覆盖率**: > 80%
- **分支覆盖率**: > 80%
- **函数覆盖率**: > 80%
- **行覆盖率**: > 80%

### 测试报告生成

#### 自定义报告生成器
```javascript
// tests/utils/test-reporter.js
class TestReporter {
    constructor() {
        this.results = {
            suites: [],
            totalTests: 0,
            passedTests: 0,
            failedTests: 0,
            duration: 0
        };
    }
    
    onSuiteStart(suite) {
        this.currentSuite = {
            name: suite.title,
            tests: [],
            startTime: Date.now()
        };
    }
    
    onSuiteEnd() {
        this.currentSuite.duration = Date.now() - this.currentSuite.startTime;
        this.results.suites.push(this.currentSuite);
    }
    
    onTestEnd(test) {
        this.results.totalTests++;
        
        if (test.state === 'passed') {
            this.results.passedTests++;
        } else {
            this.results.failedTests++;
        }
        
        this.currentSuite.tests.push({
            title: test.title,
            state: test.state,
            duration: test.duration,
            error: test.err
        });
    }
    
    generateReport() {
        const report = {
            summary: {
                total: this.results.totalTests,
                passed: this.results.passedTests,
                failed: this.results.failedTests,
                passRate: (this.results.passedTests / this.results.totalTests * 100).toFixed(2)
            },
            details: this.results.suites
        };
        
        return report;
    }
    
    exportToHTML() {
        const report = this.generateReport();
        
        const html = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>OTA系统测试报告</title>
                <style>
                    body { font-family: Arial, sans-serif; }
                    .summary { background: #f5f5f5; padding: 20px; margin-bottom: 20px; }
                    .passed { color: green; }
                    .failed { color: red; }
                    .suite { margin-bottom: 20px; border: 1px solid #ddd; padding: 10px; }
                </style>
            </head>
            <body>
                <h1>OTA系统测试报告</h1>
                <div class="summary">
                    <h2>测试概要</h2>
                    <p>总数: ${report.summary.total}</p>
                    <p class="passed">通过: ${report.summary.passed}</p>
                    <p class="failed">失败: ${report.summary.failed}</p>
                    <p>通过率: ${report.summary.passRate}%</p>
                </div>
                ${this._generateSuiteHTML(report.details)}
            </body>
            </html>
        `;
        
        return html;
    }
    
    _generateSuiteHTML(suites) {
        return suites.map(suite => `
            <div class="suite">
                <h3>${suite.name}</h3>
                <ul>
                    ${suite.tests.map(test => `
                        <li class="${test.state}">
                            ${test.title} (${test.duration}ms)
                            ${test.error ? `<pre>${test.error.message}</pre>` : ''}
                        </li>
                    `).join('')}
                </ul>
            </div>
        `).join('');
    }
}
```

## 🔄 CI/CD 集成

### GitHub Actions 配置
```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run unit tests
        run: npm run test:unit
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
  
  integration-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Start test server
        run: npm run start:test &
      
      - name: Wait for server
        run: npx wait-on http://localhost:3000
      
      - name: Run integration tests
        run: npm run test:integration
  
  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Install Playwright
        run: npx playwright install
      
      - name: Run E2E tests
        run: npm run test:e2e
      
      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
```

### 测试脚本
```json
{
  "scripts": {
    "test": "npm run test:unit && npm run test:integration && npm run test:e2e",
    "test:unit": "jest --config=jest.config.js",
    "test:integration": "jest --config=jest.integration.config.js",
    "test:e2e": "playwright test",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:performance": "lighthouse-ci autorun",
    "start:test": "node test-server.js"
  }
}
```

## 📚 最佳实践

### 测试编写原则
1. **AAA 模式**: Arrange, Act, Assert
2. **单一职责**: 每个测试只验证一个功能点
3. **独立性**: 测试之间不应相互依赖
4. **可重复**: 测试结果应该可重复
5. **有意义**: 测试名称应该清晰表达意图

### 测试数据管理
1. **使用工厂函数**: 创建测试数据
2. **数据隔离**: 测试数据与生产数据分离
3. **随机数据**: 使用随机数据增加测试鲁棒性
4. **边界值测试**: 测试极限情况

### 性能测试最佳实践
1. **基线建立**: 建立性能基线指标
2. **监控回归**: 持续监控性能变化
3. **真实环境**: 在真实环境中测试
4. **多指标衡量**: 综合考虑多个性能指标

## 📚 相关资源

### 文档链接
- [API参考文档](API-Reference.md)
- [开发者指南](Development-Guide.md)
- [架构设计指南](Architecture-Guide.md)
- [性能优化指南](Performance-Guide.md)

### 外部资源
- [Jest 官方文档](https://jestjs.io/docs/getting-started)
- [Playwright 指南](https://playwright.dev/)
- [Lighthouse CI](https://github.com/GoogleChrome/lighthouse-ci)
- [Web.dev 测试指南](https://web.dev/testing/)

---
*测试指南版本: v1.0 | 最后更新: 2025-07-27*