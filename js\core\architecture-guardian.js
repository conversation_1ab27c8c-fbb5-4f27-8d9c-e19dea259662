/**
 * @OTA_CORE 架构违规警告系统
 * 🏷️ 标签: @OTA_ARCHITECTURE_GUARDIAN
 * 📝 功能: 实时监控架构违规，防止代码质量退化
 * ⚠️ 警告: 已注册，请勿重复开发
 * 
 * 防护机制:
 * - 实时监控全局变量污染
 * - 检测未注册的OTA函数
 * - 监控文件大小和复杂度
 * - 警告违反架构原则的代码
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * @OTA_CORE 架构守护者
     * 实时监控和警告架构违规行为
     */
    const ArchitectureGuardian = {
        // 监控配置 - 增强版
        config: {
            // 全局变量监控
            globalVarLimit: 50,           // 全局变量数量限制
            otaVarLimit: 30,              // OTA命名空间变量限制

            // 函数监控
            maxFunctionLength: 100,       // 单个函数最大行数
            maxFileSize: 1000,            // 单个文件最大行数（估算）

            // 性能监控 - 更新阈值
            maxMemoryUsage: 100 * 1024 * 1024, // 100MB内存限制（与性能监控器一致）
            maxResponseTime: 3000,         // 3秒响应时间限制
            maxDOMQueries: 200,           // DOM查询次数限制（与性能监控器一致）

            // 检查间隔 - 优化频率
            monitoringInterval: 30000,     // 30秒检查一次（提高频率）
            warningThreshold: 3,           // 3次违规后发出警告
            criticalThreshold: 5,          // 5次违规后标记为严重

            // 实时监控配置
            realTimeMonitoring: {
                enabled: true,
                checkInterval: 5000,       // 5秒实时检查
                maxViolationsPerMinute: 10, // 每分钟最大违规次数
                autoCorrection: false      // 自动修正（暂时禁用）
            },

            // 智能检测配置
            intelligentDetection: {
                enabled: true,
                learningMode: true,        // 学习模式
                adaptiveThresholds: true,  // 自适应阈值
                patternRecognition: true   // 模式识别
            }
        },

        // 合并的重复检测配置（来自duplicate-detector.js和duplicate-checker.js）
        duplicateDetectionConfig: {
            // 检测规则配置
            detectionRules: {
                // 全局函数模式
                globalFunctions: {
                    patterns: [/^get[A-Z]\w+/, /^create[A-Z]\w+/, /^init[A-Z]\w+/],
                    whitelist: ['getComputedStyle', 'getElementById', 'getElementsByClassName']
                },

                // OTA相关函数
                otaFunctions: {
                    patterns: [/^getOTA/, /^OTA/, /ota/i],
                    critical: true
                },

                // 服务定位器函数
                serviceLocator: {
                    patterns: [/getLogger|getAppState|getGeminiService|getAPIService|getUtils/],
                    maxAllowed: 2 // 允许最多2个定义（权威定义+服务定位器）
                }
            },

            // 合法全局函数白名单（来自duplicate-checker.js）
            whitelist: new Set([
                // 核心OTA函数
                'getService', 'getLogger', 'getAppState', 'getAPIService', 'getGeminiService',
                'getMultiOrderManager', 'getOrderHistoryManager', 'getImageUploadManager',
                'getCurrencyConverter', 'getI18nManager',

                // 浏览器原生函数
                'alert', 'confirm', 'prompt', 'console', 'setTimeout', 'setInterval',
                'fetch', 'XMLHttpRequest', 'addEventListener', 'removeEventListener',

                // OTA调试命令
                'otaRegistryReport', 'performSystemHealthCheck',

                // 历史兼容函数
                'getOtaConfigForUser'
            ])
        },

        // 函数定义位置记录（来自duplicate-detector.js）
        functionRegistry: new Map(),

        // 检测到的重复项（来自duplicate-checker.js）
        duplicates: new Map(),
        
        // 违规记录
        violations: [],
        warningHistory: [],
        
        // 监控状态
        isMonitoring: false,
        monitoringTimer: null,
        
        // 基线数据（用于比较变化）
        baseline: {
            globalVarCount: 0,
            otaVarCount: 0,
            memoryUsage: 0,
            timestamp: null
        },
        
        /**
         * 初始化架构守护者
         */
        init() {
            this.establishBaseline();
            this.setupGlobalMonitoring();
            this.startMonitoring();
            
            const logger = window.getLogger ? window.getLogger() : console;
            if (logger && logger.log) {
                logger.log('🛡️ 架构守护者已启动', 'info', {
                    config: this.config,
                    baseline: this.baseline
                });
            }
        },
        
        /**
         * 建立基线数据
         */
        establishBaseline() {
            this.baseline = {
                globalVarCount: Object.keys(window).length,
                otaVarCount: window.OTA ? Object.keys(window.OTA).length : 0,
                memoryUsage: this.getMemoryUsage(),
                timestamp: new Date().toISOString()
            };
        },
        
        /**
         * 设置全局监控
         */
        setupGlobalMonitoring() {
            // 监控新的全局变量
            const originalWindow = { ...window };
            
            // 使用Proxy监控window对象变化（如果支持）
            if (typeof Proxy !== 'undefined') {
                this.setupProxyMonitoring();
            }
            
            // 监控未捕获的错误
            window.addEventListener('error', (event) => {
                this.recordViolation('UNCAUGHT_ERROR', {
                    message: event.message,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno
                }, 'critical');
            });
            
            // 监控Promise拒绝
            window.addEventListener('unhandledrejection', (event) => {
                this.recordViolation('UNHANDLED_PROMISE_REJECTION', {
                    reason: event.reason
                }, 'high');
            });
        },
        
        /**
         * 设置Proxy监控（如果浏览器支持）
         */
        setupProxyMonitoring() {
            try {
                // 注意：这是一个实验性功能，可能影响性能
                const guardian = this;
                
                // 监控OTA命名空间的变化
                if (window.OTA && typeof window.OTA === 'object') {
                    const originalOTA = window.OTA;
                    window.OTA = new Proxy(originalOTA, {
                        set(target, property, value) {
                            // 检查是否为新的OTA属性
                            if (!(property in target)) {
                                guardian.checkOTAPropertyAddition(property, value);
                            }
                            
                            return Reflect.set(target, property, value);
                        }
                    });
                }
            } catch (error) {
                // Proxy不支持或设置失败，使用传统监控方式
                console.warn('Proxy监控设置失败，使用传统监控方式', error);
            }
        },
        
        /**
         * 检查OTA属性添加
         */
        checkOTAPropertyAddition(property, value) {
            // 检查是否有适当的标签
            if (typeof value === 'function') {
                const funcStr = value.toString();
                const hasOTATag = /@OTA_\w+/.test(funcStr);
                
                if (!hasOTATag) {
                    this.recordViolation('UNTAGGED_OTA_FUNCTION', {
                        property,
                        functionName: value.name || 'anonymous',
                        recommendation: `为 ${property} 添加适当的@OTA_标签`
                    }, 'medium');
                }
            }
            
            // 检查命名规范
            if (!/^[a-zA-Z][a-zA-Z0-9]*$/.test(property)) {
                this.recordViolation('INVALID_PROPERTY_NAME', {
                    property,
                    recommendation: '属性名应使用驼峰命名法'
                }, 'low');
            }
        },
        
        /**
         * 开始监控
         */
        startMonitoring() {
            if (this.isMonitoring) return;
            
            this.isMonitoring = true;
            this.monitoringTimer = setInterval(() => {
                this.performSystemCheck();
            }, this.config.monitoringInterval);

            // 移除调试日志：架构监控已启动
        },
        
        /**
         * 停止监控
         */
        stopMonitoring() {
            if (this.monitoringTimer) {
                clearInterval(this.monitoringTimer);
                this.monitoringTimer = null;
            }
            this.isMonitoring = false;
            // 移除调试日志：架构监控已停止
        },
        
        /**
         * 执行系统检查
         */
        performSystemCheck() {
            const results = {
                timestamp: new Date().toISOString(),
                checks: {}
            };
            
            // 全局变量检查
            results.checks.globalVars = this.checkGlobalVariables();
            
            // OTA变量检查  
            results.checks.otaVars = this.checkOTAVariables();
            
            // 内存使用检查
            results.checks.memory = this.checkMemoryUsage();
            
            // 性能检查
            results.checks.performance = this.checkPerformance();
            
            // 注册检查
            results.checks.registry = this.checkRegistryCompliance();
            
            // 生成警告
            this.processCheckResults(results);
            
            return results;
        },
        
        /**
         * 检查全局变量
         */
        checkGlobalVariables() {
            const currentCount = Object.keys(window).length;
            const increase = currentCount - this.baseline.globalVarCount;
            
            const result = {
                currentCount,
                baselineCount: this.baseline.globalVarCount,
                increase,
                status: increase > 10 ? 'warning' : increase > 20 ? 'critical' : 'ok'
            };
            
            if (increase > 10) {
                this.recordViolation('GLOBAL_VARIABLE_INCREASE', {
                    increase,
                    currentCount,
                    recommendation: '考虑将新的全局变量注册到OTA命名空间'
                }, increase > 20 ? 'critical' : 'medium');
            }
            
            return result;
        },
        
        /**
         * 检查OTA变量
         */
        checkOTAVariables() {
            if (!window.OTA) {
                return { status: 'error', message: 'OTA命名空间不存在' };
            }
            
            const currentCount = Object.keys(window.OTA).length;
            const increase = currentCount - this.baseline.otaVarCount;
            
            const result = {
                currentCount,
                baselineCount: this.baseline.otaVarCount,
                increase,
                status: currentCount > this.config.otaVarLimit ? 'warning' : 'ok'
            };
            
            if (currentCount > this.config.otaVarLimit) {
                this.recordViolation('OTA_NAMESPACE_BLOAT', {
                    currentCount,
                    limit: this.config.otaVarLimit,
                    recommendation: '考虑重构以减少OTA命名空间的变量数量'
                }, 'medium');
            }
            
            return result;
        },
        
        /**
         * 检查内存使用
         */
        checkMemoryUsage() {
            const currentUsage = this.getMemoryUsage();
            const result = {
                currentUsage,
                limit: this.config.maxMemoryUsage,
                status: currentUsage > this.config.maxMemoryUsage ? 'critical' : 'ok'
            };
            
            if (currentUsage > this.config.maxMemoryUsage) {
                this.recordViolation('MEMORY_USAGE_HIGH', {
                    currentUsage,
                    limit: this.config.maxMemoryUsage,
                    recommendation: '检查是否有内存泄漏或优化内存使用'
                }, 'critical');
            }
            
            return result;
        },
        
        /**
         * 检查性能
         */
        checkPerformance() {
            // 简单的性能检查
            const start = performance.now();
            
            // 模拟一些操作
            for (let i = 0; i < 1000; i++) {
                Math.random();
            }
            
            const responseTime = performance.now() - start;
            const result = {
                responseTime,
                limit: this.config.maxResponseTime,
                status: responseTime > this.config.maxResponseTime ? 'warning' : 'ok'
            };
            
            return result;
        },
        
        /**
         * 检查注册合规性
         */
        checkRegistryCompliance() {
            if (!window.OTA.Registry) {
                return { status: 'error', message: 'OTA.Registry不存在' };
            }
            
            const registryInfo = window.OTA.Registry.getRegistryInfo();
            const result = {
                registered: registryInfo.totalRegistered,
                duplicates: registryInfo.totalDuplicates,
                status: registryInfo.totalDuplicates > 0 ? 'warning' : 'ok'
            };
            
            if (registryInfo.totalDuplicates > 0) {
                this.recordViolation('REGISTRY_DUPLICATES', {
                    duplicateCount: registryInfo.totalDuplicates,
                    recommendation: '运行 detectDuplicates() 并清理重复定义'
                }, 'high');
            }
            
            return result;
        },
        
        /**
         * 记录违规
         */
        recordViolation(type, details, severity = 'medium') {
            const violation = {
                type,
                details,
                severity,
                timestamp: new Date().toISOString(),
                id: `${type}_${Date.now()}`
            };
            
            this.violations.push(violation);
            
            // 限制违规记录数量
            if (this.violations.length > 100) {
                this.violations = this.violations.slice(-50);
            }
            
            // 根据严重性发出警告
            if (severity === 'critical') {
                console.error('🚨 严重架构违规:', violation);
            } else if (severity === 'high') {
                console.warn('⚠️ 高级架构违规:', violation);
            }
            
            return violation;
        },
        
        /**
         * 处理检查结果
         */
        processCheckResults(results) {
            const warningTypes = [];
            
            Object.entries(results.checks).forEach(([checkType, result]) => {
                if (result.status === 'warning' || result.status === 'critical') {
                    warningTypes.push(checkType);
                }
            });
            
            if (warningTypes.length > 0) {
                const warning = {
                    timestamp: results.timestamp,
                    types: warningTypes,
                    severity: warningTypes.some(t => results.checks[t].status === 'critical') ? 'critical' : 'warning'
                };
                
                this.warningHistory.push(warning);
                
                // 生成警告报告
                if (this.warningHistory.length % this.config.warningThreshold === 0) {
                    this.generateWarningReport();
                }
            }
        },
        
        /**
         * 生成警告报告
         */
        generateWarningReport() {
            const recentWarnings = this.warningHistory.slice(-10);
            const report = {
                timestamp: new Date().toISOString(),
                summary: {
                    totalWarnings: this.warningHistory.length,
                    recentWarnings: recentWarnings.length,
                    criticalCount: recentWarnings.filter(w => w.severity === 'critical').length
                },
                recommendations: this.generateRecommendations(),
                recentViolations: this.violations.slice(-20)
            };
            
            console.group('🛡️ 架构守护者报告');
            console.table(report.summary);
            console.log('💡 建议:', report.recommendations);
            console.groupEnd();
            
            return report;
        },
        
        /**
         * 合并的重复检测方法（来自duplicate-detector.js和duplicate-checker.js）
         */

        /**
         * 扫描全局变量重复（来自duplicate-checker.js）
         */
        checkForDuplicates() {
            const warnings = [];
            const globalFunctions = [];

            // 扫描所有以get开头的全局函数
            Object.keys(window).forEach(key => {
                if (key.startsWith('get') && typeof window[key] === 'function') {
                    globalFunctions.push(key);

                    // 检查是否在白名单中
                    if (!this.duplicateDetectionConfig.whitelist.has(key)) {
                        warnings.push({
                            type: 'UNREGISTERED_GLOBAL_FUNCTION',
                            name: key,
                            message: `⚠️ 未注册的全局函数: ${key}`,
                            severity: 'warning',
                            suggestion: `请将 ${key} 注册到OTA.Registry或添加到白名单`
                        });
                    }
                }
            });

            // 检查重复定义问题
            this.checkDuplicateDefinitions(globalFunctions, warnings);

            // 检查OTA函数标签
            this.checkOTAFunctionTags(globalFunctions, warnings);

            return {
                globalFunctions,
                warnings,
                duplicateCount: this.duplicates.size
            };
        },

        /**
         * 检查重复定义问题（来自duplicate-checker.js）
         */
        checkDuplicateDefinitions(globalFunctions, warnings) {
            // 检查已知的重复问题模式
            const knownDuplicates = ['getLogger', 'getAppState', 'getGeminiService'];

            knownDuplicates.forEach(funcName => {
                if (globalFunctions.includes(funcName)) {
                    // 检查是否有多个定义源
                    const registry = window.OTA && window.OTA.Registry;
                    if (registry && registry.duplicateDetections && registry.duplicateDetections.has(funcName)) {
                        const count = registry.duplicateDetections.get(funcName);
                        if (count > 0) {
                            warnings.push({
                                type: 'DUPLICATE_FUNCTION_DEFINITION',
                                name: funcName,
                                message: `🚨 检测到重复定义: ${funcName} (重复次数: ${count})`,
                                severity: 'error',
                                suggestion: `请移除 ${funcName} 的重复定义，只保留核心文件中的版本`
                            });
                        }
                    }
                }
            });
        },

        /**
         * 检查OTA函数标签（来自duplicate-checker.js）
         */
        checkOTAFunctionTags(globalFunctions, warnings) {
            globalFunctions.forEach(funcName => {
                if (funcName.toLowerCase().includes('ota') || funcName.startsWith('getOTA')) {
                    // 这里可以添加更复杂的标签检查逻辑
                    // 目前只是记录OTA相关函数
                    warnings.push({
                        type: 'OTA_FUNCTION_DETECTED',
                        name: funcName,
                        message: `📋 检测到OTA函数: ${funcName}`,
                        severity: 'info',
                        suggestion: `确保 ${funcName} 有适当的@OTA_标签`
                    });
                }
            });
        },

        /**
         * 高级重复检测（来自duplicate-detector.js）
         */
        performAdvancedDuplicateDetection() {
            const results = {
                timestamp: new Date().toISOString(),
                detectedDuplicates: [],
                violations: [],
                recommendations: []
            };

            // 检测函数定义模式
            const rules = this.duplicateDetectionConfig.detectionRules;

            Object.keys(window).forEach(key => {
                if (typeof window[key] === 'function') {
                    // 检查全局函数规则
                    if (rules.globalFunctions.patterns.some(pattern => pattern.test(key))) {
                        if (!rules.globalFunctions.whitelist.includes(key)) {
                            results.violations.push({
                                type: 'GLOBAL_FUNCTION_VIOLATION',
                                function: key,
                                message: `全局函数 ${key} 应该注册到OTA.Registry`,
                                severity: 'warning',
                                rule: 'globalFunctions'
                            });
                        }
                    }

                    // 检查OTA函数规则
                    if (rules.otaFunctions.patterns.some(pattern => pattern.test(key))) {
                        results.violations.push({
                            type: 'OTA_FUNCTION_DETECTED',
                            function: key,
                            message: `OTA相关函数 ${key} 需要添加@OTA_标签`,
                            severity: rules.otaFunctions.critical ? 'error' : 'warning',
                            rule: 'otaFunctions'
                        });
                    }

                    // 检查服务定位器函数
                    if (rules.serviceLocator.patterns.some(pattern => pattern.test(key))) {
                        const locations = this.functionRegistry.get(key) || [];
                        if (locations.length > rules.serviceLocator.maxAllowed) {
                            results.violations.push({
                                type: 'SERVICE_LOCATOR_VIOLATION',
                                function: key,
                                message: `服务定位器函数 ${key} 定义过多 (${locations.length}/${rules.serviceLocator.maxAllowed})`,
                                severity: 'error',
                                rule: 'serviceLocator'
                            });
                        }
                    }
                }
            });

            return results;
        },

        /**
         * 生成建议
         */
        generateRecommendations() {
            const recommendations = [];

            const violationTypes = [...new Set(this.violations.map(v => v.type))];

            if (violationTypes.includes('GLOBAL_VARIABLE_INCREASE')) {
                recommendations.push('考虑使用OTA命名空间而不是全局变量');
            }

            if (violationTypes.includes('UNTAGGED_OTA_FUNCTION')) {
                recommendations.push('为所有OTA函数添加适当的@OTA_标签');
            }

            if (violationTypes.includes('REGISTRY_DUPLICATES')) {
                recommendations.push('运行重复检测工具并清理重复定义');
            }

            if (violationTypes.includes('MEMORY_USAGE_HIGH')) {
                recommendations.push('检查内存泄漏并优化内存使用');
            }

            // 添加重复检测相关建议
            if (violationTypes.includes('DUPLICATE_FUNCTION_DEFINITION')) {
                recommendations.push('清理重复的函数定义，统一使用服务定位器模式');
            }

            if (violationTypes.includes('UNREGISTERED_GLOBAL_FUNCTION')) {
                recommendations.push('将未注册的全局函数迁移到OTA.Registry');
            }

            return recommendations;
        },
        
        /**
         * 获取内存使用情况
         */
        getMemoryUsage() {
            if (performance.memory) {
                return performance.memory.usedJSHeapSize;
            }
            return 0; // 无法获取内存信息时返回0
        },
        
        /**
         * 生成完整报告
         */
        generateFullReport() {
            const systemCheck = this.performSystemCheck();
            const report = {
                timestamp: new Date().toISOString(),
                config: this.config,
                baseline: this.baseline,
                currentStatus: systemCheck,
                violationsSummary: {
                    total: this.violations.length,
                    bySeverity: this.groupViolationsBySeverity(),
                    byType: this.groupViolationsByType()
                },
                recommendations: this.generateRecommendations(),
                healthScore: this.calculateHealthScore()
            };
            
            return report;
        },
        
        /**
         * 按严重性分组违规
         */
        groupViolationsBySeverity() {
            return this.violations.reduce((acc, violation) => {
                acc[violation.severity] = (acc[violation.severity] || 0) + 1;
                return acc;
            }, {});
        },
        
        /**
         * 按类型分组违规
         */
        groupViolationsByType() {
            return this.violations.reduce((acc, violation) => {
                acc[violation.type] = (acc[violation.type] || 0) + 1;
                return acc;
            }, {});
        },
        
        /**
         * 计算健康评分
         */
        calculateHealthScore() {
            const severityWeights = { low: 1, medium: 3, high: 5, critical: 10 };
            const penalty = this.violations.reduce((total, violation) => {
                return total + (severityWeights[violation.severity] || 3);
            }, 0);
            
            const baseScore = 100;
            const score = Math.max(0, baseScore - penalty);
            const grade = score >= 90 ? 'A' : score >= 80 ? 'B' : score >= 70 ? 'C' : score >= 60 ? 'D' : 'F';
            
            return {
                score,
                grade,
                status: score >= 80 ? 'healthy' : score >= 60 ? 'warning' : 'critical',
                totalPenalty: penalty
            };
        }
    };

    // 注册到OTA命名空间
    window.OTA.ArchitectureGuardian = ArchitectureGuardian;
    
    // 全局访问（向后兼容）
    window.ArchitectureGuardian = ArchitectureGuardian;
    
    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerUtil('ArchitectureGuardian', ArchitectureGuardian, '@OTA_ARCHITECTURE_GUARDIAN');
    }
    
    // 导出全局调试命令（合并了所有架构保护功能）
    window.architectureReport = () => {
        const report = ArchitectureGuardian.generateFullReport();
        console.group('🛡️ 架构守护者完整报告');
        console.log('📊 健康评分:', report.healthScore);
        console.table(report.violationsSummary);
        console.log('💡 建议:', report.recommendations);
        console.groupEnd();
        return report;
    };

    window.startArchitectureGuardian = () => {
        ArchitectureGuardian.init();
        return '🛡️ 架构守护者已启动';
    };

    // 合并的重复检测命令（来自duplicate-detector.js和duplicate-checker.js）
    window.checkDuplicates = () => {
        const result = ArchitectureGuardian.checkForDuplicates();
        console.group('🔍 重复检测报告');
        console.log('📋 全局函数数量:', result.globalFunctions.length);
        console.log('⚠️ 警告数量:', result.warnings.length);
        console.log('🚨 重复项数量:', result.duplicateCount);
        if (result.warnings.length > 0) {
            console.table(result.warnings);
        }
        console.groupEnd();
        return result;
    };

    window.advancedDuplicateDetection = () => {
        const result = ArchitectureGuardian.performAdvancedDuplicateDetection();
        console.group('🔬 高级重复检测报告');
        console.log('📊 检测时间:', result.timestamp);
        console.log('🚨 违规数量:', result.violations.length);
        if (result.violations.length > 0) {
            console.table(result.violations);
        }
        console.groupEnd();
        return result;
    };

    // 统一的架构健康检查命令
    window.fullArchitectureCheck = () => {
        console.group('🏗️ 完整架构检查');

        // 基础架构报告
        const basicReport = window.architectureReport();

        // 重复检测
        const duplicateReport = window.checkDuplicates();

        // 高级检测
        const advancedReport = window.advancedDuplicateDetection();

        // 综合评分
        const totalViolations = basicReport.violationsSummary.length +
                               duplicateReport.warnings.length +
                               advancedReport.violations.length;

        const healthScore = Math.max(0, 100 - totalViolations * 5);

        console.log('🎯 综合健康评分:', `${healthScore}/100`);
        console.log('📈 改进建议:', [
            ...basicReport.recommendations,
            '定期运行架构检查',
            '保持代码质量标准',
            '及时清理重复代码'
        ]);

        console.groupEnd();

        return {
            healthScore,
            basicReport,
            duplicateReport,
            advancedReport,
            totalViolations
        };
    };
    
    window.stopArchitectureGuardian = () => {
        ArchitectureGuardian.stopMonitoring();
        return '⏹️ 架构守护者已停止';
    };
    
    // 阶段3优化后的重复开发预警机制 (2025-01-28)
    const Stage3PreventionSystem = {
        // 基于阶段3清理成果的预警规则
        preventionRules: {
            // 工具函数重复预防
            utilityFunctions: {
                patterns: [
                    /formatPrice|format.*price/i,
                    /formatPhone|format.*phone/i,
                    /formatDate|format.*date/i,
                    /isValid|validate/i
                ],
                maxAllowed: 1, // 每种类型只允许1个统一实现
                redirectTo: 'window.OTA.utils',
                message: '检测到工具函数重复！请使用 window.OTA.utils 中的统一函数'
            },

            // 命名空间污染预防
            namespaceViolations: {
                patterns: [
                    /^window\.[a-z]/,  // 直接在window上定义小写变量
                    /^utils$/,         // 直接定义utils变量
                    /^logger$/,        // 直接定义logger变量
                    /^apiService$/     // 直接定义apiService变量
                ],
                redirectTo: 'window.OTA',
                message: '检测到命名空间污染！请使用 window.OTA 统一命名空间'
            },

            // 日志过度输出预防
            excessiveLogging: {
                patterns: [/console\.log|console\.debug/],
                maxPerFile: 5,
                message: '检测到过多console.log！请使用条件日志或移除调试日志'
            },

            // 文件大小控制
            fileSizeControl: {
                maxLines: 800,
                message: '文件过大！请拆分为多个模块，遵循单一职责原则'
            }
        },

        // 实时监控新增代码
        monitorNewCode() {
            // 监控新的全局变量定义
            const originalDefineProperty = Object.defineProperty;
            Object.defineProperty = function(obj, prop, descriptor) {
                if (obj === window && Stage3PreventionSystem.checkViolation('namespace', prop)) {
                    Stage3PreventionSystem.reportViolation('namespace', prop);
                }
                return originalDefineProperty.call(this, obj, prop, descriptor);
            };

            // 监控新的函数定义
            const originalFunction = window.Function;
            window.Function = function(...args) {
                const funcStr = args[args.length - 1];
                Stage3PreventionSystem.checkFunctionContent(funcStr);
                return originalFunction.apply(this, args);
            };
        },

        // 检查违规行为
        checkViolation(type, content) {
            const rules = this.preventionRules;

            switch (type) {
                case 'namespace':
                    return rules.namespaceViolations.patterns.some(pattern =>
                        pattern.test(content)
                    );

                case 'utility':
                    return rules.utilityFunctions.patterns.some(pattern =>
                        pattern.test(content)
                    );

                case 'logging':
                    const logMatches = content.match(rules.excessiveLogging.patterns[0]);
                    return logMatches && logMatches.length > rules.excessiveLogging.maxPerFile;

                default:
                    return false;
            }
        },

        // 检查函数内容
        checkFunctionContent(funcStr) {
            // 检查工具函数重复
            if (this.checkViolation('utility', funcStr)) {
                this.reportViolation('utility', funcStr.substring(0, 100));
            }

            // 检查日志过度输出
            if (this.checkViolation('logging', funcStr)) {
                this.reportViolation('logging', '函数中包含过多console.log');
            }
        },

        // 报告违规行为
        reportViolation(type, content) {
            const rules = this.preventionRules;
            let message = '';
            let redirectTo = '';

            switch (type) {
                case 'namespace':
                    message = rules.namespaceViolations.message;
                    redirectTo = rules.namespaceViolations.redirectTo;
                    break;
                case 'utility':
                    message = rules.utilityFunctions.message;
                    redirectTo = rules.utilityFunctions.redirectTo;
                    break;
                case 'logging':
                    message = rules.excessiveLogging.message;
                    break;
            }

            // 发出警告
            console.warn(`🚨 阶段3预警系统: ${message}`);
            if (redirectTo) {
                console.warn(`💡 建议使用: ${redirectTo}`);
            }
            console.warn(`📍 违规内容: ${content}`);

            // 记录违规
            ArchitectureGuardian.violations.push({
                type: `stage3_${type}`,
                content: content,
                message: message,
                timestamp: new Date().toISOString(),
                stack: new Error().stack
            });

            // 触发违规事件
            window.dispatchEvent(new CustomEvent('architectureViolation', {
                detail: { type, content, message, redirectTo }
            }));
        },

        // 生成预防报告
        generatePreventionReport() {
            const violations = ArchitectureGuardian.violations.filter(v =>
                v.type.startsWith('stage3_')
            );

            return {
                totalViolations: violations.length,
                violationsByType: violations.reduce((acc, v) => {
                    acc[v.type] = (acc[v.type] || 0) + 1;
                    return acc;
                }, {}),
                recentViolations: violations.filter(v =>
                    Date.now() - new Date(v.timestamp).getTime() < 86400000 // 24小时内
                ),
                recommendations: this.generateRecommendations(violations)
            };
        },

        // 生成改进建议
        generateRecommendations(violations) {
            const recommendations = [];
            const violationTypes = [...new Set(violations.map(v => v.type))];

            if (violationTypes.includes('stage3_utility')) {
                recommendations.push('使用 window.OTA.utils 中的统一工具函数');
                recommendations.push('删除重复的工具函数实现');
            }

            if (violationTypes.includes('stage3_namespace')) {
                recommendations.push('将全局变量迁移到 window.OTA 命名空间');
                recommendations.push('避免直接在 window 对象上定义变量');
            }

            if (violationTypes.includes('stage3_logging')) {
                recommendations.push('使用条件日志输出，避免生产环境日志污染');
                recommendations.push('移除临时调试用的 console.log');
            }

            return recommendations;
        },

        // 初始化预防系统
        init() {
            this.monitorNewCode();

            // 定期检查
            setInterval(() => {
                this.performPreventionCheck();
            }, 300000); // 每5分钟检查一次

            console.log('🛡️ 阶段3重复开发预警系统已启动');
        },

        // 执行预防检查
        performPreventionCheck() {
            const report = this.generatePreventionReport();

            if (report.totalViolations > 0) {
                console.warn('🚨 检测到重复开发违规:', report);

                // 如果违规严重，发出严重警告
                if (report.totalViolations > 5) {
                    console.error('❌ 严重违规！系统正在退化到阶段3优化前的状态');
                    console.error('📋 立即执行建议:', report.recommendations);
                }
            }
        }
    };

    // 扩展架构守护者功能
    ArchitectureGuardian.stage3Prevention = Stage3PreventionSystem;

    // 添加新的全局命令
    window.stage3PreventionReport = () => {
        console.group('🛡️ 阶段3重复开发预防报告');
        const report = Stage3PreventionSystem.generatePreventionReport();
        console.log('📊 违规统计:', report);
        console.log('💡 改进建议:', report.recommendations);
        console.groupEnd();
        return report;
    };

    /**
     * 实时架构监控系统
     * 提供实时的架构违规检测和智能分析
     */
    const RealTimeArchitectureMonitor = {
        // 监控状态
        isActive: false,
        monitoringIntervals: [],
        violationHistory: [],
        performanceBaseline: null,

        // 智能检测器
        intelligentDetectors: {
            // 内存泄漏检测器
            memoryLeakDetector: {
                enabled: true,
                samples: [],
                maxSamples: 20,
                leakThreshold: 10 * 1024 * 1024 // 10MB增长
            },

            // DOM查询过度检测器
            domQueryDetector: {
                enabled: true,
                queryCount: 0,
                lastReset: Date.now(),
                resetInterval: 60000 // 1分钟重置
            },

            // 循环依赖检测器
            circularDependencyDetector: {
                enabled: true,
                dependencyGraph: new Map(),
                lastCheck: Date.now()
            },

            // 代码复杂度检测器
            complexityDetector: {
                enabled: true,
                complexityThreshold: 10,
                functionSizeThreshold: 50
            }
        },

        /**
         * 启动实时监控
         */
        startRealTimeMonitoring() {
            if (this.isActive) {
                console.warn('实时架构监控已在运行');
                return;
            }

            this.isActive = true;
            console.log('🔍 启动实时架构监控系统...');

            // 建立性能基线
            this.establishPerformanceBaseline();

            // 启动各种监控器
            this.startMemoryMonitoring();
            this.startDOMQueryMonitoring();
            this.startDependencyMonitoring();
            this.startComplexityMonitoring();

            // 启动主监控循环
            const mainInterval = setInterval(() => {
                this.performRealTimeCheck();
            }, ArchitectureGuardian.config.realTimeMonitoring.checkInterval);

            this.monitoringIntervals.push(mainInterval);

            console.log('✅ 实时架构监控系统已启动');
        },

        /**
         * 停止实时监控
         */
        stopRealTimeMonitoring() {
            this.isActive = false;

            // 清除所有监控间隔
            this.monitoringIntervals.forEach(interval => clearInterval(interval));
            this.monitoringIntervals = [];

            console.log('🛑 实时架构监控系统已停止');
        },

        /**
         * 建立性能基线
         */
        establishPerformanceBaseline() {
            const baseline = {
                memory: performance.memory ? performance.memory.usedJSHeapSize : 0,
                domElements: document.querySelectorAll('*').length,
                globalVariables: Object.keys(window).length,
                otaVariables: Object.keys(window.OTA || {}).length,
                timestamp: Date.now()
            };

            this.performanceBaseline = baseline;
            console.log('📊 性能基线已建立:', baseline);
        },

        /**
         * 启动内存监控
         */
        startMemoryMonitoring() {
            const detector = this.intelligentDetectors.memoryLeakDetector;

            const memoryInterval = setInterval(() => {
                if (!performance.memory) return;

                const currentMemory = performance.memory.usedJSHeapSize;
                detector.samples.push({
                    memory: currentMemory,
                    timestamp: Date.now()
                });

                // 保持样本数量限制
                if (detector.samples.length > detector.maxSamples) {
                    detector.samples.shift();
                }

                // 检测内存泄漏
                this.detectMemoryLeak();

            }, 10000); // 每10秒检查一次

            this.monitoringIntervals.push(memoryInterval);
        },

        /**
         * 检测内存泄漏
         */
        detectMemoryLeak() {
            const detector = this.intelligentDetectors.memoryLeakDetector;
            const samples = detector.samples;

            if (samples.length < 5) return;

            // 计算内存增长趋势
            const recent = samples.slice(-5);
            let increasingCount = 0;

            for (let i = 1; i < recent.length; i++) {
                if (recent[i].memory > recent[i-1].memory) {
                    increasingCount++;
                }
            }

            // 如果连续4次增长，且增长量超过阈值
            if (increasingCount >= 4) {
                const growth = recent[recent.length - 1].memory - recent[0].memory;
                if (growth > detector.leakThreshold) {
                    this.reportViolation('memory_leak', {
                        growth: `${(growth / 1024 / 1024).toFixed(2)}MB`,
                        trend: '持续增长',
                        severity: 'WARNING'
                    });
                }
            }
        },

        /**
         * 启动DOM查询监控
         */
        startDOMQueryMonitoring() {
            const detector = this.intelligentDetectors.domQueryDetector;

            // 重写querySelector方法进行监控
            const originalQuerySelector = document.querySelector;
            const originalQuerySelectorAll = document.querySelectorAll;

            document.querySelector = function(...args) {
                detector.queryCount++;
                return originalQuerySelector.apply(this, args);
            };

            document.querySelectorAll = function(...args) {
                detector.queryCount++;
                return originalQuerySelectorAll.apply(this, args);
            };

            // 定期检查查询次数
            const queryInterval = setInterval(() => {
                const now = Date.now();
                if (now - detector.lastReset > detector.resetInterval) {
                    if (detector.queryCount > ArchitectureGuardian.config.maxDOMQueries) {
                        this.reportViolation('excessive_dom_queries', {
                            count: detector.queryCount,
                            threshold: ArchitectureGuardian.config.maxDOMQueries,
                            severity: 'WARNING'
                        });
                    }

                    detector.queryCount = 0;
                    detector.lastReset = now;
                }
            }, 30000); // 每30秒检查一次

            this.monitoringIntervals.push(queryInterval);
        },

        /**
         * 启动依赖监控
         */
        startDependencyMonitoring() {
            const detector = this.intelligentDetectors.circularDependencyDetector;

            const dependencyInterval = setInterval(() => {
                this.checkCircularDependencies();
            }, 60000); // 每分钟检查一次

            this.monitoringIntervals.push(dependencyInterval);
        },

        /**
         * 检查循环依赖
         */
        checkCircularDependencies() {
            // 简化的循环依赖检测
            const services = window.OTA || {};
            const dependencies = new Map();

            // 构建依赖图（简化版）
            Object.keys(services).forEach(serviceName => {
                const service = services[serviceName];
                if (service && typeof service === 'object') {
                    dependencies.set(serviceName, this.extractDependencies(service));
                }
            });

            // 检测循环
            const cycles = this.detectCycles(dependencies);
            if (cycles.length > 0) {
                this.reportViolation('circular_dependency', {
                    cycles: cycles,
                    count: cycles.length,
                    severity: 'CRITICAL'
                });
            }
        },

        /**
         * 提取服务依赖（简化版）
         */
        extractDependencies(service) {
            const deps = [];
            const serviceStr = service.toString();

            // 查找OTA服务引用
            const otaMatches = serviceStr.match(/window\.OTA\.(\w+)/g);
            if (otaMatches) {
                otaMatches.forEach(match => {
                    const serviceName = match.replace('window.OTA.', '');
                    if (serviceName !== 'getService') {
                        deps.push(serviceName);
                    }
                });
            }

            return deps;
        },

        /**
         * 检测依赖循环
         */
        detectCycles(dependencies) {
            const cycles = [];
            const visited = new Set();
            const recursionStack = new Set();

            const dfs = (node, path) => {
                if (recursionStack.has(node)) {
                    // 找到循环
                    const cycleStart = path.indexOf(node);
                    cycles.push(path.slice(cycleStart).concat(node));
                    return;
                }

                if (visited.has(node)) return;

                visited.add(node);
                recursionStack.add(node);

                const deps = dependencies.get(node) || [];
                deps.forEach(dep => {
                    if (dependencies.has(dep)) {
                        dfs(dep, path.concat(node));
                    }
                });

                recursionStack.delete(node);
            };

            dependencies.forEach((_, node) => {
                if (!visited.has(node)) {
                    dfs(node, []);
                }
            });

            return cycles;
        },

        /**
         * 启动复杂度监控
         */
        startComplexityMonitoring() {
            // 监控新添加的函数复杂度
            const complexityInterval = setInterval(() => {
                this.checkCodeComplexity();
            }, 120000); // 每2分钟检查一次

            this.monitoringIntervals.push(complexityInterval);
        },

        /**
         * 检查代码复杂度
         */
        checkCodeComplexity() {
            // 检查全局函数的复杂度
            const globalFunctions = Object.keys(window).filter(key =>
                typeof window[key] === 'function' &&
                !key.startsWith('webkit') &&
                !key.startsWith('chrome')
            );

            globalFunctions.forEach(funcName => {
                const func = window[funcName];
                const complexity = this.calculateComplexity(func.toString());

                if (complexity > this.intelligentDetectors.complexityDetector.complexityThreshold) {
                    this.reportViolation('high_complexity', {
                        function: funcName,
                        complexity: complexity,
                        threshold: this.intelligentDetectors.complexityDetector.complexityThreshold,
                        severity: 'WARNING'
                    });
                }
            });
        },

        /**
         * 计算函数复杂度（简化版）
         */
        calculateComplexity(funcStr) {
            let complexity = 1; // 基础复杂度

            // 计算分支语句
            const branches = (funcStr.match(/\b(if|else|switch|case|for|while|do|catch)\b/g) || []).length;
            complexity += branches;

            // 计算逻辑操作符
            const logicalOps = (funcStr.match(/(\|\||&&)/g) || []).length;
            complexity += logicalOps;

            // 计算三元操作符
            const ternaryOps = (funcStr.match(/\?.*:/g) || []).length;
            complexity += ternaryOps;

            return complexity;
        },

        /**
         * 执行实时检查
         */
        performRealTimeCheck() {
            if (!this.isActive) return;

            // 检查性能指标
            this.checkPerformanceMetrics();

            // 检查违规频率
            this.checkViolationFrequency();

            // 自适应调整阈值
            if (ArchitectureGuardian.config.intelligentDetection.adaptiveThresholds) {
                this.adjustThresholds();
            }
        },

        /**
         * 检查性能指标
         */
        checkPerformanceMetrics() {
            if (!this.performanceBaseline) return;

            const current = {
                memory: performance.memory ? performance.memory.usedJSHeapSize : 0,
                domElements: document.querySelectorAll('*').length,
                globalVariables: Object.keys(window).length,
                otaVariables: Object.keys(window.OTA || {}).length
            };

            // 检查内存增长
            const memoryGrowth = current.memory - this.performanceBaseline.memory;
            if (memoryGrowth > ArchitectureGuardian.config.maxMemoryUsage * 0.5) {
                this.reportViolation('memory_growth', {
                    growth: `${(memoryGrowth / 1024 / 1024).toFixed(2)}MB`,
                    current: `${(current.memory / 1024 / 1024).toFixed(2)}MB`,
                    severity: 'WARNING'
                });
            }

            // 检查全局变量增长
            const globalVarGrowth = current.globalVariables - this.performanceBaseline.globalVariables;
            if (globalVarGrowth > 10) {
                this.reportViolation('global_variable_growth', {
                    growth: globalVarGrowth,
                    current: current.globalVariables,
                    severity: 'WARNING'
                });
            }
        },

        /**
         * 检查违规频率
         */
        checkViolationFrequency() {
            const now = Date.now();
            const oneMinuteAgo = now - 60000;

            const recentViolations = this.violationHistory.filter(v =>
                v.timestamp > oneMinuteAgo
            );

            const maxViolations = ArchitectureGuardian.config.realTimeMonitoring.maxViolationsPerMinute;
            if (recentViolations.length > maxViolations) {
                console.error(`🚨 违规频率过高: ${recentViolations.length}次/分钟 (限制: ${maxViolations}次/分钟)`);
                console.error('📋 建议立即检查系统状态');
            }
        },

        /**
         * 自适应调整阈值
         */
        adjustThresholds() {
            // 基于历史违规数据调整阈值
            const recentViolations = this.violationHistory.filter(v =>
                Date.now() - v.timestamp < 24 * 60 * 60 * 1000 // 24小时内
            );

            if (recentViolations.length === 0) {
                // 无违规，可以适当降低阈值提高敏感度
                console.log('📈 系统稳定，提高监控敏感度');
            } else if (recentViolations.length > 50) {
                // 违规过多，适当提高阈值避免噪音
                console.log('📉 违规较多，调整监控阈值');
            }
        },

        /**
         * 报告违规
         */
        reportViolation(type, details) {
            const violation = {
                type: `realtime_${type}`,
                details: details,
                timestamp: Date.now(),
                severity: details.severity || 'INFO'
            };

            this.violationHistory.push(violation);

            // 保持历史记录大小
            if (this.violationHistory.length > 1000) {
                this.violationHistory = this.violationHistory.slice(-500);
            }

            // 输出警告
            const emoji = details.severity === 'CRITICAL' ? '🚨' :
                         details.severity === 'WARNING' ? '⚠️' : 'ℹ️';

            console.warn(`${emoji} 实时架构监控 [${type}]:`, details);

            // 触发事件
            window.dispatchEvent(new CustomEvent('realtimeArchitectureViolation', {
                detail: violation
            }));

            // 记录到主违规列表
            ArchitectureGuardian.violations.push(violation);
        },

        /**
         * 获取监控状态
         */
        getMonitoringStatus() {
            return {
                isActive: this.isActive,
                activeMonitors: this.monitoringIntervals.length,
                violationCount: this.violationHistory.length,
                recentViolations: this.violationHistory.filter(v =>
                    Date.now() - v.timestamp < 60000
                ).length,
                performanceBaseline: this.performanceBaseline,
                detectorStatus: Object.keys(this.intelligentDetectors).reduce((acc, key) => {
                    acc[key] = this.intelligentDetectors[key].enabled;
                    return acc;
                }, {})
            };
        }
    };

    // 扩展架构守护者功能
    ArchitectureGuardian.stage3Prevention = Stage3PreventionSystem;
    ArchitectureGuardian.realTimeMonitor = RealTimeArchitectureMonitor;

    // 添加新的全局命令
    window.stage3PreventionReport = () => {
        console.group('🛡️ 阶段3重复开发预防报告');
        const report = Stage3PreventionSystem.generatePreventionReport();
        console.log('📊 违规统计:', report);
        console.log('💡 改进建议:', report.recommendations);
        console.groupEnd();
        return report;
    };

    // 添加实时监控命令
    window.startRealTimeMonitoring = () => {
        RealTimeArchitectureMonitor.startRealTimeMonitoring();
    };

    window.stopRealTimeMonitoring = () => {
        RealTimeArchitectureMonitor.stopRealTimeMonitoring();
    };

    window.getMonitoringStatus = () => {
        return RealTimeArchitectureMonitor.getMonitoringStatus();
    };

    // 自动初始化
    if (typeof window !== 'undefined') {
        // 延迟初始化，确保其他模块已加载
        setTimeout(() => {
            ArchitectureGuardian.init();
            Stage3PreventionSystem.init();

            // 如果启用了实时监控，自动启动
            if (ArchitectureGuardian.config.realTimeMonitoring.enabled) {
                RealTimeArchitectureMonitor.startRealTimeMonitoring();
            }
        }, 5000);
    }

})();