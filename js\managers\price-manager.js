/**
 * 价格管理器模块
 * 负责价格转换、货币处理和价格显示逻辑
 * 支持多种货币转换和实时价格更新
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.managers = window.OTA.managers || {};

(function() {
    'use strict';

    // 获取依赖模块 - 使用统一的服务定位器

    /**
     * 价格管理器类
     * 负责价格相关的所有操作
     */
    class PriceManager {
        constructor(elements) {
            this.elements = elements;
            this.currencyConverter = null;
            
            // 手动编辑状态跟踪
            this.manualEditMode = false;        // 是否处于手动编辑模式
            this.isAutoFilling = false;         // 是否正在程序自动填充
            this.editTimeout = null;            // 编辑状态超时器
            this.lastManualValue = null;        // 最后一次手动输入的值
        }

        /**
         * 获取国际化管理器
         */
        getI18nManager() {
            return window.OTA?.i18nManager || window.i18nManager;
        }

        /**
         * 进入手动编辑模式
         */
        enterManualEditMode() {
            if (!this.manualEditMode) {
                this.manualEditMode = true;
                this.lastManualValue = this.elements.otaPrice?.value || null;
                
                // 添加视觉提示
                this.addManualEditIndicator();
                
                getLogger().log('进入手动编辑模式', 'info', {
                    currentValue: this.lastManualValue
                });
            }
        }

        /**
         * 退出手动编辑模式
         */
        exitManualEditMode() {
            if (this.manualEditMode) {
                this.manualEditMode = false;
                this.lastManualValue = null;
                
                // 清除编辑状态超时器
                if (this.editTimeout) {
                    clearTimeout(this.editTimeout);
                    this.editTimeout = null;
                }
                
                // 移除视觉提示
                this.removeManualEditIndicator();
                
                getLogger().log('退出手动编辑模式', 'info');
            }
        }

        /**
         * 设置自动填充状态
         * @param {boolean} isAutoFilling - 是否正在自动填充
         */
        setAutoFillingState(isAutoFilling) {
            this.isAutoFilling = isAutoFilling;
            getLogger().log(`自动填充状态: ${isAutoFilling}`, 'debug');
        }

        /**
         * 添加手动编辑模式的视觉提示
         */
        addManualEditIndicator() {
            const priceGroup = document.querySelector('#otaPriceGroup');
            if (priceGroup && !priceGroup.querySelector('.manual-edit-indicator')) {
                const indicator = document.createElement('div');
                indicator.className = 'manual-edit-indicator';
                indicator.innerHTML = '✏️ 手动编辑中';
                indicator.style.cssText = `
                    position: absolute;
                    top: -8px;
                    right: -8px;
                    background: #ffc107;
                    color: #000;
                    font-size: 10px;
                    padding: 2px 6px;
                    border-radius: 8px;
                    z-index: 10;
                    pointer-events: none;
                `;
                priceGroup.style.position = 'relative';
                priceGroup.appendChild(indicator);
            }
        }

        /**
         * 移除手动编辑模式的视觉提示
         */
        removeManualEditIndicator() {
            const indicator = document.querySelector('.manual-edit-indicator');
            if (indicator) {
                indicator.remove();
            }
        }

        /**
         * 手动重置编辑模式并恢复汇率转换
         * 提供给用户主动恢复汇率功能的接口
         */
        resetManualEditModeAndUpdateConversion() {
            if (this.manualEditMode) {
                this.exitManualEditMode();
                this.updatePriceConversion(true);
                getLogger().log('用户手动重置编辑模式并恢复汇率转换', 'info');
            }
        }

        /**
         * 初始化价格管理器
         */
        init() {
            this.currencyConverter = window.CurrencyConverter ? new window.CurrencyConverter() : null;
            this.createPriceConversionDisplay();
            this.setupPriceValidation();
            getLogger().log('价格管理器初始化完成', 'success');
        }

        /**
         * 创建价格转换显示元素
         */
        createPriceConversionDisplay() {
            const priceGroup = document.querySelector('.price-input-group');
            if (!priceGroup || priceGroup.querySelector('.price-conversion-display')) {
                return; // 已存在或找不到容器
            }

            const conversionDisplay = document.createElement('div');
            conversionDisplay.className = 'price-conversion-display';
            conversionDisplay.innerHTML = `
                <div class="conversion-info">
                    <div class="original-price"></div>
                    <div class="conversion-arrow">→</div>
                    <div class="converted-price"></div>
                </div>
                <div class="conversion-rate"></div>
                <div class="conversion-controls">
                    <button type="button" class="reset-exchange-btn" onclick="this.closest('.price-conversion-display').priceManager.resetManualEditModeAndUpdateConversion()">
                        重置汇率转换
                    </button>
                </div>
            `;
            conversionDisplay.style.display = 'none';
            conversionDisplay.priceManager = this; // 绑定引用以便按钮调用
            priceGroup.appendChild(conversionDisplay);

            getLogger().log('价格转换显示元素已创建', 'info');
        }

        /**
         * 更新实时价格转换显示
         * @param {boolean} force - 是否强制更新（忽略手动编辑模式）
         */
        updatePriceConversion(force = false) {
            // 检查是否应该跳过汇率转换更新
            if (!force && this.manualEditMode && !this.isAutoFilling) {
                getLogger().log('手动编辑模式：跳过汇率转换显示更新', 'debug');
                return;
            }
            const priceInput = this.elements.otaPrice;
            const currencySelect = this.elements.currency;
            const conversionDisplay = document.querySelector('.price-conversion-display');
            const originalPriceDisplay = conversionDisplay?.querySelector('.original-price');
            const convertedPriceDisplay = conversionDisplay?.querySelector('.converted-price');
            const conversionRate = conversionDisplay?.querySelector('.conversion-rate');

            if (!priceInput || !currencySelect || !conversionDisplay || !conversionRate) {
                return;
            }

            const amount = parseFloat(priceInput.value);
            const fromCurrency = currencySelect.value;

            // 如果没有输入价格或货币，隐藏转换显示
            if (!amount || amount <= 0 || !fromCurrency) {
                conversionDisplay.style.display = 'none';
                return;
            }

            // 使用货币转换器进行转换
            if (this.currencyConverter) {
                const conversionResult = this.currencyConverter.convertToMYR(amount, fromCurrency);
                
                if (conversionResult.needsConversion) {
                    // 显示转换信息：原价 → 转换后
                    const originalPrice = this.currencyConverter.formatPrice(amount, fromCurrency);
                    const convertedPrice = this.currencyConverter.formatPrice(conversionResult.convertedAmount, 'MYR');
                    const i18n = this.getI18nManager();
                    
                    if (originalPriceDisplay) {
                        const originalText = i18n ? i18n.t('price.originalPrice') : '原价';
                        originalPriceDisplay.textContent = `${originalText} ${originalPrice}`;
                    }
                    if (convertedPriceDisplay) {
                        const convertedText = i18n ? i18n.t('price.convertedPrice') : '转换后价格';
                        convertedPriceDisplay.textContent = `${convertedText} ${convertedPrice}`;
                    }
                    if (conversionRate) {
                        const rateText = i18n ? i18n.t('price.exchangeRate') : '汇率';
                        conversionRate.textContent = `${rateText}: 1 ${fromCurrency} = ${conversionResult.exchangeRate} MYR`;
                    }
                    
                    // 控制重置按钮的显示
                    const resetButton = conversionDisplay.querySelector('.reset-exchange-btn');
                    if (resetButton) {
                        resetButton.style.display = this.manualEditMode ? 'inline-block' : 'none';
                    }
                    
                    // 为手动编辑状态添加视觉样式
                    if (this.manualEditMode) {
                        conversionDisplay.classList.add('manual-edit-mode');
                    } else {
                        conversionDisplay.classList.remove('manual-edit-mode');
                    }
                    
                    conversionDisplay.style.display = 'block';
                    getLogger().log('价格转换显示已更新', 'info', {
                        original: originalPrice,
                        converted: convertedPrice,
                        rate: conversionResult.exchangeRate,
                        manualEditMode: this.manualEditMode
                    });
                } else {
                    // MYR订单，隐藏转换显示
                    conversionDisplay.style.display = 'none';
                    getLogger().log('MYR订单，隐藏价格转换显示', 'info');
                }
            } else {
                getLogger().log('货币转换器未初始化', 'warning');
                conversionDisplay.style.display = 'none';
            }
        }

        /**
         * 验证价格输入
         */
        validatePriceInput() {
            const priceInput = this.elements.otaPrice;
            const priceGroup = document.querySelector('.price-input-group');

            if (!priceInput || !priceGroup) {
                return;
            }

            const amount = parseFloat(priceInput.value);

            // 移除之前的验证状态
            priceGroup.classList.remove('invalid', 'valid');

            if (priceInput.value && (isNaN(amount) || amount <= 0)) {
                priceGroup.classList.add('invalid');
            } else if (amount > 0) {
                priceGroup.classList.add('valid');
            }
        }

        /**
         * 货币切换时的智能汇率转换
         * @param {string} newCurrency - 新选择的货币
         * @param {string} oldCurrency - 之前的货币
         */
        convertPriceOnCurrencyChange(newCurrency, oldCurrency) {
            // 如果在手动编辑模式且不是自动填充，跳过转换
            if (this.manualEditMode && !this.isAutoFilling) {
                getLogger().log('手动编辑模式：跳过货币切换转换', 'debug');
                return;
            }

            const priceInput = this.elements.otaPrice;
            if (!priceInput || !priceInput.value || !this.currencyConverter) {
                return;
            }

            const currentAmount = parseFloat(priceInput.value);
            if (isNaN(currentAmount) || currentAmount <= 0) {
                return;
            }

            try {
                // 先转换为MYR基准货币
                const toMYRResult = this.currencyConverter.convertToMYR(currentAmount, oldCurrency);

                // 再从MYR转换为目标货币
                const exchangeRates = this.currencyConverter.getExchangeRates();
                const targetRate = exchangeRates[newCurrency.toUpperCase()];

                if (!targetRate) {
                    getLogger().log(`不支持的目标货币: ${newCurrency}`, 'warning');
                    return;
                }

                // 计算转换后的金额：MYR金额 / 目标货币汇率
                const convertedAmount = Math.round((toMYRResult.convertedAmount / targetRate) * 100) / 100;

                // 更新价格输入框
                priceInput.value = convertedAmount.toFixed(2);

                getLogger().log('货币切换转换完成', 'info', {
                    from: `${currentAmount} ${oldCurrency}`,
                    to: `${convertedAmount} ${newCurrency}`,
                    viaBaseCurrency: `${toMYRResult.convertedAmount} MYR`
                });

                // 更新价格转换显示
                this.updatePriceConversion();

            } catch (error) {
                getLogger().log('货币切换转换失败', 'error', { error: error.message });
            }
        }

        /**
         * 设置价格验证
         */
        setupPriceValidation() {
            if (this.elements.otaPrice) {
                // 焦点进入事件 - 检测用户开始手动编辑
                this.elements.otaPrice.addEventListener('focus', () => {
                    if (!this.isAutoFilling) {
                        // 延迟检测，避免程序填充时误触发
                        setTimeout(() => {
                            if (!this.isAutoFilling) {
                                this.enterManualEditMode();
                            }
                        }, 100);
                    }
                });

                // 键盘输入事件 - 确认用户主动输入
                this.elements.otaPrice.addEventListener('keydown', (event) => {
                    if (!this.isAutoFilling) {
                        // 排除非输入键（如Tab, Shift等）
                        const inputKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'];
                        const isDigitKey = /^[0-9]$/.test(event.key);
                        const isSpecialKey = ['.', ',', '-'].includes(event.key);

                        if (isDigitKey || isSpecialKey || inputKeys.includes(event.key)) {
                            this.enterManualEditMode();
                        }
                    }
                });

                // 粘贴事件 - 用户粘贴内容
                this.elements.otaPrice.addEventListener('paste', () => {
                    if (!this.isAutoFilling) {
                        this.enterManualEditMode();
                    }
                });

                // 输入事件 - 处理值变化
                this.elements.otaPrice.addEventListener('input', () => {
                    this.validatePriceInput();

                    // 只有在非手动编辑模式下才更新汇率转换
                    if (!this.manualEditMode || this.isAutoFilling) {
                        this.updatePriceConversion();
                    } else {
                        getLogger().log('手动编辑模式：跳过汇率转换更新', 'debug');
                    }
                });

                // 失去焦点事件 - 延迟退出手动编辑模式
                this.elements.otaPrice.addEventListener('blur', () => {
                    this.validatePriceInput();

                    // 延迟退出手动编辑模式，给用户一些时间
                    if (this.manualEditMode) {
                        this.editTimeout = setTimeout(() => {
                            this.exitManualEditMode();
                            // 退出手动编辑后强制更新一次汇率显示
                            this.updatePriceConversion(true);
                        }, 2000); // 2秒后自动退出手动编辑模式
                    }
                });
            }

            // 货币切换监听器 - 支持智能汇率转换
            if (this.elements.currency) {
                let previousCurrency = this.elements.currency.value || 'MYR';

                this.elements.currency.addEventListener('change', () => {
                    const newCurrency = this.elements.currency.value;

                    // 执行智能汇率转换
                    this.convertPriceOnCurrencyChange(newCurrency, previousCurrency);

                    // 更新上一次的货币值
                    previousCurrency = newCurrency;
                });
            }
        }

        /**
         * 处理Gemini分析结果中的价格转换
         * @param {Object} orderData - 订单数据
         */
        processPriceConversion(orderData) {
            if (!orderData) {
                return;
            }

            try {
                if (!this.currencyConverter) {
                    getLogger().log('货币转换器未初始化', 'warning');
                    return;
                }

                // 处理订单价格转换
                if (orderData.ota_price && orderData.currency) {
                    // 处理订单价格转换
                    const processedData = this.currencyConverter.processOrderPrice({
                        price: orderData.ota_price,
                        currency: orderData.currency
                    });

                    // 更新表单字段
                    if (this.elements.otaPrice) {
                        this.elements.otaPrice.value = processedData.originalPrice || orderData.ota_price;
                    }
                    if (this.elements.currency) {
                        this.elements.currency.value = processedData.originalCurrency || orderData.currency;
                    }

                    // 更新价格转换显示
                    this.updatePriceConversion();

                    getLogger().log('价格转换处理完成', 'success', {
                        originalPrice: processedData.originalPrice,
                        originalCurrency: processedData.originalCurrency,
                        convertedAmount: processedData.convertedAmount
                    });
                }
            } catch (error) {
                getLogger().log('价格转换处理失败', 'error', { error: error.message });
            }
        }

        /**
         * 获取价格转换结果
         * @param {number} amount - 金额
         * @param {string} fromCurrency - 源货币
         * @returns {Object} 转换结果
         */
        getConversionResult(amount, fromCurrency) {
            if (!this.currencyConverter) {
                return null;
            }

            return this.currencyConverter.convertToMYR(amount, fromCurrency);
        }

        /**
         * 格式化价格显示
         * @param {number} amount - 金额
         * @param {string} currency - 货币
         * @returns {string} 格式化后的价格字符串
         */
        formatPrice(amount, currency) {
            if (!this.currencyConverter) {
                return `${amount} ${currency}`;
            }

            return this.currencyConverter.formatPrice(amount, currency);
        }

        /**
         * 检查是否需要价格转换
         * @param {string} currency - 货币类型
         * @returns {boolean} 是否需要转换
         */
        needsConversion(currency) {
            if (!this.currencyConverter) {
                return false;
            }

            const result = this.currencyConverter.convertToMYR(100, currency);
            return result.needsConversion;
        }

        /**
         * 获取支持的货币列表
         * @returns {Array} 支持的货币列表
         */
        getSupportedCurrencies() {
            if (!this.currencyConverter) {
                return ['MYR', 'USD', 'SGD', 'CNY'];
            }

            return this.currencyConverter.getSupportedCurrencies();
        }

        /**
         * 获取汇率信息
         * @param {string} fromCurrency - 源货币
         * @param {string} toCurrency - 目标货币
         * @returns {number} 汇率
         */
        getExchangeRate(fromCurrency, toCurrency = 'MYR') {
            if (!this.currencyConverter) {
                return 1;
            }

            const result = this.currencyConverter.convertToMYR(1, fromCurrency);
            return result.exchangeRate;
        }

        /**
         * 重置价格相关字段
         */
        resetPriceFields() {
            if (this.elements.otaPrice) {
                this.elements.otaPrice.value = '';
            }
            if (this.elements.currency) {
                this.elements.currency.value = 'MYR';
            }

            // 隐藏价格转换显示
            const conversionDisplay = document.querySelector('.price-conversion-display');
            if (conversionDisplay) {
                conversionDisplay.style.display = 'none';
            }

            // 移除验证状态
            const priceGroup = document.querySelector('.price-input-group');
            if (priceGroup) {
                priceGroup.classList.remove('invalid', 'valid');
            }

            getLogger().log('价格字段已重置', 'info');
        }

        /**
         * 设置价格和货币
         * @param {number} amount - 金额
         * @param {string} currency - 货币
         */
        setPriceAndCurrency(amount, currency) {
            if (this.elements.otaPrice) {
                this.elements.otaPrice.value = amount;
            }
            if (this.elements.currency) {
                this.elements.currency.value = currency;
            }

            // 触发验证和转换更新
            this.validatePriceInput();
            this.updatePriceConversion();

            getLogger().log('价格和货币已设置', 'info', { amount, currency });
        }
    }

    // 导出到全局命名空间
    window.OTA.managers.PriceManager = PriceManager;

})();
