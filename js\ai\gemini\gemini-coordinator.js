/**
 * @COORDINATOR Gemini AI主协调器
 * 🏷️ 标签: @GEMINI_COORDINATOR
 * 📝 说明: 新的轻量级主协调器，统一调度所有OTA处理组件，替代原有的monolithic gemini-service.js
 * 🎯 功能: 组件协调、请求路由、错误处理、性能监控、向后兼容
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};

(function() {
    'use strict';

    /**
     * Gemini AI主协调器类
     * 作为整个Gemini AI系统的统一入口点和协调中心
     */
    class GeminiCoordinator {
        constructor() {
            this.logger = window.getLogger?.() || console;
            this.version = '1.0.0';
            this.coordinatorName = 'GeminiCoordinator';
            
            // 组件引用
            this.components = {
                registry: null,
                configManager: null,
                channelIdentifier: null,
                processorRouter: null,
                languageIntegration: null,
                referenceEngine: null
            };
            
            // 协调器配置
            this.config = {
                // 初始化配置
                initialization: {
                    timeout: 10000,           // 初始化超时时间
                    retryCount: 3,            // 初始化重试次数
                    retryDelay: 1000          // 重试延迟
                },
                
                // 处理配置（性能优化）
                processing: {
                    timeout: 15000,           // 减少到15秒超时
                    maxConcurrent: 20,        // 增加到20个并发
                    queueSize: 200,           // 增加队列大小到200
                    enableBatching: true,     // 启用批处理
                    batchSize: 5,             // 批处理大小
                    queueProcessInterval: 50  // 50ms间隔处理队列
                },
                
                // 缓存配置（性能优化）
                cache: {
                    enabled: true,
                    maxSize: 5000,            // 增加到5000条缓存
                    maxAge: 15 * 60 * 1000,   // 增加到15分钟缓存
                    cleanupInterval: 5 * 60 * 1000, // 5分钟清理间隔
                    enableLRU: true,          // 启用LRU淘汰策略
                    enableCompression: false  // 暂不启用压缩（避免CPU开销）
                },
                
                // 性能监控配置
                monitoring: {
                    enabled: true,
                    metricsInterval: 30 * 1000,  // 30秒指标收集间隔
                    enableProfiling: false,      // 启用性能分析
                    logSlowRequests: true,       // 记录慢请求
                    slowRequestThreshold: 5000   // 慢请求阈值（毫秒）
                },
                
                // 错误处理配置
                errorHandling: {
                    enableRetry: true,
                    maxRetries: 3,
                    retryDelay: 1000,
                    backoffMultiplier: 2,
                    enableFallback: true,
                    logErrors: true
                }
            };
            
            // 处理队列
            this.processingQueue = [];
            this.activeProcessing = new Map();
            
            // 结果缓存
            this.resultCache = new Map();
            
            // 性能统计
            this.stats = {
                totalRequests: 0,
                successfulRequests: 0,
                failedRequests: 0,
                averageProcessingTime: 0,
                cacheHits: 0,
                cacheMisses: 0,
                queuedRequests: 0,
                concurrentRequests: 0,
                lastMetricsUpdate: Date.now()
            };
            
            // 初始化状态
            this.initializationState = {
                isInitialized: false,
                isInitializing: false,
                initializationError: null,
                initializationTime: null
            };
            
            // 向后兼容接口映射
            this.legacyMethods = new Map();

            // 向后兼容的状态和配置
            this.realtimeConfig = {
                enabled: true,
                minInputLength: 20,
                debounceDelay: 1500,
                maxRetries: 3
            };

            this.idMappings = {
                backendUsers: {},
                serviceTypes: {},
                carTypes: {},
                languages: {}
            };

            this.lastAnalyzedText = '';

            // 初始化协调器
            this.initialize();
        }

        /**
         * 初始化协调器
         */
        async initialize() {
            if (this.initializationState.isInitializing || this.initializationState.isInitialized) {
                return;
            }
            
            this.initializationState.isInitializing = true;
            const startTime = Date.now();
            
            try {
                this.logger.log('Gemini主协调器初始化开始', 'info');
                
                // 1. 初始化组件引用
                await this.initializeComponents();
                
                // 2. 设置向后兼容接口
                this.setupLegacyCompatibility();
                
                // 3. 启动监控和清理任务
                this.startBackgroundTasks();
                
                // 4. 验证初始化
                await this.validateInitialization();
                
                this.initializationState.isInitialized = true;
                this.initializationState.initializationTime = Date.now() - startTime;
                
                this.logger.log(`Gemini主协调器初始化完成 (${this.initializationState.initializationTime}ms)`, 'info');
                
            } catch (error) {
                this.initializationState.initializationError = error;
                this.logger.logError('Gemini主协调器初始化失败', error);
                throw error;
            } finally {
                this.initializationState.isInitializing = false;
            }
        }

        /**
         * 初始化组件引用
         */
        async initializeComponents() {
            // 获取服务注册中心
            this.components.registry = window.OTA?.Registry;
            if (!this.components.registry) {
                throw new Error('服务注册中心未找到');
            }
            
            // 获取配置管理器
            this.components.configManager = window.OTA?.getService?.('configManager') || 
                                          window.OTA?.gemini?.core?.getConfigManager?.();
            
            // 获取OTA渠道识别器
            this.components.channelIdentifier = window.OTA?.getService?.('otaChannelIdentifier') || 
                                              window.OTA?.gemini?.core?.getOTAChannelIdentifier?.();
            
            // 获取处理器路由系统
            this.components.processorRouter = window.OTA?.getService('processorRouter') || 
                                            window.OTA?.gemini?.core?.getProcessorRouter?.();
            
            // 获取语言-OTA集成组件
            this.components.languageIntegration = window.OTA?.getService('languageOTAIntegration') || 
                                                 window.OTA?.gemini?.core?.getLanguageOTAIntegration?.();
            
            // 获取参考号识别引擎
            this.components.referenceEngine = window.OTA?.getService('otaReferenceEngine') || 
                                            window.OTA?.gemini?.core?.getOTAReferenceEngine?.();
            
            // 获取新的核心服务实例
            this.components.flightNumberProcessor = window.OTA?.gemini?.core?.getFlightNumberProcessor?.();
            this.components.dataNormalizer = window.OTA?.gemini?.core?.getDataNormalizer?.();
            this.components.addressTranslator = window.OTA?.gemini?.core?.getAddressTranslator?.();
            this.components.promptTemplateEngine = window.OTA?.gemini?.core?.getPromptTemplateEngine?.();
            this.components.errorRecoveryEngine = window.OTA?.gemini?.core?.getErrorRecoveryEngine?.();
            this.components.imageAnalysisEngine = window.OTA?.gemini?.core?.getImageAnalysisEngine?.();

            // 获取Gemini API服务
            this.components.geminiService = window.getGeminiService?.() ||
                                          window.OTA?.services?.getGeminiService?.();

            // 验证关键组件
            const requiredComponents = ['configManager', 'channelIdentifier', 'processorRouter'];
            for (const componentName of requiredComponents) {
                if (!this.components[componentName]) {
                    this.logger.logWarning(`关键组件 ${componentName} 未找到，将使用降级模式`);
                }
            }

            this.logger.log('所有组件引用初始化完成', 'info');
        }

        /**
         * 设置向后兼容接口
         */
        setupLegacyCompatibility() {
            // 映射原有的主要方法到新的实现
            this.legacyMethods.set('parseOrderText', this.processOrder.bind(this));
            this.legacyMethods.set('analyzeOrder', this.processOrder.bind(this));
            this.legacyMethods.set('extractOrderData', this.processOrder.bind(this));
            this.legacyMethods.set('processGeminiRequest', this.processOrder.bind(this));

            // 创建向后兼容的方法
            for (const [legacyMethod, newMethod] of this.legacyMethods.entries()) {
                this[legacyMethod] = newMethod;
            }

            // 实现原有GeminiService的核心接口
            this.parseOrder = this.parseOrderCompatible.bind(this);
            this.parseMultipleOrders = this.parseMultipleOrdersCompatible.bind(this);
            this.analyzeImage = this.analyzeImageCompatible.bind(this);
            this.getStatus = this.getStatusCompatible.bind(this);
            this.configureRealtimeAnalysis = this.configureRealtimeAnalysisCompatible.bind(this);
            this.updateIdMappings = this.updateIdMappingsCompatible.bind(this);

            // 兼容旧的方法名
            this.setRealtimeAnalysis = this.configureRealtimeAnalysis.bind(this);
        }

        /**
         * 启动后台任务
         */
        startBackgroundTasks() {
            // 启动缓存清理
            if (this.config.cache.enabled) {
                setInterval(() => {
                    this.cleanupCache();
                }, this.config.cache.cleanupInterval);
            }
            
            // 启动性能监控
            if (this.config.monitoring.enabled) {
                setInterval(() => {
                    this.updateMetrics();
                }, this.config.monitoring.metricsInterval);
            }
            
            // 启动队列处理（性能优化）
            setInterval(() => {
                this.processQueue();
            }, this.config.processing.queueProcessInterval || 50); // 使用配置的间隔，默认50ms
        }

        /**
         * 验证初始化
         */
        async validateInitialization() {
            // 验证关键组件可用性
            const testOrder = 'Test Order: ABC123456789';
            
            try {
                // 测试基本处理流程
                const result = await this.processOrder(testOrder, { 
                    test: true, 
                    timeout: 5000 
                });
                
                if (!result) {
                    throw new Error('基本处理流程测试失败');
                }
                
                this.logger.log('初始化验证通过', 'info');
                
            } catch (error) {
                this.logger.logWarning('初始化验证失败，但协调器仍可使用', error);
            }
        }

        /**
         * 处理订单 - 主要入口方法
         * @param {string} orderText - 订单文本
         * @param {Object} options - 处理选项
         * @returns {Promise<Object>} 处理结果
         */
        async processOrder(orderText, options = {}) {
            const requestId = this.generateRequestId();
            const startTime = Date.now();

            this.stats.totalRequests++;

            // 更新最后分析的文本（用于向后兼容）
            this.lastAnalyzedText = orderText.substring(0, 100);

            try {
                // 参数验证
                if (!orderText || typeof orderText !== 'string') {
                    throw new Error('订单文本必须是非空字符串');
                }

                // 检查缓存
                if (this.config.cache.enabled && !options.skipCache) {
                    const cachedResult = this.getCachedResult(orderText, options);
                    if (cachedResult) {
                        this.stats.cacheHits++;
                        return this.formatResult(cachedResult, requestId, Date.now() - startTime, true);
                    }
                }

                this.stats.cacheMisses++;
                
                // 检查并发限制
                if (this.activeProcessing.size >= this.config.processing.maxConcurrent) {
                    if (options.queue !== false) {
                        return await this.queueRequest(orderText, options, requestId);
                    } else {
                        throw new Error('系统繁忙，请稍后重试');
                    }
                }
                
                // 开始处理
                this.activeProcessing.set(requestId, { orderText, options, startTime });
                
                // 执行处理流程
                const result = await this.executeProcessingPipeline(orderText, options, requestId);
                
                // 缓存结果
                if (this.config.cache.enabled && result.success) {
                    this.cacheResult(orderText, options, result);
                }
                
                this.stats.successfulRequests++;
                
                return this.formatResult(result, requestId, Date.now() - startTime, false);
                
            } catch (error) {
                this.stats.failedRequests++;
                this.logger.logError(`订单处理失败 [${requestId}]`, error);
                
                // 错误处理和降级
                return await this.handleProcessingError(error, orderText, options, requestId, startTime);
                
            } finally {
                this.activeProcessing.delete(requestId);
                this.updateProcessingTimeStats(Date.now() - startTime);
            }
        }

        /**
         * 执行处理流程
         * @param {string} orderText - 订单文本
         * @param {Object} options - 选项
         * @param {string} requestId - 请求ID
         * @returns {Promise<Object>} 处理结果
         */
        async executeProcessingPipeline(orderText, options, requestId) {
            // 1. 语言检测和预处理（如果启用）
            let languageContext = null;
            if (this.components.languageIntegration && !options.skipLanguageDetection) {
                try {
                    languageContext = await this.components.languageIntegration.processWithLanguageContext(
                        orderText, 
                        { ...options, requestId }
                    );
                } catch (error) {
                    this.logger.logWarning(`语言检测失败 [${requestId}]`, error);
                }
            }
            
            // 2. OTA渠道识别
            let channelResult = null;
            if (this.components.channelIdentifier) {
                try {
                    channelResult = await this.components.channelIdentifier.identifyChannel(
                        orderText, 
                        { 
                            ...options, 
                            languageContext,
                            requestId 
                        }
                    );
                } catch (error) {
                    this.logger.logWarning(`渠道识别失败 [${requestId}]`, error);
                }
            }
            
            // 3. 处理器路由和执行
            let processingResult = null;
            if (this.components.processorRouter) {
                try {
                    const channelName = channelResult?.channel || 'generic';
                    const processor = await this.components.processorRouter.routeToProcessor(
                        channelName, 
                        { ...options, requestId }
                    );
                    
                    if (processor) {
                        processingResult = await processor.processOrder(orderText, {
                            ...options,
                            channelResult,
                            languageContext,
                            requestId
                        });
                    }
                } catch (error) {
                    this.logger.logWarning(`处理器执行失败 [${requestId}]`, error);
                }
            }
            
            // 4. 结果整合和后处理
            return this.integrateResults({
                orderText,
                options,
                requestId,
                languageContext,
                channelResult,
                processingResult
            });
        }

        /**
         * 整合处理结果
         * @param {Object} context - 处理上下文
         * @returns {Object} 整合后的结果
         */
        integrateResults(context) {
            const { 
                orderText, 
                options, 
                requestId, 
                languageContext, 
                channelResult, 
                processingResult 
            } = context;
            
            // 基础结果结构
            const result = {
                success: false,
                data: {},
                metadata: {
                    requestId,
                    coordinator: this.coordinatorName,
                    version: this.version,
                    processingPipeline: []
                }
            };
            
            // 整合语言检测结果
            if (languageContext) {
                result.metadata.languageContext = {
                    detectedLanguages: languageContext.languageAnalysis?.detectedLanguages,
                    primaryLanguage: languageContext.languageAnalysis?.primaryLanguage,
                    confidence: languageContext.languageAnalysis?.confidence
                };
                result.metadata.processingPipeline.push('language-detection');
            }
            
            // 整合渠道识别结果
            if (channelResult) {
                result.metadata.channelResult = {
                    channel: channelResult.channel,
                    confidence: channelResult.confidence,
                    method: channelResult.method
                };
                result.metadata.processingPipeline.push('channel-identification');
            }
            
            // 整合处理器结果
            if (processingResult) {
                result.success = processingResult.success;
                result.data = processingResult.data || {};
                result.metadata.processor = {
                    name: processingResult.processor,
                    version: processingResult.version,
                    confidence: processingResult.confidence
                };
                result.metadata.processingPipeline.push('order-processing');
                
                // 合并处理器的元数据
                if (processingResult.metadata) {
                    result.metadata.processorMetadata = processingResult.metadata;
                }
            }
            
            // 如果没有成功的处理结果，使用降级处理
            if (!result.success) {
                return this.generateFallbackResult(orderText, options, requestId);
            }
            
            return result;
        }

        /**
         * 生成降级结果
         * @param {string} orderText - 订单文本
         * @param {Object} options - 选项
         * @param {string} requestId - 请求ID
         * @returns {Object} 降级结果
         */
        generateFallbackResult(orderText, options, requestId) {
            // 尝试使用通用处理器
            const genericProcessor = window.OTA?.gemini?.processors?.getGenericProcessor?.();
            
            if (genericProcessor) {
                try {
                    return genericProcessor.processOrder(orderText, { 
                        ...options, 
                        fallback: true, 
                        requestId 
                    });
                } catch (error) {
                    this.logger.logError(`降级处理也失败 [${requestId}]`, error);
                }
            }
            
            // 最终降级：返回基本结构
            return {
                success: false,
                data: {
                    original_text: orderText,
                    ota_reference_number: this.extractBasicReference(orderText) || 'UNKNOWN',
                    service_type_id: 2, // 默认接机
                    car_type_id: 1,     // 默认车型
                    languages_id_array: {"0": "2"} // 默认英文
                },
                metadata: {
                    requestId,
                    coordinator: this.coordinatorName,
                    fallback: true,
                    processingPipeline: ['fallback-processing']
                }
            };
        }

        /**
         * 提取基本参考号
         * @param {string} text - 文本
         * @returns {string|null} 参考号
         */
        extractBasicReference(text) {
            const patterns = [
                /[A-Z]{2,4}\d{6,12}/g,
                /\d{8,15}/g,
                /[A-Z0-9]{8,20}/g
            ];
            
            for (const pattern of patterns) {
                const matches = text.match(pattern);
                if (matches && matches.length > 0) {
                    return matches[0];
                }
            }
            
            return null;
        }

        /**
         * 处理错误
         * @param {Error} error - 错误对象
         * @param {string} orderText - 订单文本
         * @param {Object} options - 选项
         * @param {string} requestId - 请求ID
         * @param {number} startTime - 开始时间
         * @returns {Promise<Object>} 错误处理结果
         */
        async handleProcessingError(error, orderText, options, requestId, startTime) {
            // 如果启用重试
            if (this.config.errorHandling.enableRetry && !options.noRetry) {
                const retryCount = options.retryCount || 0;
                
                if (retryCount < this.config.errorHandling.maxRetries) {
                    this.logger.log(`重试处理订单 [${requestId}] (${retryCount + 1}/${this.config.errorHandling.maxRetries})`, 'info');
                    
                    // 延迟重试
                    const delay = this.config.errorHandling.retryDelay * 
                                Math.pow(this.config.errorHandling.backoffMultiplier, retryCount);
                    
                    await new Promise(resolve => setTimeout(resolve, delay));
                    
                    return this.processOrder(orderText, {
                        ...options,
                        retryCount: retryCount + 1,
                        noRetry: false
                    });
                }
            }
            
            // 如果启用降级处理
            if (this.config.errorHandling.enableFallback) {
                try {
                    const fallbackResult = this.generateFallbackResult(orderText, options, requestId);
                    return this.formatResult(fallbackResult, requestId, Date.now() - startTime, false, error);
                } catch (fallbackError) {
                    this.logger.logError(`降级处理也失败 [${requestId}]`, fallbackError);
                }
            }
            
            // 返回错误结果
            return this.formatResult({
                success: false,
                error: error.message,
                data: {}
            }, requestId, Date.now() - startTime, false, error);
        }

        /**
         * 格式化结果
         * @param {Object} result - 原始结果
         * @param {string} requestId - 请求ID
         * @param {number} processingTime - 处理时间
         * @param {boolean} fromCache - 是否来自缓存
         * @param {Error} error - 错误对象
         * @returns {Object} 格式化后的结果
         */
        formatResult(result, requestId, processingTime, fromCache = false, error = null) {
            const formattedResult = {
                ...result,
                metadata: {
                    ...result.metadata,
                    requestId,
                    processingTime,
                    fromCache,
                    timestamp: new Date().toISOString(),
                    coordinator: {
                        name: this.coordinatorName,
                        version: this.version
                    }
                }
            };
            
            if (error) {
                formattedResult.error = {
                    message: error.message,
                    type: error.name,
                    stack: error.stack
                };
            }
            
            // 记录慢请求
            if (this.config.monitoring.logSlowRequests && 
                processingTime > this.config.monitoring.slowRequestThreshold) {
                this.logger.logWarning(`慢请求检测 [${requestId}]: ${processingTime}ms`);
            }
            
            return formattedResult;
        }

        /**
         * 生成请求ID
         * @returns {string} 请求ID
         */
        generateRequestId() {
            return `gc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }

        /**
         * 获取缓存结果
         * @param {string} orderText - 订单文本
         * @param {Object} options - 选项
         * @returns {Object|null} 缓存结果
         */
        getCachedResult(orderText, options) {
            const cacheKey = this.generateCacheKey(orderText, options);
            const cacheEntry = this.resultCache.get(cacheKey);
            
            if (!cacheEntry) {
                return null;
            }
            
            // 检查缓存是否过期
            if (Date.now() - cacheEntry.timestamp > this.config.cache.maxAge) {
                this.resultCache.delete(cacheKey);
                return null;
            }
            
            return cacheEntry.result;
        }

        /**
         * 缓存结果
         * @param {string} orderText - 订单文本
         * @param {Object} options - 选项
         * @param {Object} result - 结果
         */
        cacheResult(orderText, options, result) {
            if (this.resultCache.size >= this.config.cache.maxSize) {
                this.cleanupOldestCache();
            }
            
            const cacheKey = this.generateCacheKey(orderText, options);
            this.resultCache.set(cacheKey, {
                result: JSON.parse(JSON.stringify(result)), // 深拷贝
                timestamp: Date.now()
            });
        }

        /**
         * 生成缓存键
         * @param {string} orderText - 订单文本
         * @param {Object} options - 选项
         * @returns {string} 缓存键
         */
        generateCacheKey(orderText, options) {
            const keyData = {
                text: orderText.substring(0, 200), // 限制长度
                options: {
                    skipLanguageDetection: options.skipLanguageDetection,
                    preferredChannel: options.preferredChannel,
                    userId: options.userId
                }
            };
            
            return btoa(JSON.stringify(keyData)).replace(/[^a-zA-Z0-9]/g, '');
        }

        /**
         * 清理最旧的缓存
         */
        cleanupOldestCache() {
            let oldestKey = null;
            let oldestTime = Date.now();
            
            for (const [key, entry] of this.resultCache.entries()) {
                if (entry.timestamp < oldestTime) {
                    oldestTime = entry.timestamp;
                    oldestKey = key;
                }
            }
            
            if (oldestKey) {
                this.resultCache.delete(oldestKey);
            }
        }

        /**
         * 清理过期缓存
         */
        cleanupCache() {
            const now = Date.now();
            const maxAge = this.config.cache.maxAge;
            
            for (const [key, entry] of this.resultCache.entries()) {
                if (now - entry.timestamp > maxAge) {
                    this.resultCache.delete(key);
                }
            }
        }

        /**
         * 队列请求
         * @param {string} orderText - 订单文本
         * @param {Object} options - 选项
         * @param {string} requestId - 请求ID
         * @returns {Promise<Object>} 处理结果
         */
        async queueRequest(orderText, options, requestId) {
            return new Promise((resolve, reject) => {
                if (this.processingQueue.length >= this.config.processing.queueSize) {
                    reject(new Error('处理队列已满'));
                    return;
                }
                
                this.processingQueue.push({
                    orderText,
                    options,
                    requestId,
                    resolve,
                    reject,
                    queueTime: Date.now()
                });
                
                this.stats.queuedRequests++;
            });
        }

        /**
         * 处理队列
         */
        async processQueue() {
            while (this.processingQueue.length > 0 && 
                   this.activeProcessing.size < this.config.processing.maxConcurrent) {
                
                const queuedRequest = this.processingQueue.shift();
                if (!queuedRequest) break;
                
                try {
                    const result = await this.processOrder(queuedRequest.orderText, {
                        ...queuedRequest.options,
                        queue: false // 避免再次排队
                    });
                    queuedRequest.resolve(result);
                } catch (error) {
                    queuedRequest.reject(error);
                }
            }
        }

        /**
         * 更新处理时间统计
         * @param {number} processingTime - 处理时间
         */
        updateProcessingTimeStats(processingTime) {
            const currentAverage = this.stats.averageProcessingTime;
            const totalRequests = this.stats.totalRequests;
            
            this.stats.averageProcessingTime = 
                ((currentAverage * (totalRequests - 1)) + processingTime) / totalRequests;
        }

        /**
         * 更新指标
         */
        updateMetrics() {
            this.stats.concurrentRequests = this.activeProcessing.size;
            this.stats.lastMetricsUpdate = Date.now();
            
            // 这里可以添加更多的指标收集逻辑
            if (this.config.monitoring.enableProfiling) {
                this.collectProfilingData();
            }
        }

        /**
         * 收集性能分析数据
         */
        collectProfilingData() {
            // 实现性能分析数据收集
            // 例如内存使用、CPU使用等
        }

        /**
         * 获取协调器统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                ...this.stats,
                initialization: this.initializationState,
                cache: {
                    size: this.resultCache.size,
                    hitRate: this.stats.totalRequests > 0 ? 
                        ((this.stats.cacheHits / this.stats.totalRequests) * 100).toFixed(2) + '%' : '0%'
                },
                queue: {
                    size: this.processingQueue.length,
                    active: this.activeProcessing.size
                },
                components: Object.keys(this.components).reduce((acc, key) => {
                    acc[key] = !!this.components[key];
                    return acc;
                }, {})
            };
        }

        /**
         * 获取健康状态
         * @returns {Object} 健康状态
         */
        getHealthStatus() {
            const stats = this.getStats();
            const isHealthy = this.initializationState.isInitialized && 
                            !this.initializationState.initializationError &&
                            stats.queue.size < this.config.processing.queueSize * 0.8;
            
            return {
                healthy: isHealthy,
                status: isHealthy ? 'healthy' : 'degraded',
                details: {
                    initialized: this.initializationState.isInitialized,
                    queueUtilization: (stats.queue.size / this.config.processing.queueSize * 100).toFixed(2) + '%',
                    cacheUtilization: (this.resultCache.size / this.config.cache.maxSize * 100).toFixed(2) + '%',
                    successRate: stats.totalRequests > 0 ? 
                        ((stats.successfulRequests / stats.totalRequests) * 100).toFixed(2) + '%' : '0%'
                }
            };
        }

        /**
         * 重置统计信息
         */
        resetStats() {
            this.stats = {
                totalRequests: 0,
                successfulRequests: 0,
                failedRequests: 0,
                averageProcessingTime: 0,
                cacheHits: 0,
                cacheMisses: 0,
                queuedRequests: 0,
                concurrentRequests: 0,
                lastMetricsUpdate: Date.now()
            };
        }

        /**
         * 清理所有缓存
         */
        clearAllCache() {
            this.resultCache.clear();
            this.logger.log('所有缓存已清理', 'info');
        }

        // ==================== 向后兼容接口实现 ====================

        /**
         * 向后兼容的parseOrder方法
         * 保持与原GeminiService.parseOrder完全相同的接口和行为
         * @param {string} text - 订单文本
         * @param {boolean} isRealtime - 是否为实时分析
         * @returns {Promise<Object|null>} 解析结果
         */
        async parseOrderCompatible(text, isRealtime = false) {
            try {
                // 参数验证 - 与原方法保持一致
                if (!text || text.length < (isRealtime ? 20 : 10)) {
                    return null;
                }

                // 调用新的处理流程
                const result = await this.processOrder(text, {
                    isRealtime,
                    compatibilityMode: true
                });

                // 转换为原有的返回格式
                if (result.success) {
                    return result.data;
                } else {
                    return null;
                }
            } catch (error) {
                this.logger.logError('parseOrder兼容接口执行失败', error);
                return null;
            }
        }

        /**
         * 向后兼容的parseMultipleOrders方法
         * @param {Array} orderSegments - 订单片段数组
         * @returns {Promise<Array>} 解析结果数组
         */
        async parseMultipleOrdersCompatible(orderSegments) {
            try {
                // 参数验证
                if (!Array.isArray(orderSegments) || orderSegments.length === 0) {
                    return [];
                }

                this.logger.log(`开始解析 ${orderSegments.length} 个订单片段`, 'info');

                // 并行处理所有订单片段
                const promises = orderSegments.map(async (segment, index) => {
                    try {
                        const result = await this.parseOrderCompatible(segment, false);
                        return result;
                    } catch (error) {
                        this.logger.logError(`订单片段 ${index + 1} 解析失败`, error);
                        return null;
                    }
                });

                const results = await Promise.all(promises);

                // 过滤掉null结果
                const validResults = results.filter(result => result !== null);

                this.logger.log(`多订单解析完成: ${validResults.length}/${orderSegments.length} 成功`, 'info');

                return validResults;
            } catch (error) {
                this.logger.logError('parseMultipleOrders兼容接口执行失败', error);
                return [];
            }
        }

        /**
         * 向后兼容的analyzeImage方法
         * @param {string} base64Image - Base64编码的图片数据
         * @param {Object} options - 分析选项
         * @returns {Promise<Object>} 分析结果
         */
        async analyzeImageCompatible(base64Image, options = {}) {
            try {
                // 参数验证
                if (!base64Image || typeof base64Image !== 'string') {
                    throw new Error('无效的图片数据');
                }

                // 调用新的处理流程，标记为图像分析
                const result = await this.processOrder(base64Image, {
                    imageAnalysis: true,
                    compatibilityMode: true,
                    ...options
                });

                // 转换为原有的返回格式
                if (result.success) {
                    return {
                        success: true,
                        data: result.data,
                        metadata: result.metadata
                    };
                } else {
                    throw new Error(result.error || '图像分析失败');
                }
            } catch (error) {
                this.logger.logError('analyzeImage兼容接口执行失败', error);
                throw error;
            }
        }

        /**
         * 向后兼容的getStatus方法
         * @returns {Object} 状态信息
         */
        getStatusCompatible() {
            const stats = this.getStats();

            // 返回与原GeminiService.getStatus相同的格式
            return {
                isAnalyzing: stats.queue.active > 0,
                lastAnalyzedText: this.lastAnalyzedText || '',
                totalRequests: stats.totalRequests,
                successfulRequests: stats.successfulRequests,
                failedRequests: stats.failedRequests,
                averageProcessingTime: stats.averageProcessingTime,
                cacheHitRate: stats.cache.hitRate,
                queueSize: stats.queue.size,
                isInitialized: this.initializationState.isInitialized
            };
        }

        /**
         * 向后兼容的configureRealtimeAnalysis方法
         * @param {Object} config - 实时分析配置
         */
        configureRealtimeAnalysisCompatible(config) {
            try {
                // 参数校验，确保传入为对象
                if (typeof config !== 'object' || config === null) {
                    return;
                }

                // 更新协调器的实时分析配置
                this.realtimeConfig = {
                    ...this.realtimeConfig,
                    ...config
                };

                // 如果有配置管理器，也更新配置管理器
                if (this.components.configManager) {
                    this.components.configManager.updateConfig('realtime', config);
                }

                this.logger.log('实时分析配置已更新', 'info', config);
            } catch (error) {
                this.logger.logError('configureRealtimeAnalysis兼容接口执行失败', error);
            }
        }

        /**
         * 向后兼容的updateIdMappings方法
         * @param {Object} systemData - 包含最新ID映射的对象
         */
        updateIdMappingsCompatible(systemData) {
            try {
                if (!systemData || typeof systemData !== 'object') {
                    return;
                }

                // 更新协调器的ID映射
                this.idMappings = this.idMappings || {};

                // 更新后端用户映射
                if (systemData.backend_users) {
                    this.idMappings.backendUsers = systemData.backend_users.reduce((acc, user) => {
                        acc[user.email] = user.id;
                        return acc;
                    }, {});
                }

                // 更新其他映射
                if (systemData.service_types) {
                    this.idMappings.serviceTypes = systemData.service_types;
                }

                if (systemData.car_types) {
                    this.idMappings.carTypes = systemData.car_types;
                }

                if (systemData.languages) {
                    this.idMappings.languages = systemData.languages;
                }

                // 如果有配置管理器，也更新配置管理器
                if (this.components.configManager) {
                    this.components.configManager.updateConfig('idMappings', this.idMappings);
                }

                // 通知所有处理器更新ID映射
                if (this.components.processorRouter) {
                    this.components.processorRouter.updateAllProcessorsIdMappings(this.idMappings);
                }

                this.logger.log('ID映射已更新', 'info');
            } catch (error) {
                this.logger.logError('updateIdMappings兼容接口执行失败', error);
            }
        }
    }

    // 创建全局单例实例
    function getGeminiCoordinator() {
        if (!window.OTA.gemini.coordinator) {
            window.OTA.gemini.coordinator = new GeminiCoordinator();
        }
        return window.OTA.gemini.coordinator;
    }

    // 暴露到全局命名空间
    window.OTA.gemini.GeminiCoordinator = GeminiCoordinator;
    window.OTA.gemini.getGeminiCoordinator = getGeminiCoordinator;

    // 向后兼容
    window.getGeminiCoordinator = getGeminiCoordinator;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('geminiCoordinator', getGeminiCoordinator(), '@GEMINI_COORDINATOR');
        window.OTA.Registry.registerFactory('getGeminiCoordinator', getGeminiCoordinator, '@GEMINI_COORDINATOR_FACTORY');
    }

    // 创建向后兼容的全局接口
    // 这些方法将替代原有的 gemini-service.js 中的方法
    window.parseOrderWithGemini = function(orderText, options = {}) {
        const coordinator = getGeminiCoordinator();
        return coordinator.processOrder(orderText, options);
    };

    window.analyzeOrderText = function(orderText, options = {}) {
        const coordinator = getGeminiCoordinator();
        return coordinator.processOrder(orderText, { ...options, analysis: true });
    };

    window.extractOrderData = function(orderText, options = {}) {
        const coordinator = getGeminiCoordinator();
        return coordinator.processOrder(orderText, { ...options, extraction: true });
    };

    // 兼容原有的 GeminiService 类接口
    window.GeminiService = {
        parseOrder: function(orderText, isRealtime = false) {
            const coordinator = getGeminiCoordinator();
            return coordinator.parseOrderCompatible(orderText, isRealtime);
        },

        parseMultipleOrders: function(orderSegments) {
            const coordinator = getGeminiCoordinator();
            return coordinator.parseMultipleOrdersCompatible(orderSegments);
        },

        analyzeImage: function(base64Image, options = {}) {
            const coordinator = getGeminiCoordinator();
            return coordinator.analyzeImageCompatible(base64Image, options);
        },

        getStatus: function() {
            const coordinator = getGeminiCoordinator();
            return coordinator.getStatusCompatible();
        },

        configureRealtimeAnalysis: function(config) {
            const coordinator = getGeminiCoordinator();
            return coordinator.configureRealtimeAnalysisCompatible(config);
        },

        updateIdMappings: function(systemData) {
            const coordinator = getGeminiCoordinator();
            return coordinator.updateIdMappingsCompatible(systemData);
        },

        // 兼容旧方法名
        setRealtimeAnalysis: function(config) {
            const coordinator = getGeminiCoordinator();
            return coordinator.configureRealtimeAnalysisCompatible(config);
        },

        // 额外的兼容方法
        analyzeText: function(text, options = {}) {
            const coordinator = getGeminiCoordinator();
            return coordinator.parseOrderCompatible(text, false);
        },

        getStats: function() {
            const coordinator = getGeminiCoordinator();
            return coordinator.getStats();
        },

        getHealth: function() {
            const coordinator = getGeminiCoordinator();
            return coordinator.getHealthStatus();
        },

        clearCache: function() {
            const coordinator = getGeminiCoordinator();
            return coordinator.clearAllCache();
        },

        // 从原gemini-service.js迁移的核心功能方法
        getFlightNumberPatterns: function() {
            const coordinator = getGeminiCoordinator();
            return coordinator.components.flightNumberProcessor?.getFlightNumberPatterns?.() || null;
        },

        getAsianAirlineCodes: function() {
            const coordinator = getGeminiCoordinator();
            return coordinator.components.flightNumberProcessor?.getAsianAirlineCodes?.() || [];
        },

        validateFlightNumber: function(flightNumber) {
            const coordinator = getGeminiCoordinator();
            return coordinator.components.flightNumberProcessor?.validateFlightNumber?.(flightNumber) || false;
        },

        extractFlightNumber: function(text) {
            const coordinator = getGeminiCoordinator();
            return coordinator.components.flightNumberProcessor?.extractFlightNumber?.(text) || null;
        },

        normalizeDataFormats: function(data) {
            const coordinator = getGeminiCoordinator();
            return coordinator.components.dataNormalizer?.normalizeDataFormats?.(data) || data;
        },

        normalizePhoneNumber: function(phone) {
            const coordinator = getGeminiCoordinator();
            return coordinator.components.dataNormalizer?.normalizePhoneNumber?.(phone) || phone;
        },

        normalizeDate: function(date) {
            const coordinator = getGeminiCoordinator();
            return coordinator.components.dataNormalizer?.normalizeDate?.(date) || date;
        },

        normalizeTime: function(time) {
            const coordinator = getGeminiCoordinator();
            return coordinator.components.dataNormalizer?.normalizeTime?.(time) || time;
        },

        normalizeLocation: function(location) {
            const coordinator = getGeminiCoordinator();
            return coordinator.components.dataNormalizer?.normalizeLocation?.(location) || location;
        },

        enhancedOtaReferenceExtractor: function(text, otaType = null) {
            const coordinator = getGeminiCoordinator();
            return coordinator.components.referenceEngine?.enhancedOtaReferenceExtractor?.(text, otaType) || null;
        },

        isValidOtaReference: function(reference) {
            const coordinator = getGeminiCoordinator();
            return coordinator.components.referenceEngine?.isValidOtaReference?.(reference) || false;
        },

        translateAddress: function(address, targetLanguage = 'en') {
            const coordinator = getGeminiCoordinator();
            return coordinator.components.addressTranslator?.translateAddress?.(address, targetLanguage) || address;
        },

        queryAirportTranslation: function(query) {
            const coordinator = getGeminiCoordinator();
            return coordinator.components.addressTranslator?.queryAirportTranslation?.(query) || null;
        },

        queryHotelKnowledgeBase: function(hotelName) {
            const coordinator = getGeminiCoordinator();
            return coordinator.components.addressTranslator?.queryHotelKnowledgeBase?.(hotelName) || null;
        },

        buildUniversalPrompt: function(orderText, options = {}) {
            const coordinator = getGeminiCoordinator();
            return coordinator.components.promptTemplateEngine?.buildUniversalPrompt?.(orderText, options) || orderText;
        },

        getContextualPrompt: function(context, options = {}) {
            const coordinator = getGeminiCoordinator();
            return coordinator.components.promptTemplateEngine?.getContextualPrompt?.(context, options) || '';
        },

        selectOptimalPrompt: function(orderText, analysisResult = {}) {
            const coordinator = getGeminiCoordinator();
            return coordinator.components.promptTemplateEngine?.selectOptimalPrompt?.(orderText, analysisResult) || orderText;
        },

        intelligentErrorRecovery: function(responseText, originalText = '') {
            const coordinator = getGeminiCoordinator();
            return coordinator.components.errorRecoveryEngine?.intelligentErrorRecovery?.(responseText, originalText) || { success: false, data: null };
        },

        tryFixJsonFormat: function(text) {
            const coordinator = getGeminiCoordinator();
            return coordinator.components.errorRecoveryEngine?.tryFixJsonFormat?.(text) || { success: false };
        },

        tryExtractPartialData: function(responseText, originalText = '') {
            const coordinator = getGeminiCoordinator();
            return coordinator.components.errorRecoveryEngine?.tryExtractPartialData?.(responseText, originalText) || { success: false };
        },

        // 图片分析引擎方法
        analyzeImageAdvanced: function(imageInput, options = {}) {
            const coordinator = getGeminiCoordinator();
            return coordinator.components.imageAnalysisEngine?.analyzeImage?.(imageInput, options) || Promise.resolve({ success: false });
        },

        preprocessImage: function(imageInput, options = {}) {
            const coordinator = getGeminiCoordinator();
            return coordinator.components.imageAnalysisEngine?.preprocessImage?.(imageInput, options) || Promise.resolve(imageInput);
        },

        validateImageFormat: function(imageData) {
            const coordinator = getGeminiCoordinator();
            return coordinator.components.imageAnalysisEngine?.validateImage?.(imageData) || Promise.resolve({ valid: false });
        }
    };

    // 自动初始化协调器（延迟初始化以确保依赖加载）
    setTimeout(() => {
        try {
            const coordinator = getGeminiCoordinator();
            console.log('✅ Gemini AI主协调器自动初始化完成');
        } catch (error) {
            console.error('❌ Gemini AI主协调器自动初始化失败:', error);
        }
    }, 1000);

    console.log('✅ Gemini AI主协调器已加载');

})();
