/**
 * @OTA_TRANSFORMER 多订单数据转换器
 * 🏷️ 标签: @OTA_MULTI_ORDER_TRANSFORMER
 * 📝 说明: 负责字段映射和格式化，确保数据转换的完整性和一致性
 * ⚠️ 警告: 已注册，请勿重复开发
 * <AUTHOR>
 * @version 1.0.0
 */

// 防止重复加载
if (window.OTA && window.OTA.MultiOrderTransformer) {
    console.log('多订单数据转换器已存在，跳过重复加载');
} else {

/**
 * 多订单数据转换器类
 * 提供字段映射、数据验证和格式化功能
 */
class MultiOrderTransformer {
    constructor(config = {}) {
        this.config = {
            strictValidation: config.strictValidation || false,
            preserveOriginalFields: config.preserveOriginalFields || true,
            ...config
        };
        
        this.logger = this.getLogger();
        this.fieldMappingConfig = this.getFieldMappingConfig();
        this.validator = this.getValidator();
    }

    /**
     * 获取日志服务
     * @returns {Object} 日志服务实例
     */
    getLogger() {
        return window.getLogger?.() || {
            log: console.log.bind(console),
            logError: console.error.bind(console)
        };
    }

    /**
     * 获取字段映射配置
     * @returns {Object} 字段映射配置
     */
    getFieldMappingConfig() {
        return window.OTA?.FieldMappingConfig || window.FIELD_MAPPING_CONFIG || {};
    }

    /**
     * 获取字段映射验证器
     * @returns {Object} 验证器实例
     */
    getValidator() {
        return window.OTA?.FieldMappingValidator || window.FieldMappingValidator;
    }

    /**
     * 转换订单数据（主要入口方法）
     * @param {Object} orderData - 原始订单数据
     * @returns {Object} 转换后的订单数据
     */
    transformOrderData(orderData) {
        if (!orderData || typeof orderData !== 'object') {
            this.logger?.logError('无效的订单数据', { orderData });
            return orderData;
        }

        try {
            this.logger?.log('🔄 开始数据转换', 'info', { 
                originalFields: Object.keys(orderData).length 
            });

            // 1. 应用备用字段映射
            let transformedData = this.applyAlternativeFieldMapping({ ...orderData });

            // 2. 应用AI到前端字段映射
            transformedData = this.applyAIToFrontendMapping(transformedData);

            // 3. 验证和格式化字段
            transformedData = this.validateAndFormatOrderFields(transformedData);

            // 4. 处理Paging服务
            transformedData = this.processPagingServiceForOrder(transformedData);

            // 5. 处理中文语言检测
            transformedData = this.processChineseLanguageDetection(transformedData);

            // 6. 应用一致性数据处理
            transformedData = this.applyConsistentDataProcessing(transformedData);

            this.logger?.log('✅ 数据转换完成', 'success', { 
                transformedFields: Object.keys(transformedData).length 
            });

            return transformedData;

        } catch (error) {
            this.logger?.logError('数据转换失败', error);
            return orderData; // 返回原始数据作为降级方案
        }
    }

    /**
     * 应用备用字段映射
     * @param {Object} order - 订单对象
     * @returns {Object} 映射后的订单对象
     */
    applyAlternativeFieldMapping(order) {
        if (!this.fieldMappingConfig.ALTERNATIVE_FIELDS) {
            return order;
        }

        const mappedOrder = { ...order };

        // 应用备用字段映射规则
        Object.entries(this.fieldMappingConfig.ALTERNATIVE_FIELDS).forEach(([primaryField, alternatives]) => {
            if (!mappedOrder[primaryField]) {
                for (const altField of alternatives) {
                    if (mappedOrder[altField]) {
                        mappedOrder[primaryField] = mappedOrder[altField];
                        this.logger?.log(`🔄 字段映射: ${altField} → ${primaryField}`, 'info');
                        
                        // 根据配置决定是否保留原始字段
                        if (!this.config.preserveOriginalFields) {
                            delete mappedOrder[altField];
                        }
                        break;
                    }
                }
            }
        });

        return mappedOrder;
    }

    /**
     * 应用AI到前端字段映射
     * @param {Object} order - 订单对象
     * @returns {Object} 映射后的订单对象
     */
    applyAIToFrontendMapping(order) {
        if (!this.fieldMappingConfig.AI_TO_FRONTEND) {
            return order;
        }

        const mappedOrder = { ...order };

        // 应用AI到前端的字段映射
        Object.entries(this.fieldMappingConfig.AI_TO_FRONTEND).forEach(([aiField, frontendField]) => {
            if (order.hasOwnProperty(aiField) && !mappedOrder.hasOwnProperty(frontendField)) {
                mappedOrder[frontendField] = order[aiField];
                this.logger?.log(`🤖 AI字段映射: ${aiField} → ${frontendField}`, 'info');
                
                // 根据配置决定是否保留原始字段
                if (!this.config.preserveOriginalFields) {
                    delete mappedOrder[aiField];
                }
            }
        });

        return mappedOrder;
    }

    /**
     * 验证和格式化订单字段
     * @param {Object} order - 订单对象
     * @returns {Object} 验证和格式化后的订单对象
     */
    validateAndFormatOrderFields(order) {
        const processedOrder = { ...order };

        try {
            // 处理特殊字段规则
            if (this.fieldMappingConfig.SPECIAL_FIELD_RULES) {
                Object.entries(this.fieldMappingConfig.SPECIAL_FIELD_RULES).forEach(([fieldName, rules]) => {
                    if (processedOrder.hasOwnProperty(fieldName) && rules.toAPI) {
                        const originalValue = processedOrder[fieldName];
                        const processedValue = rules.toAPI(originalValue);
                        
                        if (processedValue !== originalValue) {
                            processedOrder[fieldName] = processedValue;
                            this.logger?.log(`🔧 字段格式化: ${fieldName}`, 'info', {
                                original: originalValue,
                                processed: processedValue
                            });
                        }
                    }
                });
            }

            // 特殊处理languages_id_array字段
            if (processedOrder.languagesIdArray && this.validator) {
                const languageResult = this.validator.validateLanguagesIdArrayFormat(processedOrder.languagesIdArray);
                if (languageResult.converted) {
                    processedOrder.languagesIdArray = languageResult.converted;
                    this.logger?.log('🔧 languages_id_array格式转换', 'info', languageResult);
                }
            }

            // 数据类型转换
            this.convertDataTypes(processedOrder);

            return processedOrder;

        } catch (error) {
            this.logger?.logError('字段验证和格式化失败', error);
            return order;
        }
    }

    /**
     * 数据类型转换
     * @param {Object} order - 订单对象
     */
    convertDataTypes(order) {
        // 数值字段转换
        const numericFields = ['passengerCount', 'luggageCount', 'otaPrice', 'subCategoryId', 'carTypeId', 'drivingRegionId'];
        numericFields.forEach(field => {
            if (order[field] !== undefined && order[field] !== null) {
                const numValue = parseFloat(order[field]);
                if (!isNaN(numValue)) {
                    order[field] = numValue;
                }
            }
        });

        // 整数字段转换
        const integerFields = ['passengerCount', 'luggageCount', 'subCategoryId', 'carTypeId', 'drivingRegionId'];
        integerFields.forEach(field => {
            if (order[field] !== undefined && order[field] !== null) {
                const intValue = parseInt(order[field]);
                if (!isNaN(intValue)) {
                    order[field] = intValue;
                }
            }
        });

        // 布尔字段转换
        const booleanFields = ['meetAndGreet', 'babyChair', 'tourGuide'];
        booleanFields.forEach(field => {
            if (order[field] !== undefined && order[field] !== null) {
                if (typeof order[field] === 'string') {
                    order[field] = order[field].toLowerCase() === 'true' || order[field] === '1';
                } else {
                    order[field] = Boolean(order[field]);
                }
            }
        });
    }

    /**
     * 处理Paging服务订单
     * @param {Object} order - 订单对象
     * @returns {Object} 处理后的订单对象
     */
    processPagingServiceForOrder(order) {
        try {
            // 检测是否为Paging服务订单
            const isPagingOrder = this.detectPagingService(order);
            
            if (isPagingOrder) {
                order.is_paging_order = true;
                order.carTypeId = 34; // Paging服务的车型ID
                order.subCategoryId = 2; // 默认为接机服务
                
                this.logger?.log('🏷️ 检测到Paging服务订单', 'info', {
                    customerName: order.customerName,
                    carTypeId: order.carTypeId
                });
            }

            return order;

        } catch (error) {
            this.logger?.logError('Paging服务处理失败', error);
            return order;
        }
    }

    /**
     * 检测是否为Paging服务
     * @param {Object} order - 订单对象
     * @returns {boolean} 是否为Paging服务
     */
    detectPagingService(order) {
        // 检测关键词
        const pagingKeywords = [
            '举牌', '接机牌', '接机服务', 'paging', 'meet and greet',
            '机场接待', '接机员', '举牌接机', '接机牌服务'
        ];

        const orderText = JSON.stringify(order).toLowerCase();
        
        return pagingKeywords.some(keyword => 
            orderText.includes(keyword.toLowerCase())
        );
    }

    /**
     * 处理中文语言检测
     * @param {Object} order - 订单对象
     * @returns {Object} 处理后的订单对象
     */
    processChineseLanguageDetection(order) {
        try {
            // 检测客户姓名是否包含中文
            const hasChineseName = this.containsChinese(order.customerName || '');
            
            // 检测其他字段是否包含中文
            const fieldsToCheck = ['extraRequirement', 'pickup', 'dropoff'];
            const hasChineseContent = fieldsToCheck.some(field => 
                this.containsChinese(order[field] || '')
            );

            // 如果检测到中文，确保语言数组包含中文
            if (hasChineseName || hasChineseContent) {
                if (!order.languagesIdArray) {
                    order.languagesIdArray = [4, 2]; // 中文 + 英文
                } else if (Array.isArray(order.languagesIdArray) && !order.languagesIdArray.includes(4)) {
                    order.languagesIdArray.unshift(4); // 将中文添加到首位
                }
                
                this.logger?.log('🇨🇳 检测到中文内容，已添加中文语言', 'info', {
                    hasChineseName,
                    hasChineseContent,
                    languagesIdArray: order.languagesIdArray
                });
            }

            return order;

        } catch (error) {
            this.logger?.logError('中文语言检测失败', error);
            return order;
        }
    }

    /**
     * 检测文本是否包含中文字符
     * @param {string} text - 待检测文本
     * @returns {boolean} 是否包含中文
     */
    containsChinese(text) {
        if (!text || typeof text !== 'string') {
            return false;
        }
        
        // 中文字符的Unicode范围
        const chineseRegex = /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/;
        return chineseRegex.test(text);
    }

    /**
     * 应用一致性数据处理
     * @param {Object} order - 订单对象
     * @returns {Object} 处理后的订单对象
     */
    applyConsistentDataProcessing(order) {
        try {
            // 确保必要字段存在默认值
            const defaults = {
                passengerCount: 1,
                luggageCount: 0,
                subCategoryId: 2, // 默认接机
                carTypeId: 1, // 默认Comfort 5 Seater
                drivingRegionId: 1, // 默认区域
                languagesIdArray: [2] // 默认英文
            };

            Object.entries(defaults).forEach(([field, defaultValue]) => {
                if (order[field] === undefined || order[field] === null || order[field] === '') {
                    order[field] = defaultValue;
                    this.logger?.log(`🔧 设置默认值: ${field} = ${defaultValue}`, 'info');
                }
            });

            // 确保价格字段为数值
            if (order.otaPrice) {
                order.otaPrice = parseFloat(order.otaPrice) || 0;
            }

            // 确保日期时间格式正确
            if (order.pickupDate && !/^\d{4}-\d{2}-\d{2}$/.test(order.pickupDate)) {
                // 尝试转换日期格式
                const dateObj = new Date(order.pickupDate);
                if (!isNaN(dateObj.getTime())) {
                    order.pickupDate = dateObj.toISOString().split('T')[0];
                    this.logger?.log('🔧 日期格式转换', 'info', { 
                        original: order.pickupDate, 
                        converted: order.pickupDate 
                    });
                }
            }

            // 确保时间格式正确
            if (order.pickupTime && !/^\d{2}:\d{2}$/.test(order.pickupTime)) {
                // 尝试提取时间部分
                const timeMatch = order.pickupTime.match(/(\d{1,2}):(\d{2})/);
                if (timeMatch) {
                    const hours = timeMatch[1].padStart(2, '0');
                    const minutes = timeMatch[2];
                    order.pickupTime = `${hours}:${minutes}`;
                    this.logger?.log('🔧 时间格式转换', 'info', { 
                        original: order.pickupTime, 
                        converted: order.pickupTime 
                    });
                }
            }

            return order;

        } catch (error) {
            this.logger?.logError('一致性数据处理失败', error);
            return order;
        }
    }

    /**
     * 转换为API格式
     * @param {Object} frontendData - 前端格式数据
     * @returns {Object} API格式数据
     */
    transformToAPIFormat(frontendData) {
        if (!this.fieldMappingConfig.FRONTEND_TO_API) {
            return frontendData;
        }

        const apiData = { ...frontendData };

        // 应用前端到API的字段映射
        Object.entries(this.fieldMappingConfig.FRONTEND_TO_API).forEach(([frontendField, apiField]) => {
            if (frontendData.hasOwnProperty(frontendField)) {
                apiData[apiField] = frontendData[frontendField];
                
                // 删除前端字段名（避免重复）
                if (frontendField !== apiField) {
                    delete apiData[frontendField];
                }
            }
        });

        // 特殊处理languages_id_array格式
        if (apiData.languages_id_array && this.validator) {
            const languageResult = this.validator.validateLanguagesIdArrayFormat(apiData.languages_id_array);
            apiData.languages_id_array = languageResult.converted;
        }

        return apiData;
    }

    /**
     * 验证字段映射完整性
     * @param {Object} originalData - 原始数据
     * @param {Object} transformedData - 转换后数据
     * @returns {Object} 验证结果
     */
    validateFieldMappingIntegrity(originalData, transformedData) {
        if (!this.validator) {
            return { success: true, message: '验证器不可用' };
        }

        try {
            const comparison = this.validator.compareFieldMapping(originalData, transformedData);
            
            const report = {
                success: comparison.identical || comparison.differences.length === 0,
                identical: comparison.identical,
                differences: comparison.differences,
                missingFields: comparison.missingInTransformed,
                addedFields: comparison.addedInTransformed,
                summary: comparison.summary
            };

            if (!report.success) {
                this.logger?.log('⚠️ 字段映射完整性检查发现问题', 'warn', report);
            }

            return report;

        } catch (error) {
            this.logger?.logError('字段映射完整性验证失败', error);
            return { success: false, error: error.message };
        }
    }
}

// 导出数据转换器
window.OTA = window.OTA || {};
window.OTA.MultiOrderTransformer = MultiOrderTransformer;

// 向后兼容
window.MultiOrderTransformer = MultiOrderTransformer;

console.log('✅ 多订单数据转换器已加载');

// 结束防重复加载检查
}
