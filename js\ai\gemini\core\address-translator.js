/**
 * @CORE 地址翻译处理器
 * 🏷️ 标签: @ADDRESS_TRANSLATOR
 * 📝 说明: 负责地址翻译、机场查询和酒店知识库查询的核心组件
 * 🎯 功能: 中英文地址翻译、机场代码查询、酒店名称标准化
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.core = window.OTA.gemini.core || {};

(function() {
    'use strict';

    /**
     * 地址翻译处理器类
     * 负责地址翻译和地点信息标准化
     */
    class AddressTranslator {
        constructor() {
            this.logger = window.getLogger?.() || console;
            
            // 初始化翻译数据库
            this.initializeTranslationDatabase();
            
            // 初始化机场数据库
            this.initializeAirportDatabase();
            
            // 初始化酒店知识库
            this.initializeHotelKnowledgeBase();
        }

        /**
         * 初始化翻译数据库
         */
        initializeTranslationDatabase() {
            // 常用地点中英文对照
            this.locationTranslations = {
                // 机场
                '吉隆坡国际机场': 'Kuala Lumpur International Airport',
                'KLIA': 'Kuala Lumpur International Airport',
                'KLIA2': 'Kuala Lumpur International Airport 2',
                '梳邦机场': 'Sultan Abdul Aziz Shah Airport',
                '新加坡樟宜机场': 'Singapore Changi Airport',
                '樟宜机场': 'Singapore Changi Airport',
                
                // 城市区域
                '吉隆坡': 'Kuala Lumpur',
                '新山': 'Johor Bahru',
                '槟城': 'Penang',
                '马六甲': 'Malacca',
                '怡保': 'Ipoh',
                '关丹': 'Kuantan',
                '亚庇': 'Kota Kinabalu',
                '古晋': 'Kuching',
                
                // 购物中心
                '双子塔': 'Petronas Twin Towers',
                '武吉免登': 'Bukit Bintang',
                '金河广场': 'Sungei Wang Plaza',
                '谷中城': 'Mid Valley Megamall',
                '1乌达玛': '1 Utama',
                
                // 酒店区域
                '吉隆坡市中心': 'Kuala Lumpur City Centre',
                '金三角': 'Golden Triangle',
                '蒲种': 'Puchong',
                '八打灵再也': 'Petaling Jaya',
                '莎阿南': 'Shah Alam'
            };

            // 反向翻译映射
            this.reverseTranslations = {};
            for (const [chinese, english] of Object.entries(this.locationTranslations)) {
                this.reverseTranslations[english.toLowerCase()] = chinese;
            }
        }

        /**
         * 初始化机场数据库
         */
        initializeAirportDatabase() {
            this.airportDatabase = {
                'KUL': {
                    name: 'Kuala Lumpur International Airport',
                    chineseName: '吉隆坡国际机场',
                    city: 'Kuala Lumpur',
                    country: 'Malaysia',
                    aliases: ['KLIA', 'KL International Airport']
                },
                'SZB': {
                    name: 'Sultan Abdul Aziz Shah Airport',
                    chineseName: '梳邦机场',
                    city: 'Subang',
                    country: 'Malaysia',
                    aliases: ['Subang Airport', 'Sultan Abdul Aziz Shah']
                },
                'SIN': {
                    name: 'Singapore Changi Airport',
                    chineseName: '新加坡樟宜机场',
                    city: 'Singapore',
                    country: 'Singapore',
                    aliases: ['Changi Airport', 'Singapore Airport']
                },
                'JHB': {
                    name: 'Senai International Airport',
                    chineseName: '士乃国际机场',
                    city: 'Johor Bahru',
                    country: 'Malaysia',
                    aliases: ['Johor Airport', 'Senai Airport']
                },
                'PEN': {
                    name: 'Penang International Airport',
                    chineseName: '槟城国际机场',
                    city: 'Penang',
                    country: 'Malaysia',
                    aliases: ['Penang Airport', 'Bayan Lepas Airport']
                }
            };
        }

        /**
         * 初始化酒店知识库
         */
        initializeHotelKnowledgeBase() {
            this.hotelKnowledgeBase = {
                // 五星级酒店
                'mandarin oriental': {
                    standardName: 'Mandarin Oriental Kuala Lumpur',
                    chineseName: '文华东方酒店',
                    area: 'KLCC',
                    category: '5-star'
                },
                'grand hyatt': {
                    standardName: 'Grand Hyatt Kuala Lumpur',
                    chineseName: '君悦大酒店',
                    area: 'KLCC',
                    category: '5-star'
                },
                'shangri-la': {
                    standardName: 'Shangri-La Hotel Kuala Lumpur',
                    chineseName: '香格里拉大酒店',
                    area: 'KLCC',
                    category: '5-star'
                },
                'ritz carlton': {
                    standardName: 'The Ritz-Carlton Kuala Lumpur',
                    chineseName: '丽思卡尔顿酒店',
                    area: 'KLCC',
                    category: '5-star'
                },
                
                // 四星级酒店
                'hilton': {
                    standardName: 'Hilton Kuala Lumpur',
                    chineseName: '希尔顿酒店',
                    area: 'Sentral',
                    category: '4-star'
                },
                'marriott': {
                    standardName: 'JW Marriott Hotel Kuala Lumpur',
                    chineseName: '万豪酒店',
                    area: 'Bukit Bintang',
                    category: '4-star'
                },
                'sheraton': {
                    standardName: 'Sheraton Imperial Kuala Lumpur Hotel',
                    chineseName: '喜来登帝国酒店',
                    area: 'Jalan Sultan Ismail',
                    category: '4-star'
                }
            };
        }

        /**
         * 翻译地址（从原gemini-service.js迁移）
         * @param {string} address - 原始地址
         * @param {string} targetLanguage - 目标语言 ('en' 或 'zh')
         * @returns {string} 翻译后的地址
         */
        translateAddress(address, targetLanguage = 'en') {
            if (!address || typeof address !== 'string') {
                return address;
            }

            const cleanAddress = address.trim();
            
            try {
                if (targetLanguage === 'en') {
                    // 中文转英文
                    return this.translateToEnglish(cleanAddress);
                } else if (targetLanguage === 'zh') {
                    // 英文转中文
                    return this.translateToChinese(cleanAddress);
                }
                
                return cleanAddress;
            } catch (error) {
                this.logger.logError('地址翻译失败', error);
                return cleanAddress;
            }
        }

        /**
         * 翻译为英文
         * @param {string} chineseAddress - 中文地址
         * @returns {string} 英文地址
         */
        translateToEnglish(chineseAddress) {
            let translatedAddress = chineseAddress;

            // 逐个替换已知的中文地点
            for (const [chinese, english] of Object.entries(this.locationTranslations)) {
                if (translatedAddress.includes(chinese)) {
                    translatedAddress = translatedAddress.replace(new RegExp(chinese, 'g'), english);
                }
            }

            return translatedAddress;
        }

        /**
         * 翻译为中文
         * @param {string} englishAddress - 英文地址
         * @returns {string} 中文地址
         */
        translateToChinese(englishAddress) {
            let translatedAddress = englishAddress;
            const lowerAddress = englishAddress.toLowerCase();

            // 逐个替换已知的英文地点
            for (const [english, chinese] of Object.entries(this.reverseTranslations)) {
                if (lowerAddress.includes(english)) {
                    const regex = new RegExp(english.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
                    translatedAddress = translatedAddress.replace(regex, chinese);
                }
            }

            return translatedAddress;
        }

        /**
         * 查询机场翻译（从原gemini-service.js迁移）
         * @param {string} query - 查询字符串
         * @returns {Object|null} 机场信息
         */
        queryAirportTranslation(query) {
            if (!query || typeof query !== 'string') {
                return null;
            }

            const cleanQuery = query.trim().toLowerCase();

            try {
                // 直接匹配机场代码
                for (const [code, info] of Object.entries(this.airportDatabase)) {
                    if (code.toLowerCase() === cleanQuery) {
                        return {
                            code: code,
                            ...info,
                            matchType: 'code'
                        };
                    }
                }

                // 匹配机场名称或别名
                for (const [code, info] of Object.entries(this.airportDatabase)) {
                    // 检查英文名称
                    if (info.name.toLowerCase().includes(cleanQuery)) {
                        return {
                            code: code,
                            ...info,
                            matchType: 'name'
                        };
                    }

                    // 检查中文名称
                    if (info.chineseName && info.chineseName.includes(query.trim())) {
                        return {
                            code: code,
                            ...info,
                            matchType: 'chinese_name'
                        };
                    }

                    // 检查别名
                    if (info.aliases) {
                        for (const alias of info.aliases) {
                            if (alias.toLowerCase().includes(cleanQuery)) {
                                return {
                                    code: code,
                                    ...info,
                                    matchType: 'alias'
                                };
                            }
                        }
                    }
                }

                this.logger.log(`未找到机场信息: ${query}`, 'debug');
                return null;

            } catch (error) {
                this.logger.logError('机场查询失败', error);
                return null;
            }
        }

        /**
         * 查询酒店知识库（从原gemini-service.js迁移）
         * @param {string} hotelName - 酒店名称
         * @returns {Object|null} 酒店信息
         */
        queryHotelKnowledgeBase(hotelName) {
            if (!hotelName || typeof hotelName !== 'string') {
                return null;
            }

            const cleanName = hotelName.trim().toLowerCase();

            try {
                // 直接匹配
                if (this.hotelKnowledgeBase[cleanName]) {
                    return {
                        ...this.hotelKnowledgeBase[cleanName],
                        matchType: 'exact'
                    };
                }

                // 模糊匹配
                for (const [key, info] of Object.entries(this.hotelKnowledgeBase)) {
                    if (cleanName.includes(key) || key.includes(cleanName)) {
                        return {
                            ...info,
                            matchType: 'partial'
                        };
                    }

                    // 检查中文名称匹配
                    if (info.chineseName && hotelName.includes(info.chineseName)) {
                        return {
                            ...info,
                            matchType: 'chinese'
                        };
                    }
                }

                this.logger.log(`未找到酒店信息: ${hotelName}`, 'debug');
                return null;

            } catch (error) {
                this.logger.logError('酒店查询失败', error);
                return null;
            }
        }

        /**
         * 标准化地点名称
         * @param {string} location - 原始地点名称
         * @returns {string} 标准化后的地点名称
         */
        standardizeLocation(location) {
            if (!location || typeof location !== 'string') {
                return location;
            }

            let standardized = location.trim();

            // 查询机场信息
            const airportInfo = this.queryAirportTranslation(standardized);
            if (airportInfo) {
                return airportInfo.name;
            }

            // 查询酒店信息
            const hotelInfo = this.queryHotelKnowledgeBase(standardized);
            if (hotelInfo) {
                return hotelInfo.standardName;
            }

            // 使用翻译数据库标准化
            const translated = this.translateAddress(standardized, 'en');
            if (translated !== standardized) {
                return translated;
            }

            return standardized;
        }

        /**
         * 检测地址语言
         * @param {string} address - 地址字符串
         * @returns {string} 语言代码 ('zh', 'en', 'mixed')
         */
        detectAddressLanguage(address) {
            if (!address || typeof address !== 'string') {
                return 'unknown';
            }

            const chineseChars = address.match(/[\u4e00-\u9fff]/g);
            const englishChars = address.match(/[a-zA-Z]/g);

            const chineseCount = chineseChars ? chineseChars.length : 0;
            const englishCount = englishChars ? englishChars.length : 0;

            if (chineseCount > 0 && englishCount > 0) {
                return 'mixed';
            } else if (chineseCount > 0) {
                return 'zh';
            } else if (englishCount > 0) {
                return 'en';
            } else {
                return 'unknown';
            }
        }

        /**
         * 获取翻译器统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                locationTranslations: Object.keys(this.locationTranslations).length,
                airportDatabase: Object.keys(this.airportDatabase).length,
                hotelKnowledgeBase: Object.keys(this.hotelKnowledgeBase).length,
                supportedLanguages: ['zh', 'en']
            };
        }
    }

    // 暴露到全局命名空间
    window.OTA.gemini.core.AddressTranslator = AddressTranslator;

    // 创建单例实例
    let addressTranslatorInstance = null;

    /**
     * 获取地址翻译器单例实例
     * @returns {AddressTranslator} 地址翻译器实例
     */
    function getAddressTranslator() {
        if (!addressTranslatorInstance) {
            addressTranslatorInstance = new AddressTranslator();
        }
        return addressTranslatorInstance;
    }

    // 暴露工厂函数
    window.OTA.gemini.core.getAddressTranslator = getAddressTranslator;

    // 注册到服务注册中心
    if (window.OTA?.gemini?.core?.ServiceRegistry) {
        window.OTA.gemini.core.ServiceRegistry.register('addressTranslator', getAddressTranslator, '@ADDRESS_TRANSLATOR');
    }

    console.log('✅ 地址翻译处理器模块已加载');

})();
