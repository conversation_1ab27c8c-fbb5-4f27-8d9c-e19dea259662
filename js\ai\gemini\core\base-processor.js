/**
 * @OTA_PROCESSOR OTA渠道处理器基类
 * 🏷️ 标签: @BASE_PROCESSOR
 * 📝 说明: 定义所有OTA渠道处理器的统一接口和基础功能
 * 🎯 功能: 统一接口、基础处理、配置管理、错误处理
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.core = window.OTA.gemini.core || {};

(function() {
    'use strict';

    /**
     * OTA渠道处理器基类
     * 所有具体的OTA处理器都应继承此基类
     */
    class BaseProcessor {
        constructor(platformName, config = {}) {
            this.platformName = platformName;
            this.config = config;
            this.logger = window.getLogger?.() || console;
            
            // 处理统计
            this.stats = {
                processedCount: 0,
                successCount: 0,
                errorCount: 0,
                averageProcessingTime: 0
            };

            // 初始化处理器
            this.initialize();
        }

        /**
         * 初始化处理器 - 子类可重写
         */
        initialize() {
            this.logger.log(`初始化${this.platformName}处理器`, 'info');
        }

        /**
         * 处理订单数据 - 主要处理方法
         * @param {string} orderText - 订单文本
         * @param {Object} options - 处理选项
         * @returns {Promise<Object>} 处理结果
         */
        async processOrder(orderText, options = {}) {
            const startTime = Date.now();
            this.stats.processedCount++;

            try {
                // 预处理
                const preprocessedText = await this.preprocessText(orderText, options);
                
                // 参考号识别
                const referenceResult = await this.identifyReference(preprocessedText, options);
                
                // 字段提取
                const extractedFields = await this.extractFields(preprocessedText, options);
                
                // 数据标准化
                const normalizedData = await this.normalizeData(extractedFields, options);
                
                // 应用预设配置
                const finalData = await this.applyPresets(normalizedData, options);
                
                // 验证结果
                const validationResult = await this.validateResult(finalData, options);
                
                const processingTime = Date.now() - startTime;
                this.updateStats(true, processingTime);
                
                return {
                    success: true,
                    platform: this.platformName,
                    reference: referenceResult,
                    data: finalData,
                    validation: validationResult,
                    processingTime,
                    metadata: {
                        processor: this.constructor.name,
                        version: this.getVersion(),
                        timestamp: new Date().toISOString()
                    }
                };

            } catch (error) {
                const processingTime = Date.now() - startTime;
                this.updateStats(false, processingTime);
                
                this.logger.logError(`${this.platformName}处理器处理失败`, error);
                
                return {
                    success: false,
                    platform: this.platformName,
                    error: error.message,
                    processingTime,
                    metadata: {
                        processor: this.constructor.name,
                        version: this.getVersion(),
                        timestamp: new Date().toISOString()
                    }
                };
            }
        }

        /**
         * 预处理文本 - 子类可重写
         * @param {string} text - 原始文本
         * @param {Object} options - 选项
         * @returns {Promise<string>} 预处理后的文本
         */
        async preprocessText(text, options = {}) {
            if (!text || typeof text !== 'string') {
                throw new Error('无效的输入文本');
            }

            // 基础清理
            let cleanText = text
                .trim()
                .replace(/\s+/g, ' ')  // 标准化空格
                .replace(/[""'']/g, '"') // 标准化引号
                .replace(/[：]/g, ':');  // 标准化冒号

            // 平台特定的预处理
            cleanText = await this.platformSpecificPreprocess(cleanText, options);

            return cleanText;
        }

        /**
         * 平台特定预处理 - 子类必须实现
         * @param {string} text - 文本
         * @param {Object} options - 选项
         * @returns {Promise<string>} 处理后的文本
         */
        async platformSpecificPreprocess(text, options = {}) {
            // 基类默认实现，子类应重写
            return text;
        }

        /**
         * 识别参考号 - 子类可重写
         * @param {string} text - 文本
         * @param {Object} options - 选项
         * @returns {Promise<Object>} 参考号识别结果
         */
        async identifyReference(text, options = {}) {
            // 使用OTA参考号识别引擎
            const engine = window.OTA.gemini.core.getOTAReferenceEngine?.();
            if (engine) {
                return engine.identifyReference(text, this.platformName);
            }

            // 降级处理
            return {
                found: false,
                reference: null,
                platform: this.platformName,
                confidence: 0,
                method: 'fallback'
            };
        }

        /**
         * 提取字段 - 子类必须实现
         * @param {string} text - 文本
         * @param {Object} options - 选项
         * @returns {Promise<Object>} 提取的字段
         */
        async extractFields(text, options = {}) {
            throw new Error(`${this.platformName}处理器必须实现extractFields方法`);
        }

        /**
         * 数据标准化 - 子类可重写
         * @param {Object} rawData - 原始数据
         * @param {Object} options - 选项
         * @returns {Promise<Object>} 标准化后的数据
         */
        async normalizeData(rawData, options = {}) {
            // 基础标准化
            const normalized = { ...rawData };

            // 日期时间标准化
            if (normalized.pickup_date) {
                normalized.pickup_date = this.normalizeDateFormat(normalized.pickup_date);
            }

            // 时间标准化
            if (normalized.pickup_time) {
                normalized.pickup_time = this.normalizeTimeFormat(normalized.pickup_time);
            }

            // 电话号码标准化
            if (normalized.customer_phone) {
                normalized.customer_phone = this.normalizePhoneNumber(normalized.customer_phone);
            }

            // 价格标准化
            if (normalized.ota_price) {
                normalized.ota_price = this.normalizePriceFormat(normalized.ota_price);
            }

            return normalized;
        }

        /**
         * 应用预设配置 - 子类可重写
         * @param {Object} data - 数据
         * @param {Object} options - 选项
         * @returns {Promise<Object>} 应用预设后的数据
         */
        async applyPresets(data, options = {}) {
            const presets = this.getPresetConfig();
            const result = { ...data };

            // 应用默认语言设置
            if (presets.defaultLanguage && !result.languages_id_array) {
                result.languages_id_array = presets.defaultLanguage;
            }

            // 应用默认车型
            if (presets.defaultCarType && !result.car_type_id) {
                result.car_type_id = presets.defaultCarType;
            }

            // 应用额外要求模板
            if (presets.extraRequirementsTemplate && !result.extra_requirements) {
                result.extra_requirements = presets.extraRequirementsTemplate;
            }

            return result;
        }

        /**
         * 验证结果 - 子类可重写
         * @param {Object} data - 数据
         * @param {Object} options - 选项
         * @returns {Promise<Object>} 验证结果
         */
        async validateResult(data, options = {}) {
            const validation = {
                isValid: true,
                errors: [],
                warnings: []
            };

            // 必填字段检查
            const requiredFields = this.getRequiredFields();
            for (const field of requiredFields) {
                if (!data[field] || data[field] === '') {
                    validation.errors.push(`缺少必填字段: ${field}`);
                    validation.isValid = false;
                }
            }

            // 数据格式检查
            const formatValidation = this.validateDataFormats(data);
            validation.errors.push(...formatValidation.errors);
            validation.warnings.push(...formatValidation.warnings);

            if (validation.errors.length > 0) {
                validation.isValid = false;
            }

            return validation;
        }

        /**
         * 获取必填字段列表 - 子类可重写
         * @returns {Array} 必填字段列表
         */
        getRequiredFields() {
            return [
                'ota_reference_number',
                'pickup_location',
                'pickup_date',
                'pickup_time',
                'customer_name',
                'customer_phone'
            ];
        }

        /**
         * 验证数据格式
         * @param {Object} data - 数据
         * @returns {Object} 验证结果
         */
        validateDataFormats(data) {
            const errors = [];
            const warnings = [];

            // 日期格式验证
            if (data.pickup_date && !this.isValidDateFormat(data.pickup_date)) {
                errors.push('日期格式无效');
            }

            // 时间格式验证
            if (data.pickup_time && !this.isValidTimeFormat(data.pickup_time)) {
                errors.push('时间格式无效');
            }

            // 电话号码验证
            if (data.customer_phone && !this.isValidPhoneNumber(data.customer_phone)) {
                warnings.push('电话号码格式可能无效');
            }

            return { errors, warnings };
        }

        /**
         * 获取预设配置 - 子类应重写
         * @returns {Object} 预设配置
         */
        getPresetConfig() {
            return {
                defaultLanguage: { "0": "2" }, // English
                defaultCarType: 1, // Comfort 5 Seater
                extraRequirementsTemplate: ''
            };
        }

        /**
         * 获取处理器版本
         * @returns {string} 版本号
         */
        getVersion() {
            return '1.0.0';
        }

        /**
         * 日期格式标准化
         * @param {string} dateStr - 日期字符串
         * @returns {string} 标准化后的日期
         */
        normalizeDateFormat(dateStr) {
            // 转换为DD-MM-YYYY格式
            try {
                const date = new Date(dateStr);
                if (isNaN(date.getTime())) {
                    return dateStr; // 无法解析，返回原值
                }
                
                const day = String(date.getDate()).padStart(2, '0');
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const year = date.getFullYear();
                
                return `${day}-${month}-${year}`;
            } catch (error) {
                return dateStr;
            }
        }

        /**
         * 时间格式标准化
         * @param {string} timeStr - 时间字符串
         * @returns {string} 标准化后的时间
         */
        normalizeTimeFormat(timeStr) {
            // 转换为HH:MM格式
            try {
                const timeMatch = timeStr.match(/(\d{1,2}):?(\d{2})/);
                if (timeMatch) {
                    const hours = String(parseInt(timeMatch[1])).padStart(2, '0');
                    const minutes = String(parseInt(timeMatch[2])).padStart(2, '0');
                    return `${hours}:${minutes}`;
                }
                return timeStr;
            } catch (error) {
                return timeStr;
            }
        }

        /**
         * 电话号码标准化
         * @param {string} phoneStr - 电话号码字符串
         * @returns {string} 标准化后的电话号码
         */
        normalizePhoneNumber(phoneStr) {
            // 移除非数字字符，保留+号
            return phoneStr.replace(/[^\d+]/g, '');
        }

        /**
         * 价格格式标准化
         * @param {string} priceStr - 价格字符串
         * @returns {string} 标准化后的价格
         */
        normalizePriceFormat(priceStr) {
            // 提取数字部分
            const match = priceStr.match(/[\d.]+/);
            return match ? match[0] : priceStr;
        }

        /**
         * 验证日期格式
         * @param {string} dateStr - 日期字符串
         * @returns {boolean} 是否有效
         */
        isValidDateFormat(dateStr) {
            return /^\d{2}-\d{2}-\d{4}$/.test(dateStr);
        }

        /**
         * 验证时间格式
         * @param {string} timeStr - 时间字符串
         * @returns {boolean} 是否有效
         */
        isValidTimeFormat(timeStr) {
            return /^\d{2}:\d{2}$/.test(timeStr);
        }

        /**
         * 验证电话号码格式
         * @param {string} phoneStr - 电话号码字符串
         * @returns {boolean} 是否有效
         */
        isValidPhoneNumber(phoneStr) {
            return /^[\+]?[\d\s\-\(\)]{8,20}$/.test(phoneStr);
        }

        /**
         * 更新统计信息
         * @param {boolean} success - 是否成功
         * @param {number} processingTime - 处理时间
         */
        updateStats(success, processingTime) {
            if (success) {
                this.stats.successCount++;
            } else {
                this.stats.errorCount++;
            }

            // 更新平均处理时间
            const totalTime = this.stats.averageProcessingTime * (this.stats.processedCount - 1) + processingTime;
            this.stats.averageProcessingTime = Math.round(totalTime / this.stats.processedCount);
        }

        /**
         * 获取统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                ...this.stats,
                successRate: this.stats.processedCount > 0 ? 
                    ((this.stats.successCount / this.stats.processedCount) * 100).toFixed(2) + '%' : '0%'
            };
        }

        /**
         * 重置统计信息
         */
        resetStats() {
            this.stats = {
                processedCount: 0,
                successCount: 0,
                errorCount: 0,
                averageProcessingTime: 0
            };
        }
    }

    // 暴露到全局命名空间
    window.OTA.gemini.core.BaseProcessor = BaseProcessor;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerClass('BaseProcessor', BaseProcessor, '@BASE_PROCESSOR');
    }

    console.log('✅ OTA处理器基类已加载');

})();
