/**
 * @CONFIG_FILE 处理器预设值配置
 * 🏷️ 标签: @PROCESSOR_PRESET_VALUES_CONFIG
 * 📝 说明: 定义各OTA处理器的预设值规则，包括默认值、智能推荐值和条件预设
 * 🎯 功能: 预设值管理、智能推荐、条件预设、默认值配置
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.configs = window.OTA.gemini.configs || {};

(function() {
    'use strict';

    /**
     * 处理器预设值配置对象
     * 包含所有OTA处理器的预设值规则
     */
    const processorPresetValues = {
        // 版本信息
        version: '1.0.0',
        lastUpdated: '2024-01-15',
        
        // 通用预设值（所有处理器共享）
        commonPresets: {
            // 服务类型预设
            service_type_id: {
                default: 2,  // 接机服务
                conditions: [
                    {
                        keywords: ['接机', 'pickup', 'arrival', '到达', '机场接'],
                        value: 2,
                        confidence: 0.9
                    },
                    {
                        keywords: ['送机', 'dropoff', 'departure', '出发', '机场送'],
                        value: 3,
                        confidence: 0.9
                    },
                    {
                        keywords: ['包车', 'charter', 'rental', '用车', '包车服务'],
                        value: 4,
                        confidence: 0.8
                    }
                ]
            },
            
            // 车型预设
            car_type_id: {
                default: 1,  // Comfort 5 Seater
                conditions: [
                    {
                        passengerCount: { min: 1, max: 4 },
                        value: 1,  // Comfort 5 Seater
                        confidence: 0.8
                    },
                    {
                        passengerCount: { min: 5, max: 7 },
                        value: 2,  // MPV 7 Seater
                        confidence: 0.9
                    },
                    {
                        passengerCount: { min: 8, max: 12 },
                        value: 3,  // Van 12 Seater
                        confidence: 0.9
                    },
                    {
                        keywords: ['豪华', 'luxury', 'premium', '高端'],
                        value: 4,  // Luxury
                        confidence: 0.7
                    }
                ]
            },
            
            // 语言预设
            languages_id_array: {
                default: [2],  // English
                conditions: [
                    {
                        keywords: ['中文', '中国', '华人', '华语', 'chinese', '普通话'],
                        value: [4],  // Chinese
                        confidence: 0.8
                    },
                    {
                        keywords: ['马来', 'malay', 'bahasa', '马来语'],
                        value: [3],  // Malay
                        confidence: 0.8
                    },
                    {
                        keywords: ['英文', 'english', '英语'],
                        value: [2],  // English
                        confidence: 0.8
                    }
                ]
            },
            
            // 行李数量预设
            luggage_number: {
                default: 2,
                conditions: [
                    {
                        passengerCount: { min: 1, max: 2 },
                        value: 2,
                        confidence: 0.7
                    },
                    {
                        passengerCount: { min: 3, max: 4 },
                        value: 4,
                        confidence: 0.7
                    },
                    {
                        passengerCount: { min: 5, max: 7 },
                        value: 6,
                        confidence: 0.7
                    }
                ]
            },
            
            // 负责人预设
            responsible_person_id: {
                default: 1,  // 第一个后台用户
                conditions: [
                    {
                        userEmail: '<EMAIL>',
                        value: 37,
                        confidence: 1.0
                    },
                    {
                        userEmail: '<EMAIL>',
                        value: 310,
                        confidence: 1.0
                    }
                ]
            }
        },

        // 处理器特定预设值
        processorSpecific: {
            // Chong Dealer特定预设
            'ChongDealer': {
                service_type_id: {
                    default: 2,
                    conditions: [
                        {
                            keywords: ['举牌', 'sign', 'board', '接机牌'],
                            value: 2,
                            confidence: 0.95,
                            additionalData: { pickup_sign: true }
                        },
                        {
                            keywords: ['包车', 'charter', '小时', 'hours', '全天'],
                            value: 4,
                            confidence: 0.9
                        }
                    ]
                },
                pickup_sign: {
                    default: false,
                    conditions: [
                        {
                            keywords: ['举牌', 'sign', 'board', '接机牌', '名牌'],
                            value: true,
                            confidence: 0.9
                        }
                    ]
                },
                charter_duration: {
                    default: null,
                    conditions: [
                        {
                            keywords: ['小时', 'hours', 'hrs'],
                            extractPattern: /(\d+)\s*(?:小时|hours?|hrs?)/i,
                            confidence: 0.8
                        },
                        {
                            keywords: ['全天', 'full day', 'whole day'],
                            value: 8,
                            confidence: 0.7
                        },
                        {
                            keywords: ['半天', 'half day'],
                            value: 4,
                            confidence: 0.7
                        }
                    ]
                }
            },

            // Fliggy特定预设
            'Fliggy': {
                languages_id_array: {
                    default: [4],  // Chinese (飞猪主要服务中文用户)
                    conditions: [
                        {
                            keywords: ['中文', '中国', '华人'],
                            value: [4],
                            confidence: 0.9
                        }
                    ]
                },
                currency: {
                    default: 'CNY',
                    conditions: [
                        {
                            keywords: ['人民币', '¥', 'CNY', 'RMB'],
                            value: 'CNY',
                            confidence: 0.9
                        }
                    ]
                }
            },

            // JRCoach特定预设
            'JRCoach': {
                languages_id_array: {
                    default: [2, 3],  // English + Malay
                    conditions: [
                        {
                            keywords: ['malay', 'bahasa', '马来'],
                            value: [3],
                            confidence: 0.8
                        }
                    ]
                },
                currency: {
                    default: 'MYR',
                    conditions: [
                        {
                            keywords: ['马币', 'MYR', 'RM', '令吉'],
                            value: 'MYR',
                            confidence: 0.9
                        }
                    ]
                },
                vehicle_type: {
                    default: 'sedan',
                    conditions: [
                        {
                            keywords: ['MPV', 'van'],
                            value: 'mpv',
                            confidence: 0.8
                        },
                        {
                            keywords: ['luxury', '豪华'],
                            value: 'luxury',
                            confidence: 0.7
                        }
                    ]
                }
            },

            // KKday特定预设
            'KKday': {
                languages_id_array: {
                    default: [2, 4],  // English + Chinese
                    conditions: [
                        {
                            keywords: ['繁体', '台湾', 'traditional'],
                            value: [4],  // Chinese Traditional
                            confidence: 0.8
                        }
                    ]
                },
                currency: {
                    default: 'USD',
                    conditions: [
                        {
                            keywords: ['台币', 'NT$', 'TWD'],
                            value: 'TWD',
                            confidence: 0.9
                        },
                        {
                            keywords: ['美金', '$', 'USD'],
                            value: 'USD',
                            confidence: 0.8
                        }
                    ]
                },
                activity_type: {
                    default: 'tour',
                    conditions: [
                        {
                            keywords: ['门票', 'ticket', 'admission'],
                            value: 'ticket',
                            confidence: 0.8
                        },
                        {
                            keywords: ['体验', 'experience', 'activity'],
                            value: 'experience',
                            confidence: 0.7
                        }
                    ]
                }
            },

            // Klook特定预设
            'Klook': {
                languages_id_array: {
                    default: [2, 4],  // English + Chinese
                    conditions: [
                        {
                            keywords: ['简体', '大陆', 'simplified'],
                            value: [4],  // Chinese Simplified
                            confidence: 0.8
                        },
                        {
                            keywords: ['繁体', '香港', '台湾', 'traditional'],
                            value: [4],  // Chinese Traditional
                            confidence: 0.8
                        }
                    ]
                },
                currency: {
                    default: 'USD',
                    conditions: [
                        {
                            keywords: ['港币', 'HK$', 'HKD'],
                            value: 'HKD',
                            confidence: 0.9
                        },
                        {
                            keywords: ['新币', 'S$', 'SGD'],
                            value: 'SGD',
                            confidence: 0.9
                        }
                    ]
                },
                region: {
                    default: 'asia',
                    conditions: [
                        {
                            keywords: ['香港', 'hong kong', 'hk'],
                            value: 'hong_kong',
                            confidence: 0.9
                        },
                        {
                            keywords: ['新加坡', 'singapore', 'sg'],
                            value: 'singapore',
                            confidence: 0.9
                        },
                        {
                            keywords: ['台湾', 'taiwan', 'tw'],
                            value: 'taiwan',
                            confidence: 0.9
                        }
                    ]
                }
            },

            // Ctrip特定预设
            'Ctrip': {
                languages_id_array: {
                    default: [4],  // Chinese (携程主要服务中文用户)
                    conditions: [
                        {
                            keywords: ['中文', '中国', '华人'],
                            value: [4],
                            confidence: 0.9
                        }
                    ]
                },
                currency: {
                    default: 'CNY',
                    conditions: [
                        {
                            keywords: ['人民币', '¥', 'CNY', 'RMB'],
                            value: 'CNY',
                            confidence: 0.9
                        }
                    ]
                },
                supplier_type: {
                    default: 'local',
                    conditions: [
                        {
                            keywords: ['当地', 'local', '本地'],
                            value: 'local',
                            confidence: 0.8
                        },
                        {
                            keywords: ['国际', 'international', '全球'],
                            value: 'international',
                            confidence: 0.7
                        }
                    ]
                }
            },

            // Agoda特定预设
            'Agoda': {
                languages_id_array: {
                    default: [2],  // English
                    conditions: [
                        {
                            keywords: ['中文', 'chinese'],
                            value: [4],
                            confidence: 0.8
                        }
                    ]
                },
                currency: {
                    default: 'USD',
                    conditions: [
                        {
                            keywords: ['美金', '$', 'USD'],
                            value: 'USD',
                            confidence: 0.8
                        },
                        {
                            keywords: ['欧元', '€', 'EUR'],
                            value: 'EUR',
                            confidence: 0.8
                        }
                    ]
                },
                property_type: {
                    default: 'hotel',
                    conditions: [
                        {
                            keywords: ['酒店', 'hotel'],
                            value: 'hotel',
                            confidence: 0.9
                        },
                        {
                            keywords: ['度假村', 'resort'],
                            value: 'resort',
                            confidence: 0.8
                        },
                        {
                            keywords: ['公寓', 'apartment'],
                            value: 'apartment',
                            confidence: 0.7
                        }
                    ]
                }
            },

            // Booking.com特定预设
            'Booking': {
                languages_id_array: {
                    default: [2],  // English
                    conditions: [
                        {
                            keywords: ['中文', 'chinese'],
                            value: [4],
                            confidence: 0.8
                        },
                        {
                            keywords: ['德语', 'german', 'deutsch'],
                            value: [5],  // German
                            confidence: 0.8
                        }
                    ]
                },
                currency: {
                    default: 'EUR',
                    conditions: [
                        {
                            keywords: ['欧元', '€', 'EUR'],
                            value: 'EUR',
                            confidence: 0.9
                        },
                        {
                            keywords: ['美金', '$', 'USD'],
                            value: 'USD',
                            confidence: 0.8
                        },
                        {
                            keywords: ['英镑', '£', 'GBP'],
                            value: 'GBP',
                            confidence: 0.8
                        }
                    ]
                },
                property_type: {
                    default: 'hotel',
                    conditions: [
                        {
                            keywords: ['酒店', 'hotel'],
                            value: 'hotel',
                            confidence: 0.9
                        },
                        {
                            keywords: ['民宿', 'b&b', 'bed and breakfast'],
                            value: 'bnb',
                            confidence: 0.8
                        },
                        {
                            keywords: ['公寓', 'apartment'],
                            value: 'apartment',
                            confidence: 0.7
                        }
                    ]
                },
                region: {
                    default: 'europe',
                    conditions: [
                        {
                            keywords: ['欧洲', 'europe', 'european'],
                            value: 'europe',
                            confidence: 0.9
                        },
                        {
                            keywords: ['亚洲', 'asia', 'asian'],
                            value: 'asia',
                            confidence: 0.8
                        }
                    ]
                }
            }
        },

        // 条件评估权重
        conditionWeights: {
            keywords: 0.4,
            passengerCount: 0.3,
            userEmail: 0.5,
            extractPattern: 0.6,
            contextual: 0.2
        },

        // 预设值应用规则
        applicationRules: {
            // 最小置信度阈值
            minConfidence: 0.5,
            
            // 是否允许覆盖已有值
            allowOverride: false,
            
            // 是否启用智能推荐
            enableSmartRecommendation: true,
            
            // 最大推荐数量
            maxRecommendations: 3
        }
    };

    /**
     * 获取处理器预设值配置
     * @param {string} processorName - 处理器名称（可选）
     * @returns {Object} 预设值配置
     */
    function getProcessorPresetValues(processorName = null) {
        if (processorName) {
            return {
                common: processorPresetValues.commonPresets,
                specific: processorPresetValues.processorSpecific[processorName] || {},
                weights: processorPresetValues.conditionWeights,
                rules: processorPresetValues.applicationRules
            };
        }
        return processorPresetValues;
    }

    /**
     * 应用预设值
     * @param {Object} data - 订单数据
     * @param {string} processorName - 处理器名称
     * @param {Object} context - 上下文信息
     * @returns {Object} 应用预设值后的数据
     */
    function applyPresetValues(data, processorName, context = {}) {
        const presets = getProcessorPresetValues(processorName);
        const result = { ...data };
        const appliedPresets = [];

        // 应用通用预设
        for (const [fieldName, presetConfig] of Object.entries(presets.common)) {
            const presetValue = evaluatePresetConditions(presetConfig, data, context);
            if (presetValue.value !== null && presetValue.confidence >= presets.rules.minConfidence) {
                if (!result[fieldName] || presets.rules.allowOverride) {
                    result[fieldName] = presetValue.value;
                    appliedPresets.push({
                        field: fieldName,
                        value: presetValue.value,
                        confidence: presetValue.confidence,
                        source: 'common'
                    });
                }
            }
        }

        // 应用处理器特定预设
        for (const [fieldName, presetConfig] of Object.entries(presets.specific)) {
            const presetValue = evaluatePresetConditions(presetConfig, data, context);
            if (presetValue.value !== null && presetValue.confidence >= presets.rules.minConfidence) {
                if (!result[fieldName] || presets.rules.allowOverride) {
                    result[fieldName] = presetValue.value;
                    appliedPresets.push({
                        field: fieldName,
                        value: presetValue.value,
                        confidence: presetValue.confidence,
                        source: 'specific'
                    });
                }
            }
        }

        return {
            data: result,
            appliedPresets: appliedPresets
        };
    }

    /**
     * 评估预设条件
     * @param {Object} presetConfig - 预设配置
     * @param {Object} data - 订单数据
     * @param {Object} context - 上下文信息
     * @returns {Object} 评估结果
     */
    function evaluatePresetConditions(presetConfig, data, context) {
        let bestMatch = {
            value: presetConfig.default || null,
            confidence: presetConfig.default ? 0.3 : 0
        };

        if (!presetConfig.conditions) {
            return bestMatch;
        }

        const orderText = context.orderText || '';
        const lowerOrderText = orderText.toLowerCase();

        for (const condition of presetConfig.conditions) {
            let conditionScore = 0;
            let matchCount = 0;
            let totalChecks = 0;

            // 关键词匹配
            if (condition.keywords) {
                totalChecks++;
                const keywordMatches = condition.keywords.filter(keyword => 
                    lowerOrderText.includes(keyword.toLowerCase())
                ).length;
                
                if (keywordMatches > 0) {
                    matchCount++;
                    conditionScore += (keywordMatches / condition.keywords.length) * processorPresetValues.conditionWeights.keywords;
                }
            }

            // 乘客数量条件
            if (condition.passengerCount && data.passenger_count) {
                totalChecks++;
                const passengerCount = parseInt(data.passenger_count);
                if (passengerCount >= condition.passengerCount.min && passengerCount <= condition.passengerCount.max) {
                    matchCount++;
                    conditionScore += processorPresetValues.conditionWeights.passengerCount;
                }
            }

            // 用户邮箱条件
            if (condition.userEmail && context.userEmail) {
                totalChecks++;
                if (context.userEmail === condition.userEmail) {
                    matchCount++;
                    conditionScore += processorPresetValues.conditionWeights.userEmail;
                }
            }

            // 模式提取条件
            if (condition.extractPattern) {
                totalChecks++;
                const match = orderText.match(condition.extractPattern);
                if (match) {
                    matchCount++;
                    conditionScore += processorPresetValues.conditionWeights.extractPattern;
                    // 如果有提取模式，使用提取的值
                    if (match[1]) {
                        condition.value = parseInt(match[1]) || match[1];
                    }
                }
            }

            // 计算最终置信度
            const finalConfidence = totalChecks > 0 ? 
                (conditionScore + (condition.confidence || 0.5)) * (matchCount / totalChecks) : 
                condition.confidence || 0.5;

            // 更新最佳匹配
            if (finalConfidence > bestMatch.confidence) {
                bestMatch = {
                    value: condition.value,
                    confidence: finalConfidence,
                    additionalData: condition.additionalData || null
                };
            }
        }

        return bestMatch;
    }

    /**
     * 获取智能推荐值
     * @param {string} fieldName - 字段名
     * @param {Object} data - 订单数据
     * @param {string} processorName - 处理器名称
     * @param {Object} context - 上下文信息
     * @returns {Array} 推荐值列表
     */
    function getSmartRecommendations(fieldName, data, processorName, context = {}) {
        const presets = getProcessorPresetValues(processorName);
        if (!presets.rules.enableSmartRecommendation) {
            return [];
        }

        const recommendations = [];
        
        // 检查通用预设
        const commonPreset = presets.common[fieldName];
        if (commonPreset) {
            const evaluation = evaluatePresetConditions(commonPreset, data, context);
            if (evaluation.confidence >= presets.rules.minConfidence) {
                recommendations.push({
                    value: evaluation.value,
                    confidence: evaluation.confidence,
                    source: 'common',
                    reason: '基于通用规则推荐'
                });
            }
        }

        // 检查特定预设
        const specificPreset = presets.specific[fieldName];
        if (specificPreset) {
            const evaluation = evaluatePresetConditions(specificPreset, data, context);
            if (evaluation.confidence >= presets.rules.minConfidence) {
                recommendations.push({
                    value: evaluation.value,
                    confidence: evaluation.confidence,
                    source: 'specific',
                    reason: `基于${processorName}特定规则推荐`
                });
            }
        }

        // 按置信度排序并限制数量
        return recommendations
            .sort((a, b) => b.confidence - a.confidence)
            .slice(0, presets.rules.maxRecommendations);
    }

    /**
     * 更新预设值配置
     * @param {Object} newPresets - 新的预设配置
     */
    function updateProcessorPresetValues(newPresets) {
        Object.assign(processorPresetValues, newPresets);
        console.log('处理器预设值配置已更新');
    }

    // 暴露到全局命名空间
    window.OTA.gemini.configs.processorPresetValues = processorPresetValues;
    window.OTA.gemini.configs.getProcessorPresetValues = getProcessorPresetValues;
    window.OTA.gemini.configs.applyPresetValues = applyPresetValues;
    window.OTA.gemini.configs.evaluatePresetConditions = evaluatePresetConditions;
    window.OTA.gemini.configs.getSmartRecommendations = getSmartRecommendations;
    window.OTA.gemini.configs.updateProcessorPresetValues = updateProcessorPresetValues;

    // 向后兼容
    window.getProcessorPresetValues = getProcessorPresetValues;
    window.applyPresetValues = applyPresetValues;
    window.getSmartRecommendations = getSmartRecommendations;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerConfig('processorPresetValues', processorPresetValues, '@PROCESSOR_PRESET_VALUES_CONFIG');
        window.OTA.Registry.registerFactory('getProcessorPresetValues', getProcessorPresetValues, '@PROCESSOR_PRESET_VALUES_FACTORY');
    }

    console.log('✅ 处理器预设值配置已加载');

})();
