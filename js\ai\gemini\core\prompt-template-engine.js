/**
 * @CORE 提示词模板引擎
 * 🏷️ 标签: @PROMPT_TEMPLATE_ENGINE
 * 📝 说明: 负责Gemini AI提示词模板的构建、优化和管理
 * 🎯 功能: 通用提示词构建、上下文提示词、最优提示词选择
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.core = window.OTA.gemini.core || {};

(function() {
    'use strict';

    /**
     * 提示词模板引擎类
     * 负责构建和管理Gemini AI的提示词模板
     */
    class PromptTemplateEngine {
        constructor() {
            this.logger = window.getLogger?.() || console;
            
            // 初始化模板库
            this.initializeTemplates();
            
            // 初始化上下文规则
            this.initializeContextRules();
        }

        /**
         * 初始化提示词模板库
         */
        initializeTemplates() {
            // 基础通用模板
            this.baseTemplate = `你是一个专业的OTA订单解析助手。请仔细分析以下订单文本，提取关键信息并以JSON格式返回。

**重要规则：**
1. 必须返回有效的JSON格式
2. 所有字段都使用snake_case命名（如customer_name, pickup_date）
3. 日期格式必须是DD-MM-YYYY
4. 时间格式必须是HH:MM（24小时制）
5. 如果信息不明确或缺失，对应字段设为null
6. 价格信息保留原始货币和数值

**必需字段：**
- customer_name: 客户姓名
- customer_contact: 客户联系方式
- customer_email: 客户邮箱
- pickup: 接送地点
- dropoff: 目的地（如果是单程）
- pickup_date: 接送日期（DD-MM-YYYY格式）
- pickup_time: 接送时间（HH:MM格式）
- passenger_count: 乘客人数
- luggage_count: 行李件数
- ota_price: 订单价格（数字）
- ota_currency: 货币类型
- ota_reference_number: OTA参考号/订单号
- special_requests: 特殊要求
- flight_info: 航班信息

请分析以下订单文本：`;

            // 实时分析模板
            this.realtimeTemplate = `你是一个实时订单分析助手。请快速分析以下文本片段，识别可能的订单信息。

**分析重点：**
1. 客户信息（姓名、联系方式）
2. 时间信息（日期、时间）
3. 地点信息（接送地点）
4. 服务类型（接机、送机、包车）
5. 价格信息

**返回格式：**
返回简洁的JSON对象，只包含能够确定的字段。不确定的字段请设为null。

文本内容：`;

            // 多订单模板
            this.multiOrderTemplate = `你是一个多订单解析专家。请分析以下包含多个订单的文本，将每个订单分别解析。

**解析规则：**
1. 识别订单分隔符（如订单号、日期变化等）
2. 为每个订单提取完整信息
3. 返回订单数组格式
4. 保持字段命名一致性

**返回格式：**
{
  "orders": [
    {
      "order_index": 1,
      "customer_name": "...",
      "pickup_date": "...",
      // ... 其他字段
    },
    {
      "order_index": 2,
      // ... 第二个订单信息
    }
  ]
}

请分析以下多订单文本：`;

            // 图片分析模板
            this.imageAnalysisTemplate = `你是一个专业的图片订单分析助手。请仔细分析这张图片中的订单信息。

**分析重点：**
1. 文字识别：提取图片中的所有文字信息
2. 结构识别：识别表格、列表等结构化信息
3. 关键信息提取：客户信息、时间、地点、价格等
4. 格式标准化：将提取的信息标准化为JSON格式

**特别注意：**
- 手写文字可能不够清晰，请尽力识别
- 注意日期格式的转换
- 价格信息要保留货币符号
- 如果图片模糊或信息不完整，相应字段设为null

请分析这张订单图片：`;
        }

        /**
         * 初始化上下文规则
         */
        initializeContextRules() {
            // OTA平台特定规则
            this.otaContextRules = {
                'fliggy': {
                    emphasis: '飞猪订单通常包含详细的航班信息和酒店信息，注意提取确认号。',
                    patterns: ['确认号', '飞猪', '订单号'],
                    dateFormat: 'YYYY-MM-DD'
                },
                'ctrip': {
                    emphasis: '携程订单格式规范，注意提取产品编号和联系人信息。',
                    patterns: ['携程', '产品编号', '联系人'],
                    dateFormat: 'YYYY-MM-DD'
                },
                'klook': {
                    emphasis: 'Klook订单通常是英文格式，注意提取Booking Reference。',
                    patterns: ['Booking Reference', 'Klook', 'Confirmation'],
                    dateFormat: 'DD/MM/YYYY'
                },
                'kkday': {
                    emphasis: 'KKday订单可能包含中英文混合信息，注意提取订单编号。',
                    patterns: ['订单编号', 'KKday', 'Order Number'],
                    dateFormat: 'YYYY/MM/DD'
                }
            };

            // 服务类型上下文
            this.serviceContextRules = {
                'airport_pickup': {
                    emphasis: '接机服务重点关注航班信息、到达时间和接机地点。',
                    requiredFields: ['flight_info', 'pickup_time', 'pickup']
                },
                'airport_dropoff': {
                    emphasis: '送机服务重点关注出发时间、航班信息和出发地点。',
                    requiredFields: ['departure_time', 'flight_info', 'pickup']
                },
                'charter': {
                    emphasis: '包车服务重点关注行程安排、时长和多个地点。',
                    requiredFields: ['pickup', 'dropoff', 'duration', 'itinerary']
                }
            };
        }

        /**
         * 构建通用提示词（从原gemini-service.js迁移）
         * @param {string} orderText - 订单文本
         * @param {Object} options - 选项参数
         * @returns {string} 构建的提示词
         */
        buildUniversalPrompt(orderText, options = {}) {
            try {
                let prompt = this.baseTemplate;

                // 添加OTA特定上下文
                if (options.otaType && this.otaContextRules[options.otaType]) {
                    const otaRule = this.otaContextRules[options.otaType];
                    prompt += `\n\n**${options.otaType.toUpperCase()}特定注意事项：**\n${otaRule.emphasis}`;
                }

                // 添加服务类型上下文
                if (options.serviceType && this.serviceContextRules[options.serviceType]) {
                    const serviceRule = this.serviceContextRules[options.serviceType];
                    prompt += `\n\n**服务类型注意事项：**\n${serviceRule.emphasis}`;
                }

                // 添加实时分析标记
                if (options.isRealtime) {
                    prompt += `\n\n**实时分析模式：**\n请快速分析，重点提取明确的信息，不确定的字段设为null。`;
                }

                // 添加ID映射提示
                if (options.includeIdMapping) {
                    prompt += this.buildIdMappingPrompt();
                }

                // 添加订单文本
                prompt += `\n\n---\n订单文本：\n${orderText}\n---`;

                // 添加JSON格式要求
                prompt += `\n\n请返回标准JSON格式，确保所有字段名使用snake_case。`;

                return prompt;

            } catch (error) {
                this.logger.logError('构建通用提示词失败', error);
                return this.baseTemplate + `\n\n${orderText}`;
            }
        }

        /**
         * 获取上下文提示词（从原gemini-service.js迁移）
         * @param {string} context - 上下文类型
         * @param {Object} options - 选项参数
         * @returns {string} 上下文提示词
         */
        getContextualPrompt(context, options = {}) {
            try {
                switch (context) {
                    case 'realtime':
                        return this.realtimeTemplate;
                    
                    case 'multi_order':
                        return this.multiOrderTemplate;
                    
                    case 'image_analysis':
                        return this.imageAnalysisTemplate;
                    
                    case 'ota_specific':
                        return this.buildOtaSpecificPrompt(options.otaType);
                    
                    case 'service_specific':
                        return this.buildServiceSpecificPrompt(options.serviceType);
                    
                    default:
                        return this.baseTemplate;
                }
            } catch (error) {
                this.logger.logError('获取上下文提示词失败', error);
                return this.baseTemplate;
            }
        }

        /**
         * 选择最优提示词（从原gemini-service.js迁移）
         * @param {string} orderText - 订单文本
         * @param {Object} analysisResult - 分析结果
         * @returns {string} 最优提示词
         */
        selectOptimalPrompt(orderText, analysisResult = {}) {
            try {
                const textLength = orderText.length;
                const hasMultipleOrders = this.detectMultipleOrders(orderText);
                const detectedOta = analysisResult.detectedOta || null;
                const detectedService = analysisResult.detectedService || null;

                // 多订单检测
                if (hasMultipleOrders) {
                    this.logger.log('选择多订单模板', 'info');
                    return this.getContextualPrompt('multi_order');
                }

                // 实时分析（短文本）
                if (textLength < 200) {
                    this.logger.log('选择实时分析模板', 'info');
                    return this.getContextualPrompt('realtime');
                }

                // OTA特定模板
                if (detectedOta) {
                    this.logger.log(`选择${detectedOta}特定模板`, 'info');
                    return this.buildUniversalPrompt(orderText, { 
                        otaType: detectedOta,
                        includeIdMapping: true 
                    });
                }

                // 服务特定模板
                if (detectedService) {
                    this.logger.log(`选择${detectedService}服务模板`, 'info');
                    return this.buildUniversalPrompt(orderText, { 
                        serviceType: detectedService,
                        includeIdMapping: true 
                    });
                }

                // 默认通用模板
                this.logger.log('选择通用模板', 'info');
                return this.buildUniversalPrompt(orderText, { includeIdMapping: true });

            } catch (error) {
                this.logger.logError('选择最优提示词失败', error);
                return this.buildUniversalPrompt(orderText);
            }
        }

        /**
         * 构建OTA特定提示词
         * @param {string} otaType - OTA类型
         * @returns {string} OTA特定提示词
         */
        buildOtaSpecificPrompt(otaType) {
            if (!otaType || !this.otaContextRules[otaType]) {
                return this.baseTemplate;
            }

            const otaRule = this.otaContextRules[otaType];
            let prompt = this.baseTemplate;

            prompt += `\n\n**${otaType.toUpperCase()}平台特定规则：**\n`;
            prompt += `${otaRule.emphasis}\n`;
            prompt += `关键词模式：${otaRule.patterns.join(', ')}\n`;
            prompt += `日期格式：${otaRule.dateFormat}\n`;

            return prompt;
        }

        /**
         * 构建服务特定提示词
         * @param {string} serviceType - 服务类型
         * @returns {string} 服务特定提示词
         */
        buildServiceSpecificPrompt(serviceType) {
            if (!serviceType || !this.serviceContextRules[serviceType]) {
                return this.baseTemplate;
            }

            const serviceRule = this.serviceContextRules[serviceType];
            let prompt = this.baseTemplate;

            prompt += `\n\n**${serviceType.toUpperCase()}服务特定规则：**\n`;
            prompt += `${serviceRule.emphasis}\n`;
            prompt += `重点字段：${serviceRule.requiredFields.join(', ')}\n`;

            return prompt;
        }

        /**
         * 构建ID映射提示词
         * @returns {string} ID映射提示词
         */
        buildIdMappingPrompt() {
            return `\n\n**ID字段映射规则：**
- car_type_id: 根据乘客人数选择（1-3人=1, 4人=37, 5人=15, 6-7人=16, 8+人=17）
- sub_category_id: 服务类型（接机=2, 送机=3, 包车=4）
- driving_region_id: 驾驶区域（吉隆坡=1, 其他根据地点确定）
- backend_user_id: 后台用户（根据OTA类型或默认值确定）
- languages_id_array: 语言选择（中文姓名=4, 英文姓名=2）`;
        }

        /**
         * 检测多订单
         * @param {string} text - 文本内容
         * @returns {boolean} 是否包含多个订单
         */
        detectMultipleOrders(text) {
            const orderIndicators = [
                /订单\s*[号#]\s*[:：]\s*\w+.*?订单\s*[号#]\s*[:：]\s*\w+/s,
                /\d{4}-\d{2}-\d{2}.*?\d{4}-\d{2}-\d{2}/s,
                /客户\s*[:：].*?客户\s*[:：]/s,
                /姓名\s*[:：].*?姓名\s*[:：]/s
            ];

            return orderIndicators.some(pattern => pattern.test(text));
        }

        /**
         * 优化提示词长度
         * @param {string} prompt - 原始提示词
         * @param {number} maxLength - 最大长度
         * @returns {string} 优化后的提示词
         */
        optimizePromptLength(prompt, maxLength = 4000) {
            if (prompt.length <= maxLength) {
                return prompt;
            }

            // 保留核心部分，压缩示例和说明
            const corePrompt = prompt.substring(0, maxLength * 0.8);
            const suffix = '\n\n请按照上述规则分析订单文本并返回JSON格式结果。';
            
            return corePrompt + suffix;
        }

        /**
         * 获取模板引擎统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                totalTemplates: 4, // base, realtime, multi, image
                otaRules: Object.keys(this.otaContextRules).length,
                serviceRules: Object.keys(this.serviceContextRules).length,
                supportedContexts: ['realtime', 'multi_order', 'image_analysis', 'ota_specific', 'service_specific']
            };
        }
    }

    // 暴露到全局命名空间
    window.OTA.gemini.core.PromptTemplateEngine = PromptTemplateEngine;

    // 创建单例实例
    let promptTemplateEngineInstance = null;

    /**
     * 获取提示词模板引擎单例实例
     * @returns {PromptTemplateEngine} 提示词模板引擎实例
     */
    function getPromptTemplateEngine() {
        if (!promptTemplateEngineInstance) {
            promptTemplateEngineInstance = new PromptTemplateEngine();
        }
        return promptTemplateEngineInstance;
    }

    // 暴露工厂函数
    window.OTA.gemini.core.getPromptTemplateEngine = getPromptTemplateEngine;

    // 注册到服务注册中心
    if (window.OTA?.gemini?.core?.ServiceRegistry) {
        window.OTA.gemini.core.ServiceRegistry.register('promptTemplateEngine', getPromptTemplateEngine, '@PROMPT_TEMPLATE_ENGINE');
    }

    console.log('✅ 提示词模板引擎模块已加载');

})();
