<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API密钥管理器测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #4a5568;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f7fafc;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        .success {
            background: #c6f6d5;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }
        .error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #fc8181;
        }
        .info {
            background: #bee3f8;
            color: #2a4365;
            border: 1px solid #90cdf4;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #5a67d8;
        }
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .stats-table th, .stats-table td {
            border: 1px solid #e2e8f0;
            padding: 8px;
            text-align: left;
        }
        .stats-table th {
            background: #edf2f7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔑 API密钥管理器测试套件</h1>
        
        <div class="test-section">
            <h2>📋 测试控制面板</h2>
            <button onclick="runAllTests()">运行所有测试</button>
            <button onclick="testBasicFunctionality()">基础功能测试</button>
            <button onclick="testServiceIntegration()">服务集成测试</button>
            <button onclick="testBackwardCompatibility()">向后兼容性测试</button>
            <button onclick="clearResults()">清空结果</button>
        </div>

        <div class="test-section">
            <h2>🧪 测试结果</h2>
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h2>📊 API密钥管理器状态</h2>
            <div id="managerStatus"></div>
        </div>

        <div class="test-section">
            <h2>📈 访问统计</h2>
            <div id="accessStats"></div>
        </div>
    </div>

    <!-- 加载必要的依赖 -->
    <script src="../js/services/logger.js"></script>
    <script src="../js/core/api-key-manager.js"></script>
    <script src="../js/ai/kimi-service.js"></script>

    <script>
        // 测试结果容器
        const resultsContainer = document.getElementById('testResults');
        const statusContainer = document.getElementById('managerStatus');
        const statsContainer = document.getElementById('accessStats');

        // 测试结果记录
        let testResults = [];

        /**
         * 添加测试结果
         */
        function addTestResult(testName, success, message, details = null) {
            const result = {
                testName,
                success,
                message,
                details,
                timestamp: new Date().toLocaleString()
            };
            
            testResults.push(result);
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${success ? 'success' : 'error'}`;
            resultDiv.innerHTML = `
                <strong>${success ? '✅' : '❌'} ${testName}</strong><br>
                ${message}
                ${details ? `<br><small>详情: ${JSON.stringify(details, null, 2)}</small>` : ''}
                <br><small>时间: ${result.timestamp}</small>
            `;
            
            resultsContainer.appendChild(resultDiv);
        }

        /**
         * 添加信息日志
         */
        function addInfo(message) {
            const infoDiv = document.createElement('div');
            infoDiv.className = 'test-result info';
            infoDiv.innerHTML = `<strong>ℹ️ 信息</strong><br>${message}`;
            resultsContainer.appendChild(infoDiv);
        }

        /**
         * 清空测试结果
         */
        function clearResults() {
            resultsContainer.innerHTML = '';
            testResults = [];
            addInfo('测试结果已清空');
        }

        /**
         * 更新管理器状态显示
         */
        function updateManagerStatus() {
            try {
                const manager = window.OTA?.apiKeyManager;
                if (!manager) {
                    statusContainer.innerHTML = '<div class="error">❌ API密钥管理器未找到</div>';
                    return;
                }

                const allKeys = manager.getAllApiKeys();
                const availableServices = manager.getAvailableServices();
                
                let statusHTML = '<h3>🔑 密钥状态</h3>';
                statusHTML += '<table class="stats-table">';
                statusHTML += '<tr><th>服务</th><th>状态</th><th>来源</th><th>描述</th></tr>';
                
                for (const [service, info] of Object.entries(allKeys)) {
                    statusHTML += `
                        <tr>
                            <td>${service}</td>
                            <td>${info.available ? '✅ 可用' : '❌ 不可用'}</td>
                            <td>${info.source}</td>
                            <td>${info.description}</td>
                        </tr>
                    `;
                }
                
                statusHTML += '</table>';
                statusHTML += `<p><strong>可用服务数量:</strong> ${availableServices.length}</p>`;
                
                statusContainer.innerHTML = statusHTML;
                
            } catch (error) {
                statusContainer.innerHTML = `<div class="error">❌ 获取状态失败: ${error.message}</div>`;
            }
        }

        /**
         * 更新访问统计显示
         */
        function updateAccessStats() {
            try {
                const manager = window.OTA?.apiKeyManager;
                if (!manager) return;

                const stats = manager.getAccessStats();
                
                let statsHTML = '<h3>📊 访问统计</h3>';
                statsHTML += `<p><strong>总访问次数:</strong> ${stats.totalAccess}</p>`;
                statsHTML += `<p><strong>可用服务:</strong> ${stats.availableServices}/${stats.configuredServices}</p>`;
                
                if (Object.keys(stats.keyAccess).length > 0) {
                    statsHTML += '<h4>各服务访问次数:</h4>';
                    statsHTML += '<table class="stats-table">';
                    statsHTML += '<tr><th>服务</th><th>访问次数</th><th>最后访问</th></tr>';
                    
                    for (const [service, count] of Object.entries(stats.keyAccess)) {
                        const lastAccess = stats.lastAccess[service] || '未知';
                        statsHTML += `
                            <tr>
                                <td>${service}</td>
                                <td>${count}</td>
                                <td>${new Date(lastAccess).toLocaleString()}</td>
                            </tr>
                        `;
                    }
                    
                    statsHTML += '</table>';
                }
                
                statsContainer.innerHTML = statsHTML;
                
            } catch (error) {
                statsContainer.innerHTML = `<div class="error">❌ 获取统计失败: ${error.message}</div>`;
            }
        }

        /**
         * 基础功能测试
         */
        function testBasicFunctionality() {
            addInfo('开始基础功能测试...');
            
            try {
                // 测试1: 管理器存在性
                const manager = window.OTA?.apiKeyManager;
                if (!manager) {
                    addTestResult('管理器存在性', false, 'API密钥管理器未找到');
                    return;
                }
                addTestResult('管理器存在性', true, 'API密钥管理器已正确加载');

                // 测试2: 获取Gemini密钥
                const geminiKey = manager.getApiKey('gemini');
                addTestResult('Gemini密钥获取', !!geminiKey, 
                    geminiKey ? '成功获取Gemini API密钥' : '未能获取Gemini API密钥',
                    { keyLength: geminiKey?.length || 0 });

                // 测试3: 获取Kimi密钥
                const kimiKey = manager.getApiKey('kimi');
                addTestResult('Kimi密钥获取', !!kimiKey, 
                    kimiKey ? '成功获取Kimi API密钥' : '未能获取Kimi API密钥',
                    { keyLength: kimiKey?.length || 0 });

                // 测试4: 检查密钥存在性
                const hasGemini = manager.hasApiKey('gemini');
                const hasKimi = manager.hasApiKey('kimi');
                addTestResult('密钥存在性检查', hasGemini && hasKimi, 
                    `Gemini: ${hasGemini ? '存在' : '不存在'}, Kimi: ${hasKimi ? '存在' : '不存在'}`);

                // 测试5: 运行时密钥设置
                const testKey = 'test-runtime-key-12345';
                const setResult = manager.setApiKey('gemini', testKey);
                const retrievedKey = manager.getApiKey('gemini');
                addTestResult('运行时密钥设置', setResult && retrievedKey === testKey, 
                    setResult ? '运行时密钥设置成功' : '运行时密钥设置失败');

                // 测试6: 获取所有密钥信息
                const allKeys = manager.getAllApiKeys();
                addTestResult('获取所有密钥信息', !!allKeys && Object.keys(allKeys).length > 0, 
                    `获取到 ${Object.keys(allKeys || {}).length} 个服务的密钥信息`);

            } catch (error) {
                addTestResult('基础功能测试', false, `测试过程中发生错误: ${error.message}`);
            }
        }

        /**
         * 服务集成测试
         */
        function testServiceIntegration() {
            addInfo('开始服务集成测试...');
            
            try {
                // 测试Kimi服务集成
                if (window.KimiService) {
                    const kimiService = new window.KimiService();
                    const hasApiKey = !!kimiService.apiKey;
                    addTestResult('Kimi服务集成', hasApiKey, 
                        hasApiKey ? 'Kimi服务成功获取API密钥' : 'Kimi服务未能获取API密钥',
                        { apiKeyLength: kimiService.apiKey?.length || 0 });
                } else {
                    addTestResult('Kimi服务集成', false, 'KimiService类未找到');
                }

                // 测试OTA Registry集成
                const registry = window.OTA?.Registry;
                if (registry) {
                    const managerFromRegistry = registry.getService('apiKeyManager');
                    addTestResult('Registry集成', !!managerFromRegistry, 
                        managerFromRegistry ? 'API密钥管理器已正确注册到Registry' : 'Registry中未找到API密钥管理器');
                } else {
                    addTestResult('Registry集成', false, 'OTA Registry未找到');
                }

            } catch (error) {
                addTestResult('服务集成测试', false, `测试过程中发生错误: ${error.message}`);
            }
        }

        /**
         * 向后兼容性测试
         */
        function testBackwardCompatibility() {
            addInfo('开始向后兼容性测试...');
            
            try {
                // 测试全局访问方式
                const globalManager = window.OTA?.apiKeyManager;
                addTestResult('全局访问', !!globalManager, 
                    globalManager ? '可通过window.OTA.apiKeyManager访问' : '全局访问失败');

                // 测试工厂函数访问
                if (window.OTA?.Registry) {
                    const factoryManager = window.OTA.Registry.getFactory('getApiKeyManager');
                    if (factoryManager) {
                        const managerInstance = factoryManager();
                        addTestResult('工厂函数访问', !!managerInstance, 
                            managerInstance ? '工厂函数正常工作' : '工厂函数返回空值');
                    } else {
                        addTestResult('工厂函数访问', false, '工厂函数未注册');
                    }
                }

                // 测试降级机制
                const manager = window.OTA?.apiKeyManager;
                if (manager) {
                    // 清除运行时密钥，测试降级到默认值
                    manager.runtimeKeys?.clear?.();
                    const defaultKey = manager.getApiKey('gemini');
                    addTestResult('降级机制', !!defaultKey, 
                        defaultKey ? '成功降级到默认密钥' : '降级机制失败');
                }

            } catch (error) {
                addTestResult('向后兼容性测试', false, `测试过程中发生错误: ${error.message}`);
            }
        }

        /**
         * 运行所有测试
         */
        function runAllTests() {
            clearResults();
            addInfo('开始运行完整测试套件...');
            
            testBasicFunctionality();
            testServiceIntegration();
            testBackwardCompatibility();
            
            // 更新状态显示
            setTimeout(() => {
                updateManagerStatus();
                updateAccessStats();
                
                // 测试总结
                const successCount = testResults.filter(r => r.success).length;
                const totalCount = testResults.length;
                const successRate = totalCount > 0 ? (successCount / totalCount * 100).toFixed(1) : 0;
                
                addInfo(`测试完成！成功率: ${successRate}% (${successCount}/${totalCount})`);
            }, 100);
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            addInfo('API密钥管理器测试套件已加载');
            updateManagerStatus();
            updateAccessStats();
        });
    </script>
</body>
</html>
