/**
 * @CONFIG_FILE 预设值配置
 * 🏷️ 标签: @PRESET_VALUES @OTA_CONFIG
 * 📝 说明: 定义所有OTA平台的默认值和预设配置
 * 🎯 目标: 统一管理不同OTA平台的预设值，提高订单处理效率和准确性
 */

(function() {
    'use strict';
    
    // 确保OTA命名空间存在
    window.OTA = window.OTA || {};
    window.OTA.gemini = window.OTA.gemini || {};
    window.OTA.gemini.configs = window.OTA.gemini.configs || {};
    
    /**
     * 预设值配置类
     * 管理所有OTA平台的默认值和预设配置
     */
    class PresetValuesConfig {
        constructor() {
            this.logger = window.getLogger?.() || console;
            
            // 通用预设值（所有OTA平台共享）
            this.commonPresets = {
                // 服务类型预设
                service_type_id: 2, // 默认接机服务
                
                // 车型预设
                car_type_id: 1, // 默认 Comfort 5 Seater
                
                // 语言预设
                languages_id_array: { "0": "2" }, // 默认英语
                
                // 乘客数量预设
                passenger_count: 1,
                
                // 行李数量预设
                luggage_count: 1,
                
                // 货币预设
                currency: 'MYR',
                
                // 负责人预设
                responsible_person_id: 1, // 默认第一个用户
                
                // 特殊要求预设
                special_requirements: '',
                
                // 状态预设
                status: 'pending',
                
                // 优先级预设
                priority: 'normal'
            };
            
            // OTA平台特定预设值
            this.otaSpecificPresets = {
                'fliggy': {
                    // 飞猪特定预设
                    ota: 'fliggy',
                    service_type_id: 2, // 接机服务
                    car_type_id: 1,     // Comfort 5 Seater
                    languages_id_array: { "0": "4" }, // 中文优先
                    currency: 'CNY',
                    responsible_person_id: 1,
                    default_pickup_time: '09:00',
                    timezone: 'Asia/Shanghai',
                    payment_method: 'prepaid',
                    booking_source: 'fliggy_app',
                    customer_type: 'tourist',
                    service_level: 'standard'
                },
                
                'ctrip': {
                    // 携程特定预设
                    ota: 'ctrip',
                    service_type_id: 2, // 接机服务
                    car_type_id: 1,     // Comfort 5 Seater
                    languages_id_array: { "0": "4" }, // 中文优先
                    currency: 'CNY',
                    responsible_person_id: 1,
                    default_pickup_time: '10:00',
                    timezone: 'Asia/Shanghai',
                    payment_method: 'prepaid',
                    booking_source: 'ctrip_website',
                    customer_type: 'business',
                    service_level: 'premium'
                },
                
                'kkday': {
                    // KKday特定预设
                    ota: 'kkday',
                    service_type_id: 2, // 接机服务
                    car_type_id: 1,     // Comfort 5 Seater
                    languages_id_array: { "0": "4", "1": "2" }, // 中英双语
                    currency: 'TWD',
                    responsible_person_id: 1,
                    default_pickup_time: '11:00',
                    timezone: 'Asia/Taipei',
                    payment_method: 'prepaid',
                    booking_source: 'kkday_app',
                    customer_type: 'tourist',
                    service_level: 'standard'
                },
                
                'klook': {
                    // Klook特定预设
                    ota: 'klook',
                    service_type_id: 2, // 接机服务
                    car_type_id: 1,     // Comfort 5 Seater
                    languages_id_array: { "0": "2" }, // 英语优先
                    currency: 'USD',
                    responsible_person_id: 1,
                    default_pickup_time: '12:00',
                    timezone: 'Asia/Singapore',
                    payment_method: 'prepaid',
                    booking_source: 'klook_website',
                    customer_type: 'tourist',
                    service_level: 'premium'
                },
                
                'agoda': {
                    // Agoda特定预设
                    ota: 'agoda',
                    service_type_id: 2, // 接机服务
                    car_type_id: 1,     // Comfort 5 Seater
                    languages_id_array: { "0": "2" }, // 英语优先
                    currency: 'USD',
                    responsible_person_id: 1,
                    default_pickup_time: '13:00',
                    timezone: 'Asia/Bangkok',
                    payment_method: 'prepaid',
                    booking_source: 'agoda_app',
                    customer_type: 'tourist',
                    service_level: 'standard'
                },
                
                'booking': {
                    // Booking.com特定预设
                    ota: 'booking',
                    service_type_id: 2, // 接机服务
                    car_type_id: 1,     // Comfort 5 Seater
                    languages_id_array: { "0": "2" }, // 英语优先
                    currency: 'EUR',
                    responsible_person_id: 1,
                    default_pickup_time: '14:00',
                    timezone: 'Europe/Amsterdam',
                    payment_method: 'prepaid',
                    booking_source: 'booking_website',
                    customer_type: 'tourist',
                    service_level: 'premium'
                },
                
                'jrcoach': {
                    // JRCoach特定预设
                    ota: 'jrcoach',
                    service_type_id: 2, // 接机服务
                    car_type_id: 1,     // Comfort 5 Seater
                    languages_id_array: { "0": "5" }, // 日语优先
                    currency: 'JPY',
                    responsible_person_id: 1,
                    default_pickup_time: '15:00',
                    timezone: 'Asia/Tokyo',
                    payment_method: 'prepaid',
                    booking_source: 'jrcoach_app',
                    customer_type: 'tourist',
                    service_level: 'standard'
                },
                
                'generic': {
                    // 通用预设（降级使用）
                    ota: 'generic',
                    service_type_id: 2, // 接机服务
                    car_type_id: 1,     // Comfort 5 Seater
                    languages_id_array: { "0": "2" }, // 英语
                    currency: 'MYR',
                    responsible_person_id: 1,
                    default_pickup_time: '09:00',
                    timezone: 'Asia/Kuala_Lumpur',
                    payment_method: 'cash',
                    booking_source: 'unknown',
                    customer_type: 'unknown',
                    service_level: 'standard'
                }
            };
            
            // 条件预设规则
            this.conditionalPresets = {
                // 基于乘客数量的车型选择
                passengerBasedCarType: {
                    1: 1,  // 1人 -> Comfort 5 Seater
                    2: 1,  // 2人 -> Comfort 5 Seater
                    3: 1,  // 3人 -> Comfort 5 Seater
                    4: 1,  // 4人 -> Comfort 5 Seater
                    5: 2,  // 5人 -> Premium 7 Seater
                    6: 2,  // 6人 -> Premium 7 Seater
                    7: 2,  // 7人 -> Premium 7 Seater
                    8: 3   // 8人+ -> Luxury Van
                },
                
                // 基于服务类型的默认时间
                serviceTypeBasedTime: {
                    2: '09:00', // 接机
                    3: '18:00', // 送机
                    4: '10:00'  // 包车
                },
                
                // 基于OTA渠道的负责人
                otaBasedResponsiblePerson: {
                    'fliggy': 1,
                    'ctrip': 1,
                    'kkday': 1,
                    'klook': 1,
                    'agoda': 1,
                    'booking': 1,
                    'jrcoach': 1,
                    'generic': 1
                },
                
                // 基于语言的服务级别
                languageBasedServiceLevel: {
                    'zh': 'premium',  // 中文客户提供高级服务
                    'en': 'standard', // 英文客户标准服务
                    'ja': 'premium',  // 日文客户提供高级服务
                    'ko': 'standard'  // 韩文客户标准服务
                }
            };
            
            // 动态预设生成器
            this.dynamicPresetGenerators = {
                // 生成参考号前缀
                generateReferencePrefix: (otaChannel) => {
                    const prefixes = {
                        'fliggy': 'FG',
                        'ctrip': 'CT',
                        'kkday': 'KK',
                        'klook': 'KL',
                        'agoda': 'AG',
                        'booking': 'BK',
                        'jrcoach': 'JR',
                        'generic': 'GN'
                    };
                    return prefixes[otaChannel] || 'GN';
                },
                
                // 生成默认订单号
                generateDefaultOrderNumber: (otaChannel) => {
                    const prefix = this.dynamicPresetGenerators.generateReferencePrefix(otaChannel);
                    const timestamp = Date.now().toString().slice(-6);
                    const random = Math.random().toString(36).substr(2, 3).toUpperCase();
                    return `${prefix}${timestamp}${random}`;
                },
                
                // 生成默认客户名称
                generateDefaultCustomerName: (otaChannel) => {
                    const names = {
                        'fliggy': '飞猪客户',
                        'ctrip': '携程客户',
                        'kkday': 'KKday客戶',
                        'klook': 'Klook Customer',
                        'agoda': 'Agoda Guest',
                        'booking': 'Booking Guest',
                        'jrcoach': 'JRCoach乗客',
                        'generic': 'Guest'
                    };
                    return names[otaChannel] || 'Guest';
                },
                
                // 生成默认联系电话
                generateDefaultPhone: (otaChannel) => {
                    const phones = {
                        'fliggy': '+86-138-0000-0000',
                        'ctrip': '+86-139-0000-0000',
                        'kkday': '+886-9-0000-0000',
                        'klook': '+65-9000-0000',
                        'agoda': '+66-9-0000-0000',
                        'booking': '+31-6-0000-0000',
                        'jrcoach': '+81-90-0000-0000',
                        'generic': '+60-12-000-0000'
                    };
                    return phones[otaChannel] || '+60-12-000-0000';
                }
            };
            
            this.logger.log('🎯 预设值配置初始化完成', 'info');
        }
        
        /**
         * 获取OTA平台的预设值
         * @param {string} otaChannel - OTA渠道名称
         * @param {Object} context - 上下文信息（可选）
         * @returns {Object} 预设值配置
         */
        getPresetValues(otaChannel, context = {}) {
            const normalizedChannel = this.normalizeOTAChannel(otaChannel);
            const specificPresets = this.otaSpecificPresets[normalizedChannel] || this.otaSpecificPresets['generic'];
            
            // 合并通用预设和特定预设
            const basePresets = { ...this.commonPresets, ...specificPresets };
            
            // 应用条件预设
            const conditionalPresets = this.applyConditionalPresets(basePresets, context, normalizedChannel);
            
            // 应用动态预设
            const dynamicPresets = this.applyDynamicPresets(conditionalPresets, normalizedChannel, context);
            
            return dynamicPresets;
        }
        
        /**
         * 应用条件预设
         * @param {Object} basePresets - 基础预设
         * @param {Object} context - 上下文信息
         * @param {string} otaChannel - OTA渠道
         * @returns {Object} 应用条件预设后的配置
         * @private
         */
        applyConditionalPresets(basePresets, context, otaChannel) {
            const result = { ...basePresets };
            
            // 基于乘客数量调整车型
            if (context.passenger_count && this.conditionalPresets.passengerBasedCarType[context.passenger_count]) {
                result.car_type_id = this.conditionalPresets.passengerBasedCarType[context.passenger_count];
            }
            
            // 基于服务类型调整默认时间
            if (context.service_type_id && this.conditionalPresets.serviceTypeBasedTime[context.service_type_id]) {
                result.default_pickup_time = this.conditionalPresets.serviceTypeBasedTime[context.service_type_id];
            }
            
            // 基于OTA渠道调整负责人
            if (this.conditionalPresets.otaBasedResponsiblePerson[otaChannel]) {
                result.responsible_person_id = this.conditionalPresets.otaBasedResponsiblePerson[otaChannel];
            }
            
            // 基于语言调整服务级别
            if (context.primary_language && this.conditionalPresets.languageBasedServiceLevel[context.primary_language]) {
                result.service_level = this.conditionalPresets.languageBasedServiceLevel[context.primary_language];
            }
            
            return result;
        }
        
        /**
         * 应用动态预设
         * @param {Object} basePresets - 基础预设
         * @param {string} otaChannel - OTA渠道
         * @param {Object} context - 上下文信息
         * @returns {Object} 应用动态预设后的配置
         * @private
         */
        applyDynamicPresets(basePresets, otaChannel, context) {
            const result = { ...basePresets };
            
            // 如果没有参考号，生成默认参考号
            if (!context.ota_reference_number) {
                result.ota_reference_number = this.dynamicPresetGenerators.generateDefaultOrderNumber(otaChannel);
            }
            
            // 如果没有客户名称，生成默认名称
            if (!context.customer_name) {
                result.customer_name = this.dynamicPresetGenerators.generateDefaultCustomerName(otaChannel);
            }
            
            // 如果没有联系电话，生成默认电话
            if (!context.customer_phone) {
                result.customer_phone = this.dynamicPresetGenerators.generateDefaultPhone(otaChannel);
            }
            
            // 生成参考号前缀
            result.reference_prefix = this.dynamicPresetGenerators.generateReferencePrefix(otaChannel);
            
            return result;
        }
        
        /**
         * 标准化OTA渠道名称
         * @param {string} channel - 原始渠道名称
         * @returns {string} 标准化后的渠道名称
         * @private
         */
        normalizeOTAChannel(channel) {
            if (!channel) return 'generic';
            
            const normalized = channel.toLowerCase().trim();
            const channelMap = {
                'fliggy': 'fliggy',
                '飞猪': 'fliggy',
                'ctrip': 'ctrip',
                '携程': 'ctrip',
                'kkday': 'kkday',
                'klook': 'klook',
                'agoda': 'agoda',
                'booking': 'booking',
                'booking.com': 'booking',
                'jrcoach': 'jrcoach',
                'jr coach': 'jrcoach'
            };
            
            return channelMap[normalized] || 'generic';
        }

        /**
         * 获取特定字段的预设值
         * @param {string} fieldName - 字段名称
         * @param {string} otaChannel - OTA渠道
         * @param {Object} context - 上下文信息
         * @returns {any} 字段预设值
         */
        getFieldPreset(fieldName, otaChannel, context = {}) {
            const presets = this.getPresetValues(otaChannel, context);
            return presets[fieldName];
        }

        /**
         * 批量获取字段预设值
         * @param {Array} fieldNames - 字段名称数组
         * @param {string} otaChannel - OTA渠道
         * @param {Object} context - 上下文信息
         * @returns {Object} 字段预设值对象
         */
        getBatchFieldPresets(fieldNames, otaChannel, context = {}) {
            const presets = this.getPresetValues(otaChannel, context);
            const result = {};

            fieldNames.forEach(fieldName => {
                if (presets.hasOwnProperty(fieldName)) {
                    result[fieldName] = presets[fieldName];
                }
            });

            return result;
        }

        /**
         * 更新OTA平台预设值
         * @param {string} otaChannel - OTA渠道
         * @param {Object} newPresets - 新的预设值
         */
        updateOTAPresets(otaChannel, newPresets) {
            const normalizedChannel = this.normalizeOTAChannel(otaChannel);

            if (!this.otaSpecificPresets[normalizedChannel]) {
                this.otaSpecificPresets[normalizedChannel] = {};
            }

            this.otaSpecificPresets[normalizedChannel] = {
                ...this.otaSpecificPresets[normalizedChannel],
                ...newPresets
            };

            this.logger.log(`✅ 更新OTA预设值: ${normalizedChannel}`, 'info');
        }

        /**
         * 添加条件预设规则
         * @param {string} ruleName - 规则名称
         * @param {Object} ruleConfig - 规则配置
         */
        addConditionalRule(ruleName, ruleConfig) {
            this.conditionalPresets[ruleName] = ruleConfig;
            this.logger.log(`✅ 添加条件预设规则: ${ruleName}`, 'info');
        }

        /**
         * 移除条件预设规则
         * @param {string} ruleName - 规则名称
         */
        removeConditionalRule(ruleName) {
            if (this.conditionalPresets[ruleName]) {
                delete this.conditionalPresets[ruleName];
                this.logger.log(`🗑️ 移除条件预设规则: ${ruleName}`, 'info');
            }
        }

        /**
         * 验证预设值配置
         * @param {string} otaChannel - OTA渠道
         * @returns {Object} 验证结果
         */
        validatePresets(otaChannel) {
            const normalizedChannel = this.normalizeOTAChannel(otaChannel);
            const presets = this.getPresetValues(normalizedChannel);

            const validation = {
                valid: true,
                errors: [],
                warnings: [],
                completeness: 0
            };

            // 检查必要字段
            const requiredFields = ['service_type_id', 'car_type_id', 'languages_id_array', 'responsible_person_id'];
            requiredFields.forEach(field => {
                if (!presets[field]) {
                    validation.valid = false;
                    validation.errors.push(`缺少必要预设字段: ${field}`);
                }
            });

            // 检查可选字段
            const optionalFields = ['currency', 'default_pickup_time', 'timezone', 'payment_method'];
            optionalFields.forEach(field => {
                if (!presets[field]) {
                    validation.warnings.push(`缺少可选预设字段: ${field}`);
                }
            });

            // 计算完整度
            const totalFields = requiredFields.length + optionalFields.length;
            const filledFields = Object.keys(presets).length;
            validation.completeness = Math.round((filledFields / totalFields) * 100);

            return validation;
        }

        /**
         * 获取统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                totalOTAChannels: Object.keys(this.otaSpecificPresets).length,
                commonPresets: Object.keys(this.commonPresets).length,
                conditionalRules: Object.keys(this.conditionalPresets).length,
                dynamicGenerators: Object.keys(this.dynamicPresetGenerators).length,
                supportedChannels: Object.keys(this.otaSpecificPresets),
                version: '1.0.0',
                lastUpdated: new Date().toISOString()
            };
        }

        /**
         * 导出配置
         * @returns {Object} 完整配置对象
         */
        exportConfig() {
            return {
                commonPresets: this.commonPresets,
                otaSpecificPresets: this.otaSpecificPresets,
                conditionalPresets: this.conditionalPresets,
                dynamicPresetGenerators: this.dynamicPresetGenerators,
                metadata: this.getStats()
            };
        }

        /**
         * 导入配置
         * @param {Object} config - 配置对象
         */
        importConfig(config) {
            try {
                if (config.commonPresets) {
                    this.commonPresets = { ...this.commonPresets, ...config.commonPresets };
                }

                if (config.otaSpecificPresets) {
                    this.otaSpecificPresets = { ...this.otaSpecificPresets, ...config.otaSpecificPresets };
                }

                if (config.conditionalPresets) {
                    this.conditionalPresets = { ...this.conditionalPresets, ...config.conditionalPresets };
                }

                this.logger.log('✅ 预设值配置导入成功', 'info');
            } catch (error) {
                this.logger.logError('预设值配置导入失败', error);
                throw error;
            }
        }

        /**
         * 重置为默认配置
         */
        resetToDefaults() {
            this.logger.log('🔄 重置预设值配置为默认值', 'info');
            // 重新初始化配置
            const newInstance = new PresetValuesConfig();
            this.commonPresets = newInstance.commonPresets;
            this.otaSpecificPresets = newInstance.otaSpecificPresets;
            this.conditionalPresets = newInstance.conditionalPresets;
            this.dynamicPresetGenerators = newInstance.dynamicPresetGenerators;
        }
    }

    // 创建全局实例
    const presetValuesConfig = new PresetValuesConfig();
    
    // 注册到全局命名空间
    window.OTA.gemini.configs.PresetValuesConfig = PresetValuesConfig;
    window.OTA.gemini.configs.presetValuesConfig = presetValuesConfig;
    
    // 便捷访问函数
    window.OTA.gemini.configs.getPresetValues = function(otaChannel, context) {
        return presetValuesConfig.getPresetValues(otaChannel, context);
    };
    
    // 注册到服务注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('preset-values-config', presetValuesConfig, {
            dependencies: ['logger'],
            description: '预设值配置管理器，提供OTA平台预设值和动态配置功能'
        });
    }
    
    console.log('✅ 预设值配置已加载');
    
})();
