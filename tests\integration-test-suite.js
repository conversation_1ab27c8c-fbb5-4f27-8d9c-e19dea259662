/**
 * @TEST 集成测试套件
 * 🏷️ 标签: @INTEGRATION_TEST_SUITE
 * 📝 说明: 测试重构后Gemini系统与其他模块的集成功能
 * 🎯 功能: 模块间协作测试、数据流测试、API集成测试、UI集成测试
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保测试环境
if (typeof window === 'undefined') {
    global.window = {};
}

// 集成测试套件
describe('Gemini系统集成测试', function() {
    let geminiService;
    let coordinator;
    let uiManager;
    let apiService;
    let appState;

    // 测试前置设置
    beforeAll(async function() {
        // 获取各个服务实例
        geminiService = window.OTA?.geminiService;
        coordinator = window.OTA?.gemini?.getGeminiCoordinator?.();
        uiManager = window.OTA?.uiManager;
        apiService = window.OTA?.apiService;
        appState = window.OTA?.appState || window.appState;

        // 验证核心服务可用性
        if (!geminiService) {
            throw new Error('Gemini服务未初始化');
        }

        // 模拟系统数据
        const mockSystemData = {
            backend_users: {
                '1': { id: 1, name: 'Test User 1' },
                '37': { id: 37, name: 'Sky Mirror World' },
                '310': { id: 310, name: 'JCY GoMyHire' }
            },
            service_types: {
                '2': { id: 2, name: 'Airport Pickup' },
                '3': { id: 3, name: 'Airport Drop-off' },
                '4': { id: 4, name: 'Charter Service' }
            },
            car_types: {
                '1': { id: 1, name: 'Comfort 5 Seater' },
                '2': { id: 2, name: 'Premium 7 Seater' }
            },
            languages: {
                '2': { id: 2, name: 'English' },
                '4': { id: 4, name: 'Chinese' }
            }
        };

        // 更新系统数据
        if (appState && appState.set) {
            appState.set('systemData', mockSystemData);
        }

        // 更新Gemini服务的ID映射
        geminiService.updateIdMappings(mockSystemData);
    });

    // Gemini与AppState集成测试
    describe('Gemini与AppState集成', function() {
        
        it('应该能够从AppState获取系统数据', function() {
            if (!appState) {
                console.log('AppState不可用，跳过测试');
                return;
            }

            const systemData = appState.get('systemData');
            assertTrue(systemData, '应该能获取系统数据');
            assertTrue(systemData.backend_users, '应该包含后台用户数据');
            assertTrue(systemData.service_types, '应该包含服务类型数据');
        });

        it('Gemini解析结果应该能更新AppState', async function() {
            if (!appState) {
                console.log('AppState不可用，跳过测试');
                return;
            }

            const testOrder = `
                客户姓名：集成测试用户
                联系电话：+60123456789
                接送地点：KLIA
                目的地：双子塔
                航班信息：MH123 15:30抵达
            `;

            const result = await geminiService.parseOrder(testOrder);
            
            if (result.success && result.data) {
                // 模拟更新AppState
                appState.set('currentOrder', result.data);
                
                const savedOrder = appState.get('currentOrder');
                assertTrue(savedOrder, '订单数据应该保存到AppState');
                assertEqual(savedOrder.customer_name, result.data.customer_name, '客户姓名应该一致');
            }
        });
    });

    // Gemini与UIManager集成测试
    describe('Gemini与UIManager集成', function() {
        
        it('应该能够触发UI更新', async function() {
            if (!uiManager) {
                console.log('UIManager不可用，跳过测试');
                return;
            }

            const testOrder = `
                客户：UI集成测试
                电话：+60198765432
                接机：KLIA2
                送往：酒店测试
            `;

            // 模拟解析并更新UI的流程
            const result = await geminiService.parseOrder(testOrder);
            
            if (result.success && result.data) {
                // 检查UIManager是否有相关方法
                if (typeof uiManager.fillFormFromData === 'function') {
                    // 模拟填充表单
                    uiManager.fillFormFromData(result.data);
                    assertTrue(true, 'UI更新应该成功');
                } else {
                    console.log('UIManager.fillFormFromData方法不可用');
                }
            }
        });

        it('应该能够处理实时分析UI反馈', async function() {
            if (!uiManager) {
                console.log('UIManager不可用，跳过测试');
                return;
            }

            // 模拟实时分析配置
            const realtimeConfig = {
                enabled: true,
                minInputLength: 20,
                debounceDelay: 1500
            };

            const configResult = geminiService.configureRealtimeAnalysis(realtimeConfig);
            assertTrue(configResult.success, '实时分析配置应该成功');
        });
    });

    // Gemini与APIService集成测试
    describe('Gemini与APIService集成', function() {
        
        it('应该能够配合API创建订单', async function() {
            if (!apiService) {
                console.log('APIService不可用，跳过测试');
                return;
            }

            const testOrder = `
                客户姓名：API集成测试
                联系电话：+60187654321
                接送地点：酒店ABC
                目的地：KLIA
                服务时间：2024-01-15 09:30
            `;

            // 解析订单
            const parseResult = await geminiService.parseOrder(testOrder);
            
            if (parseResult.success && parseResult.data) {
                // 检查解析结果是否包含API所需字段
                const requiredFields = ['customer_name', 'customer_contact'];
                
                for (const field of requiredFields) {
                    if (parseResult.data[field]) {
                        assertTrue(true, `${field}字段存在`);
                    }
                }

                // 模拟API数据转换
                const apiData = this.convertToApiFormat(parseResult.data);
                assertTrue(apiData, 'API数据转换应该成功');
            }
        });

        it('应该正确处理API字段映射', async function() {
            const testOrder = `
                客户：字段映射测试
                电话：+60176543210
                出发地：起点A
                目的地：终点B
                日期：15-01-2024
                时间：14:30
            `;

            const result = await geminiService.parseOrder(testOrder);
            
            if (result.success && result.data) {
                // 检查字段映射
                const mappedData = this.mapFieldsForApi(result.data);
                
                // 验证关键字段映射
                if (result.data.pickup_location) {
                    assertTrue(mappedData.pickup, 'pickup_location应该映射为pickup');
                }
                if (result.data.dropoff_location) {
                    assertTrue(mappedData.destination, 'dropoff_location应该映射为destination');
                }
            }
        });

        // 辅助方法：转换为API格式
        convertToApiFormat(data) {
            return {
                customer_name: data.customer_name,
                customer_contact: data.customer_contact,
                pickup: data.pickup_location || data.pickup,
                destination: data.dropoff_location || data.dropoff,
                date: data.pickup_date || data.date,
                time: data.pickup_time || data.time,
                ota_reference_number: data.ota_reference_number,
                service_type_id: data.service_type_id || 2,
                car_type_id: data.car_type_id || 1,
                backend_user_id: data.backend_user_id || 1,
                languages_id_array: data.languages_id_array || {"0": "2"}
            };
        }

        // 辅助方法：字段映射
        mapFieldsForApi(data) {
            const mapped = { ...data };
            
            // 执行字段映射
            if (data.pickup_location) mapped.pickup = data.pickup_location;
            if (data.dropoff_location) mapped.destination = data.dropoff_location;
            if (data.pickup_date) mapped.date = data.pickup_date;
            if (data.pickup_time) mapped.time = data.pickup_time;
            
            return mapped;
        }
    });

    // 模块协作测试
    describe('模块协作测试', function() {
        
        it('应该支持完整的订单处理流程', async function() {
            const testOrder = `
                客户姓名：完整流程测试
                联系电话：+60165432109
                接送地点：吉隆坡机场T1
                目的地：市中心酒店
                航班信息：SQ807 18:45抵达
                特殊要求：需要儿童座椅
                订单号：FLOW2024010001
            `;

            // 步骤1：解析订单
            const parseResult = await geminiService.parseOrder(testOrder);
            assertTrue(parseResult.success, '订单解析应该成功');

            // 步骤2：数据验证
            if (parseResult.success && parseResult.data) {
                assertTrue(parseResult.data.customer_name, '应该提取客户姓名');
                assertTrue(parseResult.data.customer_contact, '应该提取联系电话');
                assertTrue(parseResult.data.pickup || parseResult.data.pickup_location, '应该提取接送地点');
            }

            // 步骤3：模拟UI更新
            if (uiManager && typeof uiManager.fillFormFromData === 'function') {
                uiManager.fillFormFromData(parseResult.data);
            }

            // 步骤4：模拟状态更新
            if (appState && appState.set) {
                appState.set('currentOrder', parseResult.data);
            }

            // 步骤5：验证整个流程
            assertTrue(true, '完整流程应该无错误执行');
        });

        it('应该支持多订单批处理流程', async function() {
            const multipleOrders = `
                订单A：
                客户：批处理测试A
                电话：+60154321098
                接机：KLIA
                
                ---
                
                订单B：
                客户：批处理测试B
                电话：+60143210987
                送机：KLIA2
            `;

            const segments = multipleOrders.split('---').map(s => s.trim());
            const result = await geminiService.parseMultipleOrders(segments);

            assertTrue(result.success, '多订单解析应该成功');
            if (result.success && Array.isArray(result.data)) {
                assertTrue(result.data.length >= 2, '应该解析出多个订单');
                
                // 验证每个订单都有基本信息
                result.data.forEach((order, index) => {
                    assertTrue(order.customer_name || order.customer, `订单${index + 1}应该有客户信息`);
                });
            }
        });
    });

    // 错误恢复和容错测试
    describe('错误恢复和容错测试', function() {
        
        it('应该在模块不可用时优雅降级', async function() {
            // 模拟协调器不可用的情况
            const originalCoordinator = window.OTA?.gemini?.getGeminiCoordinator;
            
            if (originalCoordinator) {
                // 临时禁用协调器
                window.OTA.gemini.getGeminiCoordinator = () => null;
            }

            const testOrder = '客户：降级测试\n电话：+60132109876';
            const result = await geminiService.parseOrder(testOrder);

            // 恢复协调器
            if (originalCoordinator) {
                window.OTA.gemini.getGeminiCoordinator = originalCoordinator;
            }

            assertTrue(result, '降级模式应该返回结果');
            if (result.data && result.data._fallback_mode) {
                assertTrue(true, '应该使用降级模式');
            }
        });

        it('应该处理部分模块初始化失败', async function() {
            // 测试在某些模块未初始化时的行为
            const testOrder = '基本订单文本';
            
            try {
                const result = await geminiService.parseOrder(testOrder);
                assertTrue(result, '即使部分模块失败也应该返回结果');
            } catch (error) {
                // 如果抛出异常，应该是可预期的错误
                assertTrue(error.message.length > 0, '错误消息应该有意义');
            }
        });
    });

    // 性能集成测试
    describe('性能集成测试', function() {
        
        it('模块间通信应该高效', async function() {
            const testOrder = '性能测试订单\n客户：性能测试\n电话：+60121098765';
            
            const startTime = Date.now();
            
            // 执行完整的集成流程
            const parseResult = await geminiService.parseOrder(testOrder);
            
            if (parseResult.success && appState) {
                appState.set('testOrder', parseResult.data);
                const retrieved = appState.get('testOrder');
                assertTrue(retrieved, '状态存取应该成功');
            }
            
            const duration = Date.now() - startTime;
            assertTrue(duration < 5000, `集成操作应该在5秒内完成，实际：${duration}ms`);
        });

        it('并发处理应该稳定', async function() {
            const orders = [
                '并发测试1\n客户：用户1',
                '并发测试2\n客户：用户2',
                '并发测试3\n客户：用户3'
            ];

            const startTime = Date.now();
            const results = await Promise.all(
                orders.map(order => geminiService.parseOrder(order))
            );
            const duration = Date.now() - startTime;

            assertEqual(results.length, 3, '应该处理所有并发请求');
            assertTrue(duration < 10000, `并发处理应该在10秒内完成，实际：${duration}ms`);
            
            // 验证所有结果都有效
            results.forEach((result, index) => {
                assertTrue(result, `结果${index + 1}应该有效`);
            });
        });
    });
});

// 日志记录
const logger = window.getLogger?.() || console;
logger.log('集成测试套件加载完成', 'info', {
    version: '1.0.0',
    integrationPoints: [
        'gemini_appstate',
        'gemini_uimanager', 
        'gemini_apiservice',
        'module_collaboration',
        'error_recovery',
        'performance_integration'
    ]
});
