<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>航班号识别优化测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .test-case {
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .test-input {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .test-result {
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛫 航班号识别优化测试</h1>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number" id="totalTests">0</div>
                <div class="stat-label">总测试数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="passedTests">0</div>
                <div class="stat-label">通过测试</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="failedTests">0</div>
                <div class="stat-label">失败测试</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="successRate">0%</div>
                <div class="stat-label">成功率</div>
            </div>
        </div>
        
        <div style="text-align: center; margin-bottom: 30px;">
            <button onclick="runAllTests()">🚀 运行所有测试</button>
            <button onclick="runBasicTests()">📝 基础格式测试</button>
            <button onclick="runAdvancedTests()">🔬 高级格式测试</button>
            <button onclick="clearResults()">🗑️ 清除结果</button>
        </div>
        
        <div class="test-section">
            <h2>📋 基础航班号格式测试</h2>
            <div id="basicTests"></div>
        </div>
        
        <div class="test-section">
            <h2>🚫 无关键词识别测试</h2>
            <div id="noKeywordTests"></div>
        </div>

        <div class="test-section">
            <h2>🔬 高级航班号格式测试</h2>
            <div id="advancedTests"></div>
        </div>

        <div class="test-section">
            <h2>🌏 亚洲航空公司测试</h2>
            <div id="asianAirlineTests"></div>
        </div>

        <div class="test-section">
            <h2>🔄 复杂场景测试</h2>
            <div id="complexTests"></div>
        </div>

        <div class="test-section">
            <h2>🛡️ 排除误识别测试</h2>
            <div id="exclusionTests"></div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="../js/logger.js"></script>
    <script src="../js/utils.js"></script>
    <script src="../js/app-state.js"></script>
    <script src="../js/gemini-service.js"></script>
    
    <script>
        // 测试统计
        let testStats = {
            total: 0,
            passed: 0,
            failed: 0
        };

        // 基础航班号格式测试用例
        const basicTestCases = [
            {
                input: "王先生乘坐MH123航班15:30抵达",
                expected: "MH123",
                description: "标准马航航班号"
            },
            {
                input: "CZ351航班明天下午3点半降落KLIA2",
                expected: "CZ351",
                description: "南航航班号"
            },
            {
                input: "新航SQ807今晚起飞",
                expected: "SQ807",
                description: "新加坡航空"
            },
            {
                input: "Flight AK169 departure at 21:10",
                expected: "AK169",
                description: "亚航英文格式"
            }
        ];

        // 无关键词识别测试用例
        const noKeywordTestCases = [
            {
                input: "MH123 15:30抵达KLIA2",
                expected: "MH123",
                description: "无关键词 - 时间前航班号"
            },
            {
                input: "客人13:45 CZ351降落",
                expected: "CZ351",
                description: "无关键词 - 时间后航班号"
            },
            {
                input: "SQ807今晚从新加坡起飞",
                expected: "SQ807",
                description: "无关键词 - 纯文本中的航班号"
            },
            {
                input: "AK169 21:10 KLIA2接机",
                expected: "AK169",
                description: "无关键词 - 机场上下文"
            },
            {
                input: "9W123印度航空起飞",
                expected: "9W123",
                description: "无关键词 - 数字开头航班号"
            },
            {
                input: "CZ-351航班信息确认",
                expected: "CZ-351",
                description: "无关键词 - 带连字符格式"
            }
        ];

        // 高级航班号格式测试用例
        const advancedTestCases = [
            {
                input: "张先生乘坐SQ807/123分段航班抵达",
                expected: "SQ807/123",
                description: "分段航班号"
            },
            {
                input: "MH123A航班延误到16:00",
                expected: "MH123A",
                description: "带字母后缀的航班号"
            },
            {
                input: "CZ-351航班信息确认",
                expected: "CZ-351",
                description: "带连字符的航班号"
            },
            {
                input: "9W123印度航空起飞",
                expected: "9W123",
                description: "数字开头的航班号"
            }
        ];

        // 亚洲航空公司测试用例
        const asianAirlineTestCases = [
            {
                input: "泰航TG415明天到达",
                expected: "TG415",
                description: "泰国航空"
            },
            {
                input: "全日空NH856航班",
                expected: "NH856",
                description: "日本全日空"
            },
            {
                input: "大韩航空KE672",
                expected: "KE672",
                description: "韩国大韩航空"
            },
            {
                input: "阿联酋航空EK343",
                expected: "EK343",
                description: "阿联酋航空"
            }
        ];

        // 复杂场景测试用例
        const complexTestCases = [
            {
                input: "李先生13800138000，明天MH123 15:30抵达KLIA2，需要接机到双子塔",
                expected: "MH123",
                description: "完整订单信息中的航班号提取"
            },
            {
                input: "订单1: 王女士, CZ351降落, 4人. 订单2: 张先生, MH456起飞, 2人",
                expected: "CZ351", // 应该提取第一个
                description: "多订单中的航班号识别"
            },
            {
                input: "客户乘坐航班：SQ807，时间：15:45，地点：KLIA2",
                expected: "SQ807",
                description: "明确标注的航班信息"
            }
        ];

        // 排除误识别测试用例
        const exclusionTestCases = [
            {
                input: "客户电话13800138000，价格RM123，订单号ABC123DEF",
                expected: null,
                description: "排除电话号码和订单号"
            },
            {
                input: "费用USD250，参考号XYZ789，联系人MR123",
                expected: null,
                description: "排除价格和参考号格式"
            },
            {
                input: "房间号A123，楼层B456，时间14:30",
                expected: null,
                description: "排除房间号等非航班号格式"
            }
        ];

        // 初始化Gemini服务
        let geminiService;

        window.addEventListener('DOMContentLoaded', function() {
            try {
                // 等待所有脚本加载完成
                setTimeout(() => {
                    if (typeof window.GeminiService !== 'undefined') {
                        geminiService = new window.GeminiService();
                        console.log('✅ Gemini服务初始化成功');
                    } else if (typeof getGeminiService === 'function') {
                        geminiService = getGeminiService();
                        console.log('✅ Gemini服务获取成功');
                    } else {
                        console.error('❌ GeminiService未找到');
                    }
                }, 100);
            } catch (error) {
                console.error('❌ Gemini服务初始化失败:', error);
            }
        });

        // 运行单个测试用例
        function runTestCase(testCase, containerId) {
            testStats.total++;
            
            const testDiv = document.createElement('div');
            testDiv.className = 'test-case';
            
            const inputDiv = document.createElement('div');
            inputDiv.className = 'test-input';
            inputDiv.textContent = `输入: "${testCase.input}"`;
            
            const descDiv = document.createElement('div');
            descDiv.textContent = `描述: ${testCase.description}`;
            descDiv.style.color = '#666';
            descDiv.style.fontSize = '0.9em';
            
            testDiv.appendChild(inputDiv);
            testDiv.appendChild(descDiv);
            
            try {
                // 检查Gemini服务是否可用
                if (!geminiService) {
                    throw new Error('Gemini服务未初始化');
                }

                // 使用Gemini服务的航班号提取方法
                const extractedFlight = geminiService.extractFlightNumber(testCase.input);
                
                const resultDiv = document.createElement('div');
                resultDiv.className = 'test-result';
                
                if (extractedFlight === testCase.expected) {
                    resultDiv.className += ' success';
                    resultDiv.innerHTML = `✅ 测试通过<br>期望: ${testCase.expected}<br>实际: ${extractedFlight}`;
                    testStats.passed++;
                } else {
                    resultDiv.className += ' error';
                    resultDiv.innerHTML = `❌ 测试失败<br>期望: ${testCase.expected}<br>实际: ${extractedFlight || 'null'}`;
                    testStats.failed++;
                }
                
                testDiv.appendChild(resultDiv);
                
            } catch (error) {
                const resultDiv = document.createElement('div');
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `💥 测试异常: ${error.message}`;
                testDiv.appendChild(resultDiv);
                testStats.failed++;
            }
            
            document.getElementById(containerId).appendChild(testDiv);
            updateStats();
        }

        // 更新统计信息
        function updateStats() {
            document.getElementById('totalTests').textContent = testStats.total;
            document.getElementById('passedTests').textContent = testStats.passed;
            document.getElementById('failedTests').textContent = testStats.failed;
            
            const successRate = testStats.total > 0 ? 
                Math.round((testStats.passed / testStats.total) * 100) : 0;
            document.getElementById('successRate').textContent = successRate + '%';
        }

        // 运行基础测试
        function runBasicTests() {
            document.getElementById('basicTests').innerHTML = '';
            basicTestCases.forEach(testCase => {
                runTestCase(testCase, 'basicTests');
            });
        }

        // 运行无关键词测试
        function runNoKeywordTests() {
            document.getElementById('noKeywordTests').innerHTML = '';
            noKeywordTestCases.forEach(testCase => {
                runTestCase(testCase, 'noKeywordTests');
            });
        }

        // 运行高级测试
        function runAdvancedTests() {
            document.getElementById('advancedTests').innerHTML = '';
            advancedTestCases.forEach(testCase => {
                runTestCase(testCase, 'advancedTests');
            });

            document.getElementById('asianAirlineTests').innerHTML = '';
            asianAirlineTestCases.forEach(testCase => {
                runTestCase(testCase, 'asianAirlineTests');
            });

            document.getElementById('complexTests').innerHTML = '';
            complexTestCases.forEach(testCase => {
                runTestCase(testCase, 'complexTests');
            });

            document.getElementById('exclusionTests').innerHTML = '';
            exclusionTestCases.forEach(testCase => {
                runTestCase(testCase, 'exclusionTests');
            });
        }

        // 运行所有测试
        function runAllTests() {
            clearResults();
            runBasicTests();
            runNoKeywordTests();
            runAdvancedTests();
        }

        // 清除结果
        function clearResults() {
            testStats = { total: 0, passed: 0, failed: 0 };
            document.getElementById('basicTests').innerHTML = '';
            document.getElementById('noKeywordTests').innerHTML = '';
            document.getElementById('advancedTests').innerHTML = '';
            document.getElementById('asianAirlineTests').innerHTML = '';
            document.getElementById('complexTests').innerHTML = '';
            document.getElementById('exclusionTests').innerHTML = '';
            updateStats();
        }
    </script>
</body>
</html>
