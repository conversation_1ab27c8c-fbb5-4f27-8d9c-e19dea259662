<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务3.2验证：优化向后兼容的双重暴露</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #5a67d8;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            background: #f8fafc;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #c6f6d5;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }
        .error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #fc8181;
        }
        .warning {
            background: #fefcbf;
            color: #744210;
            border: 1px solid #f6e05e;
        }
        .info {
            background: #bee3f8;
            color: #2a4365;
            border: 1px solid #90cdf4;
        }
        button {
            background: #5a67d8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #4c51bf;
        }
        .console-output {
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #5a67d8;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 任务3.2验证：优化向后兼容的双重暴露</h1>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalTests">0</div>
                <div class="stat-label">总测试数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="passedTests">0</div>
                <div class="stat-label">通过测试</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="warningsTriggered">0</div>
                <div class="stat-label">废弃警告触发</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="conditionalExposures">0</div>
                <div class="stat-label">条件暴露检测</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 双重暴露优化测试</h3>
            <button onclick="runAllTests()">运行所有测试</button>
            <button onclick="clearResults()">清空结果</button>
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h3>📊 废弃警告监控</h3>
            <div class="console-output" id="consoleOutput"></div>
        </div>

        <div class="test-section">
            <h3>🔍 双重暴露状态检查</h3>
            <div id="exposureStatus"></div>
        </div>
    </div>

    <script>
        // 测试统计
        let testStats = {
            total: 0,
            passed: 0,
            warnings: 0,
            conditionalExposures: 0
        };

        // 控制台输出捕获
        const originalConsoleWarn = console.warn;
        const consoleOutput = document.getElementById('consoleOutput');
        
        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            const message = args.join(' ');
            if (message.includes('[DEPRECATED]')) {
                testStats.warnings++;
                updateStats();
            }
            consoleOutput.innerHTML += `<div style="color: #fbb6ce;">[WARN] ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        };

        function addTestResult(name, status, message) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${status}`;
            resultDiv.innerHTML = `<strong>${name}</strong>: ${message}`;
            resultsDiv.appendChild(resultDiv);
            
            testStats.total++;
            if (status === 'success') {
                testStats.passed++;
            }
            updateStats();
        }

        function updateStats() {
            document.getElementById('totalTests').textContent = testStats.total;
            document.getElementById('passedTests').textContent = testStats.passed;
            document.getElementById('warningsTriggered').textContent = testStats.warnings;
            document.getElementById('conditionalExposures').textContent = testStats.conditionalExposures;
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('consoleOutput').innerHTML = '';
            testStats = { total: 0, passed: 0, warnings: 0, conditionalExposures: 0 };
            updateStats();
        }

        function runAllTests() {
            clearResults();
            
            // 测试1: utils工具函数废弃警告
            try {
                const utils = window.utils; // 应该触发废弃警告
                if (utils && utils === window.OTA.utils) {
                    addTestResult('Utils废弃警告测试', 'success', 'window.utils访问成功，应该触发废弃警告');
                } else {
                    addTestResult('Utils废弃警告测试', 'error', 'window.utils访问失败或不等于window.OTA.utils');
                }
            } catch (error) {
                addTestResult('Utils废弃警告测试', 'error', `访问失败: ${error.message}`);
            }

            // 测试2: API服务废弃警告
            try {
                const apiService = window.apiService; // 应该触发废弃警告
                if (apiService && apiService === window.OTA.apiService) {
                    addTestResult('API服务废弃警告测试', 'success', 'window.apiService访问成功，应该触发废弃警告');
                } else {
                    addTestResult('API服务废弃警告测试', 'error', 'window.apiService访问失败或不等于window.OTA.apiService');
                }
            } catch (error) {
                addTestResult('API服务废弃警告测试', 'error', `访问失败: ${error.message}`);
            }

            // 测试3: 统一数据管理器条件暴露
            try {
                const hasGlobalDataManager = typeof window.unifiedDataManager !== 'undefined';
                const hasOTADataManager = typeof window.OTA.unifiedDataManager !== 'undefined';
                
                if (hasOTADataManager) {
                    addTestResult('数据管理器OTA暴露测试', 'success', 'window.OTA.unifiedDataManager存在');
                    
                    if (hasGlobalDataManager) {
                        testStats.conditionalExposures++;
                        const dataManager = window.unifiedDataManager; // 可能触发废弃警告
                        addTestResult('数据管理器条件暴露测试', 'success', '检测到条件暴露，全局访问可用');
                    } else {
                        addTestResult('数据管理器条件暴露测试', 'info', '未检测到外部依赖，全局暴露已禁用');
                    }
                } else {
                    addTestResult('数据管理器OTA暴露测试', 'error', 'window.OTA.unifiedDataManager不存在');
                }
            } catch (error) {
                addTestResult('数据管理器条件暴露测试', 'error', `测试失败: ${error.message}`);
            }

            // 测试4: 应用实例开发环境限制
            try {
                const hasGlobalApp = typeof window.app !== 'undefined';
                const hasOTAApp = typeof window.OTA.app !== 'undefined';
                const isDevEnvironment = window.location.hostname === 'localhost' || 
                                        window.location.protocol === 'file:' ||
                                        window.location.hostname === '127.0.0.1';
                
                if (hasOTAApp) {
                    addTestResult('应用实例OTA暴露测试', 'success', 'window.OTA.app存在');
                    
                    if (isDevEnvironment && hasGlobalApp) {
                        addTestResult('应用实例开发环境测试', 'success', '开发环境：window.app已暴露');
                    } else if (!isDevEnvironment && !hasGlobalApp) {
                        addTestResult('应用实例生产环境测试', 'success', '生产环境：window.app未暴露');
                    } else {
                        addTestResult('应用实例环境限制测试', 'warning', `环境检测异常 - 开发环境:${isDevEnvironment}, 全局暴露:${hasGlobalApp}`);
                    }
                } else {
                    addTestResult('应用实例OTA暴露测试', 'error', 'window.OTA.app不存在');
                }
            } catch (error) {
                addTestResult('应用实例环境限制测试', 'error', `测试失败: ${error.message}`);
            }

            // 测试5: 全局事件协调器条件暴露
            try {
                const hasGlobalCoordinator = typeof window.globalEventCoordinator !== 'undefined';
                const hasOTACoordinator = typeof window.OTA.globalEventCoordinator !== 'undefined';
                
                if (hasOTACoordinator) {
                    addTestResult('事件协调器OTA暴露测试', 'success', 'window.OTA.globalEventCoordinator存在');
                    
                    if (hasGlobalCoordinator) {
                        testStats.conditionalExposures++;
                        addTestResult('事件协调器条件暴露测试', 'success', '检测到外部依赖，全局暴露已启用');
                    } else {
                        addTestResult('事件协调器条件暴露测试', 'info', '未检测到外部依赖，全局暴露已禁用');
                    }
                } else {
                    addTestResult('事件协调器OTA暴露测试', 'error', 'window.OTA.globalEventCoordinator不存在');
                }
            } catch (error) {
                addTestResult('事件协调器条件暴露测试', 'error', `测试失败: ${error.message}`);
            }

            // 更新双重暴露状态
            updateExposureStatus();
        }

        function updateExposureStatus() {
            const statusDiv = document.getElementById('exposureStatus');
            const exposures = [
                { name: 'utils', global: typeof window.utils !== 'undefined', ota: typeof window.OTA.utils !== 'undefined' },
                { name: 'apiService', global: typeof window.apiService !== 'undefined', ota: typeof window.OTA.apiService !== 'undefined' },
                { name: 'unifiedDataManager', global: typeof window.unifiedDataManager !== 'undefined', ota: typeof window.OTA.unifiedDataManager !== 'undefined' },
                { name: 'app', global: typeof window.app !== 'undefined', ota: typeof window.OTA.app !== 'undefined' },
                { name: 'globalEventCoordinator', global: typeof window.globalEventCoordinator !== 'undefined', ota: typeof window.OTA.globalEventCoordinator !== 'undefined' }
            ];

            let statusHTML = '<table style="width:100%; border-collapse: collapse;">';
            statusHTML += '<tr style="background:#f1f5f9;"><th style="padding:10px; border:1px solid #e2e8f0;">模块</th><th style="padding:10px; border:1px solid #e2e8f0;">全局暴露</th><th style="padding:10px; border:1px solid #e2e8f0;">OTA命名空间</th><th style="padding:10px; border:1px solid #e2e8f0;">状态</th></tr>';
            
            exposures.forEach(exp => {
                const globalStatus = exp.global ? '✅' : '❌';
                const otaStatus = exp.ota ? '✅' : '❌';
                let status = '';
                
                if (exp.ota && exp.global) {
                    status = '<span style="color:#22543d;">双重暴露</span>';
                } else if (exp.ota && !exp.global) {
                    status = '<span style="color:#2a4365;">仅OTA暴露</span>';
                } else if (!exp.ota && exp.global) {
                    status = '<span style="color:#742a2a;">仅全局暴露</span>';
                } else {
                    status = '<span style="color:#744210;">未暴露</span>';
                }
                
                statusHTML += `<tr><td style="padding:10px; border:1px solid #e2e8f0;">${exp.name}</td><td style="padding:10px; border:1px solid #e2e8f0; text-align:center;">${globalStatus}</td><td style="padding:10px; border:1px solid #e2e8f0; text-align:center;">${otaStatus}</td><td style="padding:10px; border:1px solid #e2e8f0; text-align:center;">${status}</td></tr>`;
            });
            
            statusHTML += '</table>';
            statusDiv.innerHTML = statusHTML;
        }

        // 页面加载时更新状态
        window.addEventListener('load', () => {
            updateExposureStatus();
            updateStats();
        });
    </script>
</body>
</html>
