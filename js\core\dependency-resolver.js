/**
 * @CORE 统一依赖解析器
 * 🏷️ 标签: @DEPENDENCY_RESOLVER
 * 📝 说明: 统一的依赖获取接口，解决系统中多种依赖获取方式不一致的问题
 * 🎯 功能: 依赖缓存、智能解析、降级处理、性能监控
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.core = window.OTA.core || {};

(function() {
    'use strict';

    /**
     * 统一依赖解析器类
     * 提供统一的依赖获取接口，支持缓存、降级和性能监控
     */
    class DependencyResolver {
        constructor() {
            this.logger = this.getInitialLogger();

            // 使用智能依赖缓存系统
            this.smartCache = new window.OTA.core.SmartDependencyCache({
                maxSize: 200,
                maxMemoryMB: 100,
                ttl: 60 * 60 * 1000, // 1小时TTL
                enablePreload: true,
                enableCompression: true
            });

            // 向后兼容的简单缓存接口
            this.cache = {
                has: (key) => this.smartCache.has(key),
                get: (key) => this.smartCache.get(key),
                set: (key, value) => this.smartCache.set(key, value),
                delete: (key) => this.smartCache.delete(key),
                clear: () => this.smartCache.clear(),
                size: () => this.smartCache.cache.size
            };

            // 缓存统计（向后兼容）
            this.cacheStats = {
                get hits() { return this.smartCache?.stats?.hits || 0; },
                get misses() { return this.smartCache?.stats?.misses || 0; },
                get totalRequests() { return this.smartCache?.stats?.totalRequests || 0; }
            };
            
            // 简化的依赖获取策略（使用统一服务访问器）
            this.strategies = {
                // 主策略: 统一服务访问器
                unifiedAccessor: (serviceName) => {
                    const accessor = window.OTA?.core?.unifiedServiceAccessor;
                    return accessor ? accessor.getService(serviceName) : null;
                },

                // 降级策略: 直接OTA服务获取
                directOTA: (serviceName) => {
                    return window.OTA?.getService ? window.OTA.getService(serviceName) : null;
                }
            };

            // 注意：factoryMapping已移除，因为使用统一服务访问器
            
            // 性能监控
            this.performanceMetrics = {
                totalResolveTime: 0,
                averageResolveTime: 0,
                slowestResolve: { serviceName: '', time: 0 },
                fastestResolve: { serviceName: '', time: Infinity }
            };
            
            // 错误统计
            this.errorStats = {
                totalErrors: 0,
                errorsByService: new Map(),
                lastError: null
            };
            
            this.logger.log('✅ 统一依赖解析器已初始化', 'info');
        }

        /**
         * 获取初始日志器（避免循环依赖）
         */
        getInitialLogger() {
            // 使用统一服务访问器
            try {
                return window.OTA.getService('logger');
            } catch (error) {
                // 降级到控制台
                return console;
            }
        }

        /**
         * 统一的依赖获取方法
         * @param {string} serviceName - 服务名称
         * @param {Object} options - 选项
         * @param {boolean} options.useCache - 是否使用缓存
         * @param {boolean} options.throwOnError - 获取失败时是否抛出异常
         * @returns {any} 服务实例
         */
        resolve(serviceName, options = {}) {
            const startTime = Date.now();
            const { useCache = true, throwOnError = false, priority = 'normal', tags = [] } = options;

            try {
                // 检查智能缓存
                if (useCache) {
                    const cachedService = this.smartCache.get(serviceName);
                    if (cachedService !== null) {
                        // 验证缓存的服务是否仍然有效
                        if (this.validateCachedService(cachedService, serviceName)) {
                            this.updatePerformanceMetrics(serviceName, Date.now() - startTime);
                            return cachedService;
                        } else {
                            // 缓存失效，清除缓存
                            this.smartCache.delete(serviceName);
                        }
                    }
                }
                
                // 按策略顺序尝试获取服务
                let service = null;
                const strategyNames = Object.keys(this.strategies);
                
                for (const strategyName of strategyNames) {
                    try {
                        service = this.strategies[strategyName](serviceName);
                        if (service) {
                            this.logger.log(`✅ 通过策略 ${strategyName} 获取服务 ${serviceName}`, 'debug');
                            break;
                        }
                    } catch (error) {
                        this.logger.log(`⚠️ 策略 ${strategyName} 获取服务 ${serviceName} 失败: ${error.message}`, 'warn');
                        continue;
                    }
                }
                
                if (!service) {
                    const errorMsg = `无法解析服务: ${serviceName}`;
                    this.recordError(serviceName, errorMsg);
                    
                    if (throwOnError) {
                        throw new Error(errorMsg);
                    }
                    
                    this.logger.log(`❌ ${errorMsg}`, 'error');
                    return null;
                }
                
                // 使用智能缓存存储成功获取的服务
                if (useCache) {
                    const cacheOptions = {
                        priority: priority,
                        tags: ['dependency', ...tags],
                        preloadRelated: this.getRelatedDependencies(serviceName)
                    };
                    this.smartCache.set(serviceName, service, cacheOptions);
                }
                
                this.updatePerformanceMetrics(serviceName, Date.now() - startTime);
                return service;
                
            } catch (error) {
                this.recordError(serviceName, error.message);
                
                if (throwOnError) {
                    throw error;
                }
                
                this.logger.logError(`依赖解析失败: ${serviceName}`, error);
                return null;
            }
        }

        /**
         * 批量解析依赖
         * @param {Array<string>} serviceNames - 服务名称数组
         * @param {Object} options - 选项
         * @returns {Object} 服务实例映射
         */
        resolveMultiple(serviceNames, options = {}) {
            const result = {};
            const errors = [];
            
            for (const serviceName of serviceNames) {
                try {
                    const service = this.resolve(serviceName, options);
                    if (service) {
                        result[serviceName] = service;
                    } else {
                        errors.push(`无法解析服务: ${serviceName}`);
                    }
                } catch (error) {
                    errors.push(`解析服务 ${serviceName} 时出错: ${error.message}`);
                }
            }
            
            if (errors.length > 0) {
                this.logger.log(`批量依赖解析部分失败: ${errors.join(', ')}`, 'warn');
            }
            
            return result;
        }

        /**
         * 获取工厂函数名称（已废弃，保留用于向后兼容）
         * @param {string} serviceName - 服务名称
         * @returns {string} 工厂函数名称
         * @deprecated 使用统一服务访问器替代
         */
        getFactoryName(serviceName) {
            return `get${serviceName.charAt(0).toUpperCase() + serviceName.slice(1)}`;
        }

        /**
         * 验证缓存的服务是否仍然有效
         * @param {any} service - 缓存的服务
         * @param {string} serviceName - 服务名称
         * @returns {boolean} 是否有效
         */
        validateCachedService(service, serviceName) {
            if (!service) return false;
            
            // 基本存在性检查
            if (typeof service === 'object' && service.constructor === Object && Object.keys(service).length === 0) {
                return false;
            }
            
            // 特定服务的验证逻辑
            switch (serviceName) {
                case 'logger':
                    return typeof service.log === 'function';
                case 'apiService':
                    return typeof service.createOrder === 'function';
                case 'geminiService':
                    return typeof service.parseOrder === 'function';
                case 'uiManager':
                    return typeof service.showAlert === 'function';
                default:
                    return true; // 默认认为有效
            }
        }

        /**
         * 更新性能指标
         * @param {string} serviceName - 服务名称
         * @param {number} resolveTime - 解析时间
         */
        updatePerformanceMetrics(serviceName, resolveTime) {
            this.performanceMetrics.totalResolveTime += resolveTime;
            this.performanceMetrics.averageResolveTime = 
                this.performanceMetrics.totalResolveTime / this.cacheStats.totalRequests;
            
            if (resolveTime > this.performanceMetrics.slowestResolve.time) {
                this.performanceMetrics.slowestResolve = { serviceName, time: resolveTime };
            }
            
            if (resolveTime < this.performanceMetrics.fastestResolve.time) {
                this.performanceMetrics.fastestResolve = { serviceName, time: resolveTime };
            }
        }

        /**
         * 记录错误
         * @param {string} serviceName - 服务名称
         * @param {string} errorMessage - 错误消息
         */
        recordError(serviceName, errorMessage) {
            this.errorStats.totalErrors++;
            this.errorStats.lastError = { serviceName, errorMessage, timestamp: new Date() };
            
            const serviceErrorCount = this.errorStats.errorsByService.get(serviceName) || 0;
            this.errorStats.errorsByService.set(serviceName, serviceErrorCount + 1);
        }

        /**
         * 获取相关依赖（用于预加载）
         * @param {string} serviceName - 服务名称
         * @returns {Array<string>} 相关依赖列表
         */
        getRelatedDependencies(serviceName) {
            const relatedMap = {
                'uiManager': ['logger', 'appState', 'utils'],
                'apiService': ['logger', 'appState'],
                'geminiService': ['logger', 'apiService', 'configManager'],
                'multiOrderManager': ['logger', 'utils', 'appState'],
                'formManager': ['logger', 'uiManager', 'appState'],
                'priceManager': ['logger', 'utils'],
                'eventManager': ['logger', 'uiManager']
            };

            return relatedMap[serviceName] || [];
        }

        /**
         * 预热缓存
         * @param {Array<string>} serviceNames - 要预热的服务名称
         */
        async warmupCache(serviceNames = []) {
            const defaultServices = ['logger', 'utils', 'appState', 'apiService'];
            const servicesToWarmup = serviceNames.length > 0 ? serviceNames : defaultServices;

            this.logger.log(`🔥 开始缓存预热: ${servicesToWarmup.join(', ')}`, 'info');

            for (const serviceName of servicesToWarmup) {
                try {
                    await this.resolve(serviceName, { useCache: false }); // 强制获取并缓存
                    this.logger.log(`✅ 预热完成: ${serviceName}`, 'debug');
                } catch (error) {
                    this.logger.log(`⚠️ 预热失败: ${serviceName} - ${error.message}`, 'warn');
                }
            }

            this.logger.log('🔥 缓存预热完成', 'info');
        }

        /**
         * 清除缓存
         * @param {string} serviceName - 服务名称（可选，不提供则清除所有）
         */
        clearCache(serviceName = null) {
            if (serviceName) {
                this.smartCache.delete(serviceName);
                this.logger.log(`清除服务 ${serviceName} 的缓存`, 'info');
            } else {
                this.smartCache.clear();
                this.logger.log('清除所有依赖缓存', 'info');
            }
        }

        /**
         * 按标签清除缓存
         * @param {string} tag - 标签
         */
        clearCacheByTag(tag) {
            const deletedCount = this.smartCache.deleteByTag(tag);
            this.logger.log(`按标签清除缓存: ${tag} (${deletedCount}个)`, 'info');
            return deletedCount;
        }

        /**
         * 获取统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            const smartCacheStats = this.smartCache.getStats();

            return {
                cache: {
                    ...smartCacheStats,
                    // 向后兼容的统计信息
                    hits: smartCacheStats.hits,
                    misses: smartCacheStats.misses,
                    totalRequests: smartCacheStats.totalRequests,
                    hitRate: smartCacheStats.hitRate,
                    size: smartCacheStats.size
                },
                smartCache: smartCacheStats,
                performance: this.performanceMetrics,
                errors: {
                    ...this.errorStats,
                    errorsByService: Object.fromEntries(this.errorStats.errorsByService)
                }
            };
        }

        /**
         * 重置统计信息
         */
        resetStats() {
            this.cacheStats = { hits: 0, misses: 0, totalRequests: 0 };
            this.performanceMetrics = {
                totalResolveTime: 0,
                averageResolveTime: 0,
                slowestResolve: { serviceName: '', time: 0 },
                fastestResolve: { serviceName: '', time: Infinity }
            };
            this.errorStats = {
                totalErrors: 0,
                errorsByService: new Map(),
                lastError: null
            };
            
            this.logger.log('依赖解析器统计信息已重置', 'info');
        }
    }

    // 创建全局单例实例
    function getDependencyResolver() {
        if (!window.OTA.core.dependencyResolver) {
            window.OTA.core.dependencyResolver = new DependencyResolver();
        }
        return window.OTA.core.dependencyResolver;
    }

    // 暴露到OTA命名空间
    window.OTA.core.DependencyResolver = DependencyResolver;
    window.OTA.core.getDependencyResolver = getDependencyResolver;

    // 创建全局实例
    const globalResolver = getDependencyResolver();

    // 提供统一的依赖获取函数
    window.OTA.resolve = function(serviceName, options) {
        return globalResolver.resolve(serviceName, options);
    };

    // 向后兼容的全局函数
    window.resolve = window.OTA.resolve;

    console.log('✅ 统一依赖解析器已加载');

})();
