/**
 * @CONFIG_FILE 降级处理配置
 * 🏷️ 标签: @FALLBACK_CONFIG @ERROR_RECOVERY
 * 📝 说明: 定义降级处理策略和错误恢复机制
 * 🎯 目标: 确保系统在各种异常情况下能够优雅降级，保持基本功能可用
 */

(function() {
    'use strict';
    
    // 确保OTA命名空间存在
    window.OTA = window.OTA || {};
    window.OTA.gemini = window.OTA.gemini || {};
    window.OTA.gemini.configs = window.OTA.gemini.configs || {};
    
    /**
     * 降级处理配置类
     * 管理系统的降级处理策略和错误恢复机制
     */
    class FallbackConfig {
        constructor() {
            this.logger = window.getLogger?.() || console;
            
            // 降级策略配置
            this.fallbackStrategies = {
                // OTA渠道识别降级
                otaChannelIdentification: {
                    primary: 'ai_detection',      // 主要方法：AI检测
                    fallback: 'keyword_matching', // 降级方法：关键词匹配
                    ultimate: 'generic_processor', // 最终降级：通用处理器
                    timeout: 5000,                // 超时时间（毫秒）
                    retryAttempts: 2,             // 重试次数
                    confidenceThreshold: 0.7      // 置信度阈值
                },
                
                // 参考号识别降级
                referenceNumberExtraction: {
                    primary: 'pattern_matching',   // 主要方法：模式匹配
                    fallback: 'regex_extraction',  // 降级方法：正则提取
                    ultimate: 'manual_input',      // 最终降级：手动输入
                    timeout: 3000,
                    retryAttempts: 3,
                    minLength: 6                   // 最小参考号长度
                },
                
                // 字段映射降级
                fieldMapping: {
                    primary: 'ota_specific_mapping', // 主要方法：OTA特定映射
                    fallback: 'common_mapping',      // 降级方法：通用映射
                    ultimate: 'manual_mapping',     // 最终降级：手动映射
                    timeout: 2000,
                    retryAttempts: 1,
                    requiredFields: ['pickup_location', 'dropoff_location', 'customer_name']
                },
                
                // 语言检测降级
                languageDetection: {
                    primary: 'ai_language_detection', // 主要方法：AI语言检测
                    fallback: 'character_analysis',   // 降级方法：字符分析
                    ultimate: 'default_language',     // 最终降级：默认语言
                    timeout: 4000,
                    retryAttempts: 2,
                    defaultLanguage: 'en'
                },
                
                // 数据验证降级
                dataValidation: {
                    primary: 'strict_validation',   // 主要方法：严格验证
                    fallback: 'relaxed_validation', // 降级方法：宽松验证
                    ultimate: 'minimal_validation', // 最终降级：最小验证
                    timeout: 1000,
                    retryAttempts: 1,
                    criticalFields: ['pickup_location', 'customer_name']
                }
            };
            
            // 错误恢复机制
            this.errorRecoveryMechanisms = {
                // API调用失败恢复
                apiCallFailure: {
                    strategy: 'exponential_backoff',
                    maxRetries: 3,
                    baseDelay: 1000,
                    maxDelay: 10000,
                    backoffMultiplier: 2,
                    fallbackAction: 'use_cached_data'
                },
                
                // 网络连接失败恢复
                networkFailure: {
                    strategy: 'circuit_breaker',
                    failureThreshold: 5,
                    recoveryTimeout: 30000,
                    halfOpenMaxCalls: 3,
                    fallbackAction: 'offline_mode'
                },
                
                // 数据解析失败恢复
                dataParsingFailure: {
                    strategy: 'graceful_degradation',
                    maxRetries: 2,
                    fallbackParsers: ['simple_parser', 'regex_parser', 'manual_parser'],
                    fallbackAction: 'partial_data_extraction'
                },
                
                // 内存不足恢复
                memoryExhaustion: {
                    strategy: 'resource_cleanup',
                    cleanupActions: ['clear_cache', 'garbage_collect', 'reduce_batch_size'],
                    fallbackAction: 'process_individually'
                },
                
                // 超时恢复
                timeoutFailure: {
                    strategy: 'timeout_escalation',
                    timeoutLevels: [5000, 10000, 20000],
                    fallbackAction: 'simplified_processing'
                }
            };
            
            // 降级触发条件
            this.fallbackTriggers = {
                // 性能相关触发器
                performance: {
                    responseTimeThreshold: 10000,    // 响应时间阈值（毫秒）
                    memoryUsageThreshold: 0.8,       // 内存使用率阈值
                    cpuUsageThreshold: 0.9,          // CPU使用率阈值
                    errorRateThreshold: 0.1          // 错误率阈值
                },
                
                // 质量相关触发器
                quality: {
                    confidenceThreshold: 0.6,        // 置信度阈值
                    completenessThreshold: 0.7,      // 完整度阈值
                    accuracyThreshold: 0.8           // 准确度阈值
                },
                
                // 资源相关触发器
                resource: {
                    apiQuotaThreshold: 0.9,          // API配额阈值
                    bandwidthThreshold: 0.8,         // 带宽使用率阈值
                    storageThreshold: 0.9            // 存储使用率阈值
                }
            };
            
            // 降级处理器映射
            this.fallbackProcessors = {
                'generic_processor': {
                    module: 'js/gemini/processors/generic-processor.js',
                    className: 'GenericProcessor',
                    description: '通用处理器，处理所有未识别的OTA渠道'
                },
                
                'simple_parser': {
                    module: 'js/gemini/core/simple-parser.js',
                    className: 'SimpleParser',
                    description: '简单解析器，基于基本规则解析订单'
                },
                
                'regex_parser': {
                    module: 'js/gemini/core/regex-parser.js',
                    className: 'RegexParser',
                    description: '正则表达式解析器，使用正则模式提取信息'
                },
                
                'manual_parser': {
                    module: 'js/gemini/core/manual-parser.js',
                    className: 'ManualParser',
                    description: '手动解析器，提供用户界面进行手动输入'
                }
            };
            
            // 降级状态跟踪
            this.fallbackState = {
                currentLevel: 'normal',              // 当前降级级别
                activeFallbacks: new Set(),         // 活跃的降级策略
                fallbackHistory: [],                // 降级历史记录
                recoveryAttempts: new Map(),        // 恢复尝试计数
                lastFallbackTime: null,             // 最后降级时间
                totalFallbackCount: 0               // 总降级次数
            };
            
            this.logger.log('🛡️ 降级处理配置初始化完成', 'info');
        }
        
        /**
         * 检查是否需要降级
         * @param {string} operation - 操作名称
         * @param {Object} metrics - 性能指标
         * @returns {boolean} 是否需要降级
         */
        shouldFallback(operation, metrics = {}) {
            const strategy = this.fallbackStrategies[operation];
            if (!strategy) return false;
            
            // 检查性能触发器
            if (metrics.responseTime > this.fallbackTriggers.performance.responseTimeThreshold) {
                this.logger.log(`⚠️ 响应时间超阈值，触发降级: ${operation}`, 'warning');
                return true;
            }
            
            // 检查置信度触发器
            if (metrics.confidence !== undefined && 
                metrics.confidence < strategy.confidenceThreshold) {
                this.logger.log(`⚠️ 置信度过低，触发降级: ${operation}`, 'warning');
                return true;
            }
            
            // 检查错误率触发器
            if (metrics.errorRate > this.fallbackTriggers.performance.errorRateThreshold) {
                this.logger.log(`⚠️ 错误率过高，触发降级: ${operation}`, 'warning');
                return true;
            }
            
            return false;
        }
        
        /**
         * 执行降级策略
         * @param {string} operation - 操作名称
         * @param {Object} context - 上下文信息
         * @returns {Object} 降级处理结果
         */
        async executeFallback(operation, context = {}) {
            const strategy = this.fallbackStrategies[operation];
            if (!strategy) {
                throw new Error(`未找到降级策略: ${operation}`);
            }
            
            this.recordFallback(operation, context);
            
            try {
                // 尝试降级方法
                const fallbackResult = await this.tryFallbackMethod(operation, strategy.fallback, context);
                if (fallbackResult.success) {
                    return fallbackResult;
                }
                
                // 尝试最终降级方法
                const ultimateResult = await this.tryFallbackMethod(operation, strategy.ultimate, context);
                return ultimateResult;
                
            } catch (error) {
                this.logger.logError(`降级处理失败: ${operation}`, error);
                return this.getEmergencyFallback(operation, context);
            }
        }
        
        /**
         * 尝试降级方法
         * @param {string} operation - 操作名称
         * @param {string} method - 降级方法
         * @param {Object} context - 上下文信息
         * @returns {Promise<Object>} 处理结果
         * @private
         */
        async tryFallbackMethod(operation, method, context) {
            try {
                switch (method) {
                    case 'keyword_matching':
                        return await this.keywordMatching(context);
                    case 'generic_processor':
                        return await this.useGenericProcessor(context);
                    case 'regex_extraction':
                        return await this.regexExtraction(context);
                    case 'manual_input':
                        return await this.requestManualInput(context);
                    case 'common_mapping':
                        return await this.useCommonMapping(context);
                    case 'character_analysis':
                        return await this.characterAnalysis(context);
                    case 'default_language':
                        return await this.useDefaultLanguage(context);
                    case 'relaxed_validation':
                        return await this.relaxedValidation(context);
                    case 'minimal_validation':
                        return await this.minimalValidation(context);
                    default:
                        throw new Error(`未知的降级方法: ${method}`);
                }
            } catch (error) {
                this.logger.logError(`降级方法执行失败: ${method}`, error);
                return { success: false, error: error.message };
            }
        }
        
        /**
         * 获取紧急降级方案
         * @param {string} operation - 操作名称
         * @param {Object} context - 上下文信息
         * @returns {Object} 紧急降级结果
         * @private
         */
        getEmergencyFallback(operation, context) {
            this.logger.log(`🚨 执行紧急降级: ${operation}`, 'error');
            
            return {
                success: true,
                data: {
                    ota_channel: 'generic',
                    reference_number: 'EMERGENCY_' + Date.now(),
                    confidence: 0.1,
                    fallback_reason: 'emergency_fallback',
                    processing_mode: 'manual_review_required'
                },
                warnings: ['系统执行了紧急降级，需要人工审核'],
                fallback_level: 'emergency'
            };
        }
        
        /**
         * 记录降级事件
         * @param {string} operation - 操作名称
         * @param {Object} context - 上下文信息
         * @private
         */
        recordFallback(operation, context) {
            const fallbackRecord = {
                operation,
                timestamp: new Date().toISOString(),
                context: { ...context },
                level: this.fallbackState.currentLevel
            };
            
            this.fallbackState.fallbackHistory.push(fallbackRecord);
            this.fallbackState.activeFallbacks.add(operation);
            this.fallbackState.lastFallbackTime = Date.now();
            this.fallbackState.totalFallbackCount++;
            
            // 保持历史记录在合理范围内
            if (this.fallbackState.fallbackHistory.length > 100) {
                this.fallbackState.fallbackHistory = this.fallbackState.fallbackHistory.slice(-50);
            }
        }
        
        /**
         * 获取降级统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                currentLevel: this.fallbackState.currentLevel,
                activeFallbacks: Array.from(this.fallbackState.activeFallbacks),
                totalFallbackCount: this.fallbackState.totalFallbackCount,
                recentFallbacks: this.fallbackState.fallbackHistory.slice(-10),
                availableStrategies: Object.keys(this.fallbackStrategies),
                recoveryMechanisms: Object.keys(this.errorRecoveryMechanisms),
                lastFallbackTime: this.fallbackState.lastFallbackTime,
                version: '1.0.0'
            };
        }
        
        // ==================== 降级方法实现 ====================
        
        /**
         * 关键词匹配降级方法
         * @param {Object} context - 上下文信息
         * @returns {Promise<Object>} 处理结果
         * @private
         */
        async keywordMatching(context) {
            const keywords = {
                'fliggy': ['飞猪', 'fliggy', 'taobao'],
                'ctrip': ['携程', 'ctrip', 'trip.com'],
                'kkday': ['kkday', 'kk day'],
                'klook': ['klook', '客路'],
                'agoda': ['agoda'],
                'booking': ['booking', 'booking.com'],
                'jrcoach': ['jrcoach', 'jr coach']
            };
            
            const text = (context.orderText || '').toLowerCase();
            
            for (const [channel, channelKeywords] of Object.entries(keywords)) {
                for (const keyword of channelKeywords) {
                    if (text.includes(keyword.toLowerCase())) {
                        return {
                            success: true,
                            data: {
                                ota_channel: channel,
                                confidence: 0.6,
                                method: 'keyword_matching',
                                matched_keyword: keyword
                            }
                        };
                    }
                }
            }
            
            return { success: false, error: '未找到匹配的关键词' };
        }
        
        /**
         * 使用通用处理器
         * @param {Object} context - 上下文信息
         * @returns {Promise<Object>} 处理结果
         * @private
         */
        async useGenericProcessor(context) {
            return {
                success: true,
                data: {
                    ota_channel: 'generic',
                    processor: 'generic_processor',
                    confidence: 0.5,
                    method: 'generic_processing'
                }
            };
        }
        
        /**
         * 正则表达式提取
         * @param {Object} context - 上下文信息
         * @returns {Promise<Object>} 处理结果
         * @private
         */
        async regexExtraction(context) {
            const text = context.orderText || '';
            const patterns = [
                /[A-Z]{2,3}\d{6,}/,           // 字母+数字组合
                /\d{8,}/,                     // 8位以上数字
                /[A-Z0-9]{6,}/               // 字母数字混合
            ];
            
            for (const pattern of patterns) {
                const match = text.match(pattern);
                if (match) {
                    return {
                        success: true,
                        data: {
                            reference_number: match[0],
                            confidence: 0.4,
                            method: 'regex_extraction'
                        }
                    };
                }
            }
            
            return { success: false, error: '未找到匹配的参考号模式' };
        }
        
        /**
         * 请求手动输入
         * @param {Object} context - 上下文信息
         * @returns {Promise<Object>} 处理结果
         * @private
         */
        async requestManualInput(context) {
            return {
                success: true,
                data: {
                    manual_input_required: true,
                    confidence: 0.0,
                    method: 'manual_input',
                    message: '需要手动输入或确认信息'
                }
            };
        }
        
        /**
         * 使用通用映射
         * @param {Object} context - 上下文信息
         * @returns {Promise<Object>} 处理结果
         * @private
         */
        async useCommonMapping(context) {
            return {
                success: true,
                data: {
                    mapping_type: 'common',
                    confidence: 0.5,
                    method: 'common_mapping'
                }
            };
        }
        
        /**
         * 字符分析
         * @param {Object} context - 上下文信息
         * @returns {Promise<Object>} 处理结果
         * @private
         */
        async characterAnalysis(context) {
            const text = context.orderText || '';
            const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
            const totalChars = text.length;
            
            if (chineseChars / totalChars > 0.3) {
                return {
                    success: true,
                    data: {
                        language: 'zh',
                        confidence: 0.7,
                        method: 'character_analysis'
                    }
                };
            }
            
            return {
                success: true,
                data: {
                    language: 'en',
                    confidence: 0.6,
                    method: 'character_analysis'
                }
            };
        }
        
        /**
         * 使用默认语言
         * @param {Object} context - 上下文信息
         * @returns {Promise<Object>} 处理结果
         * @private
         */
        async useDefaultLanguage(context) {
            return {
                success: true,
                data: {
                    language: 'en',
                    confidence: 0.3,
                    method: 'default_language'
                }
            };
        }
        
        /**
         * 宽松验证
         * @param {Object} context - 上下文信息
         * @returns {Promise<Object>} 处理结果
         * @private
         */
        async relaxedValidation(context) {
            return {
                success: true,
                data: {
                    validation_level: 'relaxed',
                    confidence: 0.6,
                    method: 'relaxed_validation'
                }
            };
        }
        
        /**
         * 最小验证
         * @param {Object} context - 上下文信息
         * @returns {Promise<Object>} 处理结果
         * @private
         */
        async minimalValidation(context) {
            return {
                success: true,
                data: {
                    validation_level: 'minimal',
                    confidence: 0.4,
                    method: 'minimal_validation'
                }
            };
        }
    }
    
    // 创建全局实例
    const fallbackConfig = new FallbackConfig();
    
    // 注册到全局命名空间
    window.OTA.gemini.configs.FallbackConfig = FallbackConfig;
    window.OTA.gemini.configs.fallbackConfig = fallbackConfig;
    
    // 便捷访问函数
    window.OTA.gemini.configs.shouldFallback = function(operation, metrics) {
        return fallbackConfig.shouldFallback(operation, metrics);
    };
    
    window.OTA.gemini.configs.executeFallback = function(operation, context) {
        return fallbackConfig.executeFallback(operation, context);
    };
    
    // 注册到服务注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('fallback-config', fallbackConfig, {
            dependencies: ['logger'],
            description: '降级处理配置管理器，提供系统降级策略和错误恢复机制'
        });
    }
    
    console.log('✅ 降级处理配置已加载');
    
})();
