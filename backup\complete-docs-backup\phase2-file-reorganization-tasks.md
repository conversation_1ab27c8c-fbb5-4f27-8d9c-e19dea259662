# 阶段二：文件重组任务清单

## 📋 阶段概要

**目标**：优化目录结构，减少文件数量，提高组织性  
**时间**：第3-4周  
**优先级**：高  

## 🎯 核心任务

### 任务组A：目录结构重建

#### A1: 创建新目录结构
**操作清单**：
- [ ] 创建 `js/bootstrap/` 目录
- [ ] 创建 `js/core/` 目录（重新整理）
- [ ] 创建 `js/services/` 目录
- [ ] 创建 `js/managers/` 目录（扩展）
- [ ] 创建 `js/ai/` 目录（替代gemini）
- [ ] 创建 `js/components/` 目录
- [ ] 创建 `js/utils/` 目录（重新整理）

**目录结构**：
```
js/
├── bootstrap/              # 启动和初始化（3个文件）
├── core/                  # 核心基础设施（8个文件）
├── services/              # 业务服务层（6个文件）
├── managers/              # 功能管理器（10个文件）
├── ai/                    # AI服务模块
│   ├── core/             # AI核心引擎
│   ├── processors/       # OTA处理器
│   ├── configs/          # 配置文件
│   └── integrations/     # 系统集成
├── components/            # UI组件
│   ├── multi-order/      # 多订单组件
│   ├── forms/           # 表单组件
│   └── common/          # 通用组件
└── utils/                # 工具类（4个文件）
```

### 任务组B：Bootstrap目录组织

#### B1: 移动启动相关文件
**文件移动计划**：
- [ ] `js/core/dependency-container.js` → `js/bootstrap/dependency-container.js`
- [ ] `js/core/service-locator.js` → `js/bootstrap/service-locator.js`
- [ ] `js/core/application-bootstrap.js` → `js/bootstrap/app-bootstrap.js`

**更新操作**：
- [ ] 更新 `index.html` 中的脚本路径
- [ ] 更新相关文件中的依赖引用
- [ ] 测试启动流程正常

### 任务组C：Core目录精简（22个文件 → 8个文件）

#### C1: 核心文件保留清单
**保留文件**（8个）：
- [ ] `js/core/event-coordinator.js`（由global-event-coordinator.js重命名）
- [ ] `js/core/state-manager.js`（新建，整合状态管理）
- [ ] `js/core/logger.js`（移入core）
- [ ] `js/core/utils.js`（移入core）
- [ ] `js/core/performance-monitor.js`（合并相关功能）
- [ ] `js/core/error-handler.js`（新建，整合错误处理）
- [ ] `js/core/config-manager.js`（整合配置管理）
- [ ] `js/core/lifecycle-manager.js`（整合生命周期管理）

#### C2: 文件合并计划
**合并操作**：

1. **性能监控合并**
   - [ ] 合并 `performance-monitoring-dashboard.js` + `smart-dependency-cache.js` → `performance-monitor.js`
   - [ ] 保留关键性能监控功能
   - [ ] 移除重复代码

2. **错误处理合并**
   - [ ] 新建 `error-handler.js`
   - [ ] 整合错误处理逻辑
   - [ ] 统一错误报告机制

3. **配置管理合并**
   - [ ] 合并 `unified-config-center.js` + `config-migration-tool.js` → `config-manager.js`
   - [ ] 保留配置迁移功能
   - [ ] 简化配置接口

4. **生命周期管理合并**
   - [ ] 合并 `component-lifecycle-manager.js` + 相关功能 → `lifecycle-manager.js`
   - [ ] 统一组件生命周期
   - [ ] 简化管理接口

#### C3: 删除冗余文件
**删除清单**（14个文件）：
- [ ] `js/core/ota-registry.js`（功能整合到其他文件）
- [ ] `js/core/duplicate-checker.js`（与duplicate-detector重复）
- [ ] `js/core/duplicate-detector.js`（功能合并）
- [ ] `js/core/architecture-guardian.js`（开发工具，移到dev目录）
- [ ] `js/core/unified-data-manager.js`（功能分散到相应管理器）
- [ ] `js/core/development-standards-guardian.js`（开发工具）
- [ ] `js/core/dependency-resolver.js`（功能整合到dependency-container）
- [ ] `js/core/unified-dependency-interface.js`（功能整合）
- [ ] `js/core/dom-optimization-engine.js`（功能整合到utils）
- [ ] `js/core/dom-helper.js`（移动到utils目录）
- [ ] `js/core/test-coverage-engine.js`（测试工具，移到tests目录）
- [ ] `js/core/automated-test-runner.js`（测试工具）
- [ ] `js/core/integration-test-coordinator.js`（测试工具）
- [ ] `js/core/lazy-loader.js`（功能整合到bootstrap）
- [ ] `js/core/module-hot-replacement.js`（开发工具）

### 任务组D：Services目录建立

#### D1: 业务服务文件重组
**文件移动和重命名**：
- [ ] `js/api-service.js` → `js/services/api-service.js`
- [ ] `js/gemini-service.js` → `js/services/gemini-service.js`
- [ ] `js/i18n.js` → `js/services/i18n-service.js`
- [ ] `js/hotel-data-inline.js` → `js/services/hotel-data-service.js`
- [ ] `js/ota-channel-mapping.js` → `js/services/channel-mapping-service.js`
- [ ] `js/currency-converter.js` → `js/services/currency-service.js`

**更新操作**：
- [ ] 更新所有引用这些服务的文件
- [ ] 更新HTML脚本标签
- [ ] 测试服务功能正常

### 任务组E：Managers目录扩展（5个文件 → 10个文件）

#### E1: 现有managers文件保留
**保留文件**（5个）：
- [ ] `js/managers/form-manager.js`
- [ ] `js/managers/event-manager.js`
- [ ] `js/managers/state-manager.js`
- [ ] `js/managers/price-manager.js`
- [ ] `js/managers/realtime-analysis-manager.js`

#### E2: 新增managers文件
**移入managers目录**（5个）：
- [ ] `js/ui-manager.js` → `js/managers/ui-manager.js`
- [ ] `js/multi-order-manager.js` → `js/managers/multi-order-manager.js`
- [ ] `js/order-history-manager.js` → `js/managers/order-history-manager.js`
- [ ] `js/image-upload-manager.js` → `js/managers/image-upload-manager.js`
- [ ] `js/paging-service-manager.js` → `js/managers/paging-manager.js`

**更新操作**：
- [ ] 更新所有引用这些管理器的文件
- [ ] 确保Manager模式的一致性
- [ ] 测试管理器功能

### 任务组F：AI目录重组（gemini → ai）

#### F1: AI核心引擎（gemini/core → ai/core）
**文件移动**：
- [ ] `js/gemini/core/base-processor.js` → `js/ai/core/base-processor.js`
- [ ] `js/gemini/core/config-manager.js` → `js/ai/core/config-manager.js`
- [ ] `js/gemini/core/data-normalizer.js` → `js/ai/core/data-normalizer.js`
- [ ] `js/gemini/core/error-handler.js` → `js/ai/core/error-handler.js`
- [ ] `js/gemini/core/error-handling-center.js` → 合并到 `js/ai/core/error-handler.js`
- [ ] `js/gemini/core/error-recovery-engine.js` → `js/ai/core/error-recovery.js`
- [ ] `js/gemini/core/flight-number-processor.js` → `js/ai/core/flight-processor.js`
- [ ] `js/gemini/core/image-analysis-engine.js` → `js/ai/core/image-analyzer.js`
- [ ] `js/gemini/core/language-ota-integration.js` → `js/ai/core/language-integration.js`
- [ ] `js/gemini/core/ota-channel-identifier.js` → `js/ai/core/channel-identifier.js`
- [ ] `js/gemini/core/ota-reference-engine.js` → `js/ai/core/reference-engine.js`
- [ ] `js/gemini/core/performance-monitor.js` → 合并到 `js/core/performance-monitor.js`
- [ ] `js/gemini/core/processor-router.js` → `js/ai/core/processor-router.js`
- [ ] `js/gemini/core/prompt-template-engine.js` → `js/ai/core/template-engine.js`
- [ ] `js/gemini/core/prompt-template-system.js` → 合并到 `js/ai/core/template-engine.js`
- [ ] `js/gemini/core/service-registry.js` → 合并到依赖容器
- [ ] `js/gemini/core/address-translator.js` → `js/ai/core/address-translator.js`

#### F2: AI处理器（gemini/processors → ai/processors）
**文件移动**：
- [ ] `js/gemini/processors/agoda-processor.js` → `js/ai/processors/agoda-processor.js`
- [ ] `js/gemini/processors/booking-processor.js` → `js/ai/processors/booking-processor.js`
- [ ] `js/gemini/processors/chong-dealer-processor.js` → `js/ai/processors/chong-dealer-processor.js`
- [ ] `js/gemini/processors/ctrip-processor.js` → `js/ai/processors/ctrip-processor.js`
- [ ] `js/gemini/processors/fliggy-processor.js` → `js/ai/processors/fliggy-processor.js`
- [ ] `js/gemini/processors/generic-processor.js` → `js/ai/processors/generic-processor.js`
- [ ] `js/gemini/processors/jrcoach-processor.js` → `js/ai/processors/jrcoach-processor.js`
- [ ] `js/gemini/processors/kkday-processor.js` → `js/ai/processors/kkday-processor.js`
- [ ] `js/gemini/processors/klook-processor.js` → `js/ai/processors/klook-processor.js`

#### F3: AI配置（gemini/configs → ai/configs）
**文件移动和合并**：
- [ ] `js/gemini/configs/field-mapping.js` → `js/ai/configs/field-mapping.js`
- [ ] `js/gemini/configs/preset-values.js` → `js/ai/configs/preset-values.js`
- [ ] `js/gemini/configs/fallback-config.js` → `js/ai/configs/fallback-config.js`
- [ ] `js/gemini/configs/performance-optimization.js` → 合并到 `js/ai/configs/performance.js`
- [ ] `js/gemini/configs/processor-configs.js` → 合并到 `js/ai/configs/processors.js`
- [ ] `js/gemini/configs/processor-field-mappings.js` → 合并到 `js/ai/configs/field-mapping.js`
- [ ] `js/gemini/configs/processor-preset-values.js` → 合并到 `js/ai/configs/preset-values.js`
- [ ] `js/gemini/configs/fallback-processing-config.js` → 合并到 `js/ai/configs/fallback-config.js`
- [ ] `js/gemini/configs/ota-reference-patterns.js` → `js/ai/configs/reference-patterns.js`

#### F4: AI集成（gemini/integrations → ai/integrations）
**文件移动**：
- [ ] `js/gemini/integrations/language-manager-integration.js` → `js/ai/integrations/language-integration.js`
- [ ] `js/gemini/integration/system-integration-fix.js` → `js/ai/integrations/system-integration.js`

#### F5: AI协调器重命名
**文件移动**：
- [ ] `js/gemini/gemini-coordinator.js` → `js/ai/ai-coordinator.js`

### 任务组G：Components目录建立

#### G1: 多订单组件重组（15个文件 → 3个管理器）
**创建目录**：
- [ ] `js/components/multi-order/`

**文件整合**：

1. **多订单状态管理组件**
   - [ ] 合并以下文件 → `js/components/multi-order/state-manager.js`：
     - `js/multi-order/multi-order-state-manager.js`
     - `js/multi-order/multi-order-event-manager.js`
     - `js/multi-order/multi-order-validation-manager.js`

2. **多订单UI组件**
   - [ ] 合并以下文件 → `js/components/multi-order/ui-manager.js`：
     - `js/multi-order/multi-order-ui-manager.js`
     - `js/multi-order/multi-order-renderer.js`
     - `js/multi-order/multi-order-quick-edit-manager.js`

3. **多订单处理组件**
   - [ ] 合并以下文件 → `js/components/multi-order/processor.js`：
     - `js/multi-order/multi-order-processor.js`
     - `js/multi-order/multi-order-transformer.js`
     - `js/multi-order/multi-order-batch-manager.js`
     - `js/multi-order/multi-order-cleanup-manager.js`

4. **多订单工具组件**
   - [ ] 合并以下文件 → `js/components/multi-order/utils.js`：
     - `js/multi-order/multi-order-utils.js`
     - `js/multi-order/multi-order-detector.js`
     - `js/multi-order/multi-order-chrome-mcp.js`

5. **多订单配置组件**
   - [ ] 合并以下文件 → `js/components/multi-order/config.js`：
     - `js/multi-order/field-mapping-config.js`
     - `js/multi-order/field-mapping-validator.js`
     - `js/multi-order/field-mapping-tests.js`

**删除原文件**：
- [ ] 删除 `js/multi-order/` 目录下的所有15个文件

#### G2: 表单组件目录
**创建目录**：
- [ ] `js/components/forms/`

**规划文件**（未来扩展）：
- [ ] `js/components/forms/form-validator.js`
- [ ] `js/components/forms/field-components.js`
- [ ] `js/components/forms/form-builder.js`

#### G3: 通用组件目录
**创建目录**：
- [ ] `js/components/common/`

**规划文件**（未来扩展）：
- [ ] `js/components/common/modal.js`
- [ ] `js/components/common/alert.js`
- [ ] `js/components/common/loading.js`

### 任务组H：Utils目录重组

#### H1: 工具类文件整合
**新utils目录结构**：
- [ ] `js/utils/dom-helper.js`（从core移入）
- [ ] `js/utils/performance-utils.js`（提取性能相关工具）
- [ ] `js/utils/validation-utils.js`（提取验证相关工具）
- [ ] `js/utils/formatting-utils.js`（提取格式化工具）

**文件移动**：
- [ ] `js/grid-resizer.js` → `js/utils/grid-resizer.js`
- [ ] `js/core/dom-helper.js` → `js/utils/dom-helper.js`

**功能提取**：
- [ ] 从 `js/utils.js` 提取DOM相关功能到 `dom-helper.js`
- [ ] 从 `js/utils.js` 提取性能相关功能到 `performance-utils.js`
- [ ] 从 `js/utils.js` 提取验证相关功能到 `validation-utils.js`
- [ ] 从 `js/utils.js` 提取格式化功能到 `formatting-utils.js`

### 任务组I：HTML脚本标签更新

#### I1: 新的脚本加载顺序
**关键路径脚本**（8个）：
```html
<!-- Bootstrap层 -->
<script src="js/bootstrap/dependency-container.js"></script>
<script src="js/bootstrap/service-locator.js"></script>
<script src="js/bootstrap/app-bootstrap.js"></script>

<!-- Core层 -->
<script src="js/core/logger.js"></script>
<script src="js/core/utils.js"></script>
<script src="js/core/event-coordinator.js"></script>

<!-- Services层 -->
<script src="js/services/api-service.js"></script>

<!-- Managers层 -->
<script src="js/managers/ui-manager.js"></script>
```

#### I2: 懒加载脚本配置
**非关键脚本**（异步加载）：
```html
<!-- AI服务 -->
<script src="js/services/gemini-service.js" async></script>
<script src="js/ai/ai-coordinator.js" async></script>

<!-- 功能管理器 -->
<script src="js/managers/multi-order-manager.js" async></script>
<script src="js/managers/order-history-manager.js" async></script>

<!-- 组件 -->
<script src="js/components/multi-order/state-manager.js" async></script>
<script src="js/components/multi-order/ui-manager.js" async></script>
```

#### I3: 脚本标签更新清单
- [ ] 更新所有bootstrap脚本路径
- [ ] 更新所有core脚本路径
- [ ] 更新所有services脚本路径
- [ ] 更新所有managers脚本路径
- [ ] 添加ai目录脚本
- [ ] 添加components目录脚本
- [ ] 添加utils目录脚本
- [ ] 移除已删除文件的脚本标签

## 🧪 测试验证

### 文件完整性测试
**验证清单**：
- [ ] 所有文件移动成功，无丢失
- [ ] 新目录结构符合设计
- [ ] 合并文件功能完整
- [ ] 删除文件清理干净

### 依赖关系测试
**验证清单**：
- [ ] 所有文件引用路径正确
- [ ] 模块导入导出正常
- [ ] 依赖注入功能正常
- [ ] 服务获取路径正确

### 功能集成测试
**测试场景**：
- [ ] 系统启动流程
- [ ] 用户登录功能
- [ ] 订单创建功能
- [ ] 多订单管理功能
- [ ] AI解析功能
- [ ] UI交互功能

### 性能对比测试
**测试指标**：
- [ ] 脚本加载时间
- [ ] 首屏渲染时间
- [ ] 内存使用情况
- [ ] 网络请求数量

## 📊 成功标准

### 结构指标
- [ ] 新目录结构创建完成
- [ ] 文件数量从89个减少到约45个
- [ ] core目录从22个文件减少到8个
- [ ] managers目录从5个文件扩展到10个

### 功能指标
- [ ] 所有现有功能正常运行
- [ ] 文件引用路径全部正确
- [ ] 模块化程度显著提升
- [ ] 代码重复度降低

### 性能指标
- [ ] 脚本加载时间减少≥30%
- [ ] 首屏渲染时间减少≥20%
- [ ] 网络请求优化合理
- [ ] 内存使用稳定

## 🗓️ 时间规划

### 第3周
**周一**：任务组A-B（目录创建和bootstrap重组）
**周二**：任务组C（core目录精简）
**周三**：任务组D-E（services和managers重组）
**周四**：任务组F（AI目录重组第一部分）
**周五**：任务组F（AI目录重组第二部分）

### 第4周
**周一**：任务组G（components目录建立）
**周二**：任务组H（utils目录重组）
**周三**：任务组I（HTML脚本更新）
**周四**：全面测试和问题修复
**周五**：文档更新和验收

## 🚨 风险提醒

### 高风险操作
- [ ] 大量文件移动可能导致引用错误
- [ ] AI目录重组涉及20个文件
- [ ] multi-order目录合并复杂度高
- [ ] HTML脚本标签更新影响加载

### 质量保证
- [ ] 每次文件移动后立即测试
- [ ] 使用自动化工具检查引用
- [ ] 分批次进行文件重组
- [ ] 保持Git提交记录清晰

### 回滚预案
- [ ] 每个任务组完成后创建Git标签
- [ ] 保留原始文件结构备份
- [ ] 准备快速回滚脚本
- [ ] 建立文件对照表

---

**任务清单版本**：v1.0  
**创建日期**：2025-01-27  
**预计完成**：2025-02-17  
**负责人**：前端架构组