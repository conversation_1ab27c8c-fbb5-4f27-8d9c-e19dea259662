/**
 * 服务注册验证器
 * 确保所有服务正确注册，防止运行时错误
 * 
 * 功能特性:
 * - 服务注册完整性检查
 * - 循环依赖检测
 * - 服务健康状态监控
 * - 注册冲突检测
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.core = window.OTA.core || {};

(function() {
    'use strict';

    /**
     * 服务注册验证器类
     */
    class ServiceRegistryValidator {
        constructor() {
            // 必需服务列表
            this.requiredServices = [
                'logger',
                'appState', 
                'apiService',
                'uiManager',
                'eventCoordinator'
            ];
            
            // 可选服务列表
            this.optionalServices = [
                'geminiService',
                'multiOrderManager',
                'configManager',
                'utils'
            ];
            
            // 验证结果
            this.validationResults = {
                timestamp: null,
                passed: false,
                errors: [],
                warnings: [],
                registeredServices: [],
                missingServices: [],
                healthStatus: new Map()
            };
            
            // 健康检查配置
            this.healthCheckConfig = {
                enabled: true,
                interval: 30000, // 30秒检查一次
                timeout: 5000,   // 5秒超时
                retryCount: 3
            };
            
            // 循环依赖检测
            this.dependencyGraph = new Map();
            this.visitedNodes = new Set();
            this.recursionStack = new Set();
            
            this.initialize();
        }

        /**
         * 初始化验证器
         */
        initialize() {
            // 延迟获取警告管理器
            setTimeout(() => {
                this.warningManager = window.OTA?.core?.warningManager;
            }, 100);
            
            // 启动定期验证
            this.startPeriodicValidation();
            
            console.log('✅ 服务注册验证器已初始化');
        }

        /**
         * 执行完整的服务注册验证
         * @returns {Object} 验证结果
         */
        validateServiceRegistry() {
            console.log('🔍 开始服务注册验证...');
            
            // 重置验证结果
            this.resetValidationResults();
            
            // 1. 检查服务注册完整性
            this.checkServiceRegistration();
            
            // 2. 检测循环依赖
            this.detectCircularDependencies();
            
            // 3. 执行健康检查
            if (this.healthCheckConfig.enabled) {
                this.performHealthChecks();
            }
            
            // 4. 检查注册冲突
            this.checkRegistrationConflicts();
            
            // 5. 生成验证报告
            this.generateValidationReport();
            
            return this.validationResults;
        }

        /**
         * 重置验证结果
         */
        resetValidationResults() {
            this.validationResults = {
                timestamp: new Date().toISOString(),
                passed: false,
                errors: [],
                warnings: [],
                registeredServices: [],
                missingServices: [],
                healthStatus: new Map()
            };
        }

        /**
         * 检查服务注册完整性
         */
        checkServiceRegistration() {
            const accessor = window.OTA?.core?.unifiedServiceAccessor;
            if (!accessor) {
                this.validationResults.errors.push({
                    type: 'MISSING_ACCESSOR',
                    message: '统一服务访问器未找到',
                    severity: 'critical'
                });
                return;
            }

            // 检查必需服务
            this.requiredServices.forEach(serviceName => {
                try {
                    const service = accessor.getService(serviceName);
                    if (service) {
                        this.validationResults.registeredServices.push(serviceName);
                    } else {
                        this.validationResults.missingServices.push(serviceName);
                        this.validationResults.errors.push({
                            type: 'MISSING_REQUIRED_SERVICE',
                            service: serviceName,
                            message: `必需服务 ${serviceName} 未注册`,
                            severity: 'critical'
                        });
                    }
                } catch (error) {
                    this.validationResults.missingServices.push(serviceName);
                    this.validationResults.errors.push({
                        type: 'SERVICE_ACCESS_ERROR',
                        service: serviceName,
                        message: `访问服务 ${serviceName} 时出错: ${error.message}`,
                        severity: 'critical'
                    });
                }
            });

            // 检查可选服务
            this.optionalServices.forEach(serviceName => {
                try {
                    const service = accessor.getService(serviceName);
                    if (service) {
                        this.validationResults.registeredServices.push(serviceName);
                    } else {
                        this.validationResults.warnings.push({
                            type: 'MISSING_OPTIONAL_SERVICE',
                            service: serviceName,
                            message: `可选服务 ${serviceName} 未注册`,
                            severity: 'warning'
                        });
                    }
                } catch (error) {
                    this.validationResults.warnings.push({
                        type: 'OPTIONAL_SERVICE_ACCESS_ERROR',
                        service: serviceName,
                        message: `访问可选服务 ${serviceName} 时出错: ${error.message}`,
                        severity: 'warning'
                    });
                }
            });
        }

        /**
         * 检测循环依赖
         */
        detectCircularDependencies() {
            // 构建依赖图
            this.buildDependencyGraph();
            
            // 使用DFS检测循环
            for (const serviceName of this.dependencyGraph.keys()) {
                if (!this.visitedNodes.has(serviceName)) {
                    const cycle = this.detectCycleFromNode(serviceName);
                    if (cycle.length > 0) {
                        this.validationResults.errors.push({
                            type: 'CIRCULAR_DEPENDENCY',
                            cycle: cycle,
                            message: `检测到循环依赖: ${cycle.join(' → ')}`,
                            severity: 'high'
                        });
                    }
                }
            }
        }

        /**
         * 构建依赖图
         */
        buildDependencyGraph() {
            // 简化的依赖关系映射
            const knownDependencies = {
                'uiManager': ['logger', 'eventCoordinator'],
                'eventCoordinator': ['logger'],
                'multiOrderManager': ['logger', 'uiManager'],
                'apiService': ['logger'],
                'geminiService': ['logger', 'configManager']
            };

            for (const [service, deps] of Object.entries(knownDependencies)) {
                this.dependencyGraph.set(service, deps);
            }
        }

        /**
         * 从指定节点检测循环依赖
         * @param {string} node - 起始节点
         * @returns {Array} 循环路径
         */
        detectCycleFromNode(node) {
            this.visitedNodes.add(node);
            this.recursionStack.add(node);

            const dependencies = this.dependencyGraph.get(node) || [];
            
            for (const dep of dependencies) {
                if (!this.visitedNodes.has(dep)) {
                    const cycle = this.detectCycleFromNode(dep);
                    if (cycle.length > 0) {
                        return [node, ...cycle];
                    }
                } else if (this.recursionStack.has(dep)) {
                    // 找到循环
                    return [node, dep];
                }
            }

            this.recursionStack.delete(node);
            return [];
        }

        /**
         * 执行健康检查
         */
        performHealthChecks() {
            const accessor = window.OTA?.core?.unifiedServiceAccessor;
            if (!accessor) return;

            this.validationResults.registeredServices.forEach(serviceName => {
                try {
                    const service = accessor.getService(serviceName);
                    const healthStatus = this.checkServiceHealth(service, serviceName);
                    this.validationResults.healthStatus.set(serviceName, healthStatus);
                    
                    if (!healthStatus.healthy) {
                        this.validationResults.warnings.push({
                            type: 'UNHEALTHY_SERVICE',
                            service: serviceName,
                            message: `服务 ${serviceName} 健康检查失败: ${healthStatus.reason}`,
                            severity: 'warning'
                        });
                    }
                } catch (error) {
                    this.validationResults.healthStatus.set(serviceName, {
                        healthy: false,
                        reason: error.message,
                        timestamp: Date.now()
                    });
                }
            });
        }

        /**
         * 检查单个服务的健康状态
         * @param {any} service - 服务实例
         * @param {string} serviceName - 服务名称
         * @returns {Object} 健康状态
         */
        checkServiceHealth(service, serviceName) {
            const healthStatus = {
                healthy: true,
                reason: '',
                timestamp: Date.now()
            };

            // 基本存在性检查
            if (!service) {
                healthStatus.healthy = false;
                healthStatus.reason = '服务实例为空';
                return healthStatus;
            }

            // 特定服务的健康检查
            switch (serviceName) {
                case 'logger':
                    if (typeof service.log !== 'function') {
                        healthStatus.healthy = false;
                        healthStatus.reason = '缺少log方法';
                    }
                    break;
                    
                case 'apiService':
                    if (typeof service.createOrder !== 'function') {
                        healthStatus.healthy = false;
                        healthStatus.reason = '缺少createOrder方法';
                    }
                    break;
                    
                case 'uiManager':
                    if (typeof service.updateUI !== 'function') {
                        healthStatus.healthy = false;
                        healthStatus.reason = '缺少updateUI方法';
                    }
                    break;
            }

            return healthStatus;
        }

        /**
         * 检查注册冲突
         */
        checkRegistrationConflicts() {
            // 检查全局命名空间冲突
            const globalConflicts = [];
            
            this.validationResults.registeredServices.forEach(serviceName => {
                if (window[serviceName] && window.OTA[serviceName]) {
                    globalConflicts.push(serviceName);
                }
            });

            if (globalConflicts.length > 0) {
                this.validationResults.warnings.push({
                    type: 'NAMESPACE_CONFLICT',
                    services: globalConflicts,
                    message: `检测到命名空间冲突: ${globalConflicts.join(', ')}`,
                    severity: 'warning'
                });
            }
        }

        /**
         * 生成验证报告
         */
        generateValidationReport() {
            const hasErrors = this.validationResults.errors.length > 0;
            const hasWarnings = this.validationResults.warnings.length > 0;
            
            this.validationResults.passed = !hasErrors;

            // 输出报告
            if (hasErrors) {
                console.error('❌ 服务注册验证失败');
                this.validationResults.errors.forEach(error => {
                    console.error(`  🚨 ${error.message}`);
                });
            }

            if (hasWarnings) {
                console.warn('⚠️ 服务注册验证警告');
                this.validationResults.warnings.forEach(warning => {
                    console.warn(`  ⚠️ ${warning.message}`);
                });
            }

            if (!hasErrors && !hasWarnings) {
                console.log('✅ 服务注册验证通过');
            }

            // 使用智能警告管理器报告问题
            if (this.warningManager) {
                this.validationResults.errors.forEach(error => {
                    this.warningManager.warn(
                        'SERVICE_REGISTRY_ERROR',
                        error.message,
                        'CRITICAL',
                        error
                    );
                });

                this.validationResults.warnings.forEach(warning => {
                    this.warningManager.warn(
                        'SERVICE_REGISTRY_WARNING',
                        warning.message,
                        'WARNING',
                        warning
                    );
                });
            }
        }

        /**
         * 启动定期验证
         */
        startPeriodicValidation() {
            if (this.healthCheckConfig.enabled) {
                setInterval(() => {
                    this.validateServiceRegistry();
                }, this.healthCheckConfig.interval);
            }
        }

        /**
         * 获取验证统计报告
         * @returns {Object} 统计报告
         */
        getValidationStats() {
            return {
                最后验证时间: this.validationResults.timestamp,
                验证状态: this.validationResults.passed ? '通过' : '失败',
                已注册服务数: this.validationResults.registeredServices.length,
                缺失服务数: this.validationResults.missingServices.length,
                错误数量: this.validationResults.errors.length,
                警告数量: this.validationResults.warnings.length,
                健康服务数: Array.from(this.validationResults.healthStatus.values()).filter(h => h.healthy).length
            };
        }
    }

    // 创建全局唯一的服务注册验证器实例
    const serviceRegistryValidator = new ServiceRegistryValidator();

    // 暴露到OTA命名空间
    window.OTA.core.serviceRegistryValidator = serviceRegistryValidator;

    // 提供便捷的验证接口
    window.OTA.validateServices = function() {
        return serviceRegistryValidator.validateServiceRegistry();
    };

    console.log('✅ 服务注册验证器模块已加载');

})();
