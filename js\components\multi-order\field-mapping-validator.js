/**
 * @OTA_VALIDATOR 字段映射验证器
 * 🏷️ 标签: @OTA_FIELD_MAPPING_VALIDATOR
 * 📝 说明: 验证字段映射的完整性和正确性，确保数据转换无误
 * ⚠️ 警告: 验证失败可能导致API调用错误
 * <AUTHOR>
 * @version 1.0.0
 */

// 防止重复加载
if (window.OTA && window.OTA.FieldMappingValidator) {
    console.log('字段映射验证器已存在，跳过重复加载');
} else {

/**
 * 字段映射验证器类
 * 提供各种字段映射验证和转换功能
 */
class FieldMappingValidator {
    
    /**
     * 验证API字段完整性
     * @param {Object} orderData - 订单数据
     * @returns {Object} 验证结果
     */
    static validateApiFields(orderData) {
        const config = window.OTA?.FieldMappingConfig || window.FIELD_MAPPING_CONFIG;
        if (!config) {
            throw new Error('字段映射配置未加载');
        }

        const requiredFields = config.REQUIRED_API_FIELDS;
        const missingFields = [];
        const invalidFields = [];
        const warnings = [];

        // 检查必填字段
        requiredFields.forEach(field => {
            if (!orderData.hasOwnProperty(field) || orderData[field] === null || orderData[field] === undefined) {
                missingFields.push(field);
            } else if (orderData[field] === '' && field !== 'extra_requirement') {
                // 空字符串也视为缺失（除了extra_requirement可以为空）
                missingFields.push(field);
            }
        });

        // 检查字段格式
        const formatWarnings = this.checkFieldFormats(orderData);
        warnings.push(...formatWarnings);

        // 检查languages_id_array格式
        if (orderData.languages_id_array) {
            const languageFormatCheck = this.validateLanguagesIdArrayFormat(orderData.languages_id_array);
            if (!languageFormatCheck.isValid) {
                warnings.push(languageFormatCheck.message);
            }
        }

        return {
            isValid: missingFields.length === 0 && invalidFields.length === 0,
            missingFields,
            invalidFields,
            warnings,
            fieldCount: Object.keys(orderData).length
        };
    }

    /**
     * 验证前端字段完整性
     * @param {Object} orderData - 订单数据
     * @returns {Object} 验证结果
     */
    static validateFrontendFields(orderData) {
        const config = window.OTA?.FieldMappingConfig || window.FIELD_MAPPING_CONFIG;
        if (!config) {
            throw new Error('字段映射配置未加载');
        }

        const requiredFields = config.REQUIRED_FRONTEND_FIELDS;
        const missingFields = [];
        const typeErrors = [];

        requiredFields.forEach(field => {
            if (!orderData.hasOwnProperty(field) || orderData[field] === null || orderData[field] === undefined) {
                missingFields.push(field);
            } else {
                // 检查数据类型
                const expectedType = config.FIELD_TYPES[field];
                const actualType = this.getFieldType(orderData[field]);
                
                if (expectedType && expectedType !== actualType) {
                    typeErrors.push({
                        field,
                        expected: expectedType,
                        actual: actualType,
                        value: orderData[field]
                    });
                }
            }
        });

        return {
            isValid: missingFields.length === 0 && typeErrors.length === 0,
            missingFields,
            typeErrors,
            fieldCount: Object.keys(orderData).length
        };
    }

    /**
     * 检查字段格式
     * @param {Object} orderData - 订单数据
     * @returns {Array} 警告信息数组
     */
    static checkFieldFormats(orderData) {
        const warnings = [];

        // 检查日期格式
        if (orderData.pickup_date && !/^\d{4}-\d{2}-\d{2}$/.test(orderData.pickup_date)) {
            warnings.push(`日期格式不正确: ${orderData.pickup_date}，应为YYYY-MM-DD格式`);
        }

        // 检查时间格式
        if (orderData.pickup_time && !/^\d{2}:\d{2}$/.test(orderData.pickup_time)) {
            warnings.push(`时间格式不正确: ${orderData.pickup_time}，应为HH:MM格式`);
        }

        // 检查邮箱格式
        if (orderData.customer_email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(orderData.customer_email)) {
            warnings.push(`邮箱格式不正确: ${orderData.customer_email}`);
        }

        // 检查数值字段
        if (orderData.passenger_count && (isNaN(orderData.passenger_count) || orderData.passenger_count <= 0)) {
            warnings.push(`乘客数量无效: ${orderData.passenger_count}`);
        }

        if (orderData.ota_price && (isNaN(orderData.ota_price) || orderData.ota_price <= 0)) {
            warnings.push(`价格无效: ${orderData.ota_price}`);
        }

        return warnings;
    }

    /**
     * 验证并转换languages_id_array格式
     * @param {*} languagesIdArray - 语言ID数组或对象
     * @returns {Object} 验证和转换结果
     */
    static validateLanguagesIdArrayFormat(languagesIdArray) {
        if (!languagesIdArray) {
            return {
                isValid: false,
                message: 'languages_id_array字段缺失',
                converted: {"0": "2"} // 默认英文
            };
        }

        // 如果是数组格式，转换为对象格式
        if (Array.isArray(languagesIdArray)) {
            const objectFormat = {};
            languagesIdArray.forEach((id, index) => {
                objectFormat[index.toString()] = id.toString();
            });
            
            return {
                isValid: true,
                message: '已将数组格式转换为对象格式',
                converted: objectFormat,
                wasArray: true
            };
        }

        // 如果已经是对象格式，验证格式正确性
        if (typeof languagesIdArray === 'object' && languagesIdArray !== null) {
            const keys = Object.keys(languagesIdArray);
            const isValidObjectFormat = keys.every(key => 
                /^\d+$/.test(key) && /^\d+$/.test(languagesIdArray[key].toString())
            );

            if (isValidObjectFormat) {
                return {
                    isValid: true,
                    message: '对象格式正确',
                    converted: languagesIdArray
                };
            } else {
                return {
                    isValid: false,
                    message: '对象格式不正确，键和值都应为数字字符串',
                    converted: {"0": "2"} // 默认英文
                };
            }
        }

        return {
            isValid: false,
            message: `不支持的languages_id_array格式: ${typeof languagesIdArray}`,
            converted: {"0": "2"} // 默认英文
        };
    }

    /**
     * 比较重构前后的字段映射结果
     * @param {Object} originalData - 原始数据
     * @param {Object} transformedData - 转换后数据
     * @returns {Object} 比较结果
     */
    static compareFieldMapping(originalData, transformedData) {
        const comparison = {
            identical: true,
            differences: [],
            missingInTransformed: [],
            addedInTransformed: [],
            summary: {}
        };

        // 检查原始数据中的字段是否在转换后数据中存在
        Object.keys(originalData).forEach(key => {
            if (!transformedData.hasOwnProperty(key)) {
                comparison.missingInTransformed.push(key);
                comparison.identical = false;
            } else if (originalData[key] !== transformedData[key]) {
                comparison.differences.push({
                    field: key,
                    original: originalData[key],
                    transformed: transformedData[key]
                });
                comparison.identical = false;
            }
        });

        // 检查转换后数据中新增的字段
        Object.keys(transformedData).forEach(key => {
            if (!originalData.hasOwnProperty(key)) {
                comparison.addedInTransformed.push(key);
            }
        });

        comparison.summary = {
            totalOriginalFields: Object.keys(originalData).length,
            totalTransformedFields: Object.keys(transformedData).length,
            identicalFields: Object.keys(originalData).length - comparison.differences.length - comparison.missingInTransformed.length,
            differenceCount: comparison.differences.length,
            missingCount: comparison.missingInTransformed.length,
            addedCount: comparison.addedInTransformed.length
        };

        return comparison;
    }

    /**
     * 获取字段的实际数据类型
     * @param {*} value - 字段值
     * @returns {string} 数据类型
     */
    static getFieldType(value) {
        if (value === null || value === undefined) return 'null';
        if (Array.isArray(value)) return 'array';
        if (value instanceof Date) return 'date';
        if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(value)) return 'date';
        if (typeof value === 'string' && /^\d{2}:\d{2}$/.test(value)) return 'time';
        return typeof value;
    }

    /**
     * 生成字段映射报告
     * @param {Object} orderData - 订单数据
     * @param {string} dataType - 数据类型 ('frontend' | 'api')
     * @returns {Object} 详细报告
     */
    static generateMappingReport(orderData, dataType = 'frontend') {
        const report = {
            timestamp: new Date().toISOString(),
            dataType,
            validation: null,
            fieldAnalysis: {},
            recommendations: []
        };

        // 执行相应的验证
        if (dataType === 'api') {
            report.validation = this.validateApiFields(orderData);
        } else {
            report.validation = this.validateFrontendFields(orderData);
        }

        // 分析每个字段
        Object.keys(orderData).forEach(field => {
            report.fieldAnalysis[field] = {
                value: orderData[field],
                type: this.getFieldType(orderData[field]),
                isEmpty: !orderData[field] && orderData[field] !== 0 && orderData[field] !== false,
                isRequired: dataType === 'api' ? 
                    (window.OTA?.FieldMappingConfig?.REQUIRED_API_FIELDS || []).includes(field) :
                    (window.OTA?.FieldMappingConfig?.REQUIRED_FRONTEND_FIELDS || []).includes(field)
            };
        });

        // 生成建议
        if (report.validation.missingFields.length > 0) {
            report.recommendations.push('补充缺失的必填字段');
        }
        if (report.validation.warnings && report.validation.warnings.length > 0) {
            report.recommendations.push('修复字段格式问题');
        }

        return report;
    }
}

// 导出验证器
window.OTA = window.OTA || {};
window.OTA.FieldMappingValidator = FieldMappingValidator;

// 向后兼容
window.FieldMappingValidator = FieldMappingValidator;

console.log('✅ 字段映射验证器已加载');

// 结束防重复加载检查
}
