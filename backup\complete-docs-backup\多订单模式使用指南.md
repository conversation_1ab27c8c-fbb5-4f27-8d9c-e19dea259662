# 多订单模式使用指南 v2.0

## 概述

多订单模式是OTA订单处理系统的核心功能之一，经过v2.0重大升级，现在支持一体化智能解析和结构化订单编辑，大幅提升用户体验和工作效率。

## 🚀 v2.0新特性

### 一体化智能解析
- **单次AI调用**：输入文本 → 完整解析 → 结构化显示
- **智能字段映射**：自动填充所有订单字段
- **ID智能匹配**：车型、区域、语言等自动匹配
- **货币识别**：支持MYR、USD、SGD、CNY自动识别

### 全新用户界面
- **订单卡片**：清晰的订单摘要展示
- **详细编辑器**：完整的字段编辑界面
- **实时验证**：字段输入时即时验证
- **批量操作**：支持选择性创建和批量处理

## 使用流程

### 第一步：输入多订单文本
1. 在主页面的"订单输入"框中粘贴包含多个订单的文本
2. 系统会自动检测文本长度和内容特征
3. 当检测到可能的多订单时，会自动触发AI分析

### 第二步：AI智能解析
系统会：
- 使用Gemini AI分析文本内容
- 检测是否包含多个独立订单
- 自动分割并解析每个订单的详细信息
- 填充所有可识别的字段

### 第三步：查看解析结果
多订单面板会显示：
- 每个订单的摘要卡片
- 客户信息、路线、时间、价格等关键信息
- 解析状态和置信度指示器

### 第四步：编辑订单详情
1. 点击订单卡片上的"📋 详情"按钮
2. 展开完整的字段编辑器
3. 修改任何需要调整的字段
4. 系统会实时更新订单摘要

### 第五步：批量创建订单
1. 选择要创建的订单（默认全选）
2. 点击"创建选中订单"按钮
3. 系统会显示创建进度
4. 完成后显示成功/失败统计

## 界面详解

### 订单摘要卡片
```
┌─────────────────────────────────────┐
│ ☑️ 订单 1                    [已解析] │
├─────────────────────────────────────┤
│ 客户: 张三                          │
│ 路线: 机场 → 酒店                   │
│ 时间: 2025-01-15 09:00              │
│ 乘客: 2人                          │
│ 价格: MYR 150                      │
├─────────────────────────────────────┤
│     [📋 详情]  [✏️ 编辑]           │
└─────────────────────────────────────┘
```

### 详细字段编辑器
展开后包含所有订单字段：
- **客户信息**：姓名、电话、邮箱
- **行程信息**：上车地点、目的地、日期、时间
- **服务配置**：乘客人数、行李件数、车型
- **其他信息**：航班信息、OTA订单号、价格、货币
- **特殊服务**：儿童座椅、导游服务、迎接服务

## 高级功能

### 智能字段映射
系统会根据内容自动匹配：
- **车型ID**：根据乘客人数推荐合适车型
- **服务类型**：接机(2)、送机(3)、包车(4)
- **行驶区域**：根据地点信息智能推断
- **语言要求**：默认中英文，可调整

### 实时验证
字段编辑时提供：
- **格式验证**：邮箱、电话、日期格式检查
- **逻辑验证**：日期合理性、人数范围检查
- **业务验证**：价格范围、车型匹配度检查

### 批量操作
支持的批量操作：
- **全选/取消选择**：快速选择订单
- **批量验证**：一键验证所有订单
- **选择性创建**：只创建选中的订单

## 最佳实践

### 输入文本格式建议
1. **分段清晰**：每个订单用空行分隔
2. **信息完整**：尽量包含客户、时间、地点信息
3. **格式统一**：使用一致的日期时间格式

### 编辑建议
1. **优先修改必填字段**：客户姓名、联系方式、地点
2. **检查自动映射**：确认车型和区域选择正确
3. **添加备注信息**：在额外要求中补充特殊需求

### 创建前检查
1. **字段完整性**：确保必要信息已填写
2. **逻辑合理性**：检查日期、价格是否合理
3. **客户信息**：确认联系方式格式正确

## 常见问题

### Q: AI解析不准确怎么办？
A: 可以手动编辑任何字段，系统支持实时修改。建议检查原始文本格式是否清晰。

### Q: 如何处理解析失败的订单？
A: 系统会显示错误信息，可以手动补充缺失字段或重新输入文本。

### Q: 能否部分创建订单？
A: 是的，可以取消选择不需要的订单，只创建选中的订单。

### Q: 创建失败的订单怎么处理？
A: 系统会显示失败原因，可以修改后重新尝试创建。

## 技术支持

如需技术支持或反馈问题，请：
1. 查看浏览器控制台的错误信息
2. 记录具体的操作步骤
3. 提供原始输入文本（脱敏处理）
4. 联系系统管理员

---

*本指南适用于多订单模式 v2.0及以上版本*