/**
 * @CORE 统一依赖获取接口
 * 🏷️ 标签: @UNIFIED_DEPENDENCY_INTERFACE
 * 📝 说明: 提供统一的依赖获取接口，替换系统中所有不一致的依赖获取方式
 * 🎯 功能: 统一接口、向后兼容、迁移支持、性能优化
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保依赖解析器已加载
if (!window.OTA?.core?.getDependencyResolver) {
    console.error('❌ 依赖解析器未加载，请确保 dependency-resolver.js 已正确加载');
    throw new Error('依赖解析器未找到');
}

(function() {
    'use strict';

    const resolver = window.OTA.core.getDependencyResolver();
    const logger = resolver.resolve('logger') || console;

    /**
     * 统一依赖获取接口类
     * 提供标准化的依赖获取方法，支持迁移和兼容性
     */
    class UnifiedDependencyInterface {
        constructor() {
            this.migrationWarnings = new Set();
            this.deprecationWarnings = new Set();
            
            // 迁移映射表 - 旧方法名到新方法名的映射
            this.migrationMap = {
                // 旧的全局函数到新的统一接口
                'getLogger': () => this.getService('logger'),
                'getAppState': () => this.getService('appState'),
                'getAPIService': () => this.getService('apiService'),
                'getApiService': () => this.getService('apiService'), // 兼容大小写变体
                'getGeminiService': () => this.getService('geminiService'),
                'getUIManager': () => this.getService('uiManager'),
                'getMultiOrderManager': () => this.getService('multiOrderManager'),
                'getUtils': () => this.getService('utils'),
                
                // Multi-Order 相关的工厂函数
                'getMultiOrderUtils': () => this.getService('multiOrderUtils'),
                'getMultiOrderCleanupManager': () => this.getService('multiOrderCleanupManager'),
                'getMultiOrderValidationManager': () => this.getService('multiOrderValidationManager'),
                'getMultiOrderUIManager': () => this.getService('multiOrderUIManager'),
                'getMultiOrderStateManager': () => this.getService('multiOrderStateManager'),
                'getMultiOrderEventManager': () => this.getService('multiOrderEventManager'),
                'getMultiOrderBatchManager': () => this.getService('multiOrderBatchManager'),
                
                // Gemini 相关的工厂函数
                'getGeminiCoordinator': () => this.getService('geminiCoordinator'),
                'getOTAChannelIdentifier': () => this.getService('otaChannelIdentifier'),
                'getProcessorRouter': () => this.getService('processorRouter'),
                'getConfigManager': () => this.getService('configManager')
            };
            
            logger.log('✅ 统一依赖获取接口已初始化', 'info');
        }

        /**
         * 统一的服务获取方法
         * @param {string} serviceName - 服务名称
         * @param {Object} options - 选项
         * @returns {any} 服务实例
         */
        getService(serviceName, options = {}) {
            return resolver.resolve(serviceName, options);
        }

        /**
         * 批量获取服务
         * @param {Array<string>} serviceNames - 服务名称数组
         * @param {Object} options - 选项
         * @returns {Object} 服务实例映射
         */
        getServices(serviceNames, options = {}) {
            return resolver.resolveMultiple(serviceNames, options);
        }

        /**
         * 检查服务是否可用
         * @param {string} serviceName - 服务名称
         * @returns {boolean} 是否可用
         */
        hasService(serviceName) {
            try {
                const service = this.getService(serviceName);
                return service !== null && service !== undefined;
            } catch (error) {
                return false;
            }
        }

        /**
         * 等待服务可用
         * @param {string} serviceName - 服务名称
         * @param {number} timeout - 超时时间（毫秒）
         * @param {number} interval - 检查间隔（毫秒）
         * @returns {Promise<any>} 服务实例
         */
        waitForService(serviceName, timeout = 5000, interval = 100) {
            return new Promise((resolve, reject) => {
                const startTime = Date.now();
                
                const checkService = () => {
                    const service = this.getService(serviceName);
                    if (service) {
                        resolve(service);
                        return;
                    }
                    
                    if (Date.now() - startTime > timeout) {
                        reject(new Error(`等待服务 ${serviceName} 超时`));
                        return;
                    }
                    
                    setTimeout(checkService, interval);
                };
                
                checkService();
            });
        }

        /**
         * 注册服务（用于测试或特殊情况）
         * @param {string} serviceName - 服务名称
         * @param {any} serviceInstance - 服务实例
         */
        registerService(serviceName, serviceInstance) {
            // 直接设置到缓存中
            resolver.cache.set(serviceName, serviceInstance);
            logger.log(`✅ 服务 ${serviceName} 已注册`, 'info');
        }

        /**
         * 注销服务
         * @param {string} serviceName - 服务名称
         */
        unregisterService(serviceName) {
            resolver.clearCache(serviceName);
            logger.log(`✅ 服务 ${serviceName} 已注销`, 'info');
        }

        /**
         * 获取依赖统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return resolver.getStats();
        }

        /**
         * 清除缓存
         * @param {string} serviceName - 服务名称（可选）
         */
        clearCache(serviceName = null) {
            resolver.clearCache(serviceName);
        }

        /**
         * 设置迁移模式（显示迁移警告）
         * @param {boolean} enabled - 是否启用
         */
        setMigrationMode(enabled) {
            this.migrationMode = enabled;
            if (enabled) {
                logger.log('⚠️ 迁移模式已启用，将显示依赖获取方式的迁移建议', 'warn');
            }
        }

        /**
         * 创建迁移包装器
         * @param {string} oldMethodName - 旧方法名
         * @returns {Function} 包装器函数
         */
        createMigrationWrapper(oldMethodName) {
            return (...args) => {
                // 显示迁移警告（每个方法只警告一次）
                if (!this.migrationWarnings.has(oldMethodName)) {
                    logger.log(`⚠️ 迁移建议: ${oldMethodName}() 已废弃，请使用 getService('${this.getServiceNameFromOldMethod(oldMethodName)}') 替代`, 'warn');
                    this.migrationWarnings.add(oldMethodName);
                }
                
                // 调用新的统一接口
                if (this.migrationMap[oldMethodName]) {
                    return this.migrationMap[oldMethodName](...args);
                }
                
                logger.log(`❌ 未找到 ${oldMethodName} 的迁移映射`, 'error');
                return null;
            };
        }

        /**
         * 从旧方法名获取服务名
         * @param {string} oldMethodName - 旧方法名
         * @returns {string} 服务名
         */
        getServiceNameFromOldMethod(oldMethodName) {
            // 移除 'get' 前缀并转换为小驼峰
            if (oldMethodName.startsWith('get')) {
                const serviceName = oldMethodName.slice(3);
                return serviceName.charAt(0).toLowerCase() + serviceName.slice(1);
            }
            return oldMethodName;
        }

        /**
         * 安装向后兼容的全局函数
         */
        installBackwardCompatibility() {
            const globalMethods = [
                'getLogger', 'getAppState', 'getAPIService', 'getApiService',
                'getGeminiService', 'getUIManager', 'getMultiOrderManager', 'getUtils',
                'getMultiOrderUtils', 'getMultiOrderCleanupManager', 'getMultiOrderValidationManager',
                'getMultiOrderUIManager', 'getMultiOrderStateManager', 'getMultiOrderEventManager',
                'getMultiOrderBatchManager', 'getGeminiCoordinator', 'getOTAChannelIdentifier',
                'getProcessorRouter', 'getConfigManager'
            ];
            
            globalMethods.forEach(methodName => {
                if (!window[methodName]) {
                    window[methodName] = this.createMigrationWrapper(methodName);
                }
            });
            
            logger.log('✅ 向后兼容的全局函数已安装', 'info');
        }

        /**
         * 卸载向后兼容的全局函数
         */
        uninstallBackwardCompatibility() {
            const globalMethods = Object.keys(this.migrationMap);
            
            globalMethods.forEach(methodName => {
                if (window[methodName]) {
                    delete window[methodName];
                }
            });
            
            logger.log('✅ 向后兼容的全局函数已卸载', 'info');
        }

        /**
         * 生成迁移报告
         * @returns {Object} 迁移报告
         */
        generateMigrationReport() {
            return {
                totalWarnings: this.migrationWarnings.size,
                warningMethods: Array.from(this.migrationWarnings),
                recommendations: Array.from(this.migrationWarnings).map(method => ({
                    oldMethod: method,
                    newMethod: `getService('${this.getServiceNameFromOldMethod(method)}')`,
                    example: `// 旧方式: ${method}()\n// 新方式: getService('${this.getServiceNameFromOldMethod(method)}')`
                })),
                stats: this.getStats()
            };
        }
    }

    // 创建全局单例实例
    function getUnifiedDependencyInterface() {
        if (!window.OTA.core.unifiedDependencyInterface) {
            window.OTA.core.unifiedDependencyInterface = new UnifiedDependencyInterface();
        }
        return window.OTA.core.unifiedDependencyInterface;
    }

    // 暴露到OTA命名空间
    window.OTA.core.UnifiedDependencyInterface = UnifiedDependencyInterface;
    window.OTA.core.getUnifiedDependencyInterface = getUnifiedDependencyInterface;

    // 创建全局实例
    const globalInterface = getUnifiedDependencyInterface();

    // 提供统一的服务获取函数
    window.OTA.getService = function(serviceName, options) {
        return globalInterface.getService(serviceName, options);
    };

    window.OTA.getServices = function(serviceNames, options) {
        return globalInterface.getServices(serviceNames, options);
    };

    window.OTA.hasService = function(serviceName) {
        return globalInterface.hasService(serviceName);
    };

    window.OTA.waitForService = function(serviceName, timeout, interval) {
        return globalInterface.waitForService(serviceName, timeout, interval);
    };

    // 向后兼容的全局函数
    window.getService = window.OTA.getService;
    window.getServices = window.OTA.getServices;
    window.hasService = window.OTA.hasService;
    window.waitForService = window.OTA.waitForService;

    // 自动安装向后兼容性
    globalInterface.installBackwardCompatibility();

    console.log('✅ 统一依赖获取接口已加载');

})();
