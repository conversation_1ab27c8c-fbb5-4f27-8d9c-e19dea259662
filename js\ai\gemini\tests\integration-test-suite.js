/**
 * @TEST_SUITE 集成测试套件
 * 🏷️ 标签: @INTEGRATION_TEST_SUITE
 * 📝 说明: 全面的Gemini处理器系统集成测试，验证各组件协同工作的正确性
 * 🎯 功能: 端到端测试、组件集成测试、性能测试、错误处理测试
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.tests = window.OTA.gemini.tests || {};

(function() {
    'use strict';

    /**
     * 集成测试套件类
     * 提供全面的系统集成测试功能
     */
    class IntegrationTestSuite {
        constructor() {
            this.logger = window.getLogger?.() || console;
            this.testResults = [];
            this.testStats = {
                total: 0,
                passed: 0,
                failed: 0,
                skipped: 0,
                startTime: null,
                endTime: null,
                duration: 0
            };
            
            // 测试配置
            this.testConfig = {
                timeout: 30000,        // 30秒超时
                retryCount: 2,         // 重试次数
                parallelTests: false,  // 是否并行测试
                verbose: true,         // 详细输出
                stopOnFirstFailure: false
            };
            
            // 测试数据
            this.testData = this.initializeTestData();
        }

        /**
         * 初始化测试数据
         * @returns {Object} 测试数据集
         */
        initializeTestData() {
            return {
                // Chong Dealer测试数据
                chongDealerOrders: [
                    {
                        text: "CD123456 接机服务 吉隆坡国际机场 到 双子塔 乘客：张三 航班：MH123 时间：2024-01-15 14:30 举牌服务",
                        expected: {
                            ota_reference_number: "CD123456",
                            service_type_id: 2,
                            pickup_location: "吉隆坡国际机场",
                            destination: "双子塔",
                            passenger_name: "张三",
                            pickup_sign: true
                        }
                    }
                ],
                
                // Fliggy测试数据
                fliggyOrders: [
                    {
                        text: "飞猪订单号：FLY789012 北京首都机场接机 目的地：王府井酒店 乘客姓名：李四 航班号：CA1234 到达时间：2024-01-16 09:15",
                        expected: {
                            ota_reference_number: "FLY789012",
                            service_type_id: 2,
                            pickup_location: "北京首都机场",
                            destination: "王府井酒店",
                            passenger_name: "李四"
                        }
                    }
                ],
                
                // JRCoach测试数据
                jrcoachOrders: [
                    {
                        text: "JR345678 Kuala Lumpur Airport pickup to Petronas Twin Towers Passenger: John Smith Flight: AK456 Time: 2024-01-17 16:45",
                        expected: {
                            ota_reference_number: "JR345678",
                            service_type_id: 2,
                            pickup_location: "Kuala Lumpur Airport",
                            destination: "Petronas Twin Towers",
                            passenger_name: "John Smith"
                        }
                    }
                ],
                
                // 错误测试数据
                errorTestData: [
                    {
                        text: "",
                        expectedError: "空文本输入"
                    },
                    {
                        text: "无效的订单信息",
                        expectedError: "无法识别OTA渠道"
                    }
                ],
                
                // 性能测试数据
                performanceTestData: {
                    shortText: "CD123 接机",
                    mediumText: "CD123456 接机服务 吉隆坡国际机场 到 双子塔 乘客：张三 航班：MH123 时间：2024-01-15 14:30",
                    longText: "CD123456 接机服务 吉隆坡国际机场KLIA2航站楼 到 吉隆坡市中心双子塔附近的丽思卡尔顿酒店 乘客姓名：张三先生 联系电话：+60123456789 航班信息：马来西亚航空MH123 预计到达时间：2024年1月15日下午2点30分 特殊要求：需要举牌服务，乘客是VIP客户，请安排豪华车型，司机需要会说中文，行李较多请准备大容量后备箱"
                }
            };
        }

        /**
         * 运行所有集成测试
         * @returns {Promise<Object>} 测试结果
         */
        async runAllTests() {
            this.logger.log('🚀 开始运行集成测试套件', 'info');
            this.testStats.startTime = Date.now();
            this.testResults = [];

            try {
                // 1. 组件初始化测试
                await this.runComponentInitializationTests();
                
                // 2. OTA渠道识别测试
                await this.runChannelIdentificationTests();
                
                // 3. 处理器路由测试
                await this.runProcessorRoutingTests();
                
                // 4. 端到端处理测试
                await this.runEndToEndProcessingTests();
                
                // 5. 降级处理测试
                await this.runFallbackProcessingTests();
                
                // 6. 语言管理器集成测试
                await this.runLanguageManagerIntegrationTests();
                
                // 7. 配置管理测试
                await this.runConfigurationManagementTests();
                
                // 8. 性能测试
                await this.runPerformanceTests();
                
                // 9. 错误处理测试
                await this.runErrorHandlingTests();
                
                // 10. 并发测试
                await this.runConcurrencyTests();

            } catch (error) {
                this.logger.logError('集成测试套件执行失败', error);
                this.addTestResult('TestSuite', 'Critical Error', false, error.message);
            }

            this.testStats.endTime = Date.now();
            this.testStats.duration = this.testStats.endTime - this.testStats.startTime;
            
            return this.generateTestReport();
        }

        /**
         * 组件初始化测试
         */
        async runComponentInitializationTests() {
            this.logger.log('📋 运行组件初始化测试', 'info');

            // 测试OTA参考引擎初始化
            await this.runTest('ComponentInit', 'OTA Reference Engine', async () => {
                const engine = window.OTA.gemini.core.getOtaReferenceEngine?.();
                if (!engine) throw new Error('OTA参考引擎未初始化');
                
                const testResult = engine.identifyReference('CD123456');
                if (!testResult) throw new Error('参考引擎功能异常');
                
                return true;
            });

            // 测试处理器路由器初始化
            await this.runTest('ComponentInit', 'Processor Router', async () => {
                const router = window.OTA.gemini.core.getProcessorRouter?.();
                if (!router) throw new Error('处理器路由器未初始化');
                
                const processor = await router.routeToProcessor('ChongDealer');
                if (!processor) throw new Error('处理器路由功能异常');
                
                return true;
            });

            // 测试配置管理器初始化
            await this.runTest('ComponentInit', 'Config Manager', async () => {
                const configManager = window.OTA.gemini.core.getConfigManager?.();
                if (!configManager) throw new Error('配置管理器未初始化');
                
                const config = await configManager.loadConfig('otaReferencePatterns');
                if (!config) throw new Error('配置加载功能异常');
                
                return true;
            });

            // 测试Gemini协调器初始化
            await this.runTest('ComponentInit', 'Gemini Coordinator', async () => {
                const coordinator = window.OTA.gemini.getGeminiCoordinator?.();
                if (!coordinator) throw new Error('Gemini协调器未初始化');
                
                // 测试基本功能
                if (typeof coordinator.processOrder !== 'function') {
                    throw new Error('协调器processOrder方法不存在');
                }
                
                return true;
            });
        }

        /**
         * OTA渠道识别测试
         */
        async runChannelIdentificationTests() {
            this.logger.log('🔍 运行OTA渠道识别测试', 'info');

            const identifier = window.OTA.gemini.core.getOtaChannelIdentifier?.();
            if (!identifier) {
                this.addTestResult('ChannelIdentification', 'Identifier Not Found', false, 'OTA渠道识别器未找到');
                return;
            }

            // 测试Chong Dealer识别
            await this.runTest('ChannelIdentification', 'Chong Dealer Recognition', async () => {
                const result = await identifier.identifyChannel('CD123456 接机服务');
                if (result.channel !== 'ChongDealer') {
                    throw new Error(`期望识别为ChongDealer，实际为${result.channel}`);
                }
                if (result.confidence < 0.8) {
                    throw new Error(`置信度过低：${result.confidence}`);
                }
                return true;
            });

            // 测试Fliggy识别
            await this.runTest('ChannelIdentification', 'Fliggy Recognition', async () => {
                const result = await identifier.identifyChannel('飞猪订单号：FLY789012');
                if (result.channel !== 'Fliggy') {
                    throw new Error(`期望识别为Fliggy，实际为${result.channel}`);
                }
                return true;
            });

            // 测试未知渠道降级
            await this.runTest('ChannelIdentification', 'Unknown Channel Fallback', async () => {
                const result = await identifier.identifyChannel('未知订单格式');
                if (result.channel !== 'fallback') {
                    throw new Error(`期望降级为fallback，实际为${result.channel}`);
                }
                return true;
            });
        }

        /**
         * 处理器路由测试
         */
        async runProcessorRoutingTests() {
            this.logger.log('🚦 运行处理器路由测试', 'info');

            const router = window.OTA.gemini.core.getProcessorRouter?.();
            if (!router) {
                this.addTestResult('ProcessorRouting', 'Router Not Found', false, '处理器路由器未找到');
                return;
            }

            // 测试各处理器路由
            const processors = ['ChongDealer', 'Fliggy', 'JRCoach', 'KKday', 'Klook', 'Ctrip', 'Agoda', 'Booking'];
            
            for (const processorName of processors) {
                await this.runTest('ProcessorRouting', `${processorName} Routing`, async () => {
                    const processor = await router.routeToProcessor(processorName);
                    if (!processor) {
                        throw new Error(`无法路由到${processorName}处理器`);
                    }
                    if (typeof processor.processOrder !== 'function') {
                        throw new Error(`${processorName}处理器缺少processOrder方法`);
                    }
                    return true;
                });
            }

            // 测试缓存功能
            await this.runTest('ProcessorRouting', 'Processor Caching', async () => {
                const processor1 = await router.routeToProcessor('ChongDealer');
                const processor2 = await router.routeToProcessor('ChongDealer');
                
                // 验证缓存是否工作（应该返回同一实例）
                if (processor1 !== processor2) {
                    this.logger.log('处理器缓存可能未启用（返回不同实例）', 'warning');
                }
                
                return true;
            });
        }

        /**
         * 端到端处理测试
         */
        async runEndToEndProcessingTests() {
            this.logger.log('🔄 运行端到端处理测试', 'info');

            const coordinator = window.OTA.gemini.getGeminiCoordinator?.();
            if (!coordinator) {
                this.addTestResult('EndToEnd', 'Coordinator Not Found', false, 'Gemini协调器未找到');
                return;
            }

            // 测试Chong Dealer订单处理
            for (const testCase of this.testData.chongDealerOrders) {
                await this.runTest('EndToEnd', 'Chong Dealer Processing', async () => {
                    const result = await coordinator.processOrder(testCase.text);
                    
                    if (!result.success) {
                        throw new Error(`处理失败：${result.error}`);
                    }
                    
                    // 验证关键字段
                    for (const [key, expectedValue] of Object.entries(testCase.expected)) {
                        if (result.data[key] !== expectedValue) {
                            throw new Error(`字段${key}不匹配：期望${expectedValue}，实际${result.data[key]}`);
                        }
                    }
                    
                    return true;
                });
            }

            // 测试Fliggy订单处理
            for (const testCase of this.testData.fliggyOrders) {
                await this.runTest('EndToEnd', 'Fliggy Processing', async () => {
                    const result = await coordinator.processOrder(testCase.text);
                    
                    if (!result.success) {
                        throw new Error(`处理失败：${result.error}`);
                    }
                    
                    // 验证关键字段
                    for (const [key, expectedValue] of Object.entries(testCase.expected)) {
                        if (result.data[key] !== expectedValue) {
                            throw new Error(`字段${key}不匹配：期望${expectedValue}，实际${result.data[key]}`);
                        }
                    }
                    
                    return true;
                });
            }
        }

        /**
         * 降级处理测试
         */
        async runFallbackProcessingTests() {
            this.logger.log('⬇️ 运行降级处理测试', 'info');

            const coordinator = window.OTA.gemini.getGeminiCoordinator?.();
            if (!coordinator) {
                this.addTestResult('Fallback', 'Coordinator Not Found', false, 'Gemini协调器未找到');
                return;
            }

            // 测试低置信度降级
            await this.runTest('Fallback', 'Low Confidence Fallback', async () => {
                const result = await coordinator.processOrder('模糊的订单信息');
                
                // 应该触发降级处理
                if (result.metadata && result.metadata.fallback && result.metadata.fallback.isFallback) {
                    return true;
                } else {
                    throw new Error('未触发降级处理');
                }
            });

            // 测试处理器错误降级
            await this.runTest('Fallback', 'Processor Error Fallback', async () => {
                // 模拟处理器错误（通过无效数据）
                const result = await coordinator.processOrder('INVALID_PROCESSOR_TEST');
                
                // 检查是否有错误处理
                if (!result.success && result.error) {
                    return true; // 错误被正确处理
                } else if (result.metadata && result.metadata.fallback) {
                    return true; // 触发了降级处理
                } else {
                    throw new Error('错误处理或降级机制未工作');
                }
            });
        }

        /**
         * 语言管理器集成测试
         */
        async runLanguageManagerIntegrationTests() {
            this.logger.log('🌐 运行语言管理器集成测试', 'info');

            const languageIntegration = window.OTA.gemini.integrations.getLanguageManagerIntegration?.();
            if (!languageIntegration) {
                this.addTestResult('LanguageIntegration', 'Integration Not Found', false, '语言管理器集成未找到');
                return;
            }

            // 测试语言检测
            await this.runTest('LanguageIntegration', 'Language Detection', async () => {
                const chineseResult = await languageIntegration.detectLanguage('这是中文订单');
                if (!chineseResult.language.startsWith('zh')) {
                    throw new Error(`中文检测失败：${chineseResult.language}`);
                }
                
                const englishResult = await languageIntegration.detectLanguage('This is English order');
                if (englishResult.language !== 'en') {
                    throw new Error(`英文检测失败：${englishResult.language}`);
                }
                
                return true;
            });

            // 测试处理器语言支持
            await this.runTest('LanguageIntegration', 'Processor Language Support', async () => {
                const supportedLangs = languageIntegration.getProcessorSupportedLanguages('ChongDealer');
                if (!Array.isArray(supportedLangs) || supportedLangs.length === 0) {
                    throw new Error('处理器支持语言列表为空');
                }
                
                return true;
            });
        }

        /**
         * 配置管理测试
         */
        async runConfigurationManagementTests() {
            this.logger.log('⚙️ 运行配置管理测试', 'info');

            // 测试OTA参考模式配置
            await this.runTest('Configuration', 'OTA Reference Patterns', async () => {
                const config = window.OTA.gemini.configs.getOtaReferencePatterns?.();
                if (!config || !config.platformRules) {
                    throw new Error('OTA参考模式配置未找到');
                }
                
                if (!config.platformRules['Chong Dealer']) {
                    throw new Error('Chong Dealer配置缺失');
                }
                
                return true;
            });

            // 测试处理器预设值配置
            await this.runTest('Configuration', 'Processor Preset Values', async () => {
                const config = window.OTA.gemini.configs.getProcessorPresetValues?.();
                if (!config || !config.commonPresets) {
                    throw new Error('处理器预设值配置未找到');
                }
                
                return true;
            });

            // 测试降级处理配置
            await this.runTest('Configuration', 'Fallback Processing Config', async () => {
                const config = window.OTA.gemini.configs.getFallbackProcessingConfig?.();
                if (!config || !config.fallbackTriggers) {
                    throw new Error('降级处理配置未找到');
                }
                
                return true;
            });
        }

        /**
         * 性能测试
         */
        async runPerformanceTests() {
            this.logger.log('⚡ 运行性能测试', 'info');

            const coordinator = window.OTA.gemini.getGeminiCoordinator?.();
            if (!coordinator) {
                this.addTestResult('Performance', 'Coordinator Not Found', false, 'Gemini协调器未找到');
                return;
            }

            // 测试短文本处理性能
            await this.runTest('Performance', 'Short Text Processing', async () => {
                const startTime = Date.now();
                const result = await coordinator.processOrder(this.testData.performanceTestData.shortText);
                const duration = Date.now() - startTime;
                
                if (duration > 5000) { // 5秒超时
                    throw new Error(`处理时间过长：${duration}ms`);
                }
                
                this.logger.log(`短文本处理时间：${duration}ms`, 'info');
                return true;
            });

            // 测试长文本处理性能
            await this.runTest('Performance', 'Long Text Processing', async () => {
                const startTime = Date.now();
                const result = await coordinator.processOrder(this.testData.performanceTestData.longText);
                const duration = Date.now() - startTime;
                
                if (duration > 15000) { // 15秒超时
                    throw new Error(`处理时间过长：${duration}ms`);
                }
                
                this.logger.log(`长文本处理时间：${duration}ms`, 'info');
                return true;
            });
        }

        /**
         * 错误处理测试
         */
        async runErrorHandlingTests() {
            this.logger.log('❌ 运行错误处理测试', 'info');

            const coordinator = window.OTA.gemini.getGeminiCoordinator?.();
            if (!coordinator) {
                this.addTestResult('ErrorHandling', 'Coordinator Not Found', false, 'Gemini协调器未找到');
                return;
            }

            // 测试空输入处理
            await this.runTest('ErrorHandling', 'Empty Input', async () => {
                const result = await coordinator.processOrder('');
                if (result.success) {
                    throw new Error('空输入应该返回错误');
                }
                if (!result.error) {
                    throw new Error('错误信息缺失');
                }
                return true;
            });

            // 测试无效输入处理
            await this.runTest('ErrorHandling', 'Invalid Input', async () => {
                const result = await coordinator.processOrder(null);
                if (result.success) {
                    throw new Error('null输入应该返回错误');
                }
                return true;
            });
        }

        /**
         * 并发测试
         */
        async runConcurrencyTests() {
            this.logger.log('🔀 运行并发测试', 'info');

            const coordinator = window.OTA.gemini.getGeminiCoordinator?.();
            if (!coordinator) {
                this.addTestResult('Concurrency', 'Coordinator Not Found', false, 'Gemini协调器未找到');
                return;
            }

            // 测试并发处理
            await this.runTest('Concurrency', 'Concurrent Processing', async () => {
                const promises = [];
                const testTexts = [
                    'CD123456 接机服务',
                    'FLY789012 飞猪订单',
                    'JR345678 JRCoach booking'
                ];

                for (const text of testTexts) {
                    promises.push(coordinator.processOrder(text));
                }

                const results = await Promise.all(promises);
                
                // 验证所有结果
                for (let i = 0; i < results.length; i++) {
                    if (!results[i]) {
                        throw new Error(`并发处理结果${i}为空`);
                    }
                }

                return true;
            });
        }

        /**
         * 运行单个测试
         * @param {string} category - 测试分类
         * @param {string} name - 测试名称
         * @param {Function} testFunction - 测试函数
         */
        async runTest(category, name, testFunction) {
            const testId = `${category}.${name}`;
            this.testStats.total++;

            try {
                this.logger.log(`🧪 运行测试: ${testId}`, 'info');
                
                const startTime = Date.now();
                const result = await Promise.race([
                    testFunction(),
                    new Promise((_, reject) => 
                        setTimeout(() => reject(new Error('测试超时')), this.testConfig.timeout)
                    )
                ]);
                const duration = Date.now() - startTime;

                this.addTestResult(category, name, true, null, duration);
                this.testStats.passed++;
                
                this.logger.log(`✅ 测试通过: ${testId} (${duration}ms)`, 'info');

            } catch (error) {
                this.addTestResult(category, name, false, error.message);
                this.testStats.failed++;
                
                this.logger.logError(`❌ 测试失败: ${testId}`, error);

                if (this.testConfig.stopOnFirstFailure) {
                    throw new Error(`测试失败，停止执行: ${testId}`);
                }
            }
        }

        /**
         * 添加测试结果
         * @param {string} category - 分类
         * @param {string} name - 名称
         * @param {boolean} passed - 是否通过
         * @param {string} error - 错误信息
         * @param {number} duration - 执行时间
         */
        addTestResult(category, name, passed, error = null, duration = 0) {
            this.testResults.push({
                category,
                name,
                passed,
                error,
                duration,
                timestamp: new Date().toISOString()
            });
        }

        /**
         * 生成测试报告
         * @returns {Object} 测试报告
         */
        generateTestReport() {
            const report = {
                summary: {
                    ...this.testStats,
                    successRate: this.testStats.total > 0 ? 
                        (this.testStats.passed / this.testStats.total * 100).toFixed(2) + '%' : '0%'
                },
                results: this.testResults,
                categories: this.groupResultsByCategory(),
                failedTests: this.testResults.filter(r => !r.passed),
                slowestTests: this.testResults
                    .filter(r => r.passed && r.duration > 0)
                    .sort((a, b) => b.duration - a.duration)
                    .slice(0, 5)
            };

            this.logger.log('📊 集成测试报告生成完成', 'info');
            this.logger.log(`总测试数: ${report.summary.total}`, 'info');
            this.logger.log(`通过: ${report.summary.passed}`, 'info');
            this.logger.log(`失败: ${report.summary.failed}`, 'info');
            this.logger.log(`成功率: ${report.summary.successRate}`, 'info');
            this.logger.log(`总耗时: ${report.summary.duration}ms`, 'info');

            if (report.failedTests.length > 0) {
                this.logger.log('❌ 失败的测试:', 'error');
                report.failedTests.forEach(test => {
                    this.logger.log(`  - ${test.category}.${test.name}: ${test.error}`, 'error');
                });
            }

            return report;
        }

        /**
         * 按分类分组测试结果
         * @returns {Object} 分组结果
         */
        groupResultsByCategory() {
            const categories = {};
            
            for (const result of this.testResults) {
                if (!categories[result.category]) {
                    categories[result.category] = {
                        total: 0,
                        passed: 0,
                        failed: 0,
                        tests: []
                    };
                }
                
                categories[result.category].total++;
                if (result.passed) {
                    categories[result.category].passed++;
                } else {
                    categories[result.category].failed++;
                }
                categories[result.category].tests.push(result);
            }
            
            return categories;
        }

        /**
         * 重置测试状态
         */
        reset() {
            this.testResults = [];
            this.testStats = {
                total: 0,
                passed: 0,
                failed: 0,
                skipped: 0,
                startTime: null,
                endTime: null,
                duration: 0
            };
        }
    }

    // 创建全局单例实例
    function getIntegrationTestSuite() {
        if (!window.OTA.gemini.tests.integrationTestSuite) {
            window.OTA.gemini.tests.integrationTestSuite = new IntegrationTestSuite();
        }
        return window.OTA.gemini.tests.integrationTestSuite;
    }

    // 暴露到全局命名空间
    window.OTA.gemini.tests.IntegrationTestSuite = IntegrationTestSuite;
    window.OTA.gemini.tests.getIntegrationTestSuite = getIntegrationTestSuite;

    // 向后兼容
    window.getIntegrationTestSuite = getIntegrationTestSuite;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('integrationTestSuite', getIntegrationTestSuite(), '@INTEGRATION_TEST_SUITE');
        window.OTA.Registry.registerFactory('getIntegrationTestSuite', getIntegrationTestSuite, '@INTEGRATION_TEST_SUITE_FACTORY');
    }

    console.log('✅ 集成测试套件已加载');

})();
