/**
 * @CORE 图片分析引擎
 * 🏷️ 标签: @IMAGE_ANALYSIS_ENGINE
 * 📝 说明: 负责Gemini Vision API的图片分析和订单信息提取
 * 🎯 功能: 图片预处理、OCR识别、结构化数据提取
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.core = window.OTA.gemini.core || {};

(function() {
    'use strict';

    /**
     * 图片分析引擎类
     * 负责处理图片上传、预处理和Gemini Vision API分析
     */
    class ImageAnalysisEngine {
        constructor() {
            this.logger = window.getLogger?.() || console;
            
            // 初始化配置
            this.initializeConfig();
            
            // 初始化图片处理器
            this.initializeImageProcessor();
            
            // 统计信息
            this.stats = {
                totalAnalyses: 0,
                successfulAnalyses: 0,
                failedAnalyses: 0,
                averageProcessingTime: 0,
                totalProcessingTime: 0
            };
        }

        /**
         * 初始化配置
         */
        initializeConfig() {
            this.config = {
                // 支持的图片格式
                supportedFormats: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
                
                // 图片大小限制（字节）
                maxFileSize: 10 * 1024 * 1024, // 10MB
                
                // 图片尺寸限制
                maxWidth: 4096,
                maxHeight: 4096,
                
                // 压缩质量
                compressionQuality: 0.8,
                
                // 分析超时时间（毫秒）
                analysisTimeout: 30000,
                
                // 重试配置
                maxRetries: 3,
                retryDelay: 1000
            };
        }

        /**
         * 初始化图片处理器
         */
        initializeImageProcessor() {
            // 创建Canvas用于图片处理
            this.canvas = document.createElement('canvas');
            this.ctx = this.canvas.getContext('2d');
            
            // 图片预处理选项
            this.preprocessingOptions = {
                enhanceContrast: true,
                sharpenText: true,
                removeNoise: false,
                autoRotate: true
            };
        }

        /**
         * 分析图片（从原gemini-service.js迁移）
         * @param {string|File} imageInput - 图片输入（base64字符串或File对象）
         * @param {Object} options - 分析选项
         * @returns {Promise<Object>} 分析结果
         */
        async analyzeImage(imageInput, options = {}) {
            const startTime = Date.now();
            this.stats.totalAnalyses++;

            try {
                this.logger.log('开始图片分析', 'info');

                // 步骤1: 预处理图片
                const processedImage = await this.preprocessImage(imageInput, options);
                
                // 步骤2: 获取Gemini服务
                const geminiService = this.getGeminiService();
                if (!geminiService) {
                    throw new Error('Gemini服务未找到');
                }

                // 步骤3: 构建分析提示词
                const prompt = this.buildImageAnalysisPrompt(options);

                // 步骤4: 调用Gemini Vision API
                const analysisResult = await this.callGeminiVisionAPI(
                    geminiService, 
                    processedImage.base64, 
                    prompt, 
                    options
                );

                // 步骤5: 后处理结果
                const finalResult = await this.postProcessAnalysisResult(analysisResult, options);

                // 更新统计信息
                const processingTime = Date.now() - startTime;
                this.updateStats(true, processingTime);

                this.logger.log(`图片分析完成 (${processingTime}ms)`, 'info');

                return {
                    success: true,
                    data: finalResult,
                    processingTime: processingTime,
                    imageInfo: processedImage.info,
                    confidence: finalResult._confidence || 0.7
                };

            } catch (error) {
                this.updateStats(false, Date.now() - startTime);
                this.logger.logError('图片分析失败', error);
                
                return {
                    success: false,
                    error: error.message,
                    processingTime: Date.now() - startTime
                };
            }
        }

        /**
         * 预处理图片
         * @param {string|File} imageInput - 图片输入
         * @param {Object} options - 处理选项
         * @returns {Promise<Object>} 处理结果
         */
        async preprocessImage(imageInput, options = {}) {
            try {
                // 转换为统一格式
                let imageData;
                if (typeof imageInput === 'string') {
                    // 已经是base64格式
                    imageData = imageInput;
                } else if (imageInput instanceof File) {
                    // 文件对象，需要转换
                    imageData = await this.fileToBase64(imageInput);
                } else {
                    throw new Error('不支持的图片输入格式');
                }

                // 验证图片格式
                const imageInfo = await this.validateImage(imageData);
                
                // 应用预处理
                const processedBase64 = await this.applyImagePreprocessing(imageData, options);

                return {
                    base64: processedBase64,
                    info: imageInfo
                };

            } catch (error) {
                this.logger.logError('图片预处理失败', error);
                throw error;
            }
        }

        /**
         * 文件转Base64
         * @param {File} file - 文件对象
         * @returns {Promise<string>} Base64字符串
         */
        fileToBase64(file) {
            return new Promise((resolve, reject) => {
                // 验证文件大小
                if (file.size > this.config.maxFileSize) {
                    reject(new Error(`文件大小超过限制 (${this.config.maxFileSize / 1024 / 1024}MB)`));
                    return;
                }

                // 验证文件类型
                if (!this.config.supportedFormats.includes(file.type)) {
                    reject(new Error(`不支持的文件格式: ${file.type}`));
                    return;
                }

                const reader = new FileReader();
                reader.onload = () => resolve(reader.result);
                reader.onerror = () => reject(new Error('文件读取失败'));
                reader.readAsDataURL(file);
            });
        }

        /**
         * 验证图片
         * @param {string} base64Data - Base64图片数据
         * @returns {Promise<Object>} 图片信息
         */
        validateImage(base64Data) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                
                img.onload = () => {
                    const info = {
                        width: img.width,
                        height: img.height,
                        aspectRatio: img.width / img.height,
                        size: base64Data.length
                    };

                    // 检查尺寸限制
                    if (img.width > this.config.maxWidth || img.height > this.config.maxHeight) {
                        reject(new Error(`图片尺寸超过限制 (${this.config.maxWidth}x${this.config.maxHeight})`));
                        return;
                    }

                    resolve(info);
                };

                img.onerror = () => reject(new Error('无效的图片格式'));
                img.src = base64Data;
            });
        }

        /**
         * 应用图片预处理
         * @param {string} base64Data - 原始Base64数据
         * @param {Object} options - 处理选项
         * @returns {Promise<string>} 处理后的Base64数据
         */
        async applyImagePreprocessing(base64Data, options = {}) {
            try {
                // 如果不需要预处理，直接返回
                if (!options.preprocess) {
                    return base64Data;
                }

                const img = new Image();
                
                return new Promise((resolve) => {
                    img.onload = () => {
                        // 设置Canvas尺寸
                        this.canvas.width = img.width;
                        this.canvas.height = img.height;

                        // 绘制原始图片
                        this.ctx.drawImage(img, 0, 0);

                        // 应用预处理效果
                        if (this.preprocessingOptions.enhanceContrast) {
                            this.enhanceContrast();
                        }

                        if (this.preprocessingOptions.sharpenText) {
                            this.sharpenText();
                        }

                        // 导出处理后的图片
                        const processedBase64 = this.canvas.toDataURL('image/jpeg', this.config.compressionQuality);
                        resolve(processedBase64);
                    };

                    img.src = base64Data;
                });

            } catch (error) {
                this.logger.logError('图片预处理应用失败', error);
                return base64Data; // 返回原始数据
            }
        }

        /**
         * 增强对比度
         */
        enhanceContrast() {
            const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
            const data = imageData.data;

            const contrast = 1.2; // 对比度增强系数
            const factor = (259 * (contrast * 255 + 255)) / (255 * (259 - contrast * 255));

            for (let i = 0; i < data.length; i += 4) {
                data[i] = factor * (data[i] - 128) + 128;     // Red
                data[i + 1] = factor * (data[i + 1] - 128) + 128; // Green
                data[i + 2] = factor * (data[i + 2] - 128) + 128; // Blue
            }

            this.ctx.putImageData(imageData, 0, 0);
        }

        /**
         * 锐化文字
         */
        sharpenText() {
            const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
            const data = imageData.data;
            const width = this.canvas.width;
            const height = this.canvas.height;

            // 锐化卷积核
            const kernel = [
                0, -1, 0,
                -1, 5, -1,
                0, -1, 0
            ];

            const newData = new Uint8ClampedArray(data);

            for (let y = 1; y < height - 1; y++) {
                for (let x = 1; x < width - 1; x++) {
                    for (let c = 0; c < 3; c++) { // RGB通道
                        let sum = 0;
                        for (let ky = -1; ky <= 1; ky++) {
                            for (let kx = -1; kx <= 1; kx++) {
                                const idx = ((y + ky) * width + (x + kx)) * 4 + c;
                                sum += data[idx] * kernel[(ky + 1) * 3 + (kx + 1)];
                            }
                        }
                        newData[(y * width + x) * 4 + c] = Math.max(0, Math.min(255, sum));
                    }
                }
            }

            const newImageData = new ImageData(newData, width, height);
            this.ctx.putImageData(newImageData, 0, 0);
        }

        /**
         * 构建图片分析提示词
         * @param {Object} options - 分析选项
         * @returns {string} 提示词
         */
        buildImageAnalysisPrompt(options = {}) {
            const promptTemplateEngine = window.OTA?.gemini?.core?.getPromptTemplateEngine?.();
            
            if (promptTemplateEngine) {
                return promptTemplateEngine.getContextualPrompt('image_analysis', options);
            }

            // 默认图片分析提示词
            return `你是一个专业的图片订单分析助手。请仔细分析这张图片中的订单信息。

**分析重点：**
1. 文字识别：提取图片中的所有文字信息
2. 结构识别：识别表格、列表等结构化信息
3. 关键信息提取：客户信息、时间、地点、价格等
4. 格式标准化：将提取的信息标准化为JSON格式

**返回格式：**
请返回标准的JSON格式，包含以下字段：
- customer_name: 客户姓名
- customer_contact: 客户联系方式
- pickup_date: 接送日期（DD-MM-YYYY格式）
- pickup_time: 接送时间（HH:MM格式）
- pickup: 接送地点
- dropoff: 目的地
- ota_price: 订单价格
- ota_currency: 货币类型
- ota_reference_number: 订单号
- special_requests: 特殊要求

如果某些信息不清晰或无法识别，请将对应字段设为null。`;
        }

        /**
         * 调用Gemini Vision API
         * @param {Object} geminiService - Gemini服务实例
         * @param {string} base64Image - Base64图片数据
         * @param {string} prompt - 分析提示词
         * @param {Object} options - 调用选项
         * @returns {Promise<Object>} API响应结果
         */
        async callGeminiVisionAPI(geminiService, base64Image, prompt, options = {}) {
            try {
                // 构建请求参数
                const requestOptions = {
                    image: base64Image,
                    prompt: prompt,
                    timeout: options.timeout || this.config.analysisTimeout,
                    maxRetries: options.maxRetries || this.config.maxRetries,
                    ...options
                };

                // 调用Gemini服务的图片分析方法
                const result = await geminiService.analyzeImage(base64Image, requestOptions);
                
                return result;

            } catch (error) {
                this.logger.logError('Gemini Vision API调用失败', error);
                throw error;
            }
        }

        /**
         * 后处理分析结果
         * @param {Object} analysisResult - 原始分析结果
         * @param {Object} options - 处理选项
         * @returns {Promise<Object>} 处理后的结果
         */
        async postProcessAnalysisResult(analysisResult, options = {}) {
            try {
                let processedData = analysisResult;

                // 如果分析失败，尝试错误恢复
                if (!analysisResult.success && analysisResult.responseText) {
                    const errorRecoveryEngine = window.OTA?.gemini?.core?.getErrorRecoveryEngine?.();
                    if (errorRecoveryEngine) {
                        const recoveryResult = errorRecoveryEngine.intelligentErrorRecovery(
                            analysisResult.responseText, 
                            ''
                        );
                        if (recoveryResult.success) {
                            processedData = recoveryResult.data;
                        }
                    }
                }

                // 数据标准化
                const dataNormalizer = window.OTA?.gemini?.core?.getDataNormalizer?.();
                if (dataNormalizer && processedData) {
                    processedData = dataNormalizer.normalizeDataFormats(processedData);
                }

                // 添加图片分析标记
                if (processedData && typeof processedData === 'object') {
                    processedData._source = 'image_analysis';
                    processedData._confidence = processedData._confidence || 0.6;
                }

                return processedData;

            } catch (error) {
                this.logger.logError('分析结果后处理失败', error);
                return analysisResult;
            }
        }

        /**
         * 获取Gemini服务实例
         * @returns {Object|null} Gemini服务实例
         */
        getGeminiService() {
            return window.getGeminiService?.() || 
                   window.OTA?.services?.getGeminiService?.() ||
                   null;
        }

        /**
         * 更新统计信息
         * @param {boolean} success - 是否成功
         * @param {number} processingTime - 处理时间
         */
        updateStats(success, processingTime) {
            if (success) {
                this.stats.successfulAnalyses++;
            } else {
                this.stats.failedAnalyses++;
            }

            this.stats.totalProcessingTime += processingTime;
            this.stats.averageProcessingTime = this.stats.totalProcessingTime / this.stats.totalAnalyses;
        }

        /**
         * 获取统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            const total = this.stats.totalAnalyses;
            return {
                ...this.stats,
                successRate: total > 0 ? ((this.stats.successfulAnalyses / total) * 100).toFixed(2) + '%' : '0%',
                failureRate: total > 0 ? ((this.stats.failedAnalyses / total) * 100).toFixed(2) + '%' : '0%',
                averageProcessingTime: Math.round(this.stats.averageProcessingTime) + 'ms'
            };
        }

        /**
         * 重置统计信息
         */
        resetStats() {
            this.stats = {
                totalAnalyses: 0,
                successfulAnalyses: 0,
                failedAnalyses: 0,
                averageProcessingTime: 0,
                totalProcessingTime: 0
            };
        }
    }

    // 暴露到全局命名空间
    window.OTA.gemini.core.ImageAnalysisEngine = ImageAnalysisEngine;

    // 创建单例实例
    let imageAnalysisEngineInstance = null;

    /**
     * 获取图片分析引擎单例实例
     * @returns {ImageAnalysisEngine} 图片分析引擎实例
     */
    function getImageAnalysisEngine() {
        if (!imageAnalysisEngineInstance) {
            imageAnalysisEngineInstance = new ImageAnalysisEngine();
        }
        return imageAnalysisEngineInstance;
    }

    // 暴露工厂函数
    window.OTA.gemini.core.getImageAnalysisEngine = getImageAnalysisEngine;

    // 注册到服务注册中心
    if (window.OTA?.gemini?.core?.ServiceRegistry) {
        window.OTA.gemini.core.ServiceRegistry.register('imageAnalysisEngine', getImageAnalysisEngine, '@IMAGE_ANALYSIS_ENGINE');
    }

    console.log('✅ 图片分析引擎模块已加载');

})();
