/**
 * 应用启动协调器
 * 统一管理应用的启动流程，解决初始化时序混乱问题
 * 
 * 启动阶段:
 * 1. dependencies - 注册所有依赖
 * 2. services - 初始化核心服务
 * 3. managers - 初始化管理器
 * 4. ui - 初始化用户界面
 * 5. finalization - 完成启动
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 应用启动协调器类
     */
    class ApplicationBootstrap {
        constructor() {
            this.phases = [
                'dependencies',
                'services',
                'managers', 
                'ui',
                'finalization'
            ];
            this.currentPhase = 0;
            this.startTime = null;
            this.phaseResults = [];
            this.container = null;
            this.serviceLocator = null;
        }

        /**
         * 启动应用
         */
        async start() {
            this.startTime = performance.now();
            console.log('🚀 开始启动OTA订单处理系统...');

            try {
                // 获取依赖容器和服务定位器
                this.container = window.OTA.container;
                this.serviceLocator = window.OTA.serviceLocator;

                if (!this.container) {
                    throw new Error('依赖容器未初始化');
                }

                // 逐阶段执行启动流程
                for (let i = 0; i < this.phases.length; i++) {
                    this.currentPhase = i;
                    const phase = this.phases[i];

                    console.log(`📋 执行启动阶段: ${phase} (${i + 1}/${this.phases.length})`);

                    // 阶段前依赖验证
                    const preValidation = await this.validatePhasePrerequisites(phase, i);
                    if (!preValidation.success) {
                        throw new Error(`阶段 ${phase} 前置条件验证失败: ${preValidation.errors.join(', ')}`);
                    }

                    const phaseStart = performance.now();
                    const result = await this.executePhase(phase);
                    const phaseEnd = performance.now();

                    // 阶段后验证
                    const postValidation = await this.validatePhaseResults(phase, result);
                    if (!postValidation.success) {
                        console.warn(`⚠️ 阶段 ${phase} 后置验证警告: ${postValidation.warnings.join(', ')}`);
                    }

                    this.phaseResults.push({
                        phase,
                        success: result.success,
                        duration: phaseEnd - phaseStart,
                        details: result.details,
                        errors: result.errors || [],
                        preValidation,
                        postValidation
                    });

                    if (!result.success) {
                        throw new Error(`启动阶段 ${phase} 失败: ${result.errors.join(', ')}`);
                    }
                }

                const totalTime = performance.now() - this.startTime;
                console.log(`✅ OTA系统启动完成，总耗时: ${totalTime.toFixed(2)}ms`);
                
                this.printStartupReport();
                return { success: true, duration: totalTime };

            } catch (error) {
                console.error('❌ 系统启动失败:', error);
                this.printFailureReport(error);
                return { success: false, error: error.message };
            }
        }

        /**
         * 执行启动阶段
         * @param {string} phase - 阶段名称
         */
        async executePhase(phase) {
            const result = { success: true, details: [], errors: [] };

            try {
                switch (phase) {
                    case 'dependencies':
                        await this.registerDependencies(result);
                        break;
                    case 'services':
                        await this.initializeServices(result);
                        break;
                    case 'managers':
                        await this.initializeManagers(result);
                        break;
                    case 'ui':
                        await this.initializeUI(result);
                        break;
                    case 'finalization':
                        await this.finalize(result);
                        break;
                    default:
                        throw new Error(`未知的启动阶段: ${phase}`);
                }
            } catch (error) {
                result.success = false;
                result.errors.push(error.message);
            }

            return result;
        }

        /**
         * 注册所有依赖
         */
        async registerDependencies(result) {
            const dependencies = [
                // 核心服务
                { name: 'appState', factory: () => window.OTA.appState || new window.AppState() },
                { name: 'logger', factory: () => window.OTA.logger || window.logger },
                { name: 'utils', factory: () => window.OTA.utils || window.utils },
                
                // 🔧 事件协调器 - 关键修复：确保事件协调器被正确初始化
                { name: 'eventCoordinator', factory: () => {
                    const coordinator = window.OTA.globalEventCoordinator || window.globalEventCoordinator;
                    if (coordinator && !coordinator.initialized) {
                        coordinator.init();
                    }
                    return coordinator;
                }},

                // 状态管理适配器
                { name: 'stateManagerAdapter', factory: () => {
                    const adapter = window.getStateManagerAdapter ? window.getStateManagerAdapter() : null;
                    if (adapter && !adapter.initialized) {
                        adapter.init();
                    }
                    return adapter;
                }},

                // 业务服务
                { name: 'apiService', factory: () => window.OTA.apiService || window.apiService },
                { name: 'geminiService', factory: () => window.OTA.geminiService || window.geminiService },
                { name: 'i18nManager', factory: () => window.OTA.i18nManager || window.getI18nManager?.() },
                { name: 'languageManager', factory: () => window.OTA.languageManager || window.getLanguageManager?.() },
                { name: 'kimiService', factory: () => window.OTA.kimiService || window.getKimiService?.() },

                // 功能管理器
                { name: 'imageUploadManager', factory: () => window.OTA.imageUploadManager || window.getImageUploadManager?.() },
                { name: 'currencyConverter', factory: () => window.OTA.currencyConverter || window.getCurrencyConverter?.() },
                { name: 'multiOrderManager', factory: () => {
                    // 延迟获取，避免循环依赖
                    return window.OTA.multiOrderManager || window.getMultiOrderManager?.();
                } },
                { name: 'orderHistoryManager', factory: () => window.OTA.orderHistoryManager || window.getOrderHistoryManager?.() },
                { name: 'pagingServiceManager', factory: () => window.OTA.pagingServiceManager || window.getPagingServiceManager?.() },

                // UI管理器
                { name: 'uiManager', factory: () => window.OTA.uiManager || window.getUIManager() }
            ];

            for (const dep of dependencies) {
                try {
                    this.container.register(dep.name, dep.factory);
                    result.details.push(`已注册: ${dep.name}`);
                } catch (error) {
                    result.errors.push(`注册 ${dep.name} 失败: ${error.message}`);
                }
            }

            console.log(`📦 已注册 ${result.details.length} 个依赖`);
        }

        /**
         * 初始化核心服务
         */
        async initializeServices(result) {
            const coreServices = ['appState', 'logger', 'utils', 'eventCoordinator', 'apiService', 'geminiService'];
            
            for (const serviceName of coreServices) {
                try {
                    const service = this.container.get(serviceName);
                    if (service && typeof service.init === 'function') {
                        await service.init();
                    }
                    result.details.push(`已初始化: ${serviceName}`);
                } catch (error) {
                    result.errors.push(`初始化 ${serviceName} 失败: ${error.message}`);
                }
            }

            console.log(`⚙️ 已初始化 ${result.details.length} 个核心服务`);
        }

        /**
         * 初始化管理器
         */
        async initializeManagers(result) {
            const managers = [
                'imageUploadManager',
                'currencyConverter', 
                'multiOrderManager',
                'orderHistoryManager',
                'pagingServiceManager'
            ];

            for (const managerName of managers) {
                try {
                    const manager = this.container.get(managerName);
                    if (manager && typeof manager.init === 'function') {
                        await manager.init();
                    }
                    result.details.push(`已初始化: ${managerName}`);
                } catch (error) {
                    // 管理器初始化失败不阻断启动流程
                    result.details.push(`跳过: ${managerName} (${error.message})`);
                }
            }

            console.log(`🎛️ 已处理 ${result.details.length} 个管理器`);
        }

        /**
         * 初始化用户界面
         */
        async initializeUI(result) {
            try {
                // **修复时序问题**: 先初始化国际化管理器，再初始化UI管理器
                const i18nManager = this.container.get('i18nManager');
                if (i18nManager && typeof i18nManager.init === 'function') {
                    await i18nManager.init();
                    result.details.push('国际化管理器已初始化');
                    // 等待一小段时间确保i18n完全初始化
                    await new Promise(resolve => setTimeout(resolve, 10));
                }

                const uiManager = this.container.get('uiManager');
                if (uiManager && typeof uiManager.init === 'function') {
                    await uiManager.init();
                    result.details.push('UI管理器已初始化');
                }

                console.log('🎨 用户界面初始化完成');
            } catch (error) {
                result.errors.push(`UI初始化失败: ${error.message}`);
            }
        }

        /**
         * 完成启动
         */
        async finalize(result) {
            try {
                // 执行系统健康检查
                const healthCheck = this.performHealthCheck();
                result.details.push(`健康检查: ${healthCheck.score}/100`);

                // 设置全局错误处理
                this.setupGlobalErrorHandling();
                result.details.push('全局错误处理已设置');

                // 🚀 已移除：懒加载系统初始化 - 避免CORS问题
                // await this.initializeLazyLoading(result);
                result.details.push('懒加载系统已禁用，使用静态加载方式');

                // 暴露调试接口
                this.exposeDebugInterface();
                result.details.push('调试接口已暴露');

                console.log('🏁 系统启动完成');
            } catch (error) {
                result.errors.push(`完成阶段失败: ${error.message}`);
            }
        }

        /**
         * 初始化懒加载系统
         */
        async initializeLazyLoading(result) {
            try {
                // 确保懒加载器和模块配置已加载
                const lazyLoader = window.OTA?.lazyLoader;
                const moduleConfig = window.OTA?.moduleConfig;

                if (!lazyLoader) {
                    result.details.push('懒加载器未找到，跳过懒加载初始化');
                    return;
                }

                if (!moduleConfig) {
                    result.details.push('模块配置未找到，跳过懒加载初始化');
                    return;
                }

                console.log('🔄 初始化懒加载系统...');

                // 注册所有可懒加载的模块
                const loadingPlan = moduleConfig.getLoadingPlan();
                let registeredCount = 0;

                // 注册按需加载的模块
                for (const moduleInfo of loadingPlan.onDemand) {
                    const config = moduleConfig.getModuleConfig(moduleInfo.path);
                    lazyLoader.registerModule(moduleInfo.path, config);
                    registeredCount++;
                }

                // 注册预加载模块
                for (const moduleInfo of loadingPlan.preload) {
                    const config = moduleConfig.getModuleConfig(moduleInfo.path);
                    lazyLoader.registerModule(moduleInfo.path, config);
                    registeredCount++;
                }

                result.details.push(`懒加载系统已初始化，注册模块: ${registeredCount}`);

                // 设置功能触发器
                this.setupLazyLoadingTriggers(lazyLoader, moduleConfig);
                result.details.push('懒加载触发器已设置');

                // 开始后台预加载（延迟启动以免影响主流程）
                setTimeout(() => {
                    this.startBackgroundPreloading(lazyLoader, moduleConfig);
                }, 2000);

                console.log('✅ 懒加载系统初始化完成');

            } catch (error) {
                console.error('懒加载系统初始化失败:', error);
                result.details.push(`懒加载初始化失败: ${error.message}`);
                // 不抛出错误，避免影响系统启动
            }
        }

        /**
         * 设置懒加载触发器
         */
        setupLazyLoadingTriggers(lazyLoader, moduleConfig) {
            const triggers = {
                // AI功能触发器
                'ai-analysis': () => {
                    console.log('🤖 AI分析请求，加载AI模块...');
                    return this.loadModuleGroup(lazyLoader, moduleConfig, 'ai');
                },

                // 多订单功能触发器
                'multi-order-mode': () => {
                    console.log('📋 多订单模式，加载多订单模块...');
                    return this.loadModuleGroup(lazyLoader, moduleConfig, 'multiOrder');
                },

                // Gemini AI触发器
                'gemini-analysis': () => {
                    console.log('🧠 Gemini分析请求，加载Gemini模块...');
                    return this.loadModuleGroup(lazyLoader, moduleConfig, 'gemini');
                },

                // 图片上传触发器
                'image-upload': () => {
                    console.log('📷 图片上传请求，加载工具模块...');
                    return this.loadModuleGroup(lazyLoader, moduleConfig, 'utilities');
                },

                // 历史记录触发器
                'order-history': () => {
                    console.log('📚 历史记录请求，加载历史模块...');
                    return this.loadModuleGroup(lazyLoader, moduleConfig, 'utilities');
                }
            };

            // 注册到全局事件系统
            const eventCoordinator = window.OTA?.globalEventCoordinator;
            if (eventCoordinator) {
                Object.entries(triggers).forEach(([eventName, handler]) => {
                    eventCoordinator.on(eventName, handler);
                });
            }

            // 暴露触发器到调试接口
            window.OTA.lazyLoadTriggers = triggers;
        }

        /**
         * 加载模块组
         */
        async loadModuleGroup(lazyLoader, moduleConfig, groupName) {
            try {
                const modules = moduleConfig.getModuleGroup(groupName);
                const loadPromises = modules.map(moduleId => 
                    lazyLoader.loadModule(moduleId).catch(error => {
                        console.warn(`模块加载失败: ${moduleId}`, error);
                        return null; // 继续加载其他模块
                    })
                );

                const results = await Promise.all(loadPromises);
                const successCount = results.filter(result => result !== null).length;
                
                console.log(`✅ 模块组 ${groupName} 加载完成: ${successCount}/${modules.length}`);
                return results;

            } catch (error) {
                console.error(`模块组 ${groupName} 加载失败:`, error);
                throw error;
            }
        }

        /**
         * 开始后台预加载
         */
        startBackgroundPreloading(lazyLoader, moduleConfig) {
            console.log('🔄 开始后台预加载...');

            const preloadGroups = ['managers', 'i18n'];
            
            preloadGroups.forEach(groupName => {
                const modules = moduleConfig.getModuleGroup(groupName);
                modules.forEach(moduleId => {
                    lazyLoader.preloadModule(moduleId);
                });
            });

            console.log('✅ 后台预加载已启动');
        }

        /**
         * 执行系统健康检查
         */
        performHealthCheck() {
            let score = 100;
            const issues = [];

            // 检查核心服务
            const coreServices = ['appState', 'logger', 'apiService', 'geminiService', 'uiManager'];
            for (const service of coreServices) {
                if (!this.container.has(service)) {
                    score -= 15;
                    issues.push(`缺少核心服务: ${service}`);
                }
            }

            // 检查DOM元素
            const criticalElements = ['loginPanel', 'workspace', 'orderTextInput'];
            for (const elementId of criticalElements) {
                if (!document.getElementById(elementId)) {
                    score -= 10;
                    issues.push(`缺少关键DOM元素: ${elementId}`);
                }
            }

            return { score: Math.max(0, score), issues };
        }

        /**
         * 设置全局错误处理
         */
        setupGlobalErrorHandling() {
            window.addEventListener('error', (event) => {
                const logger = this.serviceLocator?.getService('logger');
                if (logger) {
                    logger.logError('全局错误', event.error);
                }
            });

            window.addEventListener('unhandledrejection', (event) => {
                const logger = this.serviceLocator?.getService('logger');
                if (logger) {
                    logger.logError('未处理的Promise拒绝', event.reason);
                }
            });
        }

        /**
         * 暴露调试接口
         */
        exposeDebugInterface() {
            window.OTA.debug = {
                bootstrap: this,
                container: this.container,
                serviceLocator: this.serviceLocator,
                getService: (name) => this.serviceLocator?.getService(name),
                getStartupReport: () => this.getStartupReport(),
                restart: () => this.restart()
            };
        }

        /**
         * 获取启动报告
         */
        getStartupReport() {
            return {
                totalDuration: this.phaseResults.reduce((sum, phase) => sum + phase.duration, 0),
                phases: this.phaseResults,
                success: this.phaseResults.every(phase => phase.success)
            };
        }

        /**
         * 打印启动报告
         */
        printStartupReport() {
            console.group('📊 启动报告');
            this.phaseResults.forEach(phase => {
                const status = phase.success ? '✅' : '❌';
                console.log(`${status} ${phase.phase}: ${phase.duration.toFixed(2)}ms`);
                if (phase.details.length > 0) {
                    console.log(`   详情: ${phase.details.join(', ')}`);
                }
            });
            console.groupEnd();
        }

        /**
         * 验证阶段前置条件
         * @param {string} phase - 阶段名称
         * @param {number} phaseIndex - 阶段索引
         * @returns {Object} 验证结果
         */
        async validatePhasePrerequisites(phase, phaseIndex) {
            const validation = {
                success: true,
                errors: [],
                warnings: []
            };

            switch (phase) {
                case 'dependencies':
                    // 检查基础环境
                    if (typeof window === 'undefined') {
                        validation.errors.push('window对象不可用');
                        validation.success = false;
                    }
                    if (!window.OTA) {
                        validation.errors.push('OTA命名空间未初始化');
                        validation.success = false;
                    }
                    break;

                case 'services':
                    // 检查依赖容器是否可用
                    if (!this.container) {
                        validation.errors.push('依赖容器未初始化');
                        validation.success = false;
                    }
                    // 检查警告管理器
                    if (!window.OTA?.core?.warningManager) {
                        validation.warnings.push('警告管理器未找到');
                    }
                    break;

                case 'managers':
                    // 检查核心服务是否已注册
                    const requiredServices = ['logger', 'appState'];
                    for (const serviceName of requiredServices) {
                        try {
                            const service = window.OTA.getService(serviceName);
                            if (!service) {
                                validation.errors.push(`核心服务 ${serviceName} 未注册`);
                                validation.success = false;
                            }
                        } catch (error) {
                            validation.errors.push(`无法访问服务 ${serviceName}: ${error.message}`);
                            validation.success = false;
                        }
                    }
                    break;

                case 'ui':
                    // 检查管理器是否已初始化
                    if (!window.OTA.getService('uiManager')) {
                        validation.warnings.push('UI管理器未找到');
                    }
                    break;

                case 'finalization':
                    // 检查所有关键组件是否就绪
                    const criticalComponents = ['logger', 'appState', 'uiManager'];
                    for (const component of criticalComponents) {
                        try {
                            const service = window.OTA.getService(component);
                            if (!service) {
                                validation.warnings.push(`关键组件 ${component} 未就绪`);
                            }
                        } catch (error) {
                            validation.warnings.push(`关键组件 ${component} 访问异常: ${error.message}`);
                        }
                    }
                    break;
            }

            return validation;
        }

        /**
         * 验证阶段执行结果
         * @param {string} phase - 阶段名称
         * @param {Object} result - 执行结果
         * @returns {Object} 验证结果
         */
        async validatePhaseResults(phase, result) {
            const validation = {
                success: true,
                warnings: []
            };

            if (!result.success) {
                validation.success = false;
                return validation;
            }

            switch (phase) {
                case 'dependencies':
                    // 验证依赖容器是否正确初始化
                    if (!this.container) {
                        validation.warnings.push('依赖容器初始化后仍不可用');
                        validation.success = false;
                    }
                    break;

                case 'services':
                    // 验证核心服务是否可访问
                    try {
                        const logger = window.OTA.getService('logger');
                        if (!logger || typeof logger.log !== 'function') {
                            validation.warnings.push('日志服务不完整');
                        }
                    } catch (error) {
                        validation.warnings.push('日志服务验证失败');
                    }
                    break;

                case 'managers':
                    // 验证管理器是否正确初始化
                    try {
                        const uiManager = window.OTA.getService('uiManager');
                        if (!uiManager) {
                            validation.warnings.push('UI管理器初始化后不可用');
                        }
                    } catch (error) {
                        validation.warnings.push('UI管理器验证失败');
                    }
                    break;
            }

            return validation;
        }

        /**
         * 打印失败报告
         */
        printFailureReport(error) {
            console.group('❌ 启动失败报告');
            console.error('错误:', error.message);
            console.log('已完成阶段:', this.phaseResults.filter(p => p.success).map(p => p.phase));
            console.log('失败阶段:', this.phaseResults.filter(p => !p.success).map(p => p.phase));

            // 显示验证详情
            this.phaseResults.forEach(phase => {
                if (phase.preValidation && !phase.preValidation.success) {
                    console.error(`阶段 ${phase.phase} 前置验证失败:`, phase.preValidation.errors);
                }
                if (phase.postValidation && !phase.postValidation.success) {
                    console.warn(`阶段 ${phase.phase} 后置验证警告:`, phase.postValidation.warnings);
                }
            });

            console.groupEnd();
        }

        /**
         * 重启应用
         */
        async restart() {
            console.log('🔄 重启应用...');
            this.currentPhase = 0;
            this.phaseResults = [];
            return await this.start();
        }
    }

    // 暴露到OTA命名空间
    window.OTA.ApplicationBootstrap = ApplicationBootstrap;

    console.log('✅ 应用启动协调器已加载');

})();
