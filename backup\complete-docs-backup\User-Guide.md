# OTA系统用户使用指南

## 📖 概述

本指南为OTA订单处理系统的最终用户提供完整的使用说明，包括功能介绍、操作流程和最佳实践。

### 系统特色
- 🚀 **智能订单处理**：AI驱动的订单文本智能分析
- 🌍 **多语言支持**：支持英文、中文、马来文、泰米尔文
- 📱 **响应式设计**：适配各种设备屏幕
- 🔍 **实时搜索**：智能酒店匹配和搜索
- 📊 **数据分析**：实时价格分析和统计

## 🎯 快速开始

### 系统访问
1. 打开浏览器访问系统网址
2. 系统自动加载，无需登录
3. 界面加载完成后即可开始使用

### 基本界面说明

#### 主要区域
- **顶部工具栏**：语言切换、设置按钮
- **订单输入区**：文本输入和AI分析
- **表单区域**：订单详细信息填写
- **操作区域**：提交、重置、导出等功能
- **状态栏**：系统状态和提示信息

## 📝 订单处理流程

### 1. 基础订单创建

#### 方法一：文本智能识别
1. **输入订单文本**
   ```
   从KLIA机场到吉隆坡市中心
   明天下午2点
   4个人，2件行李
   联系人：张先生 +60123456789
   ```

2. **点击智能分析**
   - 系统自动识别关键信息
   - 填充表单对应字段
   - 显示分析结果和建议

3. **确认和修正**
   - 检查自动填充的信息
   - 手动修正错误或遗漏
   - 补充额外信息

#### 方法二：手动填写表单
1. **客户信息**
   - 客户姓名：必填
   - 联系电话：必填
   - 邮箱地址：可选

2. **行程信息**
   - 上车地点：必填
   - 目的地：必填
   - 接送日期：必填 (YYYY-MM-DD)
   - 接送时间：必填 (HH:MM)

3. **服务详情**
   - 乘客人数：必填
   - 行李件数：可选
   - 车型选择：从下拉列表选择
   - 服务区域：选择对应区域

### 2. 高级功能

#### 多订单模式
当系统检测到多个订单时，自动切换到多订单模式：

1. **自动检测**
   - 系统识别文本中的多个订单
   - 显示订单数量提示
   - 自动分割订单信息

2. **批量处理**
   - 每个订单独立显示卡片
   - 支持单独编辑每个订单
   - 批量提交所有订单

3. **快速编辑**
   - 一键复制订单信息
   - 批量修改相同字段
   - 智能补全重复信息

#### AI智能分析
1. **航班信息识别**
   - 自动识别航班号
   - 匹配航班时间
   - 调整接送时间

2. **酒店名称匹配**
   - 智能酒店搜索
   - 地址自动补全
   - 区域自动识别

3. **价格分析**
   - 实时价格计算
   - 费用明细显示
   - 历史价格对比

## 🌍 多语言使用

### 语言切换
1. 点击右上角语言按钮
2. 选择目标语言：
   - 🇺🇸 English
   - 🇨🇳 中文
   - 🇲🇾 Bahasa Malaysia
   - 🇱🇰 Tamil

### 多语言输入
- 系统支持混合语言输入
- 自动识别文本语言
- 智能翻译关键信息
- 保持原始输入格式

## 🔧 高级设置

### 系统设置
1. **点击设置按钮**（右上角齿轮图标）
2. **可调整选项**：
   - 默认语言设置
   - 自动保存间隔
   - AI分析敏感度
   - 界面主题选择

### 数据管理
1. **订单历史**
   - 查看历史订单
   - 重复使用订单模板
   - 导出历史数据

2. **模板管理**
   - 保存常用订单模板
   - 快速加载模板
   - 分享模板给团队

## 📊 数据导出功能

### 支持格式
- **Excel (.xlsx)**：完整的表格数据
- **CSV (.csv)**：逗号分隔值文件
- **PDF (.pdf)**：格式化的订单报告
- **JSON (.json)**：结构化数据格式

### 导出步骤
1. 选择要导出的订单
2. 点击"导出数据"按钮
3. 选择导出格式
4. 确认导出设置
5. 下载生成的文件

## 🔍 搜索和筛选

### 酒店搜索
1. **智能搜索**
   - 输入酒店名称关键词
   - 系统自动匹配
   - 显示相似酒店列表

2. **区域筛选**
   - 按地理区域筛选
   - 按服务类型筛选
   - 按价格范围筛选

### 订单查找
1. **快速查找**
   - 按客户姓名搜索
   - 按订单号搜索
   - 按日期范围搜索

2. **高级筛选**
   - 按订单状态筛选
   - 按服务类型筛选
   - 按操作员筛选

## ⚠️ 常见问题解决

### 订单提交问题

**问题**: 提交时显示"必填字段缺失"
**解决方案**:
1. 检查红色标记的必填字段
2. 确保所有必填信息已填写
3. 验证日期和时间格式正确

**问题**: AI分析结果不准确
**解决方案**:
1. 检查输入文本是否完整
2. 使用更清晰的描述
3. 手动修正分析结果
4. 系统会学习您的修正

### 界面显示问题

**问题**: 页面加载缓慢
**解决方案**:
1. 检查网络连接
2. 清除浏览器缓存
3. 刷新页面重新加载

**问题**: 多订单模式卡片重叠
**解决方案**:
1. 调整浏览器窗口大小
2. 使用横屏模式（移动设备）
3. 缩放页面显示比例

### 数据处理问题

**问题**: 导出数据不完整
**解决方案**:
1. 确认已选择所有需要的订单
2. 检查导出格式设置
3. 等待数据处理完成

**问题**: 历史订单无法加载
**解决方案**:
1. 检查浏览器存储权限
2. 清除本地存储数据
3. 重新操作几个订单

## 🚀 效率提升技巧

### 快速操作
1. **键盘快捷键**
   - `Ctrl + Enter`：快速提交订单
   - `Ctrl + R`：重置当前表单
   - `Ctrl + S`：保存为模板
   - `Ctrl + D`：复制当前订单

2. **批量操作**
   - 选择多个订单进行批量操作
   - 使用模板快速填充相似订单
   - 批量修改相同字段信息

### 智能辅助
1. **自动补全**
   - 输入时显示历史建议
   - 常用地址自动匹配
   - 客户信息智能填充

2. **错误预防**
   - 实时数据验证
   - 格式错误提示
   - 逻辑错误警告

## 📱 移动端使用

### 移动端优化
- 触屏友好的界面设计
- 手势操作支持
- 自适应屏幕尺寸
- 离线数据缓存

### 移动端特色功能
1. **语音输入**
   - 支持语音转文字
   - 多语言语音识别
   - 即时AI分析

2. **位置服务**
   - GPS定位当前位置
   - 附近酒店推荐
   - 距离计算辅助

## 🔒 数据安全

### 隐私保护
- 所有数据本地存储
- 不传输敏感个人信息
- 定期清理过期数据

### 数据备份
1. **自动备份**
   - 系统自动保存操作历史
   - 本地浏览器存储
   - 支持数据恢复

2. **手动备份**
   - 导出完整数据
   - 保存配置设置
   - 创建数据快照

## 📞 技术支持

### 获取帮助
- **在线帮助**：点击页面右下角帮助按钮
- **操作指导**：系统内置操作提示
- **视频教程**：观看操作演示视频

### 反馈渠道
- **问题报告**：使用内置反馈功能
- **功能建议**：通过设置页面提交建议
- **用户社区**：加入用户交流群组

## 📚 相关资源

### 相关文档
- [API参考文档](API-Reference.md)
- [架构设计指南](Architecture-Guide.md)
- [性能优化指南](Performance-Guide.md)

### 更新日志
- 查看系统更新历史
- 了解新功能介绍
- 获取版本升级指导

---
*用户指南版本: v1.0 | 最后更新: 2025-07-27*