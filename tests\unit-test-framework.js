/**
 * @TEST 统一单元测试框架
 * 🏷️ 标签: @UNIT_TEST_FRAMEWORK
 * 📝 说明: 为OTA系统提供统一的单元测试框架，支持异步测试、模拟对象、断言等功能
 * 🎯 功能: 测试套件管理、断言库、模拟对象、测试报告生成
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保全局命名空间
window.OTA = window.OTA || {};
window.OTA.Testing = window.OTA.Testing || {};

(function() {
    'use strict';

    /**
     * 统一单元测试框架类
     */
    class UnitTestFramework {
        constructor() {
            this.logger = window.getLogger?.() || console;
            this.testSuites = new Map();
            this.globalSetup = null;
            this.globalTeardown = null;
            this.config = {
                timeout: 30000,
                retries: 3,
                parallel: false,
                verbose: true,
                stopOnFirstFailure: false
            };
            
            // 测试统计
            this.stats = {
                total: 0,
                passed: 0,
                failed: 0,
                skipped: 0,
                startTime: null,
                endTime: null
            };

            // 断言计数
            this.assertionCount = 0;
        }

        /**
         * 配置测试框架
         * @param {Object} options - 配置选项
         */
        configure(options = {}) {
            Object.assign(this.config, options);
            this.logger.log('测试框架配置更新', 'info', this.config);
        }

        /**
         * 创建测试套件
         * @param {string} suiteName - 套件名称
         * @param {Function} suiteFunction - 套件函数
         */
        describe(suiteName, suiteFunction) {
            const suite = new TestSuite(suiteName, this);
            this.testSuites.set(suiteName, suite);
            
            // 执行套件定义
            const currentSuite = this.currentSuite;
            this.currentSuite = suite;
            try {
                suiteFunction.call(suite);
            } finally {
                this.currentSuite = currentSuite;
            }
        }

        /**
         * 创建测试用例
         * @param {string} testName - 测试名称
         * @param {Function} testFunction - 测试函数
         */
        it(testName, testFunction) {
            if (!this.currentSuite) {
                throw new Error('测试用例必须在describe块内定义');
            }
            this.currentSuite.addTest(testName, testFunction);
        }

        /**
         * 跳过测试用例
         * @param {string} testName - 测试名称
         * @param {Function} testFunction - 测试函数
         */
        xit(testName, testFunction) {
            if (!this.currentSuite) {
                throw new Error('测试用例必须在describe块内定义');
            }
            this.currentSuite.addTest(testName, testFunction, { skip: true });
        }

        /**
         * 设置全局前置操作
         * @param {Function} setupFunction - 前置函数
         */
        beforeAll(setupFunction) {
            this.globalSetup = setupFunction;
        }

        /**
         * 设置全局后置操作
         * @param {Function} teardownFunction - 后置函数
         */
        afterAll(teardownFunction) {
            this.globalTeardown = teardownFunction;
        }

        /**
         * 运行所有测试
         * @returns {Promise<Object>} 测试结果
         */
        async runAllTests() {
            this.stats.startTime = Date.now();
            this.logger.log('开始运行单元测试', 'info');

            try {
                // 执行全局前置操作
                if (this.globalSetup) {
                    await this.globalSetup();
                }

                // 运行所有测试套件
                for (const [suiteName, suite] of this.testSuites) {
                    this.logger.log(`运行测试套件: ${suiteName}`, 'info');
                    await suite.run();
                }

                // 执行全局后置操作
                if (this.globalTeardown) {
                    await this.globalTeardown();
                }

            } catch (error) {
                this.logger.logError('测试运行失败', error);
            } finally {
                this.stats.endTime = Date.now();
                return this.generateReport();
            }
        }

        /**
         * 生成测试报告
         * @returns {Object} 测试报告
         */
        generateReport() {
            // 统计所有套件的结果
            this.stats.total = 0;
            this.stats.passed = 0;
            this.stats.failed = 0;
            this.stats.skipped = 0;

            for (const suite of this.testSuites.values()) {
                this.stats.total += suite.stats.total;
                this.stats.passed += suite.stats.passed;
                this.stats.failed += suite.stats.failed;
                this.stats.skipped += suite.stats.skipped;
            }

            const duration = this.stats.endTime - this.stats.startTime;
            const report = {
                summary: {
                    total: this.stats.total,
                    passed: this.stats.passed,
                    failed: this.stats.failed,
                    skipped: this.stats.skipped,
                    duration: duration,
                    assertions: this.assertionCount
                },
                suites: Array.from(this.testSuites.values()).map(suite => suite.getReport()),
                success: this.stats.failed === 0
            };

            // 输出报告
            this.printReport(report);
            return report;
        }

        /**
         * 打印测试报告
         * @param {Object} report - 测试报告
         */
        printReport(report) {
            const { summary } = report;
            
            console.log('\n' + '='.repeat(60));
            console.log('📊 单元测试报告');
            console.log('='.repeat(60));
            console.log(`总计: ${summary.total} | 通过: ${summary.passed} | 失败: ${summary.failed} | 跳过: ${summary.skipped}`);
            console.log(`断言: ${summary.assertions} | 耗时: ${summary.duration}ms`);
            console.log(`成功率: ${((summary.passed / summary.total) * 100).toFixed(2)}%`);
            
            if (summary.failed > 0) {
                console.log('\n❌ 失败的测试:');
                report.suites.forEach(suite => {
                    suite.tests.forEach(test => {
                        if (test.status === 'failed') {
                            console.log(`  - ${suite.name} > ${test.name}: ${test.error}`);
                        }
                    });
                });
            }
            
            console.log('='.repeat(60));
            console.log(report.success ? '✅ 所有测试通过!' : '❌ 存在测试失败');
            console.log('='.repeat(60) + '\n');
        }
    }

    /**
     * 测试套件类
     */
    class TestSuite {
        constructor(name, framework) {
            this.name = name;
            this.framework = framework;
            this.tests = [];
            this.beforeEachHooks = [];
            this.afterEachHooks = [];
            this.beforeAllHooks = [];
            this.afterAllHooks = [];
            
            this.stats = {
                total: 0,
                passed: 0,
                failed: 0,
                skipped: 0
            };
        }

        /**
         * 添加测试用例
         * @param {string} name - 测试名称
         * @param {Function} testFunction - 测试函数
         * @param {Object} options - 选项
         */
        addTest(name, testFunction, options = {}) {
            this.tests.push({
                name,
                testFunction,
                options,
                status: 'pending',
                error: null,
                duration: 0
            });
        }

        /**
         * 添加前置钩子
         * @param {Function} hookFunction - 钩子函数
         */
        beforeEach(hookFunction) {
            this.beforeEachHooks.push(hookFunction);
        }

        /**
         * 添加后置钩子
         * @param {Function} hookFunction - 钩子函数
         */
        afterEach(hookFunction) {
            this.afterEachHooks.push(hookFunction);
        }

        /**
         * 添加套件前置钩子
         * @param {Function} hookFunction - 钩子函数
         */
        beforeAll(hookFunction) {
            this.beforeAllHooks.push(hookFunction);
        }

        /**
         * 添加套件后置钩子
         * @param {Function} hookFunction - 钩子函数
         */
        afterAll(hookFunction) {
            this.afterAllHooks.push(hookFunction);
        }

        /**
         * 运行测试套件
         */
        async run() {
            try {
                // 执行套件前置钩子
                for (const hook of this.beforeAllHooks) {
                    await hook();
                }

                // 运行所有测试
                for (const test of this.tests) {
                    await this.runTest(test);
                }

                // 执行套件后置钩子
                for (const hook of this.afterAllHooks) {
                    await hook();
                }

            } catch (error) {
                this.framework.logger.logError(`测试套件 ${this.name} 运行失败`, error);
            }
        }

        /**
         * 运行单个测试
         * @param {Object} test - 测试对象
         */
        async runTest(test) {
            if (test.options.skip) {
                test.status = 'skipped';
                this.stats.skipped++;
                return;
            }

            const startTime = Date.now();
            this.stats.total++;

            try {
                // 执行前置钩子
                for (const hook of this.beforeEachHooks) {
                    await hook();
                }

                // 执行测试
                await Promise.race([
                    test.testFunction(),
                    new Promise((_, reject) => 
                        setTimeout(() => reject(new Error('测试超时')), this.framework.config.timeout)
                    )
                ]);

                test.status = 'passed';
                this.stats.passed++;

                // 执行后置钩子
                for (const hook of this.afterEachHooks) {
                    await hook();
                }

            } catch (error) {
                test.status = 'failed';
                test.error = error.message;
                this.stats.failed++;
                
                if (this.framework.config.verbose) {
                    this.framework.logger.logError(`测试失败: ${this.name} > ${test.name}`, error);
                }
            } finally {
                test.duration = Date.now() - startTime;
            }
        }

        /**
         * 获取套件报告
         * @returns {Object} 套件报告
         */
        getReport() {
            return {
                name: this.name,
                stats: { ...this.stats },
                tests: this.tests.map(test => ({
                    name: test.name,
                    status: test.status,
                    duration: test.duration,
                    error: test.error
                }))
            };
        }
    }

    // 全局注册测试框架
    window.OTA.Testing.UnitTestFramework = UnitTestFramework;
    
    // 创建全局实例
    const globalFramework = new UnitTestFramework();
    
    // 导出全局测试函数
    window.describe = globalFramework.describe.bind(globalFramework);
    window.it = globalFramework.it.bind(globalFramework);
    window.xit = globalFramework.xit.bind(globalFramework);
    window.beforeAll = globalFramework.beforeAll.bind(globalFramework);
    window.afterAll = globalFramework.afterAll.bind(globalFramework);
    
    // 导出框架实例
    window.OTA.Testing.framework = globalFramework;

    /**
     * 断言库类
     */
    class AssertionLibrary {
        constructor(framework) {
            this.framework = framework;
        }

        /**
         * 基本相等断言
         * @param {*} actual - 实际值
         * @param {*} expected - 期望值
         * @param {string} message - 错误消息
         */
        assertEqual(actual, expected, message = '') {
            this.framework.assertionCount++;
            if (actual !== expected) {
                throw new Error(`断言失败: ${message || `期望 ${expected}, 实际 ${actual}`}`);
            }
        }

        /**
         * 深度相等断言
         * @param {*} actual - 实际值
         * @param {*} expected - 期望值
         * @param {string} message - 错误消息
         */
        assertDeepEqual(actual, expected, message = '') {
            this.framework.assertionCount++;
            if (!this.deepEqual(actual, expected)) {
                throw new Error(`深度断言失败: ${message || `期望 ${JSON.stringify(expected)}, 实际 ${JSON.stringify(actual)}`}`);
            }
        }

        /**
         * 真值断言
         * @param {*} value - 值
         * @param {string} message - 错误消息
         */
        assertTrue(value, message = '') {
            this.framework.assertionCount++;
            if (!value) {
                throw new Error(`真值断言失败: ${message || `期望真值, 实际 ${value}`}`);
            }
        }

        /**
         * 假值断言
         * @param {*} value - 值
         * @param {string} message - 错误消息
         */
        assertFalse(value, message = '') {
            this.framework.assertionCount++;
            if (value) {
                throw new Error(`假值断言失败: ${message || `期望假值, 实际 ${value}`}`);
            }
        }

        /**
         * 异常断言
         * @param {Function} fn - 函数
         * @param {string|RegExp} expectedError - 期望的错误
         * @param {string} message - 错误消息
         */
        async assertThrows(fn, expectedError = null, message = '') {
            this.framework.assertionCount++;
            try {
                await fn();
                throw new Error(`异常断言失败: ${message || '期望抛出异常但没有'}`);
            } catch (error) {
                if (expectedError) {
                    if (typeof expectedError === 'string' && !error.message.includes(expectedError)) {
                        throw new Error(`异常断言失败: ${message || `期望错误包含 "${expectedError}", 实际 "${error.message}"`}`);
                    }
                    if (expectedError instanceof RegExp && !expectedError.test(error.message)) {
                        throw new Error(`异常断言失败: ${message || `期望错误匹配 ${expectedError}, 实际 "${error.message}"`}`);
                    }
                }
            }
        }

        /**
         * 深度比较辅助方法
         * @param {*} a - 值A
         * @param {*} b - 值B
         * @returns {boolean} 是否相等
         */
        deepEqual(a, b) {
            if (a === b) return true;
            if (a == null || b == null) return false;
            if (typeof a !== typeof b) return false;

            if (typeof a === 'object') {
                const keysA = Object.keys(a);
                const keysB = Object.keys(b);
                if (keysA.length !== keysB.length) return false;

                for (const key of keysA) {
                    if (!keysB.includes(key)) return false;
                    if (!this.deepEqual(a[key], b[key])) return false;
                }
                return true;
            }

            return false;
        }
    }

    /**
     * 模拟对象库类
     */
    class MockLibrary {
        constructor() {
            this.mocks = new Map();
        }

        /**
         * 创建模拟函数
         * @param {Function} implementation - 实现函数
         * @returns {Function} 模拟函数
         */
        createMock(implementation = null) {
            const calls = [];
            const mockFn = function(...args) {
                calls.push({ args, timestamp: Date.now() });
                if (implementation) {
                    return implementation.apply(this, args);
                }
            };

            mockFn.calls = calls;
            mockFn.callCount = () => calls.length;
            mockFn.calledWith = (...expectedArgs) => {
                return calls.some(call =>
                    call.args.length === expectedArgs.length &&
                    call.args.every((arg, i) => arg === expectedArgs[i])
                );
            };
            mockFn.reset = () => calls.length = 0;

            return mockFn;
        }

        /**
         * 模拟对象方法
         * @param {Object} obj - 对象
         * @param {string} method - 方法名
         * @param {Function} implementation - 实现函数
         * @returns {Function} 恢复函数
         */
        mockMethod(obj, method, implementation = null) {
            const original = obj[method];
            const mockFn = this.createMock(implementation);
            obj[method] = mockFn;

            // 返回恢复函数
            return () => {
                obj[method] = original;
            };
        }

        /**
         * 清理所有模拟
         */
        clearAllMocks() {
            this.mocks.clear();
        }
    }

    // 创建断言和模拟库实例
    const assertions = new AssertionLibrary(globalFramework);
    const mocks = new MockLibrary();

    // 导出断言函数
    window.assertEqual = assertions.assertEqual.bind(assertions);
    window.assertDeepEqual = assertions.assertDeepEqual.bind(assertions);
    window.assertTrue = assertions.assertTrue.bind(assertions);
    window.assertFalse = assertions.assertFalse.bind(assertions);
    window.assertThrows = assertions.assertThrows.bind(assertions);

    // 导出模拟函数
    window.createMock = mocks.createMock.bind(mocks);
    window.mockMethod = mocks.mockMethod.bind(mocks);

    // 导出库实例
    window.OTA.Testing.assertions = assertions;
    window.OTA.Testing.mocks = mocks;

    // 日志记录
    const logger = window.getLogger?.() || console;
    logger.log('统一单元测试框架加载完成', 'info', {
        version: '1.0.0',
        features: ['async_support', 'mocking', 'assertions', 'reporting']
    });

})();
