/**
 * @OTA_CLEANUP_MANAGER 多订单清理管理器
 * 🏷️ 标签: @CLEANUP_MANAGER
 * 📝 说明: 专门负责多订单系统的资源清理、内存管理和DOM清理
 * ⚠️ 警告: 模块化重构产物，请勿重复开发
 * 重构特点：统一清理机制、防内存泄漏、定期清理
 * <AUTHOR>
 * @version 1.0.0-modular
 */

// 防止重复加载
if (window.OTA && window.OTA.MultiOrderCleanupManager) {
    console.log('多订单清理管理器已存在，跳过重复加载');
} else {

/**
 * 多订单清理管理器类
 * 负责所有清理相关的操作
 */
class MultiOrderCleanupManager {
    constructor(dependencies = {}) {
        // 🏗️ 依赖注入
        this.logger = dependencies.logger || this.getLogger();
        this.config = {
            cleanupInterval: 30000, // 30秒清理间隔
            maxCacheAge: 300000,    // 5分钟缓存过期
            ...dependencies.config
        };

        // 🏗️ 清理状态管理
        this.cleanupInterval = null;
        this.boundEventHandlers = new Map();
        this.isCleanupActive = false;

        // 🏗️ 初始化
        this.init();
    }

    /**
     * 初始化清理管理器
     */
    init() {
        this.logger?.log('🧹 多订单清理管理器初始化', 'info');
        this.setupGeneralCleanup();
        this.setupCleanupMechanism();
    }

    /**
     * 获取日志服务
     * @returns {Object} 日志服务实例
     */
    getLogger() {
        return window.getLogger?.() || {
            log: console.log.bind(console),
            logError: console.error.bind(console)
        };
    }

    /**
     * 设置通用清理机制
     */
    setupGeneralCleanup() {
        // 页面卸载时清理事件监听器
        const cleanup = () => {
            this.boundEventHandlers.forEach((handler, eventName) => {
                document.removeEventListener(eventName, handler);
            });
            this.boundEventHandlers.clear();

            if (this.debounceTimer) {
                clearTimeout(this.debounceTimer);
                this.debounceTimer = null;
            }

            this.logger?.log('多订单管理器已清理', 'info');
        };

        window.addEventListener('beforeunload', cleanup);
        this.boundEventHandlers.set('beforeunload', cleanup);
    }

    /**
     * 🔧 设置清理机制（防止内存泄漏）
     */
    setupCleanupMechanism() {
        // 设置页面卸载时的清理
        this.boundEventHandlers.set('beforeunload', () => {
            this.cleanup();
        });
        
        // 设置定期清理机制（防止内存泄漏）
        this.boundEventHandlers.set('periodicCleanup', () => {
            // 每30秒清理一次未使用的资源
            this.periodicCleanup();
        });
        
        // 绑定事件监听器
        window.addEventListener('beforeunload', this.boundEventHandlers.get('beforeunload'));
        
        // 设置定期清理（使用防抖处理）
        this.startPeriodicCleanup();
        
        this.logger?.log('🔧 多订单管理器清理机制已设置', 'info');
    }

    /**
     * 🔧 启动定期清理机制
     */
    startPeriodicCleanup() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
        
        // 每30秒执行一次清理
        this.cleanupInterval = setInterval(() => {
            this.periodicCleanup();
        }, this.config.cleanupInterval);
        
        this.logger?.log(`🕐 定期清理机制已启动（${this.config.cleanupInterval/1000}秒间隔）`, 'info');
    }

    /**
     * 🔧 定期清理未使用资源
     */
    periodicCleanup() {
        try {
            if (this.isCleanupActive) {
                this.logger?.log('⏳ 清理正在进行中，跳过本次定期清理', 'warn');
                return;
            }

            this.isCleanupActive = true;
            
            // 清理过期的处理状态
            const now = Date.now();
            
            // 清理未使用的快捷编辑面板
            const unusedOverlays = document.querySelectorAll('.quick-edit-overlay:not(.in-use)');
            unusedOverlays.forEach(overlay => {
                if (!overlay.querySelector('.quick-edit-panel.show')) {
                    overlay.remove();
                }
            });
            
            this.logger?.log('🧹 定期清理完成', 'info');
            
        } catch (error) {
            this.logger?.logError('定期清理失败', error);
        } finally {
            this.isCleanupActive = false;
        }
    }

    /**
     * 🔧 停止定期清理机制
     */
    stopPeriodicCleanup() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
            this.logger?.log('🛑 定期清理机制已停止', 'info');
        }
    }

    /**
     * 清除所有选择
     * @param {Object} state - 状态对象
     */
    clearAllSelections(state) {
        const checkboxes = document.querySelectorAll('.order-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
            const card = checkbox.closest('.order-card');
            if (card) {
                card.classList.remove('selected');
            }
        });
        
        // 清理状态
        if (state && state.selectedOrders) {
            state.selectedOrders.clear();
        }
        
        // 更新计数器
        this.updateSelectedCount();
    }

    /**
     * 更新选择计数器
     */
    updateSelectedCount() {
        const checkboxes = document.querySelectorAll('.order-checkbox:checked');
        const countElement = document.getElementById('selectedOrderCount');
        if (countElement) {
            countElement.textContent = checkboxes.length;
        }
    }

    /**
     * 🔧 清理缓存
     * @param {Object} state - 状态对象
     */
    clearCache(state) {
        try {
            if (state) {
                // 清理结果缓存
                if (state.processedOrders) state.processedOrders.clear();
                if (state.currentSegments) state.currentSegments = [];
                if (state.parsedOrders) state.parsedOrders = [];
                
                // 清理选择状态
                if (state.selectedOrders) state.selectedOrders.clear();
            }
            
            this.logger?.log('🧹 缓存已清理', 'info');
        } catch (error) {
            this.logger?.logError('清理缓存失败', error);
        }
    }

    /**
     * 🔧 清理快捷编辑面板和DOM节点（防止内存泄漏）
     */
    cleanupQuickEditPanels() {
        try {
            // 清理所有快捷编辑面板
            const overlays = document.querySelectorAll('.quick-edit-overlay');
            overlays.forEach(overlay => {
                // 清理事件监听器
                const panel = overlay.querySelector('.quick-edit-panel');
                if (panel) {
                    const inputs = panel.querySelectorAll('input');
                    inputs.forEach(input => {
                        input.removeEventListener('blur', this.handleQuickEditBlur);
                        input.removeEventListener('keypress', this.handleQuickEditKeypress);
                    });
                }
                overlay.remove();
            });
            
            // 清理订单项的编辑状态
            const orderItems = document.querySelectorAll('.order-item.editing');
            orderItems.forEach(item => {
                item.classList.remove('editing');
            });
            
            this.logger?.log('🧹 快捷编辑面板已清理', 'info');
        } catch (error) {
            this.logger?.logError('清理快捷编辑面板失败', error);
        }
    }

    /**
     * 🔧 清理批量处理状态
     * @param {Object} state - 状态对象
     */
    cleanupBatchProcessing(state) {
        try {
            if (state && state.batchProgress) {
                // 重置批量进度
                state.batchProgress = {
                    total: 0,
                    completed: 0,
                    failed: 0,
                    isRunning: false,
                    startTime: null,
                    processingOrder: null
                };
            }
            
            // 清理批量处理相关的DOM状态
            const processBtn = document.getElementById('processBatchBtn');
            if (processBtn) {
                processBtn.disabled = false;
                processBtn.textContent = '批量处理';
                processBtn.classList.remove('btn-warning', 'btn-outline');
                processBtn.classList.add('btn-primary');
            }
            
            this.logger?.log('🧹 批量处理状态已清理', 'info');
        } catch (error) {
            this.logger?.logError('清理批量处理状态失败', error);
        }
    }

    /**
     * 🔧 清理DOM引用
     */
    cleanupDomReferences() {
        try {
            // 清理面板拖拽相关的事件监听器
            const multiOrderPanel = document.getElementById('multiOrderPanel');
            if (multiOrderPanel) {
                const header = multiOrderPanel.querySelector('.panel-header');
                if (header && header.dataset.dragEnabled) {
                    document.removeEventListener('mousemove', this.handleDrag);
                    document.removeEventListener('mouseup', this.handleDragEnd);
                    header.removeEventListener('mousedown', this.handleDragStart);
                    delete header.dataset.dragEnabled;
                }
            }

            // 清理其他DOM引用
            const editingElements = document.querySelectorAll('.editing-field');
            editingElements.forEach(element => {
                element.classList.remove('editing-field');
            });

            this.logger?.log('🧹 DOM引用已清理', 'info');
        } catch (error) {
            this.logger?.logError('清理DOM引用失败', error);
        }
    }

    /**
     * 🔧 清理资源（防止内存泄漏）
     * @param {Object} state - 状态对象
     * @param {Object} boundEventHandlers - 事件处理器映射
     * @param {Object} debounceTimer - 防抖定时器
     */
    cleanup(state, boundEventHandlers, debounceTimer) {
        try {
            // 清理防抖定时器
            if (debounceTimer) {
                clearTimeout(debounceTimer);
                debounceTimer = null;
            }

            // 清理定期清理机制
            this.stopPeriodicCleanup();

            // 清理快捷编辑面板和DOM节点
            this.cleanupQuickEditPanels();

            // 清理批量处理状态
            this.cleanupBatchProcessing(state);

            // 清理缓存
            this.clearCache(state);

            // 移除事件监听器
            if (boundEventHandlers) {
                if (boundEventHandlers.has('multiOrderDetected')) {
                    document.removeEventListener('multiOrderDetected', boundEventHandlers.get('multiOrderDetected'));
                }
                if (boundEventHandlers.has('appStateChanged')) {
                    document.removeEventListener('appStateChanged', boundEventHandlers.get('appStateChanged'));
                }
                if (boundEventHandlers.has('beforeunload')) {
                    window.removeEventListener('beforeunload', boundEventHandlers.get('beforeunload'));
                }
                if (boundEventHandlers.has('periodicCleanup')) {
                    clearInterval(boundEventHandlers.get('periodicCleanup'));
                }

                // 清理状态
                if (state) {
                    if (state.selectedOrders) state.selectedOrders.clear();
                    if (state.processedOrders) state.processedOrders.clear();
                    if (state.currentSegments) state.currentSegments = [];
                    if (state.parsedOrders) state.parsedOrders = [];
                }
                boundEventHandlers.clear();
            }

            // 移除键盘事件监听
            document.removeEventListener('keydown', this.handleQuickEditEscape);

            // 清理DOM引用
            this.cleanupDomReferences();

            this.logger?.log('🧹 多订单管理器资源已完全清理', 'info');
        } catch (error) {
            this.logger?.logError('清理资源失败', error);
        }
    }

    /**
     * 退出字段编辑模式
     * @param {HTMLElement} fieldElement - 字段元素
     * @param {number} index - 订单索引
     * @param {string} fieldName - 字段名
     */
    exitFieldEdit(fieldElement, index, fieldName) {
        // 移除编辑状态
        fieldElement.classList.remove('editing-field');

        // 移除输入框
        const inputElement = fieldElement.querySelector('.field-edit-input');
        if (inputElement) {
            inputElement.remove();
        }

        // 显示原始值元素
        const valueElement = fieldElement.querySelector('.grid-value');
        if (valueElement) {
            valueElement.style.display = 'inline';
        }

        this.logger?.log(`退出字段编辑: 订单${index + 1} - ${fieldName}`, 'info');
    }

    /**
     * 退出快捷编辑模式
     * @param {number} index - 订单索引
     */
    exitQuickEdit(index) {
        const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
        if (orderItem) {
            orderItem.classList.remove('editing');
        }

        // 使用专门的清理方法来移除快捷编辑面板
        this.cleanupQuickEditPanels();

        this.logger?.log(`退出订单 ${index + 1} 快捷编辑模式`, 'info');
    }

    /**
     * 清理验证状态
     * @param {HTMLElement} fieldGroup - 字段组元素
     */
    clearValidationState(fieldGroup) {
        if (fieldGroup) {
            fieldGroup.classList.remove('invalid', 'valid');
            const validationContainer = fieldGroup.querySelector('.validation-message');
            if (validationContainer) {
                validationContainer.innerHTML = '';
            }
        }
    }

    /**
     * 清理CSS类
     * @param {HTMLElement} element - 目标元素
     * @param {string|Array} classNames - 要清理的类名
     */
    clearCssClasses(element, classNames) {
        if (!element) return;

        const classes = Array.isArray(classNames) ? classNames : [classNames];
        classes.forEach(className => {
            element.classList.remove(className);
        });
    }

    /**
     * 销毁清理管理器
     */
    destroy() {
        this.stopPeriodicCleanup();
        this.boundEventHandlers.clear();
        this.isCleanupActive = false;
        this.logger?.log('🧹 清理管理器已销毁', 'info');
    }
}

// 导出到全局作用域
window.OTA = window.OTA || {};
window.OTA.MultiOrderCleanupManager = MultiOrderCleanupManager;

// 工厂函数
function getMultiOrderCleanupManager(dependencies = {}) {
    return new MultiOrderCleanupManager(dependencies);
}

window.getMultiOrderCleanupManager = getMultiOrderCleanupManager;

// 结束防重复加载检查
}
