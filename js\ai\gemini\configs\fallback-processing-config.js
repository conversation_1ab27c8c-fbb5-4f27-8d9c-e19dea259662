/**
 * @CONFIG_FILE 降级处理配置
 * 🏷️ 标签: @FALLBACK_PROCESSING_CONFIG
 * 📝 说明: 定义OTA处理器降级策略，确保系统在特定处理器失败时能够优雅降级
 * 🎯 功能: 降级策略、处理器选择、置信度调整、错误恢复
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.configs = window.OTA.gemini.configs || {};

(function() {
    'use strict';

    /**
     * 降级处理配置对象
     * 包含所有降级策略和规则
     */
    const fallbackProcessingConfig = {
        // 版本信息
        version: '1.0.0',
        lastUpdated: '2024-01-15',
        
        // 降级触发条件
        fallbackTriggers: {
            // 处理器错误
            processorError: {
                enabled: true,
                priority: 10,
                description: '处理器抛出异常时触发降级'
            },
            
            // 低置信度结果
            lowConfidence: {
                enabled: true,
                threshold: 0.3,
                priority: 8,
                description: '处理结果置信度低于阈值时触发降级'
            },
            
            // 关键字段缺失
            missingCriticalFields: {
                enabled: true,
                criticalFields: ['ota_reference_number', 'pickup_location', 'passenger_name'],
                priority: 9,
                description: '关键字段缺失时触发降级'
            },
            
            // 处理超时
            processingTimeout: {
                enabled: true,
                timeout: 30000, // 30秒
                priority: 7,
                description: '处理超时时触发降级'
            },
            
            // 数据验证失败
            validationFailure: {
                enabled: true,
                priority: 6,
                description: '数据验证失败时触发降级'
            },
            
            // 处理器不可用
            processorUnavailable: {
                enabled: true,
                priority: 10,
                description: '目标处理器不可用时触发降级'
            }
        },

        // 降级策略配置
        fallbackStrategies: {
            // 通用降级处理器
            generic: {
                name: 'GenericProcessor',
                description: '通用降级处理器，使用基础规则处理订单',
                priority: 1,
                confidenceAdjustment: -0.2,
                
                capabilities: {
                    referenceNumberExtraction: true,
                    basicFieldMapping: true,
                    genericValidation: true,
                    fallbackPresets: true
                },
                
                processingRules: {
                    useGenericPatterns: true,
                    applyBasicValidation: true,
                    enableFallbackPresets: true,
                    preserveOriginalData: true
                }
            },
            
            // 相似处理器降级
            similarProcessor: {
                name: 'SimilarProcessorFallback',
                description: '使用相似的处理器进行降级处理',
                priority: 2,
                confidenceAdjustment: -0.1,
                
                similarityMapping: {
                    'ChongDealer': ['JRCoach', 'generic'],
                    'Fliggy': ['Ctrip', 'KKday', 'generic'],
                    'JRCoach': ['ChongDealer', 'generic'],
                    'KKday': ['Klook', 'Fliggy', 'generic'],
                    'Klook': ['KKday', 'Agoda', 'generic'],
                    'Ctrip': ['Fliggy', 'Agoda', 'generic'],
                    'Agoda': ['Booking', 'Klook', 'generic'],
                    'Booking': ['Agoda', 'generic']
                }
            },
            
            // 混合处理策略
            hybrid: {
                name: 'HybridProcessor',
                description: '结合多个处理器的结果进行混合处理',
                priority: 3,
                confidenceAdjustment: -0.05,
                
                hybridRules: {
                    minProcessors: 2,
                    maxProcessors: 3,
                    weightingStrategy: 'confidence',
                    consensusThreshold: 0.6
                }
            },
            
            // 人工干预策略
            manualIntervention: {
                name: 'ManualIntervention',
                description: '标记为需要人工干预的订单',
                priority: 4,
                confidenceAdjustment: 0,
                
                interventionRules: {
                    flagForReview: true,
                    preserveOriginalText: true,
                    addProcessingNotes: true,
                    notifyOperators: true
                }
            }
        },

        // 处理器特定降级配置
        processorFallbackConfig: {
            'ChongDealer': {
                primaryFallbacks: ['JRCoach', 'generic'],
                fallbackConditions: {
                    signHoldingServiceMissing: {
                        fallbackTo: 'generic',
                        adjustConfidence: -0.3,
                        addNote: '举牌服务信息缺失，使用通用处理'
                    },
                    charterDurationUnclear: {
                        fallbackTo: 'JRCoach',
                        adjustConfidence: -0.2,
                        addNote: '包车时长不明确，使用JRCoach处理'
                    }
                }
            },
            
            'Fliggy': {
                primaryFallbacks: ['Ctrip', 'KKday', 'generic'],
                fallbackConditions: {
                    chineseContentOnly: {
                        fallbackTo: 'Ctrip',
                        adjustConfidence: -0.1,
                        addNote: '纯中文内容，使用携程处理器'
                    },
                    activityBooking: {
                        fallbackTo: 'KKday',
                        adjustConfidence: -0.15,
                        addNote: '活动预订，使用KKday处理器'
                    }
                }
            },
            
            'JRCoach': {
                primaryFallbacks: ['ChongDealer', 'generic'],
                fallbackConditions: {
                    nonMalaysianLocation: {
                        fallbackTo: 'generic',
                        adjustConfidence: -0.25,
                        addNote: '非马来西亚地点，使用通用处理'
                    },
                    complexItinerary: {
                        fallbackTo: 'ChongDealer',
                        adjustConfidence: -0.1,
                        addNote: '复杂行程，使用ChongDealer处理'
                    }
                }
            },
            
            'KKday': {
                primaryFallbacks: ['Klook', 'Fliggy', 'generic'],
                fallbackConditions: {
                    nonActivityBooking: {
                        fallbackTo: 'generic',
                        adjustConfidence: -0.2,
                        addNote: '非活动预订，使用通用处理'
                    },
                    multiDayTour: {
                        fallbackTo: 'Klook',
                        adjustConfidence: -0.1,
                        addNote: '多日游，使用Klook处理器'
                    }
                }
            },
            
            'Klook': {
                primaryFallbacks: ['KKday', 'Agoda', 'generic'],
                fallbackConditions: {
                    accommodationBooking: {
                        fallbackTo: 'Agoda',
                        adjustConfidence: -0.15,
                        addNote: '住宿预订，使用Agoda处理器'
                    },
                    ticketOnly: {
                        fallbackTo: 'KKday',
                        adjustConfidence: -0.1,
                        addNote: '纯门票预订，使用KKday处理器'
                    }
                }
            },
            
            'Ctrip': {
                primaryFallbacks: ['Fliggy', 'Agoda', 'generic'],
                fallbackConditions: {
                    internationalBooking: {
                        fallbackTo: 'Agoda',
                        adjustConfidence: -0.2,
                        addNote: '国际预订，使用Agoda处理器'
                    },
                    domesticTravel: {
                        fallbackTo: 'Fliggy',
                        adjustConfidence: -0.1,
                        addNote: '国内旅游，使用飞猪处理器'
                    }
                }
            },
            
            'Agoda': {
                primaryFallbacks: ['Booking', 'Klook', 'generic'],
                fallbackConditions: {
                    europeanProperty: {
                        fallbackTo: 'Booking',
                        adjustConfidence: -0.1,
                        addNote: '欧洲酒店，使用Booking处理器'
                    },
                    activityIncluded: {
                        fallbackTo: 'Klook',
                        adjustConfidence: -0.15,
                        addNote: '包含活动，使用Klook处理器'
                    }
                }
            },
            
            'Booking': {
                primaryFallbacks: ['Agoda', 'generic'],
                fallbackConditions: {
                    asianProperty: {
                        fallbackTo: 'Agoda',
                        adjustConfidence: -0.1,
                        addNote: '亚洲酒店，使用Agoda处理器'
                    },
                    nonAccommodation: {
                        fallbackTo: 'generic',
                        adjustConfidence: -0.25,
                        addNote: '非住宿预订，使用通用处理'
                    }
                }
            }
        },

        // 降级决策规则
        fallbackDecisionRules: {
            // 决策优先级
            decisionPriority: [
                'processorError',
                'processorUnavailable', 
                'missingCriticalFields',
                'lowConfidence',
                'processingTimeout',
                'validationFailure'
            ],
            
            // 最大降级尝试次数
            maxFallbackAttempts: 3,
            
            // 降级链深度限制
            maxFallbackChainDepth: 2,
            
            // 降级成功的最低置信度
            minFallbackConfidence: 0.2,
            
            // 是否启用循环降级检测
            enableCircularFallbackDetection: true,
            
            // 降级历史记录
            trackFallbackHistory: true
        },

        // 降级结果标记
        fallbackResultMarkers: {
            // 标记字段
            fallbackMarkerField: 'metadata.fallback',
            
            // 标记信息结构
            fallbackMarkerStructure: {
                isFallback: true,
                originalProcessor: null,
                fallbackProcessor: null,
                fallbackReason: null,
                fallbackChain: [],
                confidenceAdjustment: 0,
                fallbackTimestamp: null,
                processingNotes: []
            },
            
            // 置信度调整规则
            confidenceAdjustmentRules: {
                firstFallback: -0.1,
                secondFallback: -0.2,
                thirdFallback: -0.3,
                genericFallback: -0.2,
                manualIntervention: 0
            }
        },

        // 性能监控配置
        performanceMonitoring: {
            // 是否启用降级性能监控
            enabled: true,
            
            // 监控指标
            metrics: {
                fallbackRate: true,
                fallbackLatency: true,
                fallbackSuccessRate: true,
                processorReliability: true
            },
            
            // 告警阈值
            alertThresholds: {
                fallbackRate: 0.3,        // 降级率超过30%告警
                fallbackLatency: 5000,    // 降级处理超过5秒告警
                processorFailureRate: 0.2  // 处理器失败率超过20%告警
            }
        },

        // 降级优化配置
        fallbackOptimization: {
            // 是否启用智能降级选择
            enableSmartFallbackSelection: true,
            
            // 学习型降级（基于历史成功率）
            enableLearningFallback: true,
            
            // 降级缓存
            enableFallbackCaching: true,
            fallbackCacheSize: 1000,
            fallbackCacheTTL: 3600000, // 1小时
            
            // 预测性降级
            enablePredictiveFallback: false
        }
    };

    /**
     * 获取降级处理配置
     * @param {string} processorName - 处理器名称（可选）
     * @returns {Object} 降级配置
     */
    function getFallbackProcessingConfig(processorName = null) {
        if (processorName) {
            return {
                triggers: fallbackProcessingConfig.fallbackTriggers,
                strategies: fallbackProcessingConfig.fallbackStrategies,
                processorConfig: fallbackProcessingConfig.processorFallbackConfig[processorName] || {},
                decisionRules: fallbackProcessingConfig.fallbackDecisionRules,
                resultMarkers: fallbackProcessingConfig.fallbackResultMarkers,
                optimization: fallbackProcessingConfig.fallbackOptimization
            };
        }
        return fallbackProcessingConfig;
    }

    /**
     * 获取处理器降级链
     * @param {string} processorName - 处理器名称
     * @returns {Array} 降级链
     */
    function getProcessorFallbackChain(processorName) {
        const processorConfig = fallbackProcessingConfig.processorFallbackConfig[processorName];
        if (!processorConfig) {
            return ['generic'];
        }
        
        return processorConfig.primaryFallbacks || ['generic'];
    }

    /**
     * 评估是否需要降级
     * @param {Object} processingResult - 处理结果
     * @param {Object} context - 上下文信息
     * @returns {Object} 降级评估结果
     */
    function evaluateFallbackNeed(processingResult, context = {}) {
        const triggers = fallbackProcessingConfig.fallbackTriggers;
        const evaluationResult = {
            needsFallback: false,
            reasons: [],
            recommendedStrategy: null,
            priority: 0
        };

        // 检查处理器错误
        if (triggers.processorError.enabled && processingResult.error) {
            evaluationResult.needsFallback = true;
            evaluationResult.reasons.push({
                trigger: 'processorError',
                priority: triggers.processorError.priority,
                description: '处理器发生错误'
            });
        }

        // 检查低置信度
        if (triggers.lowConfidence.enabled && 
            processingResult.confidence < triggers.lowConfidence.threshold) {
            evaluationResult.needsFallback = true;
            evaluationResult.reasons.push({
                trigger: 'lowConfidence',
                priority: triggers.lowConfidence.priority,
                description: `置信度过低: ${processingResult.confidence}`
            });
        }

        // 检查关键字段缺失
        if (triggers.missingCriticalFields.enabled && processingResult.data) {
            const missingFields = triggers.missingCriticalFields.criticalFields.filter(
                field => !processingResult.data[field]
            );
            
            if (missingFields.length > 0) {
                evaluationResult.needsFallback = true;
                evaluationResult.reasons.push({
                    trigger: 'missingCriticalFields',
                    priority: triggers.missingCriticalFields.priority,
                    description: `缺失关键字段: ${missingFields.join(', ')}`
                });
            }
        }

        // 检查处理超时
        if (triggers.processingTimeout.enabled && context.processingTime > triggers.processingTimeout.timeout) {
            evaluationResult.needsFallback = true;
            evaluationResult.reasons.push({
                trigger: 'processingTimeout',
                priority: triggers.processingTimeout.priority,
                description: `处理超时: ${context.processingTime}ms`
            });
        }

        // 检查数据验证失败
        if (triggers.validationFailure.enabled && processingResult.validationErrors) {
            evaluationResult.needsFallback = true;
            evaluationResult.reasons.push({
                trigger: 'validationFailure',
                priority: triggers.validationFailure.priority,
                description: '数据验证失败'
            });
        }

        // 确定最高优先级和推荐策略
        if (evaluationResult.reasons.length > 0) {
            evaluationResult.priority = Math.max(...evaluationResult.reasons.map(r => r.priority));
            
            // 根据优先级推荐策略
            if (evaluationResult.priority >= 9) {
                evaluationResult.recommendedStrategy = 'similarProcessor';
            } else if (evaluationResult.priority >= 7) {
                evaluationResult.recommendedStrategy = 'generic';
            } else if (evaluationResult.priority >= 5) {
                evaluationResult.recommendedStrategy = 'hybrid';
            } else {
                evaluationResult.recommendedStrategy = 'manualIntervention';
            }
        }

        return evaluationResult;
    }

    /**
     * 选择降级处理器
     * @param {string} originalProcessor - 原始处理器
     * @param {string} strategy - 降级策略
     * @param {Object} context - 上下文信息
     * @returns {string} 降级处理器名称
     */
    function selectFallbackProcessor(originalProcessor, strategy, context = {}) {
        const strategies = fallbackProcessingConfig.fallbackStrategies;
        
        switch (strategy) {
            case 'similarProcessor':
                const similarMapping = strategies.similarProcessor.similarityMapping[originalProcessor];
                if (similarMapping && similarMapping.length > 0) {
                    // 选择第一个可用的相似处理器
                    return similarMapping[0];
                }
                return 'generic';
                
            case 'generic':
                return 'generic';
                
            case 'hybrid':
                return 'hybrid';
                
            case 'manualIntervention':
                return 'manual';
                
            default:
                return 'generic';
        }
    }

    /**
     * 创建降级结果标记
     * @param {string} originalProcessor - 原始处理器
     * @param {string} fallbackProcessor - 降级处理器
     * @param {string} reason - 降级原因
     * @param {Array} fallbackChain - 降级链
     * @returns {Object} 降级标记
     */
    function createFallbackMarker(originalProcessor, fallbackProcessor, reason, fallbackChain = []) {
        const markerStructure = fallbackProcessingConfig.fallbackResultMarkers.fallbackMarkerStructure;
        const adjustmentRules = fallbackProcessingConfig.fallbackResultMarkers.confidenceAdjustmentRules;
        
        return {
            ...markerStructure,
            originalProcessor,
            fallbackProcessor,
            fallbackReason: reason,
            fallbackChain: [...fallbackChain, fallbackProcessor],
            confidenceAdjustment: adjustmentRules[`${fallbackChain.length + 1}Fallback`] || adjustmentRules.genericFallback,
            fallbackTimestamp: new Date().toISOString(),
            processingNotes: [`降级原因: ${reason}`, `降级处理器: ${fallbackProcessor}`]
        };
    }

    /**
     * 更新降级处理配置
     * @param {Object} newConfig - 新配置
     */
    function updateFallbackProcessingConfig(newConfig) {
        Object.assign(fallbackProcessingConfig, newConfig);
        console.log('降级处理配置已更新');
    }

    /**
     * 验证降级配置
     * @returns {Object} 验证结果
     */
    function validateFallbackConfig() {
        const errors = [];
        const warnings = [];
        
        // 验证处理器降级链
        for (const [processor, config] of Object.entries(fallbackProcessingConfig.processorFallbackConfig)) {
            if (!config.primaryFallbacks || config.primaryFallbacks.length === 0) {
                warnings.push(`处理器 ${processor} 没有配置降级链`);
            }
        }
        
        // 验证触发器配置
        const triggers = fallbackProcessingConfig.fallbackTriggers;
        if (triggers.lowConfidence.threshold < 0 || triggers.lowConfidence.threshold > 1) {
            errors.push('低置信度阈值必须在0-1之间');
        }
        
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }

    // 暴露到全局命名空间
    window.OTA.gemini.configs.fallbackProcessingConfig = fallbackProcessingConfig;
    window.OTA.gemini.configs.getFallbackProcessingConfig = getFallbackProcessingConfig;
    window.OTA.gemini.configs.getProcessorFallbackChain = getProcessorFallbackChain;
    window.OTA.gemini.configs.evaluateFallbackNeed = evaluateFallbackNeed;
    window.OTA.gemini.configs.selectFallbackProcessor = selectFallbackProcessor;
    window.OTA.gemini.configs.createFallbackMarker = createFallbackMarker;
    window.OTA.gemini.configs.updateFallbackProcessingConfig = updateFallbackProcessingConfig;
    window.OTA.gemini.configs.validateFallbackConfig = validateFallbackConfig;

    // 向后兼容
    window.getFallbackProcessingConfig = getFallbackProcessingConfig;
    window.evaluateFallbackNeed = evaluateFallbackNeed;
    window.selectFallbackProcessor = selectFallbackProcessor;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerConfig('fallbackProcessingConfig', fallbackProcessingConfig, '@FALLBACK_PROCESSING_CONFIG');
        window.OTA.Registry.registerFactory('getFallbackProcessingConfig', getFallbackProcessingConfig, '@FALLBACK_PROCESSING_CONFIG_FACTORY');
    }

    // 配置验证
    const validation = validateFallbackConfig();
    if (validation.isValid) {
        console.log('✅ 降级处理配置已加载并验证通过');
        if (validation.warnings.length > 0) {
            console.warn('⚠️ 配置警告:', validation.warnings);
        }
    } else {
        console.error('❌ 降级处理配置验证失败:', validation.errors);
    }

})();
