<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA系统 - 单元测试运行器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .test-results {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 5px solid #007bff;
        }

        .test-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
        }

        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .skipped { color: #ffc107; }
        .total { color: #007bff; }

        .test-output {
            background: #1e1e1e;
            color: #f8f8f2;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.5;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .test-suite {
            background: white;
            margin-bottom: 15px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .suite-header {
            background: #f8f9fa;
            padding: 15px 20px;
            font-weight: bold;
            border-bottom: 1px solid #dee2e6;
        }

        .test-case {
            padding: 10px 20px;
            border-bottom: 1px solid #f1f3f4;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .test-case:last-child {
            border-bottom: none;
        }

        .test-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-passed {
            background: #d4edda;
            color: #155724;
        }

        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }

        .status-skipped {
            background: #fff3cd;
            color: #856404;
        }

        .error-details {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            margin-top: 5px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 OTA系统单元测试</h1>
            <p>统一测试框架 - 验证系统功能完整性</p>
        </div>

        <div class="controls">
            <button class="btn btn-primary" onclick="runAllTests()">
                ▶️ 运行所有测试
            </button>
            <button class="btn btn-secondary" onclick="runGeminiTests()">
                🤖 运行Gemini测试
            </button>
            <button class="btn btn-success" onclick="clearResults()">
                🗑️ 清空结果
            </button>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在运行测试，请稍候...</p>
        </div>

        <div class="test-results" id="results" style="display: none;">
            <h3>📊 测试结果</h3>
            <div class="test-summary" id="summary"></div>
            <div id="suiteResults"></div>
        </div>

        <div class="test-output" id="output" style="display: none;"></div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="../js/logger.js"></script>
    <script src="../js/utils.js"></script>
    <script src="unit-test-framework.js"></script>

    <script>
        // 测试运行器脚本
        let currentTestRun = null;

        /**
         * 运行所有测试
         */
        async function runAllTests() {
            showLoading();
            clearOutput();

            try {
                // 加载所有测试文件
                await loadTestFiles([
                    'gemini-refactor-validation.test.js',
                    'gemini-performance-comparison.test.js'
                ]);

                // 运行测试
                const framework = window.OTA.Testing.framework;
                const results = await framework.runAllTests();
                
                displayResults(results);
                
            } catch (error) {
                displayError('测试运行失败: ' + error.message);
            } finally {
                hideLoading();
            }
        }

        /**
         * 运行Gemini相关测试
         */
        async function runGeminiTests() {
            showLoading();
            clearOutput();

            try {
                // 只加载Gemini相关测试
                await loadTestFiles([
                    'gemini-refactor-validation.test.js'
                ]);

                const framework = window.OTA.Testing.framework;
                const results = await framework.runAllTests();
                
                displayResults(results);
                
            } catch (error) {
                displayError('Gemini测试运行失败: ' + error.message);
            } finally {
                hideLoading();
            }
        }

        /**
         * 动态加载测试文件
         */
        async function loadTestFiles(files) {
            for (const file of files) {
                await loadScript(file);
            }
        }

        /**
         * 加载脚本文件
         */
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }

        /**
         * 显示测试结果
         */
        function displayResults(results) {
            const resultsDiv = document.getElementById('results');
            const summaryDiv = document.getElementById('summary');
            const suitesDiv = document.getElementById('suiteResults');

            // 显示统计摘要
            summaryDiv.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number total">${results.summary.total}</div>
                    <div class="stat-label">总计</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number passed">${results.summary.passed}</div>
                    <div class="stat-label">通过</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number failed">${results.summary.failed}</div>
                    <div class="stat-label">失败</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number skipped">${results.summary.skipped}</div>
                    <div class="stat-label">跳过</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${results.summary.duration}ms</div>
                    <div class="stat-label">耗时</div>
                </div>
            `;

            // 显示详细结果
            suitesDiv.innerHTML = results.suites.map(suite => `
                <div class="test-suite">
                    <div class="suite-header">
                        📁 ${suite.name} (${suite.stats.passed}/${suite.stats.total})
                    </div>
                    ${suite.tests.map(test => `
                        <div class="test-case">
                            <span>${test.name}</span>
                            <div>
                                <span class="test-status status-${test.status}">${getStatusText(test.status)}</span>
                                <small style="margin-left: 10px; color: #666;">${test.duration}ms</small>
                            </div>
                        </div>
                        ${test.error ? `<div class="error-details">${test.error}</div>` : ''}
                    `).join('')}
                </div>
            `).join('');

            resultsDiv.style.display = 'block';
        }

        /**
         * 获取状态文本
         */
        function getStatusText(status) {
            const statusMap = {
                'passed': '✅ 通过',
                'failed': '❌ 失败',
                'skipped': '⏭️ 跳过'
            };
            return statusMap[status] || status;
        }

        /**
         * 显示错误
         */
        function displayError(message) {
            const outputDiv = document.getElementById('output');
            outputDiv.textContent = message;
            outputDiv.style.display = 'block';
        }

        /**
         * 显示加载状态
         */
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
        }

        /**
         * 隐藏加载状态
         */
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        /**
         * 清空结果
         */
        function clearResults() {
            document.getElementById('results').style.display = 'none';
            document.getElementById('output').style.display = 'none';
        }

        /**
         * 清空输出
         */
        function clearOutput() {
            document.getElementById('output').textContent = '';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 测试运行器已准备就绪');
        });
    </script>
</body>
</html>
