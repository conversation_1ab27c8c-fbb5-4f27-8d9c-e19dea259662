# Gemini AI系统部署指南

## 📋 部署概述

本指南详细说明了重构后Gemini AI系统的生产环境部署流程，包括环境配置、依赖管理和性能调优。

**适用版本**: 2.0.0 (重构版本)  
**部署环境**: 生产环境  
**更新时间**: 2024-01-01

## 🚀 快速部署

### 前置要求
- **Web服务器**: Nginx 1.18+ 或 Apache 2.4+
- **CDN**: 建议使用Netlify、Vercel或Cloudflare
- **API访问**: Google Gemini API密钥
- **浏览器支持**: Chrome 90+, Firefox 88+, Safari 14+

### 部署步骤

#### 1. 文件准备
```bash
# 确保所有必要文件存在
create job/
├── index.html
├── js/
│   ├── gemini-service.js          # 轻量级入口
│   └── gemini/
│       ├── gemini-coordinator.js  # 主协调器
│       ├── core/                  # 核心模块 (7个文件)
│       ├── processors/            # 处理器 (6个文件)
│       └── monitoring/            # 监控模块 (2个文件)
└── tests/                         # 测试文件 (可选)
```

#### 2. 环境配置
```javascript
// 在index.html中配置API密钥
<script>
window.GEMINI_CONFIG = {
    apiKey: 'YOUR_GEMINI_API_KEY',
    model: 'gemini-2.5-flash-lite-preview-06-17',
    environment: 'production'
};
</script>
```

#### 3. 性能优化配置
```javascript
// 生产环境优化配置
const productionConfig = {
    cache: {
        enabled: true,
        maxSize: 5000,
        maxAge: 15 * 60 * 1000,
        enableLRU: true
    },
    processing: {
        timeout: 15000,
        maxConcurrent: 20,
        queueSize: 200
    },
    monitoring: {
        enabled: true,
        enableProfiling: false,  // 生产环境关闭
        logSlowRequests: true
    }
};
```

## 🔧 详细配置

### 1. Web服务器配置

#### Nginx配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # HTML文件不缓存
    location ~* \.html$ {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # 压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
}
```

#### Apache配置
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /path/to/create-job
    
    # 缓存配置
    <LocationMatch "\.(js|css|png|jpg|jpeg|gif|ico|svg)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
        Header append Cache-Control "public, immutable"
    </LocationMatch>
    
    # 压缩
    LoadModule deflate_module modules/mod_deflate.so
    <Location />
        SetOutputFilter DEFLATE
        SetEnvIfNoCase Request_URI \
            \.(?:gif|jpe?g|png)$ no-gzip dont-vary
        SetEnvIfNoCase Request_URI \
            \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
    </Location>
</VirtualHost>
```

### 2. CDN部署 (推荐)

#### Netlify部署
```toml
# netlify.toml
[build]
  publish = "."

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "SAMEORIGIN"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.html"
  [headers.values]
    Cache-Control = "no-cache, no-store, must-revalidate"
```

#### Vercel部署
```json
{
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "SAMEORIGIN"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        }
      ]
    },
    {
      "source": "/(.*\\.(js|css))",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=31536000, immutable"
        }
      ]
    }
  ]
}
```

### 3. 环境变量配置

#### 生产环境配置
```javascript
// 在部署前设置环境特定配置
window.DEPLOYMENT_CONFIG = {
    environment: 'production',
    debug: false,
    apiTimeout: 15000,
    cacheEnabled: true,
    performanceMonitoring: true,
    errorReporting: true
};
```

#### 开发环境配置
```javascript
window.DEPLOYMENT_CONFIG = {
    environment: 'development',
    debug: true,
    apiTimeout: 30000,
    cacheEnabled: false,
    performanceMonitoring: true,
    errorReporting: false
};
```

## 📊 性能调优

### 1. 缓存优化

#### 浏览器缓存
```javascript
// 优化缓存配置
const cacheConfig = {
    // 增加缓存大小
    maxSize: 10000,
    
    // 延长缓存时间
    maxAge: 30 * 60 * 1000, // 30分钟
    
    // 启用压缩
    enableCompression: true,
    
    // 智能清理
    cleanupInterval: 10 * 60 * 1000 // 10分钟
};
```

#### CDN缓存
```javascript
// 设置适当的缓存头
const cacheHeaders = {
    'Cache-Control': 'public, max-age=31536000, immutable', // 静态资源
    'ETag': 'W/"hash-value"', // 版本控制
    'Last-Modified': new Date().toUTCString()
};
```

### 2. 网络优化

#### 资源预加载
```html
<!-- 预加载关键资源 -->
<link rel="preload" href="js/gemini-service.js" as="script">
<link rel="preload" href="js/gemini/gemini-coordinator.js" as="script">
<link rel="prefetch" href="js/gemini/core/service-registry.js" as="script">
```

#### 资源压缩
```bash
# 使用工具压缩JavaScript文件
npx terser js/gemini-service.js -o js/gemini-service.min.js
npx terser js/gemini/gemini-coordinator.js -o js/gemini/gemini-coordinator.min.js
```

### 3. 并发优化

#### 连接池配置
```javascript
// 优化并发处理
const concurrencyConfig = {
    maxConcurrent: 30,        // 增加并发数
    queueSize: 500,           // 增加队列大小
    batchSize: 10,            // 批处理大小
    timeout: 12000            // 减少超时时间
};
```

## 🔍 监控和日志

### 1. 性能监控

#### 关键指标监控
```javascript
// 监控关键性能指标
const monitoringConfig = {
    metrics: [
        'responseTime',
        'throughput',
        'errorRate',
        'cacheHitRate',
        'memoryUsage'
    ],
    alertThresholds: {
        responseTime: 8000,     // 8秒告警
        errorRate: 0.05,        // 5%错误率告警
        memoryUsage: 200 * 1024 * 1024 // 200MB内存告警
    }
};
```

#### 实时监控仪表板
```javascript
// 创建监控仪表板
function createMonitoringDashboard() {
    const metrics = coordinator.getMetrics();
    
    console.log('=== Gemini系统监控 ===');
    console.log('响应时间:', metrics.processingStats.averageProcessingTime + 'ms');
    console.log('缓存命中率:', (metrics.cacheStats.hitRate * 100).toFixed(2) + '%');
    console.log('并发请求:', metrics.concurrencyStats.current);
    console.log('错误率:', (metrics.processingStats.failedRequests / metrics.processingStats.totalRequests * 100).toFixed(2) + '%');
}

// 每30秒更新一次监控数据
setInterval(createMonitoringDashboard, 30000);
```

### 2. 错误日志

#### 错误收集配置
```javascript
// 配置错误收集
window.addEventListener('error', (event) => {
    const errorData = {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
    };
    
    // 发送到错误收集服务
    sendErrorReport(errorData);
});
```

## 🚨 故障排除

### 常见部署问题

#### 1. API密钥问题
```javascript
// 检查API密钥配置
if (!window.GEMINI_CONFIG?.apiKey) {
    console.error('Gemini API密钥未配置');
    // 显示配置提示
}
```

#### 2. 模块加载失败
```javascript
// 检查模块加载状态
setTimeout(() => {
    if (!window.OTA?.geminiService) {
        console.error('Gemini服务加载失败');
        // 尝试重新加载
    }
}, 5000);
```

#### 3. 性能问题诊断
```javascript
// 性能诊断工具
function diagnosePerfomance() {
    const metrics = coordinator.getMetrics();
    
    if (metrics.processingStats.averageProcessingTime > 10000) {
        console.warn('响应时间过长，检查网络连接');
    }
    
    if (metrics.cacheStats.hitRate < 0.5) {
        console.warn('缓存命中率过低，检查缓存配置');
    }
}
```

## 📋 部署检查清单

### 部署前检查
- [ ] API密钥已正确配置
- [ ] 所有必要文件已上传
- [ ] 缓存配置已优化
- [ ] 安全头已设置
- [ ] 压缩已启用

### 部署后验证
- [ ] 系统正常初始化
- [ ] 订单解析功能正常
- [ ] 性能指标在预期范围内
- [ ] 错误日志正常收集
- [ ] 监控仪表板正常显示

### 持续监控
- [ ] 定期检查性能指标
- [ ] 监控错误率变化
- [ ] 检查缓存命中率
- [ ] 验证系统稳定性

---

**部署支持**: OTA系统开发团队  
**文档版本**: 2.0.0  
**最后更新**: 2024-01-01
