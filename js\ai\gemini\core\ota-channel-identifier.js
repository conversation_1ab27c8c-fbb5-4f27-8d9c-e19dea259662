/**
 * @OTA_SERVICE OTA渠道智能识别器
 * 🏷️ 标签: @OTA_CHANNEL_IDENTIFIER
 * 📝 说明: 基于参考号识别引擎和内容分析的智能OTA渠道识别系统
 * 🎯 功能: 多层识别、置信度评估、降级处理、统计分析
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.core = window.OTA.gemini.core || {};

(function() {
    'use strict';

    /**
     * OTA渠道智能识别器类
     * 结合参考号识别和内容分析，提供多层次的渠道识别能力
     */
    class OTAChannelIdentifier {
        constructor() {
            this.logger = window.getLogger?.() || console;
            
            // 获取依赖服务
            this.referenceEngine = null;
            this.languageManager = null;
            
            // 延迟初始化依赖
            this.initializeDependencies();
            
            // 内容分析规则
            this.contentAnalysisRules = this.loadContentAnalysisRules();
            
            // 识别统计
            this.stats = {
                totalIdentifications: 0,
                successfulIdentifications: 0,
                channelDistribution: new Map(),
                methodDistribution: new Map(),
                confidenceDistribution: {
                    high: 0,    // > 0.8
                    medium: 0,  // 0.6-0.8
                    low: 0      // < 0.6
                }
            };
        }

        /**
         * 延迟初始化依赖服务
         */
        initializeDependencies() {
            // 延迟获取依赖，确保加载顺序
            setTimeout(() => {
                this.referenceEngine = window.OTA?.gemini?.core?.getOTAReferenceEngine?.();
                this.languageManager = window.OTA?.getLanguageManager?.();
                
                if (!this.referenceEngine) {
                    this.logger.logError('OTA参考号识别引擎未找到');
                }
                
                if (!this.languageManager) {
                    this.logger.log('语言管理器未找到，将使用默认语言处理', 'warning');
                }
            }, 100);
        }

        /**
         * 加载内容分析规则
         * @returns {Object} 内容分析规则配置
         */
        loadContentAnalysisRules() {
            return {
                // 品牌名称识别规则
                brandPatterns: {
                    'Chong Dealer': [
                        /崇德勒/gi,
                        /chong\s*dealer/gi,
                        /崇/g,
                        /团号/gi,
                        /确认号/gi
                    ],
                    'Klook': [
                        /klook/gi,
                        /客路/gi,
                        /kl\d+/gi,
                        /klook.*确认/gi
                    ],
                    'Ctrip': [
                        /携程/gi,
                        /ctrip/gi,
                        /trip\.com/gi,
                        /订单号/gi,
                        /ct\d+/gi
                    ],
                    'KKday': [
                        /kkday/gi,
                        /kk\d+/gi,
                        /kkday.*订单/gi
                    ],
                    'Agoda': [
                        /agoda/gi,
                        /ag\d+/gi,
                        /agoda.*booking/gi
                    ],
                    'Booking.com': [
                        /booking\.com/gi,
                        /booking/gi,
                        /bk\d+/gi,
                        /预订确认/gi
                    ]
                },

                // 格式特征识别规则
                formatPatterns: {
                    'Chong Dealer': {
                        // 中文内容比例高
                        chineseRatio: 0.3,
                        // 包含特定服务描述
                        serviceKeywords: ['接机', '送机', '包车', '举牌'],
                        // 时间格式特征
                        timeFormat: /\d{1,2}:\d{2}/,
                        // 联系方式格式
                        contactFormat: /[\+]?6\d{9,11}/
                    },
                    'Klook': {
                        // 英文内容为主
                        englishRatio: 0.6,
                        // 产品代码格式
                        productCode: /[A-Z]{2,4}\d{4,8}/,
                        // 确认邮件格式
                        emailFormat: /klook.*confirmation/gi
                    },
                    'Ctrip': {
                        // 中英文混合
                        mixedLanguage: true,
                        // 订单号格式
                        orderFormat: /\d{10,15}/,
                        // 携程特有术语
                        ctripTerms: ['产品', '行程', '出行']
                    }
                },

                // 置信度权重配置
                confidenceWeights: {
                    referenceMatch: 0.5,    // 参考号匹配权重
                    brandMatch: 0.3,        // 品牌名称匹配权重
                    formatMatch: 0.2        // 格式特征匹配权重
                }
            };
        }

        /**
         * 智能识别OTA渠道 - 主要入口方法
         * @param {string} orderText - 订单文本
         * @param {Object} options - 识别选项
         * @returns {Promise<Object>} 识别结果
         */
        async identifyChannel(orderText, options = {}) {
            this.stats.totalIdentifications++;
            
            try {
                // 第一层：参考号识别
                const referenceResult = await this.identifyByReference(orderText, options);
                
                if (referenceResult.confidence > 0.8) {
                    return this.finalizeResult(referenceResult, 'reference_primary');
                }

                // 第二层：内容分析
                const contentResult = await this.identifyByContent(orderText, options);
                
                // 综合评估
                const combinedResult = this.combineResults(referenceResult, contentResult);
                
                if (combinedResult.confidence > 0.6) {
                    return this.finalizeResult(combinedResult, 'combined_analysis');
                }

                // 第三层：降级处理
                const fallbackResult = this.createFallbackResult(orderText);
                return this.finalizeResult(fallbackResult, 'fallback');

            } catch (error) {
                this.logger.logError('OTA渠道识别失败', error);
                return this.createErrorResult(error);
            }
        }

        /**
         * 基于参考号的识别
         * @param {string} orderText - 订单文本
         * @param {Object} options - 选项
         * @returns {Promise<Object>} 识别结果
         */
        async identifyByReference(orderText, options = {}) {
            if (!this.referenceEngine) {
                return { confidence: 0, channel: null, method: 'reference_unavailable' };
            }

            try {
                const referenceResult = this.referenceEngine.identifyReference(orderText, options.preferredPlatform);
                
                if (referenceResult.found && referenceResult.platform !== 'generic') {
                    return {
                        confidence: referenceResult.confidence,
                        channel: referenceResult.platform,
                        method: 'reference_match',
                        reference: referenceResult.reference,
                        details: referenceResult
                    };
                }

                return { confidence: 0, channel: null, method: 'reference_no_match' };

            } catch (error) {
                this.logger.logError('参考号识别失败', error);
                return { confidence: 0, channel: null, method: 'reference_error', error };
            }
        }

        /**
         * 基于内容的识别
         * @param {string} orderText - 订单文本
         * @param {Object} options - 选项
         * @returns {Promise<Object>} 识别结果
         */
        async identifyByContent(orderText, options = {}) {
            const results = [];

            // 品牌名称分析
            const brandResult = this.analyzeBrandNames(orderText);
            if (brandResult.confidence > 0) {
                results.push(brandResult);
            }

            // 格式特征分析
            const formatResult = this.analyzeFormatFeatures(orderText);
            if (formatResult.confidence > 0) {
                results.push(formatResult);
            }

            // 语言特征分析（如果语言管理器可用）
            if (this.languageManager) {
                const languageResult = await this.analyzeLanguageFeatures(orderText);
                if (languageResult.confidence > 0) {
                    results.push(languageResult);
                }
            }

            // 选择最佳结果
            if (results.length === 0) {
                return { confidence: 0, channel: null, method: 'content_no_match' };
            }

            const bestResult = results.reduce((best, current) => 
                current.confidence > best.confidence ? current : best
            );

            return {
                ...bestResult,
                method: 'content_analysis',
                allResults: results
            };
        }

        /**
         * 品牌名称分析
         * @param {string} text - 文本
         * @returns {Object} 分析结果
         */
        analyzeBrandNames(text) {
            const brandPatterns = this.contentAnalysisRules.brandPatterns;
            let bestMatch = { channel: null, confidence: 0, matches: 0 };

            for (const [channel, patterns] of Object.entries(brandPatterns)) {
                let matches = 0;
                let totalScore = 0;

                for (const pattern of patterns) {
                    const matches_found = text.match(pattern);
                    if (matches_found) {
                        matches += matches_found.length;
                        totalScore += matches_found.length * this.getPatternWeight(pattern);
                    }
                }

                if (matches > 0) {
                    const confidence = Math.min(totalScore / 10, 0.9); // 最高0.9
                    if (confidence > bestMatch.confidence) {
                        bestMatch = { channel, confidence, matches, totalScore };
                    }
                }
            }

            return bestMatch;
        }

        /**
         * 格式特征分析
         * @param {string} text - 文本
         * @returns {Object} 分析结果
         */
        analyzeFormatFeatures(text) {
            const formatPatterns = this.contentAnalysisRules.formatPatterns;
            let bestMatch = { channel: null, confidence: 0, features: [] };

            for (const [channel, features] of Object.entries(formatPatterns)) {
                const featureScore = this.calculateFormatScore(text, features);
                
                if (featureScore.confidence > bestMatch.confidence) {
                    bestMatch = {
                        channel,
                        confidence: featureScore.confidence,
                        features: featureScore.matchedFeatures
                    };
                }
            }

            return bestMatch;
        }

        /**
         * 语言特征分析
         * @param {string} text - 文本
         * @returns {Promise<Object>} 分析结果
         */
        async analyzeLanguageFeatures(text) {
            try {
                const languageInfo = await this.languageManager.detectLanguage(text);
                
                // 基于语言特征推测OTA渠道
                const languageChannelMap = {
                    'zh-CN': ['Ctrip', 'Chong Dealer'],  // 简体中文偏向携程和崇德勒
                    'zh-TW': ['KKday'],                   // 繁体中文偏向KKday
                    'en': ['Klook', 'Agoda', 'Booking.com'], // 英文偏向国际平台
                    'mixed': ['Chong Dealer', 'Ctrip']    // 中英混合偏向本地化平台
                };

                const possibleChannels = languageChannelMap[languageInfo.primary] || [];
                
                if (possibleChannels.length > 0) {
                    return {
                        channel: possibleChannels[0], // 选择第一个作为推荐
                        confidence: 0.3, // 语言特征置信度较低
                        languageInfo,
                        possibleChannels
                    };
                }

                return { confidence: 0, channel: null };

            } catch (error) {
                this.logger.logError('语言特征分析失败', error);
                return { confidence: 0, channel: null, error };
            }
        }

        /**
         * 综合评估结果
         * @param {Object} referenceResult - 参考号识别结果
         * @param {Object} contentResult - 内容分析结果
         * @returns {Object} 综合结果
         */
        combineResults(referenceResult, contentResult) {
            const weights = this.contentAnalysisRules.confidenceWeights;
            
            // 如果两个结果指向同一渠道，提高置信度
            if (referenceResult.channel === contentResult.channel && referenceResult.channel) {
                const combinedConfidence = Math.min(
                    referenceResult.confidence * weights.referenceMatch + 
                    contentResult.confidence * (weights.brandMatch + weights.formatMatch) + 0.2, // 一致性加成
                    1.0
                );

                return {
                    channel: referenceResult.channel,
                    confidence: combinedConfidence,
                    method: 'combined_consistent',
                    referenceResult,
                    contentResult
                };
            }

            // 如果结果不一致，选择置信度更高的
            if (referenceResult.confidence > contentResult.confidence) {
                return {
                    ...referenceResult,
                    method: 'combined_reference_priority',
                    contentResult
                };
            } else {
                return {
                    ...contentResult,
                    method: 'combined_content_priority',
                    referenceResult
                };
            }
        }

        /**
         * 创建降级结果
         * @param {string} orderText - 订单文本
         * @returns {Object} 降级结果
         */
        createFallbackResult(orderText) {
            return {
                channel: 'generic',
                confidence: 0.5, // 降级处理的基础置信度
                method: 'fallback',
                reason: '无法识别特定OTA渠道，使用通用处理器',
                textLength: orderText.length,
                hasChineseContent: /[\u4e00-\u9fff]/.test(orderText),
                hasEnglishContent: /[a-zA-Z]/.test(orderText)
            };
        }

        /**
         * 创建错误结果
         * @param {Error} error - 错误对象
         * @returns {Object} 错误结果
         */
        createErrorResult(error) {
            return {
                channel: 'generic',
                confidence: 0,
                method: 'error_fallback',
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }

        /**
         * 完成识别结果
         * @param {Object} result - 识别结果
         * @param {string} finalMethod - 最终方法
         * @returns {Object} 最终结果
         */
        finalizeResult(result, finalMethod) {
            const finalResult = {
                ...result,
                finalMethod,
                timestamp: new Date().toISOString(),
                processingId: this.generateProcessingId()
            };

            // 更新统计信息
            this.updateStats(finalResult);

            return finalResult;
        }

        /**
         * 计算格式特征分数
         * @param {string} text - 文本
         * @param {Object} features - 特征配置
         * @returns {Object} 分数结果
         */
        calculateFormatScore(text, features) {
            let score = 0;
            const matchedFeatures = [];

            // 中文比例检查
            if (features.chineseRatio) {
                const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
                const chineseRatio = chineseChars / text.length;
                if (chineseRatio >= features.chineseRatio) {
                    score += 0.3;
                    matchedFeatures.push('chinese_ratio');
                }
            }

            // 英文比例检查
            if (features.englishRatio) {
                const englishChars = (text.match(/[a-zA-Z]/g) || []).length;
                const englishRatio = englishChars / text.length;
                if (englishRatio >= features.englishRatio) {
                    score += 0.3;
                    matchedFeatures.push('english_ratio');
                }
            }

            // 服务关键词检查
            if (features.serviceKeywords) {
                const foundKeywords = features.serviceKeywords.filter(keyword => 
                    text.includes(keyword)
                );
                if (foundKeywords.length > 0) {
                    score += foundKeywords.length * 0.1;
                    matchedFeatures.push(`service_keywords:${foundKeywords.join(',')}`);
                }
            }

            return {
                confidence: Math.min(score, 0.8),
                matchedFeatures
            };
        }

        /**
         * 获取模式权重
         * @param {RegExp} pattern - 正则模式
         * @returns {number} 权重值
         */
        getPatternWeight(pattern) {
            // 根据模式的复杂度和特异性分配权重
            const patternStr = pattern.toString();
            
            if (patternStr.includes('\\d')) return 3; // 包含数字的模式权重更高
            if (patternStr.length > 20) return 2;     // 复杂模式权重中等
            return 1;                                 // 简单模式基础权重
        }

        /**
         * 更新统计信息
         * @param {Object} result - 识别结果
         */
        updateStats(result) {
            if (result.confidence > 0.6) {
                this.stats.successfulIdentifications++;
            }

            // 渠道分布统计
            const channel = result.channel || 'unknown';
            const count = this.stats.channelDistribution.get(channel) || 0;
            this.stats.channelDistribution.set(channel, count + 1);

            // 方法分布统计
            const method = result.finalMethod || 'unknown';
            const methodCount = this.stats.methodDistribution.get(method) || 0;
            this.stats.methodDistribution.set(method, methodCount + 1);

            // 置信度分布统计
            if (result.confidence > 0.8) {
                this.stats.confidenceDistribution.high++;
            } else if (result.confidence > 0.6) {
                this.stats.confidenceDistribution.medium++;
            } else {
                this.stats.confidenceDistribution.low++;
            }
        }

        /**
         * 生成处理ID
         * @returns {string} 处理ID
         */
        generateProcessingId() {
            return `ota_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }

        /**
         * 获取识别统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                ...this.stats,
                successRate: this.stats.totalIdentifications > 0 ? 
                    ((this.stats.successfulIdentifications / this.stats.totalIdentifications) * 100).toFixed(2) + '%' : '0%',
                channelDistribution: Object.fromEntries(this.stats.channelDistribution),
                methodDistribution: Object.fromEntries(this.stats.methodDistribution)
            };
        }

        /**
         * 重置统计信息
         */
        resetStats() {
            this.stats = {
                totalIdentifications: 0,
                successfulIdentifications: 0,
                channelDistribution: new Map(),
                methodDistribution: new Map(),
                confidenceDistribution: {
                    high: 0,
                    medium: 0,
                    low: 0
                }
            };
        }
    }

    // 创建全局实例
    function getOTAChannelIdentifier() {
        if (!window.OTA.gemini.core.otaChannelIdentifier) {
            window.OTA.gemini.core.otaChannelIdentifier = new OTAChannelIdentifier();
        }
        return window.OTA.gemini.core.otaChannelIdentifier;
    }

    // 暴露到全局命名空间
    window.OTA.gemini.core.OTAChannelIdentifier = OTAChannelIdentifier;
    window.OTA.gemini.core.getOTAChannelIdentifier = getOTAChannelIdentifier;

    // 向后兼容
    window.getOTAChannelIdentifier = getOTAChannelIdentifier;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('otaChannelIdentifier', getOTAChannelIdentifier(), '@OTA_CHANNEL_IDENTIFIER');
        window.OTA.Registry.registerFactory('getOTAChannelIdentifier', getOTAChannelIdentifier, '@OTA_CHANNEL_IDENTIFIER_FACTORY');
    }

    console.log('✅ OTA渠道智能识别器已加载');

})();
