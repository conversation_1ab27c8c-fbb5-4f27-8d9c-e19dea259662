/**
 * 模块懒加载配置系统
 * 定义哪些模块可以懒加载，提升启动性能
 */

(function() {
    'use strict';

    /**
     * 模块加载配置类
     */
    class ModuleLoaderConfig {
        constructor() {
            this.moduleGroups = {
                // 关键启动模块 - 立即加载
                critical: [
                    'js/bootstrap/app-state.js',
                    'js/bootstrap/application-bootstrap.js',
                    'js/core/dependency-container.js',
                    'js/core/service-locator.js',
                    'js/services/logger.js'
                ],

                // 核心业务模块 - 启动后立即加载
                core: [
                    'js/services/api-service.js',
                    'js/managers/ui-manager.js',
                    'js/managers/event-manager.js',
                    'js/managers/form-manager.js'
                ],

                // AI服务模块 - 按需加载
                ai: [
                    'js/ai/gemini-service.js',
                    'js/ai/kimi-service.js'
                ],

                // 多订单功能模块 - 按需加载
                multiOrder: [
                    'js/managers/multi-order-manager.js',
                    'js/components/multi-order/multi-order-detector.js',
                    'js/components/multi-order/multi-order-processor.js',
                    'js/components/multi-order/multi-order-renderer.js',
                    'js/components/multi-order/multi-order-transformer.js',
                    'js/components/multi-order/multi-order-ui-manager.js',
                    'js/components/multi-order/multi-order-state-manager.js',
                    'js/components/multi-order/multi-order-event-manager.js',
                    'js/components/multi-order/multi-order-validation-manager.js',
                    'js/components/multi-order/multi-order-batch-manager.js',
                    'js/components/multi-order/multi-order-cleanup-manager.js',
                    'js/components/multi-order/multi-order-quick-edit-manager.js',
                    'js/components/multi-order/multi-order-chrome-mcp.js',
                    'js/components/multi-order/multi-order-utils.js',
                    'js/components/multi-order/field-mapping-config.js',
                    'js/components/multi-order/field-mapping-validator.js',
                    'js/components/multi-order/field-mapping-tests.js'
                ],

                // Gemini AI模块 - 按需加载
                gemini: [
                    'js/ai/gemini/gemini-coordinator.js',
                    'js/ai/gemini/core/flight-number-processor.js',
                    'js/ai/gemini/core/data-normalizer.js',
                    'js/ai/gemini/core/ota-reference-engine.js',
                    'js/ai/gemini/core/address-translator.js',
                    'js/ai/gemini/core/prompt-template-engine.js',
                    'js/ai/gemini/core/error-recovery-engine.js',
                    'js/ai/gemini/core/image-analysis-engine.js',
                    'js/ai/gemini/configs/field-mapping.js',
                    'js/ai/gemini/configs/preset-values.js',
                    'js/ai/gemini/configs/fallback-config.js',
                    'js/ai/gemini/configs/performance-optimization.js'
                ],

                // 工具和辅助模块 - 延迟加载
                utilities: [
                    'js/utils/utils.js',
                    'js/utils/hotel-data-inline.js',
                    'js/utils/hotel-name-database.js',
                    'js/utils/ota-channel-mapping.js',
                    'js/components/image-upload-manager.js',
                    'js/components/grid-resizer.js',
                    'js/managers/currency-converter.js',
                    'js/managers/order-history-manager.js',
                    'js/managers/paging-service-manager.js'
                ],

                // 管理器模块 - 按功能需要加载
                managers: [
                    'js/managers/price-manager.js',
                    'js/managers/state-manager.js',
                    'js/managers/realtime-analysis-manager.js'
                ],

                // 国际化和语言模块 - 延迟加载
                i18n: [
                    'js/services/i18n.js',
                    'js/services/language-manager.js'
                ],

                // 测试和调试模块 - 开发环境按需加载
                testing: [
                    'tests/gemini-refactor-validation.test.js',
                    'tests/gemini-performance-comparison.test.js',
                    'js/ai/gemini/tests/system-integration-test.js',
                    'js/ai/gemini/tests/comprehensive-test-suite.js'
                ]
            };

            this.loadingStrategies = {
                // 立即加载 - 关键路径
                immediate: {
                    priority: 'critical',
                    loadTiming: 'sync',
                    preload: false,
                    cache: true,
                    timeout: 5000
                },

                // 启动后加载 - 核心功能
                startup: {
                    priority: 'high',
                    loadTiming: 'post-startup',
                    preload: false,
                    cache: true,
                    timeout: 10000
                },

                // 按需加载 - 功能模块
                onDemand: {
                    priority: 'normal',
                    loadTiming: 'lazy',
                    preload: false,
                    cache: true,
                    timeout: 15000
                },

                // 预加载 - 可能用到的模块
                preload: {
                    priority: 'low',
                    loadTiming: 'idle',
                    preload: true,
                    cache: true,
                    timeout: 20000
                },

                // 延迟加载 - 辅助功能
                deferred: {
                    priority: 'low',
                    loadTiming: 'manual',
                    preload: false,
                    cache: true,
                    timeout: 30000
                }
            };

            this.triggers = {
                // AI功能触发器
                'ai-analysis': {
                    modules: ['ai', 'gemini'],
                    event: 'ai-analysis-requested'
                },

                // 多订单功能触发器
                'multi-order-mode': {
                    modules: ['multiOrder'],
                    event: 'multi-order-detected'
                },

                // 国际化触发器
                'language-change': {
                    modules: ['i18n'],
                    event: 'language-changed'
                },

                // 工具功能触发器
                'image-upload': {
                    modules: ['utilities'],
                    event: 'image-upload-requested'
                },

                // 历史记录功能触发器
                'order-history': {
                    modules: ['utilities'],
                    event: 'history-panel-opened'
                },

                // 管理功能触发器
                'admin-features': {
                    modules: ['managers'],
                    event: 'admin-panel-opened'
                }
            };

            this.loadingPlan = this.generateLoadingPlan();
        }

        /**
         * 生成加载计划
         */
        generateLoadingPlan() {
            const plan = {
                immediate: [], // 同步加载
                startup: [],   // 启动后立即加载
                preload: [],   // 空闲时预加载
                onDemand: []   // 完全按需加载
            };

            // 关键模块 - 立即加载
            plan.immediate = this.moduleGroups.critical.map(path => ({
                path,
                strategy: 'immediate',
                group: 'critical'
            }));

            // 核心模块 - 启动后加载
            plan.startup = this.moduleGroups.core.map(path => ({
                path,
                strategy: 'startup',
                group: 'core'
            }));

            // 可预加载的模块
            plan.preload = [
                ...this.moduleGroups.managers,
                ...this.moduleGroups.i18n
            ].map(path => ({
                path,
                strategy: 'preload',
                group: 'preload'
            }));

            // 按需加载的模块
            plan.onDemand = [
                ...this.moduleGroups.ai,
                ...this.moduleGroups.multiOrder,
                ...this.moduleGroups.gemini,
                ...this.moduleGroups.utilities,
                ...this.moduleGroups.testing
            ].map(path => ({
                path,
                strategy: 'onDemand',
                group: 'onDemand'
            }));

            return plan;
        }

        /**
         * 获取模块加载配置
         * @param {string} moduleId 模块ID
         * @returns {Object} 加载配置
         */
        getModuleConfig(moduleId) {
            // 查找模块所属组
            let group = null;
            let strategy = 'onDemand';

            for (const [groupName, modules] of Object.entries(this.moduleGroups)) {
                if (modules.includes(moduleId)) {
                    group = groupName;
                    break;
                }
            }

            // 根据组确定加载策略
            switch (group) {
                case 'critical':
                    strategy = 'immediate';
                    break;
                case 'core':
                    strategy = 'startup';
                    break;
                case 'managers':
                case 'i18n':
                    strategy = 'preload';
                    break;
                default:
                    strategy = 'onDemand';
            }

            const strategyConfig = this.loadingStrategies[strategy];

            return {
                id: moduleId,
                path: moduleId,
                group,
                strategy,
                ...strategyConfig,
                dependencies: this.getModuleDependencies(moduleId)
            };
        }

        /**
         * 获取模块依赖
         * @param {string} moduleId 模块ID
         * @returns {Array} 依赖列表
         */
        getModuleDependencies(moduleId) {
            const dependencies = [];

            // 定义依赖关系
            const dependencyMap = {
                // AI服务依赖
                'js/ai/gemini-service.js': ['js/services/logger.js', 'js/services/api-service.js'],
                'js/ai/kimi-service.js': ['js/services/logger.js', 'js/services/api-service.js'],

                // 管理器依赖
                'js/managers/ui-manager.js': ['js/services/logger.js', 'js/bootstrap/app-state.js'],
                'js/managers/multi-order-manager.js': ['js/managers/ui-manager.js', 'js/services/logger.js'],
                'js/managers/event-manager.js': ['js/services/logger.js'],
                'js/managers/form-manager.js': ['js/services/logger.js', 'js/bootstrap/app-state.js'],

                // 组件依赖
                'js/components/image-upload-manager.js': ['js/services/logger.js'],
                'js/components/grid-resizer.js': ['js/services/logger.js'],

                // 多订单组件依赖
                'js/components/multi-order/multi-order-detector.js': ['js/services/logger.js'],
                'js/components/multi-order/multi-order-processor.js': ['js/services/logger.js'],
                'js/components/multi-order/multi-order-ui-manager.js': ['js/managers/ui-manager.js'],

                // Gemini模块依赖
                'js/ai/gemini/gemini-coordinator.js': ['js/ai/gemini-service.js'],
                'js/ai/gemini/core/data-normalizer.js': ['js/services/logger.js'],
                'js/ai/gemini/core/prompt-template-engine.js': ['js/services/logger.js']
            };

            return dependencyMap[moduleId] || [];
        }

        /**
         * 获取触发器配置
         * @param {string} triggerName 触发器名称
         * @returns {Object} 触发器配置
         */
        getTriggerConfig(triggerName) {
            return this.triggers[triggerName];
        }

        /**
         * 获取完整的加载计划
         * @returns {Object} 加载计划
         */
        getLoadingPlan() {
            return this.loadingPlan;
        }

        /**
         * 获取分组模块列表
         * @param {string} groupName 分组名称
         * @returns {Array} 模块列表
         */
        getModuleGroup(groupName) {
            return this.moduleGroups[groupName] || [];
        }

        /**
         * 检查模块是否应该预加载
         * @param {string} moduleId 模块ID
         * @returns {boolean} 是否预加载
         */
        shouldPreload(moduleId) {
            const config = this.getModuleConfig(moduleId);
            return config.preload === true;
        }

        /**
         * 检查模块是否为关键模块
         * @param {string} moduleId 模块ID
         * @returns {boolean} 是否为关键模块
         */
        isCritical(moduleId) {
            return this.moduleGroups.critical.includes(moduleId);
        }

        /**
         * 获取性能优化建议
         * @returns {Object} 优化建议
         */
        getOptimizationSuggestions() {
            return {
                // 启动优化
                startup: {
                    criticalModulesCount: this.moduleGroups.critical.length,
                    suggestion: '关键模块应保持最少，建议不超过5个'
                },

                // 懒加载优化
                lazyLoading: {
                    lazyModulesCount: this.loadingPlan.onDemand.length,
                    suggestion: '大部分功能模块已配置为按需加载，有助于提升启动性能'
                },

                // 预加载优化
                preloading: {
                    preloadModulesCount: this.loadingPlan.preload.length,
                    suggestion: '预加载模块适中，可在用户空闲时提前准备常用功能'
                },

                // 依赖优化
                dependencies: {
                    suggestion: '检查模块依赖关系，避免循环依赖和过度依赖'
                }
            };
        }
    }

    // 创建全局实例
    const moduleConfig = new ModuleLoaderConfig();

    // 导出到全局作用域
    window.OTA = window.OTA || {};
    window.OTA.moduleConfig = moduleConfig;
    window.OTA.getModuleConfig = () => moduleConfig;

    // 向后兼容
    window.getModuleConfig = () => moduleConfig;

    console.log('✅ 模块加载配置系统已初始化', {
        criticalModules: moduleConfig.moduleGroups.critical.length,
        totalModules: Object.values(moduleConfig.moduleGroups).flat().length,
        loadingStrategies: Object.keys(moduleConfig.loadingStrategies).length
    });

})();