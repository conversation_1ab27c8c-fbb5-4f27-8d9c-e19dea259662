/**
 * 举牌服务管理器
 * 负责举牌服务的检测、订单生成和管理
 * <AUTHOR>
 * @version 1.0.0
 */

// 获取依赖模块 - 使用统一的服务定位器

class PagingServiceManager {
    constructor() {
        // 举牌服务关键词
        this.pagingKeywords = [
            '举牌', '举牌接机', '举牌服务',
            'meet and greet', 'meet & greet', 'meet-and-greet',
            '接机牌', '迎接服务', '接机服务',
            'paging', 'paging service',
            '举牌迎接', '机场迎接',
            '接机员', '迎宾服务'
        ];
        
        // 举牌订单配置
        this.pagingOrderConfig = {
            driving_region_id: 9, // Paging (PG)
            languages_id_array: [5], // Paging (PG)
            sub_category_id: 2, // 接机服务
            car_type_id: 34, // Ticket (N/A passenger, N/A luggage)
            passenger_count: 0,
            luggage_count: 0,
            price: 0,
            currency: 'MYR'
        };
        
        this.init();
    }

    /**
     * 初始化举牌服务管理器
     */
    init() {
        const logger = getLogger();
        if (logger) {
            logger.log('举牌服务管理器已初始化', 'info');
        }
    }

    /**
     * 检测文本中是否包含举牌服务
     * @param {string} text - 输入文本
     * @returns {boolean} 是否包含举牌服务
     */
    detectPagingService(text) {
        if (!text || typeof text !== 'string') {
            return false;
        }

        const lowerText = text.toLowerCase();
        
        const hasPagingKeywords = this.pagingKeywords.some(keyword => 
            lowerText.includes(keyword.toLowerCase())
        );

        if (hasPagingKeywords) {
            const logger = getLogger();
            if (logger) {
                logger.log('检测到举牌服务关键词', 'info');
            }
        }

        return hasPagingKeywords;
    }

    /**
     * 为订单数据添加举牌服务标识
     * @param {Array|Object} ordersData - 订单数据
     * @param {string} originalText - 原始文本
     * @returns {Array|Object} 处理后的订单数据
     */
    processOrdersForPagingService(ordersData, originalText) {
        if (!ordersData) return ordersData;

        const needsPaging = this.detectPagingService(originalText);
        
        if (Array.isArray(ordersData)) {
            return ordersData.map(orderData => ({
                ...orderData,
                needs_paging_service: needsPaging
            }));
        } else {
            return {
                ...ordersData,
                needs_paging_service: needsPaging
            };
        }
    }

    /**
     * 生成举牌订单
     * @param {Object} baseOrderData - 基础订单数据
     * @returns {Object} 举牌订单数据
     */
    generatePagingOrder(baseOrderData) {
        if (!baseOrderData) {
            throw new Error('基础订单数据不能为空');
        }

        // 生成举牌订单参考号
        const baseReference = baseOrderData.ota_reference_number || 'GMH-001';
        const pagingReference = this.generatePagingReference(baseReference);

        const pagingOrder = {
            // 基础信息从原订单复制
            customer_name: baseOrderData.customer_name,
            customer_contact: baseOrderData.customer_contact,
            customer_email: baseOrderData.customer_email,
            flight_info: baseOrderData.flight_info,
            pickup_date: baseOrderData.pickup_date,
            pickup_time: baseOrderData.pickup_time,
            pickup: baseOrderData.pickup,
            
            // 举牌服务特定配置
            ...this.pagingOrderConfig,
            
            // 举牌订单特定信息
            ota_reference_number: pagingReference,
            dropoff: '举牌服务点', // 举牌服务不需要目的地
            extra_requirement: `举牌服务 - ${baseOrderData.customer_name || '客户'}`,
            
            // 标识字段
            is_paging_order: true,
            related_main_order: baseOrderData.ota_reference_number,
            
            // 时间信息
            arrival_time: baseOrderData.arrival_time,
            departure_time: baseOrderData.departure_time,
            flight_type: baseOrderData.flight_type
        };

        const logger = getLogger();
        if (logger) {
            logger.log(`已生成举牌订单: ${pagingReference}`, 'info');
        }
        
        return pagingOrder;
    }

    /**
     * 生成举牌订单参考号
     * @param {string} baseReference - 基础参考号
     * @returns {string} 举牌订单参考号
     */
    generatePagingReference(baseReference) {
        if (!baseReference) {
            return 'GMH-PAGING-001';
        }

        // 如果基础参考号包含数字，在后面添加-PG
        const numberMatch = baseReference.match(/(\d+)$/);
        if (numberMatch) {
            return baseReference + '-PG';
        } else {
            return baseReference + '-PAGING';
        }
    }

    /**
     * 处理多订单的举牌服务
     * @param {Array} ordersData - 订单数据数组
     * @param {string} originalText - 原始文本
     * @returns {Array} 处理后的订单数组（包含举牌订单）
     */
    processMultiOrderPagingService(ordersData, originalText) {
        if (!Array.isArray(ordersData) || ordersData.length === 0) {
            return ordersData;
        }

        const needsPaging = this.detectPagingService(originalText);
        if (!needsPaging) {
            return ordersData;
        }

        const processedOrders = [];
        
        ordersData.forEach(orderData => {
            // 添加原订单（标记需要举牌服务）
            processedOrders.push({
                ...orderData,
                needs_paging_service: true
            });

            // 如果是接机订单，生成对应的举牌订单
            if (orderData.sub_category_id === 2) { // 接机服务
                try {
                    const pagingOrder = this.generatePagingOrder(orderData);
                    processedOrders.push(pagingOrder);
                } catch (error) {
                    getLogger().log(`生成举牌订单失败: ${error.message}`, 'error');
                }
            }
        });

        getLogger().log(`已处理多订单举牌服务，共生成 ${processedOrders.length} 个订单`, 'info');
        
        return processedOrders;
    }

    /**
     * 验证举牌订单数据
     * @param {Object} pagingOrder - 举牌订单数据
     * @returns {Object} 验证结果
     */
    validatePagingOrder(pagingOrder) {
        const errors = [];
        const warnings = [];

        // 必填字段检查
        if (!pagingOrder.customer_name) {
            errors.push('客户姓名不能为空');
        }

        if (!pagingOrder.pickup_date) {
            errors.push('接机日期不能为空');
        }

        if (!pagingOrder.pickup_time) {
            errors.push('接机时间不能为空');
        }

        if (!pagingOrder.pickup) {
            errors.push('接机地点不能为空');
        }

        // 警告检查
        if (!pagingOrder.customer_contact && !pagingOrder.customer_email) {
            warnings.push('建议提供客户联系方式');
        }

        if (!pagingOrder.flight_info) {
            warnings.push('建议提供航班信息');
        }

        return {
            isValid: errors.length === 0,
            errors: errors,
            warnings: warnings
        };
    }

    /**
     * 获取举牌服务统计信息
     * @param {Array} ordersData - 订单数据数组
     * @returns {Object} 统计信息
     */
    getPagingServiceStats(ordersData) {
        if (!Array.isArray(ordersData)) {
            return {
                hasPagingService: false,
                pagingOrderCount: 0,
                mainOrderCount: 0
            };
        }

        const pagingOrders = ordersData.filter(order => order.is_paging_order);
        const mainOrders = ordersData.filter(order => !order.is_paging_order);
        const ordersNeedingPaging = ordersData.filter(order => order.needs_paging_service);

        return {
            hasPagingService: pagingOrders.length > 0 || ordersNeedingPaging.length > 0,
            pagingOrderCount: pagingOrders.length,
            mainOrderCount: mainOrders.length,
            ordersNeedingPagingCount: ordersNeedingPaging.length,
            totalOrderCount: ordersData.length
        };
    }

    /**
     * 创建举牌服务摘要
     * @param {Object} stats - 统计信息
     * @returns {string} 摘要文本
     */
    createPagingServiceSummary(stats) {
        if (!stats.hasPagingService) {
            return '无举牌服务';
        }

        const parts = [];
        
        if (stats.pagingOrderCount > 0) {
            parts.push(`${stats.pagingOrderCount} 个举牌订单`);
        }
        
        if (stats.ordersNeedingPagingCount > 0) {
            parts.push(`${stats.ordersNeedingPagingCount} 个订单需要举牌服务`);
        }

        return parts.join('，');
    }

    /**
     * 获取举牌服务配置
     * @returns {Object} 配置对象
     */
    getPagingServiceConfig() {
        return { ...this.pagingOrderConfig };
    }

    /**
     * 更新举牌服务配置
     * @param {Object} newConfig - 新配置
     */
    updatePagingServiceConfig(newConfig) {
        this.pagingOrderConfig = { ...this.pagingOrderConfig, ...newConfig };
        getLogger().log('举牌服务配置已更新', 'info');
    }
}

// 创建全局实例 - 立即创建以确保在依赖注册时可用
let pagingServiceManagerInstance = new PagingServiceManager();

/**
 * 获取举牌服务管理器实例
 * @returns {PagingServiceManager} 管理器实例
 */
function getPagingServiceManager() {
    return pagingServiceManagerInstance;
}

// 导出到全局作用域
window.PagingServiceManager = PagingServiceManager;
window.getPagingServiceManager = getPagingServiceManager;

// 确保OTA命名空间存在并暴露管理器
window.OTA = window.OTA || {};
window.OTA.PagingServiceManager = PagingServiceManager;
window.OTA.getPagingServiceManager = getPagingServiceManager;
window.OTA.pagingServiceManager = pagingServiceManagerInstance; // 直接使用已创建的实例

// 向后兼容
window.pagingServiceManager = pagingServiceManagerInstance; // 直接使用已创建的实例
