/**
 * @OTA_CORE 配置迁移工具
 * 🏷️ 标签: @CONFIG_MIGRATION @LEGACY_SUPPORT
 * 📝 说明: 帮助现有模块迁移到统一配置中心，提供向后兼容性
 * ⚠️ 警告: 迁移工具，请勿重复开发
 */

(function() {
    'use strict';

    // 延迟获取依赖，确保加载顺序
    function getConfigCenter() {
        return window.OTA?.configCenter || window.getConfigCenter?.();
    }

    /**
     * @OTA_CORE 配置迁移工具类
     * 处理从旧配置系统到统一配置中心的迁移
     */
    class ConfigMigrationTool {
        constructor() {
            this.logger = window.OTA.getService('logger');
            this.configCenter = getConfigCenter();
            
            // 迁移映射表
            this.migrationMappings = new Map();
            
            // 已迁移的配置记录
            this.migratedConfigs = new Set();
            
            // 兼容性包装器
            this.compatibilityWrappers = new Map();
            
            // 初始化迁移映射
            this.initializeMigrationMappings();
            
            this.logger.log('✅ 配置迁移工具已初始化', 'info');
        }

        /**
         * 初始化迁移映射
         */
        initializeMigrationMappings() {
            // API服务配置迁移
            this.migrationMappings.set('apiService', {
                source: () => {
                    const apiService = window.getAPIService?.();
                    return apiService ? {
                        baseURL: apiService.baseURL,
                        timeout: apiService.timeout
                    } : null;
                },
                target: 'api',
                transform: (config) => ({
                    baseURL: config.baseURL || 'https://gomyhire.com.my/api',
                    timeout: config.timeout || 30000,
                    retries: 3,
                    rateLimit: {
                        enabled: true,
                        maxRequests: 1000,
                        windowMs: 60000
                    }
                })
            });

            // Gemini服务配置迁移
            this.migrationMappings.set('geminiService', {
                source: () => {
                    const geminiService = window.getGeminiService?.();
                    return geminiService?.config || null;
                },
                target: 'gemini',
                transform: (config) => ({
                    model: config.model || 'gemini-2.0-flash-experimental',
                    temperature: config.temperature || 0.1,
                    maxTokens: config.maxTokens || 8192,
                    timeout: config.timeout || 30000,
                    retries: config.retries || 2
                })
            });

            // 生产环境配置迁移
            this.migrationMappings.set('productionConfig', {
                source: () => window.PRODUCTION_CONFIG || null,
                target: 'production',
                transform: (config) => ({
                    environment: config.environment || {},
                    learningSystem: config.learningSystem || {},
                    performance: config.performance || {},
                    monitoring: config.monitoring || {},
                    errorHandling: config.errorHandling || {},
                    features: config.features || {},
                    api: config.api || {}
                })
            });

            // UI管理器配置迁移
            this.migrationMappings.set('uiManager', {
                source: () => {
                    const uiManager = window.getUIManager?.();
                    return uiManager?.config || null;
                },
                target: 'ui',
                transform: (config) => ({
                    theme: config.theme || 'fluent-purple',
                    animations: config.animations !== false,
                    touchOptimized: config.touchOptimized !== false,
                    minTouchTarget: config.minTouchTarget || 44,
                    debounceDelay: config.debounceDelay || 300
                })
            });
        }

        /**
         * 执行配置迁移
         * @param {string|Array} sources - 要迁移的配置源
         * @param {Object} options - 迁移选项
         * @returns {Object} 迁移结果
         */
        async migrateConfigs(sources = null, options = {}) {
            const results = {
                success: [],
                failed: [],
                skipped: [],
                warnings: []
            };

            // 如果没有指定源，迁移所有映射
            const sourcesToMigrate = sources ? 
                (Array.isArray(sources) ? sources : [sources]) : 
                Array.from(this.migrationMappings.keys());

            for (const sourceName of sourcesToMigrate) {
                try {
                    const result = await this.migrateSingleConfig(sourceName, options);
                    if (result.success) {
                        results.success.push(result);
                    } else if (result.skipped) {
                        results.skipped.push(result);
                    } else {
                        results.failed.push(result);
                    }
                    
                    if (result.warnings) {
                        results.warnings.push(...result.warnings);
                    }
                } catch (error) {
                    results.failed.push({
                        source: sourceName,
                        error: error.message
                    });
                    this.logger.logError(`配置迁移失败: ${sourceName}`, error);
                }
            }

            this.logger.log('配置迁移完成', 'info', {
                success: results.success.length,
                failed: results.failed.length,
                skipped: results.skipped.length,
                warnings: results.warnings.length
            });

            return results;
        }

        /**
         * 迁移单个配置
         * @param {string} sourceName - 配置源名称
         * @param {Object} options - 选项
         * @returns {Object} 迁移结果
         */
        async migrateSingleConfig(sourceName, options = {}) {
            const mapping = this.migrationMappings.get(sourceName);
            if (!mapping) {
                return {
                    source: sourceName,
                    success: false,
                    error: '未找到迁移映射'
                };
            }

            // 检查是否已迁移
            if (this.migratedConfigs.has(sourceName) && !options.force) {
                return {
                    source: sourceName,
                    skipped: true,
                    reason: '已迁移'
                };
            }

            try {
                // 获取源配置
                const sourceConfig = mapping.source();
                if (!sourceConfig) {
                    return {
                        source: sourceName,
                        skipped: true,
                        reason: '源配置不存在'
                    };
                }

                // 转换配置
                const transformedConfig = mapping.transform(sourceConfig);
                
                // 设置到配置中心
                this.configCenter.setConfig(mapping.target, transformedConfig, {
                    metadata: {
                        migratedFrom: sourceName,
                        migratedAt: Date.now()
                    }
                });

                // 记录已迁移
                this.migratedConfigs.add(sourceName);

                return {
                    source: sourceName,
                    target: mapping.target,
                    success: true,
                    configKeys: Object.keys(transformedConfig)
                };

            } catch (error) {
                return {
                    source: sourceName,
                    success: false,
                    error: error.message
                };
            }
        }

        /**
         * 创建向后兼容包装器
         * @param {string} legacyPath - 旧配置路径
         * @param {string} newPath - 新配置路径
         * @param {Function} transform - 转换函数
         */
        createCompatibilityWrapper(legacyPath, newPath, transform = null) {
            const pathParts = legacyPath.split('.');
            let current = window;
            
            // 创建路径
            for (let i = 0; i < pathParts.length - 1; i++) {
                if (!current[pathParts[i]]) {
                    current[pathParts[i]] = {};
                }
                current = current[pathParts[i]];
            }
            
            const finalKey = pathParts[pathParts.length - 1];
            
            // 创建getter/setter
            Object.defineProperty(current, finalKey, {
                get: () => {
                    const value = this.configCenter.getNestedConfig(newPath);
                    return transform ? transform(value) : value;
                },
                set: (value) => {
                    const transformedValue = transform ? transform(value, true) : value;
                    const configKey = newPath.split('.')[0];
                    const existingConfig = this.configCenter.getConfig(configKey) || {};
                    
                    // 更新嵌套配置
                    const newConfigParts = newPath.split('.');
                    let configToUpdate = existingConfig;
                    for (let i = 1; i < newConfigParts.length - 1; i++) {
                        if (!configToUpdate[newConfigParts[i]]) {
                            configToUpdate[newConfigParts[i]] = {};
                        }
                        configToUpdate = configToUpdate[newConfigParts[i]];
                    }
                    configToUpdate[newConfigParts[newConfigParts.length - 1]] = transformedValue;
                    
                    this.configCenter.setConfig(configKey, existingConfig);
                },
                configurable: true,
                enumerable: true
            });

            this.compatibilityWrappers.set(legacyPath, newPath);
            this.logger.log(`已创建兼容性包装器: ${legacyPath} -> ${newPath}`, 'info');
        }

        /**
         * 初始化常用的兼容性包装器
         */
        initializeCommonWrappers() {
            // API服务兼容性
            this.createCompatibilityWrapper('window.apiService.baseURL', 'api.baseURL');
            this.createCompatibilityWrapper('window.apiService.timeout', 'api.timeout');
            
            // Gemini服务兼容性
            this.createCompatibilityWrapper('window.geminiService.config.model', 'gemini.model');
            this.createCompatibilityWrapper('window.geminiService.config.temperature', 'gemini.temperature');
            
            // 生产配置兼容性
            this.createCompatibilityWrapper('window.PRODUCTION_CONFIG.api.timeout', 'api.timeout');
            this.createCompatibilityWrapper('window.PRODUCTION_CONFIG.features', 'features');
        }

        /**
         * 验证迁移结果
         * @returns {Object} 验证结果
         */
        validateMigration() {
            const results = {
                valid: true,
                issues: [],
                recommendations: []
            };

            // 检查必需配置
            const requiredConfigs = ['api', 'gemini', 'ui', 'performance', 'logging'];
            for (const configKey of requiredConfigs) {
                const config = this.configCenter.getConfig(configKey);
                if (!config) {
                    results.valid = false;
                    results.issues.push(`缺少必需配置: ${configKey}`);
                }
            }

            // 检查配置完整性
            const apiConfig = this.configCenter.getConfig('api');
            if (apiConfig && !apiConfig.baseURL) {
                results.valid = false;
                results.issues.push('API配置缺少baseURL');
            }

            // 提供建议
            if (this.migratedConfigs.size === 0) {
                results.recommendations.push('建议运行配置迁移以获得更好的配置管理');
            }

            return results;
        }

        /**
         * 获取迁移状态
         * @returns {Object} 迁移状态
         */
        getMigrationStatus() {
            return {
                totalMappings: this.migrationMappings.size,
                migratedConfigs: Array.from(this.migratedConfigs),
                compatibilityWrappers: Array.from(this.compatibilityWrappers.keys()),
                configCenterStatus: this.configCenter?.getStatus() || null
            };
        }
    }

    // 创建全局实例
    const configMigrationTool = new ConfigMigrationTool();

    // 导出到全局作用域
    window.OTA = window.OTA || {};
    window.OTA.configMigrationTool = configMigrationTool;
    window.OTA.getConfigMigrationTool = () => configMigrationTool;

    // 向后兼容
    window.getConfigMigrationTool = () => configMigrationTool;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('configMigrationTool', configMigrationTool, '@OTA_CONFIG_MIGRATION_TOOL');
        window.OTA.Registry.registerFactory('getConfigMigrationTool', () => configMigrationTool, '@OTA_CONFIG_MIGRATION_TOOL_FACTORY');
    }

})();
