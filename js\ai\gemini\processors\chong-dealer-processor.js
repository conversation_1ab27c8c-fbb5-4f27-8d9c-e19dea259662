/**
 * @OTA_PROCESSOR Chong Dealer专用处理器
 * 🏷️ 标签: @CHONG_DEALER_PROCESSOR
 * 📝 说明: 专门处理Chong Dealer订单的智能解析器
 * 🎯 功能: 特定参考号识别、举牌接机处理、包车时长解析、中文姓名处理
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.processors = window.OTA.gemini.processors || {};

(function() {
    'use strict';

    /**
     * Chong Dealer专用处理器类
     * 继承自BaseProcessor，实现Chong Dealer特定的处理逻辑
     */
    class ChongDealerProcessor extends window.OTA.gemini.core.BaseProcessor {
        constructor(config = {}) {
            super('Chong Dealer', config);
            
            // Chong Dealer特定配置
            this.chongDealerConfig = {
                // 参考号格式
                referencePatterns: [
                    /团号[:\s]*([A-Z0-9]{6,15})/i,
                    /确认号[:\s]*([A-Z0-9]{6,15})/i,
                    /^CD[A-Z0-9]{6,12}$/i,
                    /^CHONG[A-Z0-9]{4,8}$/i,
                    /^崇[A-Z0-9]{4,8}$/
                ],
                
                // 服务类型识别
                serviceTypePatterns: {
                    pickup: /接机|机场接|Airport\s*Pickup/i,
                    dropoff: /送机|机场送|Airport\s*Drop/i,
                    charter: /包车|Charter|Day\s*Tour/i
                },
                
                // 举牌服务识别
                signHoldingPatterns: [
                    /举牌/i,
                    /接机牌/i,
                    /Name\s*Board/i,
                    /Sign\s*Holding/i
                ],
                
                // 时长识别（包车服务）
                durationPatterns: [
                    /(\d+)\s*小时/i,
                    /(\d+)\s*hours?/i,
                    /(\d+)h/i
                ]
            };
        }

        /**
         * 平台特定预处理
         * @param {string} text - 文本
         * @param {Object} options - 选项
         * @returns {Promise<string>} 处理后的文本
         */
        async platformSpecificPreprocess(text, options = {}) {
            let processedText = text;

            // Chong Dealer特定的文本清理
            processedText = processedText
                .replace(/崇德勒/g, 'Chong Dealer')  // 统一品牌名称
                .replace(/崇/g, 'CHONG')             // 简化中文标识
                .replace(/团号[:：]/g, '团号: ')      // 标准化团号格式
                .replace(/确认号[:：]/g, '确认号: '); // 标准化确认号格式

            return processedText;
        }

        /**
         * 提取字段 - Chong Dealer特定实现
         * @param {string} text - 文本
         * @param {Object} options - 选项
         * @returns {Promise<Object>} 提取的字段
         */
        async extractFields(text, options = {}) {
            const extractedData = {};

            try {
                // 1. 提取参考号
                extractedData.ota_reference_number = await this.extractReferenceNumber(text);

                // 2. 提取服务类型
                extractedData.service_type_id = await this.extractServiceType(text);

                // 3. 提取客户信息
                const customerInfo = await this.extractCustomerInfo(text);
                Object.assign(extractedData, customerInfo);

                // 4. 提取行程信息
                const tripInfo = await this.extractTripInfo(text);
                Object.assign(extractedData, tripInfo);

                // 5. 提取特殊要求
                extractedData.extra_requirements = await this.extractSpecialRequirements(text);

                // 6. 提取价格信息
                extractedData.ota_price = await this.extractPriceInfo(text);

                // 7. 检测举牌服务
                extractedData.sign_holding_service = await this.detectSignHoldingService(text);

                // 8. 提取包车时长（如果是包车服务）
                if (extractedData.service_type_id === 4) { // 包车服务
                    extractedData.charter_duration = await this.extractCharterDuration(text);
                }

                return extractedData;

            } catch (error) {
                this.logger.logError('Chong Dealer字段提取失败', error);
                throw error;
            }
        }

        /**
         * 提取参考号
         * @param {string} text - 文本
         * @returns {Promise<string>} 参考号
         */
        async extractReferenceNumber(text) {
            // 使用Chong Dealer特定的参考号模式
            for (const pattern of this.chongDealerConfig.referencePatterns) {
                const match = text.match(pattern);
                if (match) {
                    return match[1] || match[0];
                }
            }

            // 如果没有找到，使用通用方法
            const referenceResult = await this.identifyReference(text);
            return referenceResult.found ? referenceResult.reference : null;
        }

        /**
         * 提取服务类型
         * @param {string} text - 文本
         * @returns {Promise<number>} 服务类型ID
         */
        async extractServiceType(text) {
            const patterns = this.chongDealerConfig.serviceTypePatterns;

            if (patterns.pickup.test(text)) {
                return 2; // 接机
            } else if (patterns.dropoff.test(text)) {
                return 3; // 送机
            } else if (patterns.charter.test(text)) {
                return 4; // 包车
            }

            // 默认返回接机
            return 2;
        }

        /**
         * 提取客户信息
         * @param {string} text - 文本
         * @returns {Promise<Object>} 客户信息
         */
        async extractCustomerInfo(text) {
            const customerInfo = {};

            // 提取客户姓名 - Chong Dealer通常包含中文姓名
            const namePatterns = [
                /客户[:\s]*([A-Za-z\u4e00-\u9fff\s]{2,30})/,
                /姓名[:\s]*([A-Za-z\u4e00-\u9fff\s]{2,30})/,
                /乘客[:\s]*([A-Za-z\u4e00-\u9fff\s]{2,30})/,
                /Name[:\s]*([A-Za-z\u4e00-\u9fff\s]{2,30})/i
            ];

            for (const pattern of namePatterns) {
                const match = text.match(pattern);
                if (match) {
                    customerInfo.customer_name = match[1].trim();
                    break;
                }
            }

            // 提取电话号码
            const phonePatterns = [
                /电话[:\s]*([\+]?[\d\s\-\(\)]{8,20})/,
                /手机[:\s]*([\+]?[\d\s\-\(\)]{8,20})/,
                /Phone[:\s]*([\+]?[\d\s\-\(\)]{8,20})/i,
                /Tel[:\s]*([\+]?[\d\s\-\(\)]{8,20})/i
            ];

            for (const pattern of phonePatterns) {
                const match = text.match(pattern);
                if (match) {
                    customerInfo.customer_phone = match[1].trim();
                    break;
                }
            }

            // 提取乘客人数
            const paxPatterns = [
                /(\d+)\s*人/,
                /(\d+)\s*pax/i,
                /(\d+)\s*passenger/i
            ];

            for (const pattern of paxPatterns) {
                const match = text.match(pattern);
                if (match) {
                    customerInfo.passenger_count = parseInt(match[1]);
                    break;
                }
            }

            return customerInfo;
        }

        /**
         * 提取行程信息
         * @param {string} text - 文本
         * @returns {Promise<Object>} 行程信息
         */
        async extractTripInfo(text) {
            const tripInfo = {};

            // 提取接送地点
            const locationPatterns = [
                /接机地点[:\s]*([^\n\r]{5,100})/,
                /送机地点[:\s]*([^\n\r]{5,100})/,
                /出发地[:\s]*([^\n\r]{5,100})/,
                /目的地[:\s]*([^\n\r]{5,100})/,
                /From[:\s]*([^\n\r]{5,100})/i,
                /To[:\s]*([^\n\r]{5,100})/i
            ];

            for (const pattern of locationPatterns) {
                const match = text.match(pattern);
                if (match) {
                    if (!tripInfo.pickup_location) {
                        tripInfo.pickup_location = match[1].trim();
                    } else if (!tripInfo.dropoff_location) {
                        tripInfo.dropoff_location = match[1].trim();
                    }
                }
            }

            // 提取日期
            const datePatterns = [
                /日期[:\s]*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})/,
                /Date[:\s]*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})/i,
                /(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})/
            ];

            for (const pattern of datePatterns) {
                const match = text.match(pattern);
                if (match) {
                    tripInfo.pickup_date = match[1];
                    break;
                }
            }

            // 提取时间
            const timePatterns = [
                /时间[:\s]*(\d{1,2}:\d{2})/,
                /Time[:\s]*(\d{1,2}:\d{2})/i,
                /(\d{1,2}:\d{2})/
            ];

            for (const pattern of timePatterns) {
                const match = text.match(pattern);
                if (match) {
                    tripInfo.pickup_time = match[1];
                    break;
                }
            }

            // 提取航班信息
            const flightPatterns = [
                /航班[:\s]*([A-Z]{2,3}\d{3,4})/i,
                /Flight[:\s]*([A-Z]{2,3}\d{3,4})/i,
                /([A-Z]{2,3}\d{3,4})/
            ];

            for (const pattern of flightPatterns) {
                const match = text.match(pattern);
                if (match) {
                    tripInfo.flight_number = match[1].toUpperCase();
                    break;
                }
            }

            return tripInfo;
        }

        /**
         * 提取特殊要求
         * @param {string} text - 文本
         * @returns {Promise<string>} 特殊要求
         */
        async extractSpecialRequirements(text) {
            const requirementPatterns = [
                /特殊要求[:\s]*([^\n\r]{5,200})/,
                /备注[:\s]*([^\n\r]{5,200})/,
                /Note[:\s]*([^\n\r]{5,200})/i,
                /Remark[:\s]*([^\n\r]{5,200})/i
            ];

            for (const pattern of requirementPatterns) {
                const match = text.match(pattern);
                if (match) {
                    return match[1].trim();
                }
            }

            return '';
        }

        /**
         * 提取价格信息
         * @param {string} text - 文本
         * @returns {Promise<string>} 价格
         */
        async extractPriceInfo(text) {
            const pricePatterns = [
                /价格[:\s]*([\d.]+)/,
                /费用[:\s]*([\d.]+)/,
                /Price[:\s]*([\d.]+)/i,
                /Cost[:\s]*([\d.]+)/i,
                /RM\s*([\d.]+)/i,
                /MYR\s*([\d.]+)/i
            ];

            for (const pattern of pricePatterns) {
                const match = text.match(pattern);
                if (match) {
                    return match[1];
                }
            }

            return '';
        }

        /**
         * 检测举牌服务
         * @param {string} text - 文本
         * @returns {Promise<boolean>} 是否需要举牌服务
         */
        async detectSignHoldingService(text) {
            for (const pattern of this.chongDealerConfig.signHoldingPatterns) {
                if (pattern.test(text)) {
                    return true;
                }
            }
            return false;
        }

        /**
         * 提取包车时长
         * @param {string} text - 文本
         * @returns {Promise<number>} 包车时长（小时）
         */
        async extractCharterDuration(text) {
            for (const pattern of this.chongDealerConfig.durationPatterns) {
                const match = text.match(pattern);
                if (match) {
                    return parseInt(match[1]);
                }
            }
            return 8; // 默认8小时
        }

        /**
         * 获取预设配置 - Chong Dealer特定
         * @returns {Object} 预设配置
         */
        getPresetConfig() {
            return {
                defaultLanguage: { "0": "4" }, // Chinese (简体中文)
                defaultCarType: 1, // Comfort 5 Seater
                extraRequirementsTemplate: '崇德勒专车服务',
                defaultOtaChannel: 'Chong Dealer',
                signHoldingDefault: true,
                charterDurationDefault: 8
            };
        }

        /**
         * 获取必填字段列表 - Chong Dealer特定
         * @returns {Array} 必填字段列表
         */
        getRequiredFields() {
            return [
                'ota_reference_number',
                'pickup_location',
                'pickup_date',
                'pickup_time',
                'customer_name',
                'customer_phone',
                'service_type_id'
            ];
        }

        /**
         * 应用预设配置 - Chong Dealer特定
         * @param {Object} data - 数据
         * @param {Object} options - 选项
         * @returns {Promise<Object>} 应用预设后的数据
         */
        async applyPresets(data, options = {}) {
            const result = await super.applyPresets(data, options);
            const presets = this.getPresetConfig();

            // 应用Chong Dealer特定预设
            if (!result.ota) {
                result.ota = presets.defaultOtaChannel;
            }

            // 如果检测到举牌服务，添加到特殊要求
            if (data.sign_holding_service && !result.extra_requirements.includes('举牌')) {
                result.extra_requirements = (result.extra_requirements + ' 举牌服务').trim();
            }

            // 如果是包车服务，添加时长信息
            if (data.service_type_id === 4 && data.charter_duration) {
                const durationText = `包车${data.charter_duration}小时`;
                if (!result.extra_requirements.includes(durationText)) {
                    result.extra_requirements = (result.extra_requirements + ' ' + durationText).trim();
                }
            }

            // 根据中文姓名自动选择中文语言
            if (result.customer_name && /[\u4e00-\u9fff]/.test(result.customer_name)) {
                result.languages_id_array = { "0": "4" }; // Chinese
            }

            return result;
        }
    }

    // 创建全局实例获取函数
    function getChongDealerProcessor() {
        if (!window.OTA.gemini.processors.chongDealerProcessor) {
            window.OTA.gemini.processors.chongDealerProcessor = new ChongDealerProcessor();
        }
        return window.OTA.gemini.processors.chongDealerProcessor;
    }

    // 暴露到全局命名空间
    window.OTA.gemini.processors.ChongDealerProcessor = ChongDealerProcessor;
    window.OTA.gemini.processors.getChongDealerProcessor = getChongDealerProcessor;

    // 向后兼容
    window.getChongDealerProcessor = getChongDealerProcessor;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerClass('ChongDealerProcessor', ChongDealerProcessor, '@CHONG_DEALER_PROCESSOR');
        window.OTA.Registry.registerFactory('getChongDealerProcessor', getChongDealerProcessor, '@CHONG_DEALER_PROCESSOR_FACTORY');
    }

    console.log('✅ Chong Dealer处理器已加载');

})();
