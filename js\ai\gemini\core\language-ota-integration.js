/**
 * @OTA_SERVICE 语言-OTA集成管理器
 * 🏷️ 标签: @LANGUAGE_OTA_INTEGRATION
 * 📝 说明: 整合语言管理器与OTA渠道处理，实现多语言环境下的智能OTA处理
 * 🎯 功能: 语言检测、OTA本地化、多语言配置、智能语言选择
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.core = window.OTA.gemini.core || {};

(function() {
    'use strict';

    /**
     * 语言-OTA集成管理器类
     * 协调语言管理器与OTA渠道处理器的协同工作
     */
    class LanguageOTAIntegration {
        constructor() {
            this.logger = window.getLogger?.() || console;
            
            // 延迟获取依赖服务
            this.languageManager = null;
            this.otaChannelIdentifier = null;
            
            // 语言-OTA映射配置
            this.languageOTAMapping = this.loadLanguageOTAMapping();
            
            // 多语言处理统计
            this.stats = {
                totalProcessed: 0,
                languageDetections: new Map(),
                otaLanguageMatches: new Map(),
                multiLanguageOrders: 0,
                languageBasedCorrections: 0
            };

            // 初始化依赖
            this.initializeDependencies();
        }

        /**
         * 延迟初始化依赖服务
         */
        initializeDependencies() {
            setTimeout(() => {
                // 获取语言管理器 - 修复集成问题
                this.languageManager = window.getLanguageManager?.() ||
                    window.OTA?.languageManager ||
                    (window.LanguageManager ? new window.LanguageManager() : null);

                // 获取OTA渠道识别器
                this.otaChannelIdentifier = window.OTA?.gemini?.core?.getOTAChannelIdentifier?.();

                if (!this.languageManager) {
                    this.logger.logError('语言管理器未找到，语言检测功能将受限');
                } else {
                    // 验证语言管理器功能
                    this.validateLanguageManagerIntegration();
                }

                if (!this.otaChannelIdentifier) {
                    this.logger.logError('OTA渠道识别器未找到');
                }

                this.logger.log('语言-OTA集成管理器依赖初始化完成', 'info');
            }, 100);
        }

        /**
         * 验证语言管理器集成
         */
        async validateLanguageManagerIntegration() {
            try {
                if (!this.languageManager) return false;

                // 测试基本功能
                const languages = await this.languageManager.getLanguages();
                if (!languages || languages.length === 0) {
                    this.logger.logError('语言管理器无法获取语言数据');
                    return false;
                }

                // 测试验证功能
                const validation = await this.languageManager.validateLanguageIds([2, 3, 4]);
                if (!validation || typeof validation.valid !== 'boolean') {
                    this.logger.logError('语言管理器验证功能异常');
                    return false;
                }

                this.logger.log('✅ 语言管理器集成验证通过', 'info');
                return true;
            } catch (error) {
                this.logger.logError('语言管理器集成验证失败', error);
                return false;
            }
        }

        /**
         * 加载语言-OTA映射配置
         * @returns {Object} 映射配置
         */
        loadLanguageOTAMapping() {
            return {
                // OTA平台的主要语言偏好
                otaPlatformLanguages: {
                    'Chong Dealer': {
                        primary: ['zh-CN', 'zh-TW'],
                        secondary: ['en'],
                        defaultLanguageId: 4, // Chinese
                        supportedLanguageIds: [2, 4], // English, Chinese
                        languageDetectionBonus: 0.2
                    },
                    'Klook': {
                        primary: ['en'],
                        secondary: ['zh-CN', 'zh-TW', 'ja', 'ko'],
                        defaultLanguageId: 2, // English
                        supportedLanguageIds: [2, 4, 5, 6], // English, Chinese, Japanese, Korean
                        languageDetectionBonus: 0.15
                    },
                    'Ctrip': {
                        primary: ['zh-CN'],
                        secondary: ['en'],
                        defaultLanguageId: 4, // Chinese
                        supportedLanguageIds: [2, 4], // English, Chinese
                        languageDetectionBonus: 0.18
                    },
                    'KKday': {
                        primary: ['zh-TW'],
                        secondary: ['en', 'ja'],
                        defaultLanguageId: 4, // Chinese (Traditional)
                        supportedLanguageIds: [2, 4, 5], // English, Chinese, Japanese
                        languageDetectionBonus: 0.15
                    },
                    'Agoda': {
                        primary: ['en'],
                        secondary: ['zh-CN', 'th', 'vi'],
                        defaultLanguageId: 2, // English
                        supportedLanguageIds: [2, 4, 7, 8], // English, Chinese, Thai, Vietnamese
                        languageDetectionBonus: 0.1
                    },
                    'Booking.com': {
                        primary: ['en'],
                        secondary: ['zh-CN', 'de', 'fr', 'es'],
                        defaultLanguageId: 2, // English
                        supportedLanguageIds: [2, 4, 9, 10, 11], // English, Chinese, German, French, Spanish
                        languageDetectionBonus: 0.1
                    },
                    'generic': {
                        primary: ['en'],
                        secondary: ['zh-CN'],
                        defaultLanguageId: 2, // English
                        supportedLanguageIds: [2, 4], // English, Chinese
                        languageDetectionBonus: 0.05
                    }
                },

                // 语言检测规则
                languageDetectionRules: {
                    // 中文内容识别
                    chinese: {
                        pattern: /[\u4e00-\u9fff]/g,
                        minRatio: 0.1,
                        indicators: ['团号', '确认号', '接机', '送机', '包车', '举牌', '客户', '乘客']
                    },
                    
                    // 英文内容识别
                    english: {
                        pattern: /[a-zA-Z]/g,
                        minRatio: 0.3,
                        indicators: ['booking', 'confirmation', 'pickup', 'dropoff', 'charter', 'passenger', 'customer']
                    },
                    
                    // 日文内容识别
                    japanese: {
                        pattern: /[\u3040-\u309f\u30a0-\u30ff]/g,
                        minRatio: 0.1,
                        indicators: ['予約', '確認', '送迎', 'お客様']
                    },
                    
                    // 韩文内容识别
                    korean: {
                        pattern: /[\uac00-\ud7af]/g,
                        minRatio: 0.1,
                        indicators: ['예약', '확인', '픽업', '고객']
                    }
                },

                // 语言优先级权重
                languagePriorityWeights: {
                    exactMatch: 0.4,        // 完全匹配OTA主要语言
                    secondaryMatch: 0.2,    // 匹配OTA次要语言
                    contentAnalysis: 0.3,   // 基于内容的语言分析
                    userPreference: 0.1     // 用户语言偏好
                }
            };
        }

        /**
         * 智能语言-OTA协同处理 - 主要入口方法
         * @param {string} orderText - 订单文本
         * @param {Object} options - 处理选项
         * @returns {Promise<Object>} 处理结果
         */
        async processWithLanguageContext(orderText, options = {}) {
            this.stats.totalProcessed++;
            
            try {
                // 1. 语言检测和分析
                const languageAnalysis = await this.analyzeLanguageContext(orderText, options);
                
                // 2. 基于语言的OTA渠道识别增强
                const enhancedOTAResult = await this.enhanceOTAIdentification(orderText, languageAnalysis, options);
                
                // 3. 语言配置应用
                const languageConfig = await this.generateLanguageConfig(enhancedOTAResult, languageAnalysis);
                
                // 4. 多语言处理优化
                const optimizedResult = await this.optimizeForMultiLanguage(enhancedOTAResult, languageConfig, options);
                
                return {
                    success: true,
                    otaResult: optimizedResult,
                    languageAnalysis,
                    languageConfig,
                    processingMetadata: {
                        hasMultiLanguageContent: languageAnalysis.isMultiLanguage,
                        detectedLanguages: languageAnalysis.detectedLanguages,
                        recommendedLanguageId: languageConfig.recommendedLanguageId,
                        confidenceBonus: languageAnalysis.confidenceBonus
                    }
                };

            } catch (error) {
                this.logger.logError('语言-OTA协同处理失败', error);
                return {
                    success: false,
                    error: error.message,
                    fallbackLanguageId: 2 // English fallback
                };
            }
        }

        /**
         * 分析语言上下文
         * @param {string} orderText - 订单文本
         * @param {Object} options - 选项
         * @returns {Promise<Object>} 语言分析结果
         */
        async analyzeLanguageContext(orderText, options = {}) {
            const analysis = {
                detectedLanguages: [],
                primaryLanguage: null,
                isMultiLanguage: false,
                languageRatios: {},
                confidenceBonus: 0,
                languageIndicators: []
            };

            try {
                // 执行自定义语言检测（主要方法）
                const customDetection = this.performCustomLanguageDetection(orderText);
                analysis.languageRatios = customDetection.ratios;
                analysis.languageIndicators = customDetection.indicators;

                // 确定主要语言
                analysis.primaryLanguage = this.determinePrimaryLanguage(analysis.languageRatios, analysis.languageIndicators);
                analysis.detectedLanguages = Object.keys(analysis.languageRatios).filter(lang => analysis.languageRatios[lang] > 0.05);

                // 使用语言管理器进行AI映射（如果可用）
                if (this.languageManager && analysis.primaryLanguage) {
                    try {
                        const mappingResult = await this.languageManager.mapAIDetectedLanguage(analysis.primaryLanguage);
                        if (mappingResult && mappingResult.mapped) {
                            analysis.mappedLanguageId = mappingResult.language?.id;
                            analysis.mappedLanguageName = mappingResult.language?.name;
                            analysis.mappingConfidence = mappingResult.confidence;
                        }
                    } catch (mappingError) {
                        this.logger.logWarning('语言映射失败，使用检测结果', mappingError);
                    }
                }

                // 判断是否为多语言内容
                const significantLanguages = Object.entries(analysis.languageRatios)
                    .filter(([lang, ratio]) => ratio > 0.1)
                    .length;

                analysis.isMultiLanguage = significantLanguages > 1;

                if (analysis.isMultiLanguage) {
                    this.stats.multiLanguageOrders++;
                }

                // 更新统计
                const primaryLang = analysis.primaryLanguage || 'unknown';
                const count = this.stats.languageDetections.get(primaryLang) || 0;
                this.stats.languageDetections.set(primaryLang, count + 1);

                return analysis;

            } catch (error) {
                this.logger.logError('语言上下文分析失败', error);
                return {
                    ...analysis,
                    error: error.message,
                    primaryLanguage: 'en' // 默认英文
                };
            }
        }

        /**
         * 确定主要语言
         * @param {Object} languageRatios - 语言比例
         * @param {Array} languageIndicators - 语言指示器
         * @returns {string} 主要语言
         */
        determinePrimaryLanguage(languageRatios, languageIndicators) {
            // 基于指示器权重计算
            const indicatorScores = {};
            languageIndicators.forEach(indicator => {
                indicatorScores[indicator.language] = (indicatorScores[indicator.language] || 0) + indicator.weight;
            });

            // 基于字符比例计算
            const ratioScores = { ...languageRatios };

            // 综合评分
            const finalScores = {};
            const allLanguages = new Set([...Object.keys(indicatorScores), ...Object.keys(ratioScores)]);

            allLanguages.forEach(lang => {
                const indicatorScore = indicatorScores[lang] || 0;
                const ratioScore = ratioScores[lang] || 0;
                finalScores[lang] = (indicatorScore * 0.6) + (ratioScore * 0.4); // 指示器权重更高
            });

            // 选择得分最高的语言
            const sortedLanguages = Object.entries(finalScores)
                .sort(([,a], [,b]) => b - a);

            return sortedLanguages.length > 0 ? sortedLanguages[0][0] : 'en';
        }

        /**
         * 执行自定义语言检测
         * @param {string} text - 文本
         * @returns {Object} 检测结果
         */
        performCustomLanguageDetection(text) {
            const rules = this.languageOTAMapping.languageDetectionRules;
            const ratios = {};
            const indicators = [];

            for (const [language, rule] of Object.entries(rules)) {
                // 计算字符比例
                const matches = text.match(rule.pattern) || [];
                const ratio = matches.length / text.length;
                ratios[language] = ratio;

                // 检查指示词
                const foundIndicators = rule.indicators.filter(indicator => 
                    text.toLowerCase().includes(indicator.toLowerCase())
                );
                
                if (foundIndicators.length > 0) {
                    indicators.push({
                        language,
                        indicators: foundIndicators,
                        weight: foundIndicators.length / rule.indicators.length
                    });
                }
            }

            return { ratios, indicators };
        }

        /**
         * 增强OTA识别
         * @param {string} orderText - 订单文本
         * @param {Object} languageAnalysis - 语言分析结果
         * @param {Object} options - 选项
         * @returns {Promise<Object>} 增强的OTA识别结果
         */
        async enhanceOTAIdentification(orderText, languageAnalysis, options = {}) {
            if (!this.otaChannelIdentifier) {
                throw new Error('OTA渠道识别器不可用');
            }

            // 获取基础OTA识别结果
            const baseResult = await this.otaChannelIdentifier.identifyChannel(orderText, options);
            
            // 基于语言分析增强置信度
            const languageBonus = this.calculateLanguageBonus(baseResult.channel, languageAnalysis);
            
            const enhancedResult = {
                ...baseResult,
                originalConfidence: baseResult.confidence,
                languageBonus,
                confidence: Math.min(baseResult.confidence + languageBonus, 1.0),
                languageAnalysis
            };

            // 如果语言分析显著提高了置信度，记录统计
            if (languageBonus > 0.1) {
                this.stats.languageBasedCorrections++;
            }

            // 更新OTA-语言匹配统计
            const otaLangKey = `${baseResult.channel}-${languageAnalysis.primaryLanguage}`;
            const matchCount = this.stats.otaLanguageMatches.get(otaLangKey) || 0;
            this.stats.otaLanguageMatches.set(otaLangKey, matchCount + 1);

            return enhancedResult;
        }

        /**
         * 计算语言加成
         * @param {string} otaChannel - OTA渠道
         * @param {Object} languageAnalysis - 语言分析
         * @returns {number} 语言加成值
         */
        calculateLanguageBonus(otaChannel, languageAnalysis) {
            const otaConfig = this.languageOTAMapping.otaPlatformLanguages[otaChannel];
            if (!otaConfig) {
                return 0;
            }

            let bonus = 0;
            const weights = this.languageOTAMapping.languagePriorityWeights;

            // 主要语言匹配加成
            if (otaConfig.primary.includes(languageAnalysis.primaryLanguage)) {
                bonus += weights.exactMatch;
            }
            
            // 次要语言匹配加成
            else if (otaConfig.secondary.includes(languageAnalysis.primaryLanguage)) {
                bonus += weights.secondaryMatch;
            }

            // 内容分析加成
            const contentBonus = this.calculateContentLanguageBonus(otaChannel, languageAnalysis);
            bonus += contentBonus * weights.contentAnalysis;

            // 平台特定语言检测加成
            bonus += otaConfig.languageDetectionBonus || 0;

            return Math.min(bonus, 0.3); // 最大加成0.3
        }

        /**
         * 计算内容语言加成
         * @param {string} otaChannel - OTA渠道
         * @param {Object} languageAnalysis - 语言分析
         * @returns {number} 内容加成值
         */
        calculateContentLanguageBonus(otaChannel, languageAnalysis) {
            let bonus = 0;

            // 基于语言指示词的加成
            for (const indicator of languageAnalysis.languageIndicators) {
                const otaConfig = this.languageOTAMapping.otaPlatformLanguages[otaChannel];
                if (otaConfig && otaConfig.primary.includes(indicator.language)) {
                    bonus += indicator.weight * 0.2;
                }
            }

            // 多语言内容的特殊处理
            if (languageAnalysis.isMultiLanguage) {
                // Chong Dealer和Ctrip通常有中英混合内容
                if (['Chong Dealer', 'Ctrip'].includes(otaChannel)) {
                    bonus += 0.1;
                }
            }

            return Math.min(bonus, 0.5);
        }

        /**
         * 生成语言配置
         * @param {Object} otaResult - OTA识别结果
         * @param {Object} languageAnalysis - 语言分析
         * @returns {Promise<Object>} 语言配置
         */
        async generateLanguageConfig(otaResult, languageAnalysis) {
            const otaConfig = this.languageOTAMapping.otaPlatformLanguages[otaResult.channel] ||
                             this.languageOTAMapping.otaPlatformLanguages['generic'];

            // 基于分析结果选择最佳语言ID
            let recommendedLanguageId = otaConfig.defaultLanguageId;
            let languageArray = { "0": recommendedLanguageId.toString() };

            // 优先使用语言管理器映射的结果
            if (languageAnalysis.mappedLanguageId && otaConfig.supportedLanguageIds.includes(languageAnalysis.mappedLanguageId)) {
                recommendedLanguageId = languageAnalysis.mappedLanguageId;
            } else if (languageAnalysis.primaryLanguage) {
                // 回退到本地映射
                const languageIdMapping = {
                    'en': 2,      // English
                    'english': 2,
                    'zh-CN': 4,   // Chinese Simplified
                    'zh-TW': 4,   // Chinese Traditional (使用同一ID)
                    'chinese': 4,
                    'ja': 5,      // Japanese
                    'japanese': 5,
                    'ko': 6,      // Korean
                    'korean': 6,
                    'th': 7,      // Thai
                    'thai': 7,
                    'vi': 8,      // Vietnamese
                    'vietnamese': 8,
                    'de': 9,      // German
                    'german': 9,
                    'fr': 10,     // French
                    'french': 10,
                    'es': 11,     // Spanish
                    'spanish': 11,
                    'ms': 3,      // Malay
                    'malay': 3
                };

                const detectedLanguageId = languageIdMapping[languageAnalysis.primaryLanguage.toLowerCase()];
                if (detectedLanguageId && otaConfig.supportedLanguageIds.includes(detectedLanguageId)) {
                    recommendedLanguageId = detectedLanguageId;
                }
            }

            // 如果是多语言订单，生成多语言数组
            if (languageAnalysis.isMultiLanguage && this.languageManager) {
                try {
                    const multiLanguageIds = await this.generateMultiLanguageArray(languageAnalysis, otaConfig);
                    if (multiLanguageIds && multiLanguageIds.length > 1) {
                        languageArray = await this.languageManager.transformForAPISync(multiLanguageIds);
                    }
                } catch (error) {
                    this.logger.logWarning('多语言数组生成失败，使用单语言配置', error);
                }
            }

            return {
                recommendedLanguageId,
                supportedLanguageIds: otaConfig.supportedLanguageIds,
                languageArray,
                isMultiLanguageOrder: languageAnalysis.isMultiLanguage,
                detectedPrimaryLanguage: languageAnalysis.primaryLanguage,
                mappedLanguageId: languageAnalysis.mappedLanguageId,
                mappingConfidence: languageAnalysis.mappingConfidence,
                otaPlatformLanguages: otaConfig.primary
            };
        }

        /**
         * 生成多语言数组
         * @param {Object} languageAnalysis - 语言分析
         * @param {Object} otaConfig - OTA配置
         * @returns {Promise<Array>} 语言ID数组
         */
        async generateMultiLanguageArray(languageAnalysis, otaConfig) {
            const languageIds = [];

            // 添加主要语言
            if (languageAnalysis.mappedLanguageId) {
                languageIds.push(languageAnalysis.mappedLanguageId);
            }

            // 添加OTA平台支持的语言
            otaConfig.supportedLanguageIds.forEach(id => {
                if (!languageIds.includes(id)) {
                    languageIds.push(id);
                }
            });

            // 限制最多5种语言
            return languageIds.slice(0, 5);
        }

        /**
         * 多语言处理优化
         * @param {Object} otaResult - OTA结果
         * @param {Object} languageConfig - 语言配置
         * @param {Object} options - 选项
         * @returns {Promise<Object>} 优化结果
         */
        async optimizeForMultiLanguage(otaResult, languageConfig, options = {}) {
            // 应用语言配置到OTA结果
            const optimizedResult = {
                ...otaResult,
                languageConfig,
                
                // 添加语言相关的处理提示
                processingHints: {
                    useMultiLanguagePrompt: languageConfig.isMultiLanguageOrder,
                    primaryLanguage: languageConfig.detectedPrimaryLanguage,
                    recommendedLanguageId: languageConfig.recommendedLanguageId
                }
            };

            // 如果是多语言订单，添加特殊处理标记
            if (languageConfig.isMultiLanguageOrder) {
                optimizedResult.requiresMultiLanguageProcessing = true;
                optimizedResult.multiLanguageStrategy = this.determineMultiLanguageStrategy(otaResult.channel, languageConfig);
            }

            return optimizedResult;
        }

        /**
         * 确定多语言处理策略
         * @param {string} otaChannel - OTA渠道
         * @param {Object} languageConfig - 语言配置
         * @returns {Object} 处理策略
         */
        determineMultiLanguageStrategy(otaChannel, languageConfig) {
            const strategies = {
                'Chong Dealer': {
                    approach: 'chinese_priority',
                    description: '优先处理中文内容，英文作为补充',
                    promptModification: 'emphasize_chinese_extraction'
                },
                'Klook': {
                    approach: 'english_priority',
                    description: '优先处理英文内容，其他语言作为补充',
                    promptModification: 'emphasize_english_extraction'
                },
                'Ctrip': {
                    approach: 'balanced_bilingual',
                    description: '中英文并重处理',
                    promptModification: 'balanced_chinese_english'
                },
                'generic': {
                    approach: 'auto_detect',
                    description: '根据内容自动选择主要语言',
                    promptModification: 'adaptive_language_processing'
                }
            };

            return strategies[otaChannel] || strategies['generic'];
        }

        /**
         * 使用语言管理器验证和转换语言数据
         * @param {Array} languageIds - 语言ID数组
         * @param {string} format - 目标格式
         * @returns {Promise<any>} 转换后的数据
         */
        async validateAndTransformLanguages(languageIds, format = 'api') {
            if (!this.languageManager) {
                this.logger.logWarning('语言管理器不可用，使用默认转换');
                return format === 'api' ? { "0": "2" } : [2];
            }

            try {
                const validation = await this.languageManager.validateLanguageIds(languageIds);

                if (!validation.valid) {
                    this.logger.log(`⚠️ 语言ID验证失败: ${validation.error}`, 'warning');

                    // 使用建议的替代方案
                    if (validation.suggestions && validation.suggestions.length > 0) {
                        const suggestedIds = validation.suggestions.map(s => s.suggestedId);
                        return await this.languageManager.transformLanguageData(suggestedIds, format);
                    }

                    // 使用默认语言
                    const defaultIds = await this.languageManager.getDefaultSelection();
                    return await this.languageManager.transformLanguageData(defaultIds, format);
                }

                return await this.languageManager.transformLanguageData(validation.validIds, format);
            } catch (error) {
                this.logger.logError('语言数据验证和转换失败', error);
                return format === 'api' ? { "0": "2" } : [2];
            }
        }

        /**
         * 获取语言管理器状态
         * @returns {Object} 语言管理器状态
         */
        getLanguageManagerStatus() {
            if (!this.languageManager) {
                return {
                    available: false,
                    error: '语言管理器未初始化'
                };
            }

            try {
                const status = this.languageManager.getStatus?.() || {};
                const performanceStats = this.languageManager.getPerformanceStats?.() || {};

                return {
                    available: true,
                    status,
                    performanceStats,
                    integrationHealth: 'good'
                };
            } catch (error) {
                return {
                    available: true,
                    error: error.message,
                    integrationHealth: 'degraded'
                };
            }
        }

        /**
         * 获取统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                ...this.stats,
                languageDetections: Object.fromEntries(this.stats.languageDetections),
                otaLanguageMatches: Object.fromEntries(this.stats.otaLanguageMatches),
                multiLanguageRate: this.stats.totalProcessed > 0 ?
                    ((this.stats.multiLanguageOrders / this.stats.totalProcessed) * 100).toFixed(2) + '%' : '0%',
                languageBasedCorrectionRate: this.stats.totalProcessed > 0 ?
                    ((this.stats.languageBasedCorrections / this.stats.totalProcessed) * 100).toFixed(2) + '%' : '0%',
                languageManagerStatus: this.getLanguageManagerStatus()
            };
        }

        /**
         * 重置统计信息
         */
        resetStats() {
            this.stats = {
                totalProcessed: 0,
                languageDetections: new Map(),
                otaLanguageMatches: new Map(),
                multiLanguageOrders: 0,
                languageBasedCorrections: 0
            };
        }
    }

    // 创建全局实例
    function getLanguageOTAIntegration() {
        if (!window.OTA.gemini.core.languageOTAIntegration) {
            window.OTA.gemini.core.languageOTAIntegration = new LanguageOTAIntegration();
        }
        return window.OTA.gemini.core.languageOTAIntegration;
    }

    // 暴露到全局命名空间
    window.OTA.gemini.core.LanguageOTAIntegration = LanguageOTAIntegration;
    window.OTA.gemini.core.getLanguageOTAIntegration = getLanguageOTAIntegration;

    // 向后兼容
    window.getLanguageOTAIntegration = getLanguageOTAIntegration;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('languageOTAIntegration', getLanguageOTAIntegration(), '@LANGUAGE_OTA_INTEGRATION');
        window.OTA.Registry.registerFactory('getLanguageOTAIntegration', getLanguageOTAIntegration, '@LANGUAGE_OTA_INTEGRATION_FACTORY');
    }

    console.log('✅ 语言-OTA集成管理器已加载');

})();
