/**
 * @TEST 用户验收测试套件
 * 🏷️ 标签: @USER_ACCEPTANCE_TEST_SUITE
 * 📝 说明: 从用户角度验证重构后Gemini系统的实际使用场景
 * 🎯 功能: 真实场景测试、用户工作流测试、业务需求验证、用户体验测试
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保测试环境
if (typeof window === 'undefined') {
    global.window = {};
}

// 用户验收测试套件
describe('Gemini系统用户验收测试', function() {
    let geminiService;
    let realWorldScenarios;

    // 测试前置设置
    beforeAll(async function() {
        geminiService = window.OTA?.geminiService;
        
        if (!geminiService) {
            throw new Error('Gemini服务未初始化');
        }

        // 初始化真实世界场景数据
        realWorldScenarios = {
            // 场景1：标准机场接机订单
            airportPickup: `
                【机场接机订单】
                客户姓名：李明华
                联系电话：+60123456789
                微信号：liminghua2024
                
                服务详情：
                接机地点：吉隆坡国际机场KLIA Terminal 1
                航班信息：马来西亚航空 MH370，预计今天下午15:30抵达
                目的地：吉隆坡双子塔丽思卡尔顿酒店
                地址：168 Jalan Imbi, Kuala Lumpur, 55100
                
                乘客信息：2大人1小孩
                行李：2个大箱子，1个手提包
                特殊要求：需要儿童安全座椅，车内准备矿泉水
                
                订单号：AP2024010001
                预订时间：2024-01-01 10:30
                备注：客户是VIP会员，请提供优质服务
            `,

            // 场景2：多站点包车服务
            charterService: `
                【包车服务订单】
                客户：王先生一家四口
                联系方式：+60198765432
                
                服务时间：明天（1月16日）全天8小时
                起始地点：Sunway Resort Hotel
                
                行程安排：
                09:00 - 酒店出发
                10:00 - 云顶高原 (Genting Highlands)
                12:30 - 午餐时间
                14:00 - 黑风洞 (Batu Caves)
                16:00 - 中央市场 (Central Market)
                17:30 - 返回酒店
                
                车型要求：7座商务车
                特殊需求：车内WiFi，充电器，雨伞
                总价：RM 450
                
                参考号：CS2024010002
            `,

            // 场景3：紧急送机订单
            urgentTransfer: `
                【紧急送机】⚠️
                客户：张女士
                电话：+60187654321
                
                紧急情况：原定司机临时有事，需要立即安排替代
                出发地：The Gardens Mall, Mid Valley
                目的地：KLIA2 Terminal
                航班：亚航AK712，起飞时间：今晚21:45
                
                乘客：1人
                行李：1个登机箱
                要求：必须在19:30前到达机场
                
                订单状态：紧急
                创建时间：今天18:45
                备注：客户非常着急，请优先处理
            `,

            // 场景4：团体订单
            groupBooking: `
                【团体预订】
                联系人：陈经理
                公司：ABC旅游公司
                电话：+60176543210
                邮箱：<EMAIL>
                
                团体信息：
                - 团队名称：新加坡商务考察团
                - 人数：15人
                - 行李：每人1个大箱子
                
                服务安排：
                日期：2024年1月20日
                时间：上午10:00
                接机地点：KLIA Terminal 1
                航班：新加坡航空SQ108
                目的地：Mandarin Oriental Hotel KL
                
                车辆安排：需要2辆7座商务车
                特殊要求：
                - 司机需会中文或英文
                - 车内准备欢迎横幅
                - 提供矿泉水和湿纸巾
                
                订单号：GB2024010003
                总金额：RM 280 x 2 = RM 560
            `,

            // 场景5：Chong Dealer特殊格式
            chongDealerOrder: `
                Chong Dealer - 举牌接机服务
                
                订单信息：
                客户：Mr. David Johnson
                联系：+60165432109
                
                服务详情：
                类型：举牌接机 (Meet & Greet)
                地点：KLIA Terminal 1 Arrival Hall
                航班：Emirates EK343
                时间：Tomorrow 14:20 arrival
                举牌内容：DAVID JOHNSON - ABC COMPANY
                
                目的地：Shangri-La Hotel KL
                车型：Premium Sedan
                服务费：RM 180 (含举牌费)
                
                特殊说明：
                - 司机需穿正装
                - 提前30分钟到达
                - 客户可能延误，请耐心等待
                
                Chong Dealer Ref: CD240101004
            `
        };
    });

    // 用户场景1：日常机场接机业务
    describe('场景1：标准机场接机服务', function() {
        
        it('应该完整解析标准接机订单的所有关键信息', async function() {
            const result = await geminiService.parseOrder(realWorldScenarios.airportPickup);
            
            assertTrue(result.success, '标准接机订单解析应该成功');
            assertTrue(result.data, '应该返回完整的解析数据');
            
            // 验证客户信息提取
            assertTrue(result.data.customer_name?.includes('李明华'), '应该正确提取客户姓名');
            assertTrue(result.data.customer_contact?.includes('+60123456789'), '应该提取联系电话');
            
            // 验证服务信息提取
            assertTrue(
                result.data.pickup?.includes('KLIA') || result.data.pickup_location?.includes('KLIA'),
                '应该提取接机地点'
            );
            assertTrue(
                result.data.dropoff?.includes('丽思卡尔顿') || result.data.dropoff_location?.includes('丽思卡尔顿'),
                '应该提取目的地'
            );
            
            // 验证航班信息提取
            if (result.data.flight_info) {
                assertTrue(result.data.flight_info.includes('MH370'), '应该提取航班号');
                assertTrue(result.data.flight_info.includes('15:30'), '应该提取航班时间');
            }
            
            // 验证特殊要求提取
            if (result.data.special_requirements || result.data.notes) {
                const requirements = result.data.special_requirements || result.data.notes || '';
                assertTrue(requirements.includes('儿童') || requirements.includes('安全座椅'), '应该提取特殊要求');
            }
            
            // 验证订单号提取
            if (result.data.ota_reference_number) {
                assertTrue(result.data.ota_reference_number.includes('AP2024010001'), '应该提取订单号');
            }
        });

        it('应该正确识别为接机服务类型', async function() {
            const result = await geminiService.parseOrder(realWorldScenarios.airportPickup);
            
            if (result.data.service_type_id) {
                assertEqual(result.data.service_type_id, 2, '应该识别为接机服务（ID=2）');
            } else if (result.data.service_type) {
                assertTrue(result.data.service_type.includes('pickup') || result.data.service_type.includes('接机'), '应该识别为接机服务');
            }
        });

        it('应该根据乘客人数推荐合适车型', async function() {
            const result = await geminiService.parseOrder(realWorldScenarios.airportPickup);
            
            // 订单中提到"2大人1小孩"，应该推荐5座或7座车型
            if (result.data.car_type_id) {
                assertTrue([1, 2].includes(parseInt(result.data.car_type_id)), '应该推荐合适的车型');
            }
        });
    });

    // 用户场景2：包车服务业务
    describe('场景2：多站点包车服务', function() {
        
        it('应该识别包车服务并提取行程信息', async function() {
            const result = await geminiService.parseOrder(realWorldScenarios.charterService);
            
            assertTrue(result.success, '包车服务订单解析应该成功');
            
            // 验证服务类型识别
            if (result.data.service_type_id) {
                assertEqual(result.data.service_type_id, 4, '应该识别为包车服务（ID=4）');
            }
            
            // 验证客户信息
            assertTrue(result.data.customer_name?.includes('王先生'), '应该提取客户姓名');
            assertTrue(result.data.customer_contact?.includes('+60198765432'), '应该提取联系电话');
            
            // 验证起始地点
            assertTrue(
                result.data.pickup?.includes('Sunway') || result.data.pickup_location?.includes('Sunway'),
                '应该提取起始地点'
            );
            
            // 验证服务时长
            if (result.data.service_duration || result.data.notes) {
                const duration = result.data.service_duration || result.data.notes || '';
                assertTrue(duration.includes('8小时') || duration.includes('8'), '应该提取服务时长');
            }
        });

        it('应该提取详细的行程安排', async function() {
            const result = await geminiService.parseOrder(realWorldScenarios.charterService);
            
            if (result.data.itinerary || result.data.notes || result.data.special_requirements) {
                const itinerary = result.data.itinerary || result.data.notes || result.data.special_requirements || '';
                assertTrue(itinerary.includes('云顶') || itinerary.includes('Genting'), '应该提取行程地点');
                assertTrue(itinerary.includes('黑风洞') || itinerary.includes('Batu'), '应该提取多个行程点');
            }
        });
    });

    // 用户场景3：紧急订单处理
    describe('场景3：紧急送机服务', function() {
        
        it('应该识别紧急订单并提取关键时间信息', async function() {
            const result = await geminiService.parseOrder(realWorldScenarios.urgentTransfer);
            
            assertTrue(result.success, '紧急订单解析应该成功');
            
            // 验证紧急标识
            if (result.data.priority || result.data.notes) {
                const priority = result.data.priority || result.data.notes || '';
                assertTrue(priority.includes('紧急') || priority.includes('urgent'), '应该识别紧急标识');
            }
            
            // 验证送机服务识别
            if (result.data.service_type_id) {
                assertEqual(result.data.service_type_id, 3, '应该识别为送机服务（ID=3）');
            }
            
            // 验证航班信息
            if (result.data.flight_info) {
                assertTrue(result.data.flight_info.includes('AK712'), '应该提取航班号');
                assertTrue(result.data.flight_info.includes('21:45'), '应该提取起飞时间');
            }
        });

        it('应该提取时间约束信息', async function() {
            const result = await geminiService.parseOrder(realWorldScenarios.urgentTransfer);
            
            if (result.data.time_constraints || result.data.notes) {
                const constraints = result.data.time_constraints || result.data.notes || '';
                assertTrue(constraints.includes('19:30') || constraints.includes('1930'), '应该提取时间约束');
            }
        });
    });

    // 用户场景4：团体预订处理
    describe('场景4：团体预订服务', function() {
        
        it('应该识别团体订单并提取团队信息', async function() {
            const result = await geminiService.parseOrder(realWorldScenarios.groupBooking);
            
            assertTrue(result.success, '团体订单解析应该成功');
            
            // 验证联系人信息
            assertTrue(result.data.customer_name?.includes('陈经理'), '应该提取联系人姓名');
            assertTrue(result.data.customer_contact?.includes('+60176543210'), '应该提取联系电话');
            
            // 验证团体信息
            if (result.data.passenger_count || result.data.notes) {
                const passengerInfo = result.data.passenger_count || result.data.notes || '';
                assertTrue(passengerInfo.includes('15') || passengerInfo.includes('十五'), '应该提取人数信息');
            }
            
            // 验证航班信息
            if (result.data.flight_info) {
                assertTrue(result.data.flight_info.includes('SQ108'), '应该提取团体航班号');
            }
        });

        it('应该识别多车辆需求', async function() {
            const result = await geminiService.parseOrder(realWorldScenarios.groupBooking);
            
            if (result.data.vehicle_count || result.data.notes) {
                const vehicleInfo = result.data.vehicle_count || result.data.notes || '';
                assertTrue(vehicleInfo.includes('2') || vehicleInfo.includes('两'), '应该识别多车辆需求');
            }
        });
    });

    // 用户场景5：特殊OTA渠道处理
    describe('场景5：Chong Dealer特殊格式', function() {
        
        it('应该识别Chong Dealer格式并提取举牌信息', async function() {
            const result = await geminiService.parseOrder(realWorldScenarios.chongDealerOrder);
            
            assertTrue(result.success, 'Chong Dealer订单解析应该成功');
            
            // 验证客户信息
            assertTrue(result.data.customer_name?.includes('David Johnson'), '应该提取英文客户姓名');
            assertTrue(result.data.customer_contact?.includes('+60165432109'), '应该提取联系电话');
            
            // 验证举牌服务信息
            if (result.data.service_type || result.data.notes) {
                const serviceInfo = result.data.service_type || result.data.notes || '';
                assertTrue(serviceInfo.includes('举牌') || serviceInfo.includes('Meet'), '应该识别举牌服务');
            }
            
            // 验证Chong Dealer参考号
            if (result.data.ota_reference_number) {
                assertTrue(result.data.ota_reference_number.includes('CD240101004'), '应该提取Chong Dealer参考号');
            }
        });

        it('应该提取举牌内容信息', async function() {
            const result = await geminiService.parseOrder(realWorldScenarios.chongDealerOrder);
            
            if (result.data.sign_content || result.data.notes) {
                const signInfo = result.data.sign_content || result.data.notes || '';
                assertTrue(signInfo.includes('DAVID JOHNSON') || signInfo.includes('ABC COMPANY'), '应该提取举牌内容');
            }
        });
    });

    // 用户工作流测试
    describe('用户工作流测试', function() {
        
        it('应该支持完整的订单处理工作流', async function() {
            // 模拟用户的完整工作流程
            const orderText = realWorldScenarios.airportPickup;
            
            // 步骤1：解析订单
            const parseResult = await geminiService.parseOrder(orderText);
            assertTrue(parseResult.success, '订单解析步骤应该成功');
            
            // 步骤2：获取服务状态
            const status = geminiService.getStatus();
            assertTrue(status.isInitialized, '服务应该处于可用状态');
            
            // 步骤3：验证数据完整性
            if (parseResult.data) {
                const requiredFields = ['customer_name', 'customer_contact'];
                const missingFields = requiredFields.filter(field => !parseResult.data[field]);
                assertTrue(missingFields.length === 0, `不应该缺少必要字段: ${missingFields.join(', ')}`);
            }
            
            // 步骤4：模拟数据提交准备
            if (parseResult.data) {
                // 检查数据是否可以被序列化（模拟API提交）
                try {
                    const jsonData = JSON.stringify(parseResult.data);
                    assertTrue(jsonData.length > 0, '数据应该可以被序列化');
                } catch (error) {
                    assertTrue(false, '数据序列化不应该失败');
                }
            }
        });

        it('应该支持批量订单处理工作流', async function() {
            const batchOrders = [
                realWorldScenarios.airportPickup,
                realWorldScenarios.urgentTransfer,
                realWorldScenarios.charterService
            ];
            
            // 批量处理
            const results = await Promise.all(
                batchOrders.map(order => geminiService.parseOrder(order))
            );
            
            assertEqual(results.length, 3, '应该处理所有批量订单');
            
            // 验证每个结果
            results.forEach((result, index) => {
                assertTrue(result.success, `批量订单${index + 1}应该解析成功`);
                assertTrue(result.data, `批量订单${index + 1}应该有数据`);
            });
            
            // 验证不同类型的订单都被正确识别
            const serviceTypes = results.map(r => r.data?.service_type_id).filter(Boolean);
            assertTrue(serviceTypes.length > 0, '应该识别出服务类型');
        });
    });

    // 用户体验测试
    describe('用户体验测试', function() {
        
        it('应该在合理时间内完成解析', async function() {
            const startTime = Date.now();
            const result = await geminiService.parseOrder(realWorldScenarios.airportPickup);
            const duration = Date.now() - startTime;
            
            assertTrue(result.success, '解析应该成功');
            assertTrue(duration < 10000, `解析时间应该少于10秒，实际：${duration}ms`);
            
            console.log(`用户体验测试 - 解析时间: ${duration}ms`);
        });

        it('应该提供有意义的错误信息', async function() {
            const invalidOrder = '';
            const result = await geminiService.parseOrder(invalidOrder);
            
            // 即使输入无效，也应该有适当的响应
            assertTrue(result, '应该返回结果对象');
            
            if (!result.success && result.error) {
                assertTrue(result.error.length > 0, '错误信息应该有意义');
            }
        });

        it('应该保持一致的数据格式', async function() {
            const orders = [
                realWorldScenarios.airportPickup,
                realWorldScenarios.charterService,
                realWorldScenarios.urgentTransfer
            ];
            
            const results = await Promise.all(
                orders.map(order => geminiService.parseOrder(order))
            );
            
            // 验证所有结果都有一致的结构
            results.forEach((result, index) => {
                assertTrue(typeof result.success === 'boolean', `结果${index + 1}应该有success字段`);
                if (result.success) {
                    assertTrue(typeof result.data === 'object', `结果${index + 1}应该有data对象`);
                }
            });
        });
    });
});

// 日志记录
const logger = window.getLogger?.() || console;
logger.log('用户验收测试套件加载完成', 'info', {
    version: '1.0.0',
    scenarios: [
        'airport_pickup_service',
        'charter_service',
        'urgent_transfer',
        'group_booking',
        'chong_dealer_format'
    ],
    testCategories: [
        'real_world_scenarios',
        'user_workflows',
        'user_experience',
        'business_requirements'
    ]
});
