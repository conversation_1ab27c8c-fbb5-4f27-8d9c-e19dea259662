/**
 * 🎨 多订单UI交互管理器
 * 负责处理多订单系统的所有用户界面交互功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-20
 */

(function() {
    'use strict';

    /**
     * 多订单UI交互管理器类
     * 专门处理用户界面交互、面板显示、编辑操作等功能
     */
    class MultiOrderUIManager {
        /**
         * 构造函数
         * @param {Object} dependencies - 依赖注入对象
         * @param {Object} dependencies.logger - 日志服务
         * @param {Object} dependencies.config - 配置对象
         * @param {Object} dependencies.state - 状态管理器
         * @param {Object} dependencies.utils - 工具函数
         * @param {Object} dependencies.validationManager - 验证管理器
         * @param {Object} dependencies.cleanupManager - 清理管理器
         */
        constructor(dependencies = {}) {
            this.logger = dependencies.logger || this.getLogger();
            this.config = {
                // UI交互配置
                animationDuration: 300,
                dragThreshold: 5,
                autoSaveDelay: 1000,
                maxPanelWidth: '95vw',
                maxPanelHeight: '90vh',
                // 快捷编辑配置
                quickEditFields: [
                    { name: 'customerName', label: '客户姓名', type: 'text', required: true },
                    { name: 'customerContact', label: '联系电话', type: 'tel', required: true },
                    { name: 'pickup', label: '上车地点', type: 'text', required: true },
                    { name: 'dropoff', label: '下车地点', type: 'text', required: true },
                    { name: 'pickupDate', label: '日期', type: 'date', required: true },
                    { name: 'pickupTime', label: '时间', type: 'time', required: true },
                    { name: 'passengerCount', label: '乘客数', type: 'number', min: 1, max: 20 },
                    { name: 'luggageCount', label: '行李数', type: 'number', min: 0, max: 50 },
                    { name: 'price', label: '价格', type: 'number', step: '0.01' },
                    { name: 'otaReferenceNumber', label: 'OTA参考号', type: 'text', required: true }
                ],
                ...dependencies.config
            };

            // 依赖注入
            this.state = dependencies.state;
            this.utils = dependencies.utils;
            this.validationManager = dependencies.validationManager;
            this.cleanupManager = dependencies.cleanupManager;

            // UI状态
            this.isDragging = false;
            this.dragPosition = { x: 0, y: 0 };
            this.currentEditingIndex = -1;
            this.boundEventHandlers = new Map();

            // 初始化
            this.init();
        }

        /**
         * 初始化UI管理器
         */
        init() {
            this.logger?.log('🎨 UI交互管理器初始化开始', 'info');
            
            try {
                // 绑定事件处理器
                this.bindEventHandlers();
                
                this.logger?.log('✅ UI交互管理器初始化完成', 'success');
            } catch (error) {
                this.logger?.logError('UI交互管理器初始化失败', error);
                throw error;
            }
        }

        /**
         * 绑定事件处理器
         */
        bindEventHandlers() {
            // 绑定快捷编辑ESC处理器
            this.boundEventHandlers.set('quickEditEscape', this.handleQuickEditEscape.bind(this));
        }

        /**
         * 显示多订单面板
         * @param {Array} orders - 订单数组
         */
        showMultiOrderPanel(orders) {
            this.logger?.log('🎨 显示多订单面板', 'info', { orderCount: orders?.length });

            try {
                // 存储订单数据到状态
                if (this.state) {
                    this.state.parsedOrders = orders;
                    this.state.selectedOrders = new Set();
                    this.state.processedOrders = new Map();
                }

                // 获取面板元素
                const multiOrderPanel = document.getElementById('multiOrderPanel');
                if (!multiOrderPanel) {
                    this.logger?.logError('多订单面板DOM元素不存在');
                    return;
                }

                // 显示面板
                multiOrderPanel.classList.remove('hidden');
                multiOrderPanel.style.display = 'block';

                // 更新面板内容
                this.updatePanelContent(orders);

                // 确保面板可见
                this.ensurePanelVisible();

                // 初始化面板事件（如果尚未初始化）
                this.initPanelEvents();

                this.logger?.log('✅ 多订单面板显示完成', 'success');
            } catch (error) {
                this.logger?.logError('显示多订单面板失败', error);
                throw error;
            }
        }

        /**
         * 隐藏多订单面板
         */
        hideMultiOrderPanel() {
            this.logger?.log('🎨 隐藏多订单面板', 'info');

            try {
                const multiOrderPanel = document.getElementById('multiOrderPanel');
                if (multiOrderPanel) {
                    multiOrderPanel.classList.add('hidden');
                    multiOrderPanel.style.display = 'none';
                }

                // 清理快捷编辑面板
                this.cleanupQuickEditPanels();

                // 重置拖拽状态
                this.resetDragState();

                this.logger?.log('✅ 多订单面板已隐藏', 'success');
            } catch (error) {
                this.logger?.logError('隐藏多订单面板失败', error);
            }
        }

        /**
         * 更新面板内容
         * @param {Array} orders - 订单数组
         */
        updatePanelContent(orders) {
            this.logger?.log('🔧 更新面板内容', 'info', { orderCount: orders?.length });
            
            try {
                const orderList = document.querySelector('#multiOrderPanel .multi-order-list');
                if (!orderList) {
                    this.logger?.logError('订单列表容器不存在');
                    return;
                }

                // 生成订单卡片HTML
                const orderCardsHTML = orders.map((order, index) => {
                    return this.generateOrderCardHTML(order, index);
                }).join('');

                // 更新DOM
                orderList.innerHTML = orderCardsHTML;

                // 绑定订单卡片事件
                this.bindOrderCardEvents();

                // 更新选中数量显示
                this.updateSelectedCount();

                this.logger?.log('✅ 面板内容更新完成', 'success');
            } catch (error) {
                this.logger?.logError('更新面板内容失败', error);
                throw error;
            }
        }

        /**
         * 生成订单卡片HTML
         * @param {Object} order - 订单对象
         * @param {number} index - 订单索引
         * @returns {string} HTML字符串
         */
        generateOrderCardHTML(order, index) {
            const isPagingOrder = order.serviceType === 'paging' || order.serviceTypeId === 1;
            const summary = this.generateOrderSummary(order, index);

            return `
                <div class="order-card ${isPagingOrder ? 'paging-order' : ''}" data-order-index="${index}" 
                     onclick="window.OTA.multiOrderManager.toggleOrderSelection(${index})" 
                     style="cursor: pointer;">
                    <div class="order-card-header">
                        <div class="order-checkbox-wrapper">
                            <input type="checkbox" id="order-${index}" class="order-checkbox" 
                                   onchange="this.closest('.order-card').classList.toggle('selected', this.checked); window.OTA.multiOrderManager.updateSelectedCount();">
                            <label for="order-${index}" class="order-number">订单 ${index + 1}</label>
                        </div>
                        <div class="order-status">
                            <span class="status-icon status-pending" title="待处理">⏳</span>
                        </div>
                    </div>
                    <div class="order-card-body" onclick="window.OTA.multiOrderManager.quickEditOrder(${index}); event.stopPropagation();">
                        ${summary}
                    </div>
                </div>
            `;
        }

        /**
         * 生成订单摘要HTML
         * @param {Object} order - 订单对象
         * @param {number} index - 订单索引
         * @returns {string} HTML字符串
         */
        generateOrderSummary(order, index) {
            const currency = order.currency || 'RM';
            const price = this.utils?.formatPrice(order.price || order.otaPrice) || '0.00';

            return `
                <div class="order-summary order-grid-layout extended">
                    <div class="order-grid-left">
                        <div class="grid-item editable-field" data-field="customerName" onclick="window.OTA.multiOrderManager.editField(${index}, 'customerName', event)">
                            <span class="grid-label">👤</span>
                            <span class="grid-value">${order.customerName || '未提供'}</span>
                            <span class="edit-indicator">✏️</span>
                        </div>
                        <div class="grid-item grid-item-route">
                            <span class="grid-label">🚗</span>
                            <div class="grid-value">${this.formatRoute(order, index)}</div>
                        </div>
                        <div class="grid-item editable-field" data-field="pickupDate" onclick="window.OTA.multiOrderManager.editField(${index}, 'pickupDate', event)">
                            <span class="grid-label">📅</span>
                            <span class="grid-value">${order.pickupDate || '未指定'}</span>
                            <span class="edit-indicator">✏️</span>
                        </div>
                        <div class="grid-item editable-field" data-field="pickupTime" onclick="window.OTA.multiOrderManager.editField(${index}, 'pickupTime', event)">
                            <span class="grid-label">⏰</span>
                            <span class="grid-value">${order.pickupTime || '未指定'}</span>
                            <span class="edit-indicator">✏️</span>
                        </div>
                        <div class="grid-item editable-field" data-field="customerContact" onclick="window.OTA.multiOrderManager.editField(${index}, 'customerContact', event)">
                            <span class="grid-label">📞</span>
                            <span class="grid-value">${this.utils?.formatPhone(order.customerContact) || '未提供'}</span>
                            <span class="edit-indicator">✏️</span>
                        </div>
                    </div>
                    <div class="order-grid-right">
                        <div class="grid-item editable-field" data-field="passengerCount" onclick="window.OTA.multiOrderManager.editField(${index}, 'passengerCount', event)">
                            <span class="grid-label">👥</span>
                            <span class="grid-value">${order.passengerCount || 1}人</span>
                            <span class="edit-indicator">✏️</span>
                        </div>
                        <div class="grid-item editable-field" data-field="luggageCount" onclick="window.OTA.multiOrderManager.editField(${index}, 'luggageCount', event)">
                            <span class="grid-label">🧳</span>
                            <span class="grid-value">${order.luggageCount || 0}件</span>
                            <span class="edit-indicator">✏️</span>
                        </div>
                        <div class="grid-item editable-field" data-field="price" onclick="window.OTA.multiOrderManager.editField(${index}, 'price', event)">
                            <span class="grid-label">💰</span>
                            <span class="grid-value">${currency}${price}</span>
                            <span class="edit-indicator">✏️</span>
                        </div>
                        <div class="grid-item editable-field" data-field="vehicleType" onclick="window.OTA.multiOrderManager.editField(${index}, 'vehicleType', event)">
                            <span class="grid-label">🚗</span>
                            <span class="grid-value">${this.utils?.getCarTypeName(order.carTypeId) || '未指定'}</span>
                            <span class="edit-indicator">✏️</span>
                        </div>
                    </div>
                </div>
                
                <div class="order-actions compact-inline-layout">
                    <button type="button" class="btn-create btn-compact" onclick="window.getMultiOrderManager().createSingleOrder(${index})">
                        创建
                    </button>
                </div>
            `;
        }

        /**
         * 格式化路线显示
         * @param {Object} order - 订单对象
         * @param {number} index - 订单索引
         * @returns {string} 格式化的路线HTML
         */
        formatRoute(order, index) {
            const pickup = order.pickup || order.pickupLocation || '未指定';
            const dropoff = order.dropoff || order.dropoffLocation || '未指定';
            
            return `
                <div class="route-display">
                    <div class="pickup-address editable" onclick="window.OTA.multiOrderManager.editAddress(${index}, 'pickup', event)">
                        <span class="address-label">上车:</span>
                        <span class="address-text">${pickup}</span>
                    </div>
                    <div class="dropoff-address editable" onclick="window.OTA.multiOrderManager.editAddress(${index}, 'dropoff', event)">
                        <span class="address-label">下车:</span>
                        <span class="address-text">${dropoff}</span>
                    </div>
                </div>
            `;
        }

        /**
         * 绑定订单卡片事件
         */
        bindOrderCardEvents() {
            // 绑定复选框变化事件
            const checkboxes = document.querySelectorAll('.order-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', () => {
                    const card = checkbox.closest('.order-card');
                    if (card) {
                        card.classList.toggle('selected', checkbox.checked);
                    }
                    this.updateSelectedCount();
                });
            });
        }

        /**
         * 更新选中数量显示
         */
        updateSelectedCount() {
            const selectedCount = document.querySelectorAll('.order-checkbox:checked').length;
            const totalCount = document.querySelectorAll('.order-checkbox').length;

            const countElement = document.getElementById('selectedCount');
            if (countElement) {
                countElement.textContent = `${selectedCount}/${totalCount}`;
            }

            // 更新批量操作按钮状态
            const batchButtons = document.querySelectorAll('.batch-action-btn');
            batchButtons.forEach(btn => {
                btn.disabled = selectedCount === 0;
            });
        }

        /**
         * 切换订单选择状态
         * @param {number} index - 订单索引
         */
        toggleOrderSelection(index) {
            const checkbox = document.getElementById(`order-${index}`);
            if (checkbox) {
                checkbox.checked = !checkbox.checked;
                checkbox.dispatchEvent(new Event('change'));

                const card = document.querySelector(`.order-card[data-order-index="${index}"]`);
                if (card) {
                    card.classList.toggle('selected', checkbox.checked);
                }
            }
        }

        /**
         * 切换语言下拉菜单
         */
        toggleLanguageDropdown() {
            const content = document.getElementById('languageDropdownContent');
            const arrow = document.querySelector('#batchLanguageDropdown .dropdown-arrow');
            if (content) {
                const isVisible = content.style.display === 'block';
                content.style.display = isVisible ? 'none' : 'block';
                if (arrow) {
                    arrow.textContent = isVisible ? '▼' : '▲';
                }
            }
        }

        /**
         * 初始化面板事件
         */
        initPanelEvents() {
            // 避免重复绑定
            const multiOrderPanel = document.getElementById('multiOrderPanel');
            if (!multiOrderPanel || multiOrderPanel.dataset.eventsInitialized === 'true') {
                return;
            }

            // 关闭按钮
            const closeBtn = document.getElementById('closeMultiOrderBtn');
            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    this.hideMultiOrderPanel();
                    this.logger?.log('多订单面板已关闭', 'info');
                });
            }

            // 全选/取消全选按钮
            const selectAllBtn = document.getElementById('selectAllOrdersBtn');
            const deselectAllBtn = document.getElementById('deselectAllOrdersBtn');

            if (selectAllBtn) {
                selectAllBtn.addEventListener('click', () => this.selectAllOrders(true));
            }

            if (deselectAllBtn) {
                deselectAllBtn.addEventListener('click', () => this.selectAllOrders(false));
            }

            // 批量操作按钮
            const validateAllBtn = document.getElementById('validateAllOrdersBtn');
            if (validateAllBtn) {
                validateAllBtn.addEventListener('click', () => this.processAllOrders());
            }

            const batchCreateBtn = document.getElementById('batchCreateOrdersBtn');
            const createSelectedBtn = document.getElementById('createSelectedOrdersBtn');

            if (batchCreateBtn) {
                batchCreateBtn.addEventListener('click', () => this.handleBatchCreate());
            }

            if (createSelectedBtn) {
                createSelectedBtn.addEventListener('click', () => this.createSelectedOrders());
            }

            // 点击面板外部关闭
            multiOrderPanel.addEventListener('click', (event) => {
                if (event.target === multiOrderPanel) {
                    this.hideMultiOrderPanel();
                }
            });

            // 添加面板拖拽功能
            this.addPanelDragFeature();

            // 返回主页按钮
            const backToMainBtn = document.getElementById('backToMainBtn');
            if (backToMainBtn) {
                backToMainBtn.addEventListener('click', () => {
                    this.hideMultiOrderPanel();
                    this.logger?.log('返回主页', 'info');
                });
            }

            // 标记事件已初始化
            multiOrderPanel.dataset.eventsInitialized = 'true';
            this.logger?.log('📱 面板事件初始化完成', 'info');
        }

        /**
         * 选择/取消选择所有订单
         * @param {boolean} selectAll - 是否全选
         */
        selectAllOrders(selectAll) {
            const checkboxes = document.querySelectorAll('.order-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll;
                const card = checkbox.closest('.order-card');
                if (card) {
                    card.classList.toggle('selected', selectAll);
                }
            });
            this.updateSelectedCount();
        }

        /**
         * 确保面板在视窗范围内可见
         */
        ensurePanelVisible() {
            const multiOrderPanel = document.getElementById('multiOrderPanel');
            if (!multiOrderPanel) return;

            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            // 获取面板的实际尺寸
            const panelRect = multiOrderPanel.getBoundingClientRect();

            // 如果面板超出视窗范围，调整其大小
            let adjustments = {};

            if (panelRect.width > viewportWidth * 0.95) {
                adjustments.width = this.config.maxPanelWidth;
            }

            if (panelRect.height > viewportHeight * 0.9) {
                adjustments.height = this.config.maxPanelHeight;
            }

            // 应用调整
            Object.assign(multiOrderPanel.style, adjustments);

            // 滚动到面板位置（如果需要）
            multiOrderPanel.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
                inline: 'center'
            });

            this.logger?.log('📱 多订单面板位置已优化', 'info', {
                viewport: `${viewportWidth}x${viewportHeight}`,
                panel: `${panelRect.width}x${panelRect.height}`,
                adjustments
            });
        }

        /**
         * 添加面板拖拽功能
         */
        addPanelDragFeature() {
            const multiOrderPanel = document.getElementById('multiOrderPanel');
            const header = multiOrderPanel?.querySelector('.multi-order-header');

            if (!multiOrderPanel || !header) return;

            // 检查是否已经添加了拖拽功能，避免重复绑定
            if (header.dataset.dragEnabled === 'true') {
                this.logger?.log('🔒 面板拖拽功能已存在，跳过重复绑定', 'info');
                return;
            }

            let isDragging = false;
            let currentX = 0;
            let currentY = 0;
            let initialX = 0;
            let initialY = 0;

            // 设置拖拽样式
            header.style.cursor = 'move';
            header.title = '拖拽面板来移动位置';

            const dragStart = (e) => {
                if (e.target.closest('button')) return; // 避免按钮干扰拖拽

                initialX = e.clientX - currentX;
                initialY = e.clientY - currentY;

                if (e.target === header || header.contains(e.target)) {
                    isDragging = true;
                    multiOrderPanel.style.transition = 'none';
                }
            };

            const dragEnd = () => {
                initialX = currentX;
                initialY = currentY;
                isDragging = false;
                multiOrderPanel.style.transition = 'all var(--transition-normal)';
            };

            const drag = (e) => {
                if (isDragging) {
                    e.preventDefault();
                    currentX = e.clientX - initialX;
                    currentY = e.clientY - initialY;

                    // 限制拖拽范围在视窗内
                    const rect = multiOrderPanel.getBoundingClientRect();
                    const maxX = window.innerWidth - rect.width;
                    const maxY = window.innerHeight - rect.height;

                    currentX = Math.max(0, Math.min(currentX, maxX));
                    currentY = Math.max(0, Math.min(currentY, maxY));

                    multiOrderPanel.style.transform = `translate(${currentX}px, ${currentY}px)`;
                }
            };

            header.addEventListener('mousedown', dragStart);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', dragEnd);

            // 标记拖拽功能已启用
            header.dataset.dragEnabled = 'true';

            this.logger?.log('🖱️ 面板拖拽功能已启用', 'info');
        }

        /**
         * 重置拖拽状态
         */
        resetDragState() {
            this.isDragging = false;
            this.dragPosition = { x: 0, y: 0 };

            const multiOrderPanel = document.getElementById('multiOrderPanel');
            if (multiOrderPanel) {
                multiOrderPanel.style.transform = '';
                multiOrderPanel.style.transition = '';
            }
        }

        /**
         * 处理快捷编辑ESC键事件
         * @param {KeyboardEvent} event - 键盘事件
         */
        handleQuickEditEscape(event) {
            if (event.key === 'Escape') {
                this.logger?.log('🔙 ESC键触发，退出快捷编辑模式', 'info');
                this.cleanupQuickEditPanels();
                event.preventDefault();
                event.stopPropagation();
            }
        }

        /**
         * 清理快捷编辑面板
         */
        cleanupQuickEditPanels() {
            if (this.cleanupManager && typeof this.cleanupManager.cleanupQuickEditPanels === 'function') {
                this.cleanupManager.cleanupQuickEditPanels();
            } else {
                // 降级方案
                document.querySelectorAll('.quick-edit-overlay').forEach(el => el.remove());
            }
        }

        /**
         * 获取日志服务
         * @returns {Object} 日志服务对象
         */
        getLogger() {
            return window.getLogger?.() || {
                log: console.log.bind(console),
                logError: console.error.bind(console)
            };
        }
    }

    /**
     * 创建UI管理器实例的工厂函数
     * @param {Object} dependencies - 依赖注入对象
     * @returns {MultiOrderUIManager} UI管理器实例
     */
    function createMultiOrderUIManager(dependencies = {}) {
        return new MultiOrderUIManager(dependencies);
    }

    // 导出到全局作用域
    window.getMultiOrderUIManager = createMultiOrderUIManager;
    window.MultiOrderUIManager = MultiOrderUIManager;

    // 确保OTA命名空间存在
    if (typeof window.OTA === 'undefined') {
        window.OTA = {};
    }
    if (typeof window.OTA.multiOrder === 'undefined') {
        window.OTA.multiOrder = {};
    }

    // 注册到OTA命名空间
    window.OTA.multiOrder.UIManager = MultiOrderUIManager;
    window.OTA.multiOrder.getUIManager = createMultiOrderUIManager;

})();
