/**
 * @OTA_CORE 集成测试协调器
 * 🏷️ 标签: @INTEGRATION_TEST_COORDINATOR @SYSTEM_VALIDATION
 * 📝 说明: 协调和执行系统级集成测试，验证各模块间的协同工作
 * ⚠️ 警告: 核心集成测试基础设施，请勿重复开发
 */

(function() {
    'use strict';

    // 延迟获取依赖，确保加载顺序
    function getConfigCenter() {
        return window.OTA?.configCenter || window.getConfigCenter?.();
    }

    function getTestCoverageEngine() {
        return window.OTA?.testCoverageEngine || window.getTestCoverageEngine?.();
    }

    function getDOMOptimizationEngine() {
        return window.OTA?.domOptimizationEngine || window.getDOMOptimizationEngine?.();
    }

    /**
     * @OTA_CORE 集成测试协调器类
     * 提供系统级集成测试协调和执行功能
     */
    class IntegrationTestCoordinator {
        constructor() {
            this.logger = window.OTA.getService('logger');
            this.configCenter = getConfigCenter();
            this.coverageEngine = getTestCoverageEngine();
            this.domEngine = getDOMOptimizationEngine();
            
            // 集成测试套件注册表
            this.integrationSuites = new Map();
            
            // 测试结果存储
            this.testResults = new Map();
            
            // 系统健康状态
            this.systemHealth = {
                overall: 'unknown',
                modules: new Map(),
                lastCheck: null,
                issues: []
            };
            
            // 集成测试配置
            this.testConfig = this.loadTestConfig();
            
            // 依赖关系图
            this.dependencyGraph = new Map();
            
            this.initialize();
            
            this.logger.log('✅ 集成测试协调器已初始化', 'info', {
                suitesRegistered: this.integrationSuites.size,
                healthCheckEnabled: this.testConfig.healthCheck.enabled,
                dependencyTracking: this.testConfig.dependencyTracking
            });
        }

        /**
         * 加载测试配置
         * @returns {Object} 测试配置
         */
        loadTestConfig() {
            const defaultConfig = {
                healthCheck: {
                    enabled: true,
                    interval: 60000, // 1分钟
                    timeout: 30000
                },
                dependencyTracking: true,
                parallelExecution: false,
                failFast: true,
                retryAttempts: 2,
                reporting: {
                    detailed: true,
                    includePerformance: true,
                    includeDependencies: true
                },
                validation: {
                    crossModuleData: true,
                    stateConsistency: true,
                    performanceThresholds: true
                }
            };

            if (this.configCenter) {
                const testConfig = this.configCenter.getConfig('testing') || {};
                return {
                    ...defaultConfig,
                    ...testConfig.integration
                };
            }

            return defaultConfig;
        }

        /**
         * 初始化协调器
         */
        initialize() {
            // 注册核心集成测试套件
            this.registerCoreIntegrationSuites();
            
            // 构建依赖关系图
            this.buildDependencyGraph();
            
            // 启动健康检查
            if (this.testConfig.healthCheck.enabled) {
                this.startHealthCheck();
            }
        }

        /**
         * 注册核心集成测试套件
         */
        registerCoreIntegrationSuites() {
            // 配置管理集成测试
            this.registerIntegrationSuite('config-management', {
                name: '配置管理集成测试',
                description: '验证统一配置中心与各模块的集成',
                dependencies: ['configCenter'],
                testFunction: this.testConfigManagementIntegration.bind(this)
            });

            // DOM优化集成测试
            this.registerIntegrationSuite('dom-optimization', {
                name: 'DOM优化集成测试',
                description: '验证DOM优化引擎与UI组件的集成',
                dependencies: ['domOptimizationEngine', 'domHelper'],
                testFunction: this.testDOMOptimizationIntegration.bind(this)
            });

            // 测试覆盖率集成测试
            this.registerIntegrationSuite('test-coverage', {
                name: '测试覆盖率集成测试',
                description: '验证测试覆盖率引擎与自动化测试的集成',
                dependencies: ['testCoverageEngine', 'automatedTestRunner'],
                testFunction: this.testCoverageIntegration.bind(this)
            });

            // 跨模块数据流测试
            this.registerIntegrationSuite('cross-module-data', {
                name: '跨模块数据流测试',
                description: '验证模块间数据传递和状态同步',
                dependencies: ['configCenter', 'domOptimizationEngine', 'testCoverageEngine'],
                testFunction: this.testCrossModuleDataFlow.bind(this)
            });
        }

        /**
         * 注册集成测试套件
         * @param {string} id - 套件ID
         * @param {Object} suite - 套件配置
         */
        registerIntegrationSuite(id, suite) {
            this.integrationSuites.set(id, {
                id,
                ...suite,
                status: 'registered',
                lastRun: null,
                results: null
            });
            
            this.logger.log(`集成测试套件已注册: ${id}`, 'debug');
        }

        /**
         * 构建依赖关系图
         */
        buildDependencyGraph() {
            const modules = [
                'configCenter',
                'domOptimizationEngine', 
                'domHelper',
                'testCoverageEngine',
                'automatedTestRunner'
            ];

            modules.forEach(module => {
                this.dependencyGraph.set(module, {
                    name: module,
                    dependencies: this.getModuleDependencies(module),
                    dependents: this.getModuleDependents(module),
                    status: this.checkModuleAvailability(module)
                });
            });
        }

        /**
         * 获取模块依赖
         * @param {string} module - 模块名
         * @returns {Array} 依赖列表
         */
        getModuleDependencies(module) {
            const dependencyMap = {
                'configCenter': ['logger'],
                'domOptimizationEngine': ['logger', 'configCenter'],
                'domHelper': ['logger', 'domOptimizationEngine'],
                'testCoverageEngine': ['logger', 'configCenter'],
                'automatedTestRunner': ['logger', 'configCenter', 'testCoverageEngine']
            };

            return dependencyMap[module] || [];
        }

        /**
         * 获取模块依赖者
         * @param {string} module - 模块名
         * @returns {Array} 依赖者列表
         */
        getModuleDependents(module) {
            const dependentMap = {
                'logger': ['configCenter', 'domOptimizationEngine', 'domHelper', 'testCoverageEngine', 'automatedTestRunner'],
                'configCenter': ['domOptimizationEngine', 'testCoverageEngine', 'automatedTestRunner'],
                'domOptimizationEngine': ['domHelper'],
                'testCoverageEngine': ['automatedTestRunner']
            };

            return dependentMap[module] || [];
        }

        /**
         * 检查模块可用性
         * @param {string} module - 模块名
         * @returns {string} 状态
         */
        checkModuleAvailability(module) {
            const moduleMap = {
                'configCenter': () => window.OTA?.configCenter,
                'domOptimizationEngine': () => window.OTA?.domOptimizationEngine,
                'domHelper': () => window.OTA?.domHelper,
                'testCoverageEngine': () => window.OTA?.testCoverageEngine,
                'automatedTestRunner': () => window.OTA?.automatedTestRunner,
                'logger': () => window.OTA?.logger || window.getLogger
            };

            try {
                const moduleInstance = moduleMap[module]?.();
                return moduleInstance ? 'available' : 'unavailable';
            } catch (error) {
                return 'error';
            }
        }

        /**
         * 运行所有集成测试
         * @returns {Promise<Object>} 测试结果
         */
        async runAllIntegrationTests() {
            const startTime = Date.now();
            const results = new Map();
            
            this.logger.log('开始运行所有集成测试', 'info');
            
            // 检查系统健康状态
            const healthCheck = await this.performHealthCheck();
            if (!healthCheck.healthy && this.testConfig.failFast) {
                return {
                    success: false,
                    error: '系统健康检查失败',
                    healthCheck,
                    duration: Date.now() - startTime
                };
            }
            
            // 按依赖顺序执行测试
            const executionOrder = this.calculateExecutionOrder();
            
            for (const suiteId of executionOrder) {
                try {
                    const result = await this.runIntegrationSuite(suiteId);
                    results.set(suiteId, result);
                    
                    if (!result.success && this.testConfig.failFast) {
                        this.logger.log(`快速失败: 停止执行剩余测试 (${suiteId} 失败)`, 'warning');
                        break;
                    }
                } catch (error) {
                    this.logger.logError(`集成测试套件执行失败: ${suiteId}`, error);
                    results.set(suiteId, {
                        success: false,
                        error: error.message
                    });
                    
                    if (this.testConfig.failFast) {
                        break;
                    }
                }
            }
            
            const endTime = Date.now();
            const totalDuration = endTime - startTime;
            
            // 生成综合报告
            const report = this.generateIntegrationReport(results, totalDuration, healthCheck);
            
            this.logger.log('所有集成测试执行完成', 'info', {
                totalSuites: results.size,
                duration: totalDuration,
                success: report.summary.success
            });
            
            return report;
        }

        /**
         * 运行单个集成测试套件
         * @param {string} suiteId - 套件ID
         * @returns {Promise<Object>} 测试结果
         */
        async runIntegrationSuite(suiteId) {
            const suite = this.integrationSuites.get(suiteId);
            if (!suite) {
                throw new Error(`集成测试套件未找到: ${suiteId}`);
            }

            const startTime = Date.now();
            this.logger.log(`开始运行集成测试套件: ${suiteId}`, 'info');

            try {
                // 检查依赖
                const dependencyCheck = this.checkSuiteDependencies(suite);
                if (!dependencyCheck.satisfied) {
                    throw new Error(`依赖检查失败: ${dependencyCheck.missing.join(', ')}`);
                }

                // 执行测试
                const testResult = await suite.testFunction();
                
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                // 更新套件状态
                suite.status = testResult.success ? 'passed' : 'failed';
                suite.lastRun = endTime;
                suite.results = testResult;
                
                // 存储结果
                this.testResults.set(suiteId, {
                    ...testResult,
                    duration,
                    timestamp: endTime,
                    dependencies: dependencyCheck
                });
                
                this.logger.log(`集成测试套件完成: ${suiteId} (${duration}ms)`, 'info');
                
                return {
                    success: testResult.success,
                    results: testResult,
                    duration,
                    dependencies: dependencyCheck
                };
                
            } catch (error) {
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                suite.status = 'error';
                suite.lastRun = endTime;
                
                this.logger.logError(`集成测试套件执行失败: ${suiteId}`, error);
                
                return {
                    success: false,
                    error: error.message,
                    duration
                };
            }
        }

        /**
         * 配置管理集成测试
         * @returns {Promise<Object>} 测试结果
         */
        async testConfigManagementIntegration() {
            const results = {
                success: true,
                tests: [],
                issues: []
            };

            try {
                // 测试配置中心可用性
                if (this.configCenter) {
                    results.tests.push({
                        name: '配置中心可用性',
                        passed: true,
                        message: '配置中心已加载'
                    });

                    // 测试配置获取
                    const testConfig = this.configCenter.getConfig('testing');
                    results.tests.push({
                        name: '配置获取功能',
                        passed: !!testConfig,
                        message: testConfig ? '配置获取成功' : '配置获取失败'
                    });

                    // 测试配置监听
                    if (typeof this.configCenter.addListener === 'function') {
                        results.tests.push({
                            name: '配置监听功能',
                            passed: true,
                            message: '配置监听器可用'
                        });
                    }
                } else {
                    results.success = false;
                    results.issues.push('配置中心未找到');
                }

            } catch (error) {
                results.success = false;
                results.issues.push(`配置管理集成测试失败: ${error.message}`);
            }

            return results;
        }

        /**
         * DOM优化集成测试
         * @returns {Promise<Object>} 测试结果
         */
        async testDOMOptimizationIntegration() {
            const results = {
                success: true,
                tests: [],
                issues: []
            };

            try {
                // 测试DOM优化引擎
                if (this.domEngine) {
                    results.tests.push({
                        name: 'DOM优化引擎可用性',
                        passed: true,
                        message: 'DOM优化引擎已加载'
                    });

                    // 测试性能指标
                    const metrics = this.domEngine.getPerformanceMetrics();
                    results.tests.push({
                        name: '性能指标获取',
                        passed: !!metrics,
                        message: metrics ? '性能指标可用' : '性能指标获取失败'
                    });

                    // 测试DOM助手集成
                    const domHelper = window.OTA?.domHelper;
                    if (domHelper) {
                        results.tests.push({
                            name: 'DOM助手集成',
                            passed: true,
                            message: 'DOM助手与优化引擎集成正常'
                        });
                    }
                } else {
                    results.success = false;
                    results.issues.push('DOM优化引擎未找到');
                }

            } catch (error) {
                results.success = false;
                results.issues.push(`DOM优化集成测试失败: ${error.message}`);
            }

            return results;
        }

        /**
         * 测试覆盖率集成测试
         * @returns {Promise<Object>} 测试结果
         */
        async testCoverageIntegration() {
            const results = {
                success: true,
                tests: [],
                issues: []
            };

            try {
                // 测试覆盖率引擎
                if (this.coverageEngine) {
                    results.tests.push({
                        name: '测试覆盖率引擎可用性',
                        passed: true,
                        message: '测试覆盖率引擎已加载'
                    });

                    // 测试质量指标
                    const metrics = this.coverageEngine.getQualityMetrics();
                    results.tests.push({
                        name: '质量指标获取',
                        passed: !!metrics,
                        message: metrics ? '质量指标可用' : '质量指标获取失败'
                    });

                    // 测试自动化测试执行器集成
                    const testRunner = window.OTA?.automatedTestRunner;
                    if (testRunner) {
                        results.tests.push({
                            name: '自动化测试执行器集成',
                            passed: true,
                            message: '测试执行器与覆盖率引擎集成正常'
                        });
                    }
                } else {
                    results.success = false;
                    results.issues.push('测试覆盖率引擎未找到');
                }

            } catch (error) {
                results.success = false;
                results.issues.push(`测试覆盖率集成测试失败: ${error.message}`);
            }

            return results;
        }

        /**
         * 跨模块数据流测试
         * @returns {Promise<Object>} 测试结果
         */
        async testCrossModuleDataFlow() {
            const results = {
                success: true,
                tests: [],
                issues: []
            };

            try {
                // 测试配置数据流
                if (this.configCenter && this.domEngine) {
                    const configData = this.configCenter.getConfig('ui');
                    results.tests.push({
                        name: '配置数据流传递',
                        passed: true,
                        message: '配置数据可在模块间传递'
                    });
                }

                // 测试状态同步
                const modules = ['configCenter', 'domOptimizationEngine', 'testCoverageEngine'];
                const moduleStates = modules.map(module => {
                    const moduleInstance = window.OTA?.[module];
                    return {
                        module,
                        available: !!moduleInstance,
                        status: moduleInstance?.getStatus?.() || null
                    };
                });

                const availableModules = moduleStates.filter(m => m.available).length;
                results.tests.push({
                    name: '模块状态同步',
                    passed: availableModules >= 2,
                    message: `${availableModules}个模块状态可获取`
                });

            } catch (error) {
                results.success = false;
                results.issues.push(`跨模块数据流测试失败: ${error.message}`);
            }

            return results;
        }

        /**
         * 检查套件依赖
         * @param {Object} suite - 测试套件
         * @returns {Object} 依赖检查结果
         */
        checkSuiteDependencies(suite) {
            const missing = [];
            const available = [];

            suite.dependencies.forEach(dep => {
                const status = this.checkModuleAvailability(dep);
                if (status === 'available') {
                    available.push(dep);
                } else {
                    missing.push(dep);
                }
            });

            return {
                satisfied: missing.length === 0,
                available,
                missing,
                total: suite.dependencies.length
            };
        }

        /**
         * 计算执行顺序
         * @returns {Array} 执行顺序
         */
        calculateExecutionOrder() {
            // 简化的拓扑排序实现
            const suiteIds = Array.from(this.integrationSuites.keys());
            
            // 按依赖数量排序，依赖少的先执行
            return suiteIds.sort((a, b) => {
                const suiteA = this.integrationSuites.get(a);
                const suiteB = this.integrationSuites.get(b);
                return suiteA.dependencies.length - suiteB.dependencies.length;
            });
        }

        /**
         * 执行健康检查
         * @returns {Promise<Object>} 健康检查结果
         */
        async performHealthCheck() {
            const startTime = Date.now();
            const moduleHealth = new Map();
            const issues = [];

            // 检查各模块健康状态
            for (const [moduleName, moduleInfo] of this.dependencyGraph) {
                try {
                    const status = this.checkModuleAvailability(moduleName);
                    moduleHealth.set(moduleName, {
                        status,
                        available: status === 'available',
                        lastCheck: Date.now()
                    });

                    if (status !== 'available') {
                        issues.push(`模块不可用: ${moduleName}`);
                    }
                } catch (error) {
                    moduleHealth.set(moduleName, {
                        status: 'error',
                        available: false,
                        error: error.message,
                        lastCheck: Date.now()
                    });
                    issues.push(`模块检查失败: ${moduleName} - ${error.message}`);
                }
            }

            const healthyModules = Array.from(moduleHealth.values()).filter(m => m.available).length;
            const totalModules = moduleHealth.size;
            const healthPercentage = totalModules > 0 ? (healthyModules / totalModules) * 100 : 0;

            const result = {
                healthy: healthPercentage >= 80, // 80%以上模块健康才算系统健康
                healthPercentage: healthPercentage.toFixed(1),
                modules: moduleHealth,
                issues,
                duration: Date.now() - startTime,
                timestamp: Date.now()
            };

            // 更新系统健康状态
            this.systemHealth = {
                overall: result.healthy ? 'healthy' : 'unhealthy',
                modules: moduleHealth,
                lastCheck: Date.now(),
                issues
            };

            return result;
        }

        /**
         * 启动健康检查
         */
        startHealthCheck() {
            setInterval(async () => {
                try {
                    await this.performHealthCheck();
                } catch (error) {
                    this.logger.logError('健康检查失败', error);
                }
            }, this.testConfig.healthCheck.interval);
        }

        /**
         * 生成集成报告
         * @param {Map} results - 测试结果
         * @param {number} duration - 总耗时
         * @param {Object} healthCheck - 健康检查结果
         * @returns {Object} 集成报告
         */
        generateIntegrationReport(results, duration, healthCheck) {
            const summary = {
                totalSuites: results.size,
                passedSuites: 0,
                failedSuites: 0,
                totalDuration: duration,
                success: true
            };

            const suiteDetails = [];

            for (const [suiteId, result] of results) {
                if (result.success) {
                    summary.passedSuites++;
                } else {
                    summary.failedSuites++;
                    summary.success = false;
                }

                suiteDetails.push({
                    id: suiteId,
                    name: this.integrationSuites.get(suiteId)?.name || suiteId,
                    success: result.success,
                    duration: result.duration,
                    error: result.error,
                    dependencies: result.dependencies
                });
            }

            return {
                summary,
                suiteDetails,
                healthCheck,
                systemHealth: this.systemHealth,
                dependencyGraph: Object.fromEntries(this.dependencyGraph),
                timestamp: Date.now()
            };
        }

        /**
         * 获取系统健康状态
         * @returns {Object} 健康状态
         */
        getSystemHealth() {
            return this.systemHealth;
        }

        /**
         * 获取依赖关系图
         * @returns {Map} 依赖关系图
         */
        getDependencyGraph() {
            return this.dependencyGraph;
        }

        /**
         * 获取协调器状态
         * @returns {Object} 状态信息
         */
        getStatus() {
            return {
                registeredSuites: this.integrationSuites.size,
                completedTests: this.testResults.size,
                systemHealth: this.systemHealth,
                dependencyGraph: this.dependencyGraph.size,
                testConfig: this.testConfig
            };
        }
    }

    // 创建全局实例
    const integrationTestCoordinator = new IntegrationTestCoordinator();

    // 导出到全局作用域
    window.OTA = window.OTA || {};
    window.OTA.integrationTestCoordinator = integrationTestCoordinator;
    window.OTA.getIntegrationTestCoordinator = () => integrationTestCoordinator;

    // 向后兼容
    window.getIntegrationTestCoordinator = () => integrationTestCoordinator;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('integrationTestCoordinator', integrationTestCoordinator, '@OTA_INTEGRATION_TEST_COORDINATOR');
        window.OTA.Registry.registerFactory('getIntegrationTestCoordinator', () => integrationTestCoordinator, '@OTA_INTEGRATION_TEST_COORDINATOR_FACTORY');
    }

})();
