/**
 * @CORE 航班号处理器
 * 🏷️ 标签: @FLIGHT_NUMBER_PROCESSOR
 * 📝 说明: 专门处理航班号识别、验证和提取的核心组件
 * 🎯 功能: 航班号格式验证、智能提取、评分系统
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.core = window.OTA.gemini.core || {};

(function() {
    'use strict';

    /**
     * 航班号处理器类
     * 负责航班号的识别、验证和智能提取
     */
    class FlightNumberProcessor {
        constructor() {
            this.logger = window.getLogger?.() || console;
            
            // 初始化航空公司代码数据
            this.initializeAirlineCodes();
            
            // 初始化航班号模式
            this.initializeFlightPatterns();
        }

        /**
         * 初始化航空公司代码
         */
        initializeAirlineCodes() {
            // 亚洲主要航空公司代码列表
            this.asianAirlineCodes = [
                // 马来西亚航空公司
                'MH', 'AK', 'FY', 'OD', 'MXD',
                // 新加坡航空公司
                'SQ', '3K', 'TR', 'MI',
                // 中国航空公司
                'CZ', 'CA', 'MU', 'HU', 'SC', 'ZH', 'FM', 'MF', 'JD', 'G5',
                // 泰国航空公司
                'TG', 'WE', 'FD', 'SL',
                // 印尼航空公司
                'GA', 'JT', 'QG', 'IN',
                // 菲律宾航空公司
                'PR', '5J', 'Z2', 'DG',
                // 越南航空公司
                'VN', 'VJ', 'BL',
                // 日本航空公司
                'JL', 'NH', 'MM', 'BC', 'GK',
                // 韩国航空公司
                'KE', 'OZ', 'LJ', '7C',
                // 印度航空公司
                'AI', '6E', 'SG', 'I5', 'UK', '9W',
                // 国际航空公司
                'EK', 'QR', 'EY', 'TK', 'LH', 'BA', 'AF', 'KL'
            ];

            // 数字开头的特殊航空公司代码
            this.numericStartAirlineCodes = [
                '9W', // Jet Airways (印度)
                '6E', // IndiGo (印度)
                '3K', // Jetstar Asia (新加坡)
                '5J', // Cebu Pacific (菲律宾)
                '7C', // Jeju Air (韩国)
                '8M', // Myanmar Airways International
                '2P', // PAL Express (菲律宾)
                '4U'  // Germanwings (德国)
            ];
        }

        /**
         * 初始化航班号模式
         */
        initializeFlightPatterns() {
            // 综合航班号格式正则表达式
            this.flightNumberPattern = /^([A-Z]{2,3}\d{1,4}[A-Z]?|[A-Z]{2}\d{3,4}\/\d{1,3}|[A-Z]{2,3}-?\d{1,4}[A-Z]?|[0-9][A-Z]\d{3,4})$/;
            
            // 显式航班号提取模式
            this.explicitPatterns = [
                /航班[号]?\s*[:：]?\s*([A-Z0-9\/\-]+)/gi,
                /flight\s*[:：]?\s*([A-Z0-9\/\-]+)/gi,
                /班机[号]?\s*[:：]?\s*([A-Z0-9\/\-]+)/gi,
                /航班信息\s*[:：]?\s*([A-Z0-9\/\-]+)/gi
            ];

            // 基础航班号格式模式（用于候选项提取）
            this.candidatePatterns = [
                /\b([A-Z]{2,3}\d{1,4}[A-Z]?)\b/g,
                /\b([0-9][A-Z]\d{3,4})\b/g,
                /\b([A-Z]{2}\d{3,4}\/\d{1,3})\b/g,
                /\b([A-Z]{2,3}-\d{1,4}[A-Z]?)\b/g
            ];
        }

        /**
         * 获取航班号格式正则表达式
         * @returns {RegExp} 航班号格式正则
         */
        getFlightNumberPatterns() {
            return this.flightNumberPattern;
        }

        /**
         * 获取亚洲主要航空公司代码列表
         * @returns {Array} 航空公司代码数组
         */
        getAsianAirlineCodes() {
            return [...this.asianAirlineCodes];
        }

        /**
         * 获取数字开头的航空公司代码列表
         * @returns {Array} 数字开头的航空公司代码
         */
        getNumericStartAirlineCodes() {
            return [...this.numericStartAirlineCodes];
        }

        /**
         * 验证航班号格式是否有效（支持数字开头）
         * @param {string} flightNumber - 航班号
         * @returns {boolean} 是否有效
         */
        validateFlightNumber(flightNumber) {
            if (!flightNumber || typeof flightNumber !== 'string') {
                return false;
            }

            const cleanFlight = flightNumber.trim().toUpperCase();
            
            // 基本格式验证
            if (!this.flightNumberPattern.test(cleanFlight)) {
                return false;
            }

            // 验证数字开头的航班号
            const numericMatch = cleanFlight.match(/^([0-9][A-Z])/);
            if (numericMatch) {
                const airlineCode = numericMatch[1];
                return this.numericStartAirlineCodes.includes(airlineCode);
            }

            // 验证字母开头的航班号
            const airlineMatch = cleanFlight.match(/^([A-Z]{2,3})/);
            if (airlineMatch) {
                const airlineCode = airlineMatch[1];
                return this.asianAirlineCodes.includes(airlineCode);
            }

            // 验证带连字符的航班号
            const hyphenMatch = cleanFlight.match(/^([A-Z]{2,3})-/);
            if (hyphenMatch) {
                const airlineCode = hyphenMatch[1];
                return this.asianAirlineCodes.includes(airlineCode);
            }

            return false;
        }

        /**
         * 从文本中智能提取航班号
         * @param {string} text - 输入文本
         * @returns {string|null} 提取的航班号或null
         */
        extractFlightNumber(text) {
            if (!text || typeof text !== 'string') {
                return null;
            }

            // 步骤1: 尝试显式模式匹配
            for (const pattern of this.explicitPatterns) {
                const matches = text.match(pattern);
                if (matches) {
                    for (const match of matches) {
                        const extracted = match.match(/([A-Z0-9\/\-]+)$/i);
                        const candidate = extracted ? extracted[1] : null;
                        if (candidate && this.validateFlightNumber(candidate)) {
                            this.logger.log(`✅ 显式模式提取航班号: ${candidate}`, 'info');
                            return candidate.toUpperCase();
                        }
                    }
                }
            }

            // 步骤2: 智能候选项提取和评分
            const candidates = this.extractFlightNumberCandidates(text);
            const scoredCandidates = candidates.map(candidate => ({
                flight: candidate,
                score: this.calculateFlightNumberScore(candidate, text)
            }));

            // 按分数排序，选择最高分的候选项
            if (scoredCandidates.length > 0) {
                scoredCandidates.sort((a, b) => b.score - a.score);
                const bestCandidate = scoredCandidates[0];

                if (bestCandidate.score >= 3) { // 最低置信度阈值
                    this.logger.log(`✅ 智能识别航班号: ${bestCandidate.flight} (分数: ${bestCandidate.score})`, 'info');
                    return bestCandidate.flight.toUpperCase();
                }
            }

            this.logger.log('❌ 未能提取有效航班号', 'warning');
            return null;
        }

        /**
         * 提取航班号候选项
         * @param {string} text - 输入文本
         * @returns {Array} 候选航班号数组
         */
        extractFlightNumberCandidates(text) {
            const candidates = [];

            for (const pattern of this.candidatePatterns) {
                let match;
                while ((match = pattern.exec(text)) !== null) {
                    const candidate = match[1];
                    if (this.validateFlightNumber(candidate) && !candidates.includes(candidate)) {
                        candidates.push(candidate);
                    }
                }
                // 重置正则表达式的lastIndex
                pattern.lastIndex = 0;
            }

            return candidates;
        }

        /**
         * 计算航班号候选项的置信度分数
         * @param {string} candidate - 候选航班号
         * @param {string} text - 原始文本
         * @returns {number} 置信度分数
         */
        calculateFlightNumberScore(candidate, text) {
            let score = 0;
            const lowerText = text.toLowerCase();
            const candidatePos = text.indexOf(candidate);

            // 基础分数：有效的航班号格式
            score += 2;

            // 上下文分析：时间信息
            const timePatterns = [
                /\d{1,2}:\d{2}/g,
                /\d{1,2}点\d{0,2}分?/g,
                /\d{1,2}时\d{0,2}分?/g
            ];

            for (const pattern of timePatterns) {
                const timeMatches = text.match(pattern);
                if (timeMatches) {
                    // 检查航班号与时间的距离
                    for (const timeMatch of timeMatches) {
                        const timePos = text.indexOf(timeMatch);
                        const distance = Math.abs(candidatePos - timePos);
                        if (distance < 50) { // 50字符内认为相关
                            score += 1.5;
                            break;
                        }
                    }
                }
            }

            // 上下文分析：机场关键词
            const airportKeywords = ['机场', '航站楼', 'airport', 'terminal', 'klia', 'changi'];
            for (const keyword of airportKeywords) {
                if (lowerText.includes(keyword.toLowerCase())) {
                    score += 1;
                    break;
                }
            }

            // 上下文分析：服务关键词
            const serviceKeywords = ['接机', '送机', '航班', 'flight', 'pickup', 'drop'];
            for (const keyword of serviceKeywords) {
                if (lowerText.includes(keyword.toLowerCase())) {
                    score += 0.5;
                    break;
                }
            }

            // 负面因素：看起来像电话号码
            if (this.looksLikePhoneNumber(candidate, text)) {
                score -= 2;
            }

            // 负面因素：看起来像订单号
            if (this.looksLikeOrderNumber(candidate, text)) {
                score -= 1;
            }

            // 负面因素：看起来像价格
            if (this.looksLikePrice(candidate, text)) {
                score -= 1.5;
            }

            return Math.max(0, score);
        }

        /**
         * 检查候选项是否看起来像电话号码
         * @param {string} candidate - 候选项
         * @param {string} text - 原始文本
         * @returns {boolean} 是否像电话号码
         */
        looksLikePhoneNumber(candidate, text) {
            // 检查是否包含在电话号码格式中
            const phonePatterns = [
                /[\+]?[\d\s\-\(\)]{8,20}/g,
                /\d{3,4}[\-\s]?\d{3,4}[\-\s]?\d{3,4}/g
            ];

            for (const pattern of phonePatterns) {
                const matches = text.match(pattern);
                if (matches) {
                    for (const match of matches) {
                        if (match.includes(candidate)) {
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        /**
         * 检查候选项是否看起来像订单号
         * @param {string} candidate - 候选项
         * @param {string} text - 原始文本
         * @returns {boolean} 是否像订单号
         */
        looksLikeOrderNumber(candidate, text) {
            const orderKeywords = [
                '订单', '团号', '确认号', '参考号', 'order', 'reference', 'confirmation',
                'booking', 'ref', 'no', 'number'
            ];

            const beforeText = text.substring(Math.max(0, text.indexOf(candidate) - 20), text.indexOf(candidate)).toLowerCase();
            const afterText = text.substring(text.indexOf(candidate) + candidate.length, text.indexOf(candidate) + candidate.length + 20).toLowerCase();

            for (const keyword of orderKeywords) {
                if (beforeText.includes(keyword) || afterText.includes(keyword)) {
                    return true;
                }
            }

            // 检查字符组成：如果字母和数字比例相对均衡，可能是订单号
            const letterCount = (candidate.match(/[A-Z]/g) || []).length;
            const digitCount = (candidate.match(/\d/g) || []).length;
            if (letterCount >= 2 && digitCount >= 3) {
                return true;
            }

            return false;
        }

        /**
         * 检查候选项是否看起来像价格
         * @param {string} candidate - 候选项
         * @param {string} text - 原始文本
         * @returns {boolean} 是否像价格
         */
        looksLikePrice(candidate, text) {
            const priceKeywords = [
                '价格', '费用', '金额', '钱', 'price', 'cost', 'fee', 'amount',
                'rm', 'myr', 'usd', 'sgd', 'cny', '$', '￥', '元'
            ];

            const beforeText = text.substring(Math.max(0, text.indexOf(candidate) - 15), text.indexOf(candidate)).toLowerCase();
            const afterText = text.substring(text.indexOf(candidate) + candidate.length, text.indexOf(candidate) + candidate.length + 15).toLowerCase();

            for (const keyword of priceKeywords) {
                if (beforeText.includes(keyword) || afterText.includes(keyword)) {
                    return true;
                }
            }

            return false;
        }

        /**
         * 获取处理器统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                supportedAirlines: this.asianAirlineCodes.length,
                numericStartAirlines: this.numericStartAirlineCodes.length,
                patternCount: this.candidatePatterns.length,
                explicitPatternCount: this.explicitPatterns.length
            };
        }
    }

    // 暴露到全局命名空间
    window.OTA.gemini.core.FlightNumberProcessor = FlightNumberProcessor;

    // 创建单例实例
    let flightNumberProcessorInstance = null;

    /**
     * 获取航班号处理器单例实例
     * @returns {FlightNumberProcessor} 航班号处理器实例
     */
    function getFlightNumberProcessor() {
        if (!flightNumberProcessorInstance) {
            flightNumberProcessorInstance = new FlightNumberProcessor();
        }
        return flightNumberProcessorInstance;
    }

    // 暴露工厂函数
    window.OTA.gemini.core.getFlightNumberProcessor = getFlightNumberProcessor;

    // 注册到服务注册中心
    if (window.OTA?.gemini?.core?.ServiceRegistry) {
        window.OTA.gemini.core.ServiceRegistry.register('flightNumberProcessor', getFlightNumberProcessor, '@FLIGHT_NUMBER_PROCESSOR');
    }

    console.log('✅ 航班号处理器模块已加载');

})();
