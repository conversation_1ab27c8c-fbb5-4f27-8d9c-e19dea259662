/**
 * 配置管理协调器
 * 统一协调unified-config-center.js和config-manager.js两个配置系统
 * 
 * 功能特性:
 * - 配置系统冲突检测和解决
 * - 统一配置访问接口
 * - 配置优先级管理
 * - 配置同步和一致性保证
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.core = window.OTA.core || {};

(function() {
    'use strict';

    /**
     * 配置管理协调器类
     */
    class ConfigManagementCoordinator {
        constructor() {
            // 配置系统引用
            this.unifiedConfigCenter = null;
            this.geminiConfigManager = null;
            
            // 配置优先级定义
            this.configPriority = {
                'gemini': 1,      // Gemini专用配置最高优先级
                'unified': 2,     // 统一配置中心次优先级
                'default': 3      // 默认配置最低优先级
            };
            
            // 配置命名空间映射
            this.namespaceMapping = {
                // Gemini相关配置使用专用管理器
                'gemini': 'gemini',
                'ai': 'gemini',
                'llm': 'gemini',
                
                // 系统配置使用统一配置中心
                'api': 'unified',
                'ui': 'unified',
                'performance': 'unified',
                'logging': 'unified',
                'features': 'unified',
                'security': 'unified'
            };
            
            // 配置冲突记录
            this.conflicts = new Map();
            
            // 配置同步状态
            this.syncStatus = {
                lastSync: null,
                syncInProgress: false,
                syncErrors: [],
                syncCount: 0
            };
            
            this.warningManager = null;
            this.initialize();
        }

        /**
         * 初始化协调器
         */
        initialize() {
            // 延迟获取依赖
            setTimeout(() => {
                this.warningManager = window.OTA?.core?.warningManager;
                this.unifiedConfigCenter = window.OTA?.configCenter;
                this.geminiConfigManager = window.OTA?.gemini?.core?.configManager;
                
                // 检测配置系统
                this.detectConfigSystems();
                
                // 解决配置冲突
                this.resolveConfigConflicts();
                
                // 建立配置同步
                this.establishConfigSync();
            }, 200);
            
            console.log('✅ 配置管理协调器已初始化');
        }

        /**
         * 检测配置系统
         */
        detectConfigSystems() {
            const systems = [];
            
            if (this.unifiedConfigCenter) {
                systems.push({
                    name: 'unified-config-center',
                    instance: this.unifiedConfigCenter,
                    priority: this.configPriority.unified
                });
            }
            
            if (this.geminiConfigManager) {
                systems.push({
                    name: 'gemini-config-manager',
                    instance: this.geminiConfigManager,
                    priority: this.configPriority.gemini
                });
            }
            
            console.log(`🔍 检测到 ${systems.length} 个配置系统:`, systems.map(s => s.name));
            
            if (systems.length > 1) {
                this.detectConfigConflicts(systems);
            }
        }

        /**
         * 检测配置冲突
         * @param {Array} systems - 配置系统列表
         */
        detectConfigConflicts(systems) {
            const configKeys = new Set();
            const conflicts = [];
            
            systems.forEach(system => {
                // 获取系统中的配置键
                const keys = this.getConfigKeys(system.instance);
                keys.forEach(key => {
                    if (configKeys.has(key)) {
                        conflicts.push({
                            key,
                            systems: systems.filter(s => this.hasConfig(s.instance, key))
                        });
                    }
                    configKeys.add(key);
                });
            });
            
            if (conflicts.length > 0) {
                console.warn(`⚠️ 检测到 ${conflicts.length} 个配置冲突:`, conflicts);
                conflicts.forEach(conflict => {
                    this.conflicts.set(conflict.key, conflict);
                });
            }
        }

        /**
         * 解决配置冲突
         */
        resolveConfigConflicts() {
            if (this.conflicts.size === 0) {
                console.log('✅ 无配置冲突需要解决');
                return;
            }
            
            console.log(`🔧 开始解决 ${this.conflicts.size} 个配置冲突...`);
            
            this.conflicts.forEach((conflict, key) => {
                try {
                    this.resolveConfigConflict(key, conflict);
                } catch (error) {
                    console.error(`配置冲突解决失败 [${key}]:`, error);
                    if (this.warningManager) {
                        this.warningManager.warn(
                            'CONFIG_CONFLICT_RESOLUTION_FAILED',
                            `配置冲突解决失败: ${key}`,
                            'WARNING',
                            { key, error: error.message }
                        );
                    }
                }
            });
        }

        /**
         * 解决单个配置冲突
         * @param {string} key - 配置键
         * @param {Object} conflict - 冲突信息
         */
        resolveConfigConflict(key, conflict) {
            // 根据优先级选择配置源
            const prioritizedSystems = conflict.systems.sort((a, b) => a.priority - b.priority);
            const primarySystem = prioritizedSystems[0];
            
            console.log(`🎯 配置 [${key}] 使用 ${primarySystem.name} 作为主要源`);
            
            // 获取主要配置值
            const primaryValue = this.getConfigValue(primarySystem.instance, key);
            
            // 同步到其他系统
            prioritizedSystems.slice(1).forEach(system => {
                try {
                    this.setConfigValue(system.instance, key, primaryValue);
                    console.log(`✅ 配置 [${key}] 已同步到 ${system.name}`);
                } catch (error) {
                    console.warn(`⚠️ 配置同步失败 [${key}] -> ${system.name}:`, error);
                }
            });
        }

        /**
         * 建立配置同步
         */
        establishConfigSync() {
            // 监听统一配置中心的变化
            if (this.unifiedConfigCenter && typeof this.unifiedConfigCenter.watch === 'function') {
                this.unifiedConfigCenter.watch('*', (key, newValue, oldValue) => {
                    this.syncConfigChange('unified', key, newValue);
                });
            }
            
            // 监听Gemini配置管理器的变化
            if (this.geminiConfigManager && typeof this.geminiConfigManager.addConfigListener === 'function') {
                this.geminiConfigManager.addConfigListener('*', (key, newValue, oldValue) => {
                    this.syncConfigChange('gemini', key, newValue);
                });
            }
            
            console.log('✅ 配置同步机制已建立');
        }

        /**
         * 同步配置变化
         * @param {string} source - 配置源
         * @param {string} key - 配置键
         * @param {any} value - 配置值
         */
        syncConfigChange(source, key, value) {
            if (this.syncStatus.syncInProgress) {
                return; // 避免循环同步
            }
            
            this.syncStatus.syncInProgress = true;
            
            try {
                const namespace = this.getConfigNamespace(key);
                
                // 只同步到对应的命名空间
                if (namespace === 'unified' && source !== 'unified' && this.unifiedConfigCenter) {
                    this.setConfigValue(this.unifiedConfigCenter, key, value);
                } else if (namespace === 'gemini' && source !== 'gemini' && this.geminiConfigManager) {
                    this.setConfigValue(this.geminiConfigManager, key, value);
                }
                
                this.syncStatus.syncCount++;
                this.syncStatus.lastSync = new Date().toISOString();
                
            } catch (error) {
                this.syncStatus.syncErrors.push({
                    key,
                    source,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
                console.error(`配置同步失败 [${key}]:`, error);
            } finally {
                this.syncStatus.syncInProgress = false;
            }
        }

        /**
         * 统一配置获取接口
         * @param {string} key - 配置键
         * @param {any} defaultValue - 默认值
         * @returns {any} 配置值
         */
        getConfig(key, defaultValue = null) {
            const namespace = this.getConfigNamespace(key);
            
            try {
                if (namespace === 'gemini' && this.geminiConfigManager) {
                    return this.getConfigValue(this.geminiConfigManager, key, defaultValue);
                } else if (namespace === 'unified' && this.unifiedConfigCenter) {
                    return this.getConfigValue(this.unifiedConfigCenter, key, defaultValue);
                }
            } catch (error) {
                console.warn(`配置获取失败 [${key}]:`, error);
            }
            
            return defaultValue;
        }

        /**
         * 统一配置设置接口
         * @param {string} key - 配置键
         * @param {any} value - 配置值
         * @param {Object} options - 选项
         */
        setConfig(key, value, options = {}) {
            const namespace = this.getConfigNamespace(key);
            
            try {
                if (namespace === 'gemini' && this.geminiConfigManager) {
                    this.setConfigValue(this.geminiConfigManager, key, value, options);
                } else if (namespace === 'unified' && this.unifiedConfigCenter) {
                    this.setConfigValue(this.unifiedConfigCenter, key, value, options);
                }
            } catch (error) {
                console.error(`配置设置失败 [${key}]:`, error);
                throw error;
            }
        }

        /**
         * 获取配置命名空间
         * @param {string} key - 配置键
         * @returns {string} 命名空间
         */
        getConfigNamespace(key) {
            // 检查直接映射
            for (const [prefix, namespace] of Object.entries(this.namespaceMapping)) {
                if (key.startsWith(prefix)) {
                    return namespace;
                }
            }
            
            // 默认使用统一配置中心
            return 'unified';
        }

        /**
         * 获取配置系统的配置键列表
         * @param {Object} system - 配置系统实例
         * @returns {Array} 配置键列表
         */
        getConfigKeys(system) {
            if (system.configs && system.configs instanceof Map) {
                return Array.from(system.configs.keys());
            } else if (system.configCache && system.configCache instanceof Map) {
                return Array.from(system.configCache.keys());
            }
            return [];
        }

        /**
         * 检查配置系统是否有指定配置
         * @param {Object} system - 配置系统实例
         * @param {string} key - 配置键
         * @returns {boolean} 是否存在
         */
        hasConfig(system, key) {
            if (system.configs && system.configs instanceof Map) {
                return system.configs.has(key);
            } else if (system.configCache && system.configCache instanceof Map) {
                return system.configCache.has(key);
            }
            return false;
        }

        /**
         * 获取配置值
         * @param {Object} system - 配置系统实例
         * @param {string} key - 配置键
         * @param {any} defaultValue - 默认值
         * @returns {any} 配置值
         */
        getConfigValue(system, key, defaultValue = null) {
            if (typeof system.getConfig === 'function') {
                return system.getConfig(key, defaultValue);
            } else if (typeof system.loadConfig === 'function') {
                return system.loadConfig(key);
            }
            return defaultValue;
        }

        /**
         * 设置配置值
         * @param {Object} system - 配置系统实例
         * @param {string} key - 配置键
         * @param {any} value - 配置值
         * @param {Object} options - 选项
         */
        setConfigValue(system, key, value, options = {}) {
            if (typeof system.setConfig === 'function') {
                system.setConfig(key, value, options);
            } else if (typeof system.updateConfig === 'function') {
                system.updateConfig(key, value, options);
            }
        }

        /**
         * 获取协调器状态
         * @returns {Object} 状态信息
         */
        getStatus() {
            return {
                配置系统数量: [this.unifiedConfigCenter, this.geminiConfigManager].filter(Boolean).length,
                配置冲突数量: this.conflicts.size,
                同步状态: this.syncStatus,
                命名空间映射: this.namespaceMapping,
                最后检查时间: new Date().toISOString()
            };
        }
    }

    // 创建全局唯一的配置管理协调器实例
    const configManagementCoordinator = new ConfigManagementCoordinator();

    // 暴露到OTA命名空间
    window.OTA.core.configManagementCoordinator = configManagementCoordinator;

    // 提供统一的配置访问接口
    window.OTA.getConfig = function(key, defaultValue) {
        return configManagementCoordinator.getConfig(key, defaultValue);
    };

    window.OTA.setConfig = function(key, value, options) {
        return configManagementCoordinator.setConfig(key, value, options);
    };

    console.log('✅ 配置管理协调器模块已加载');

})();
