/**
 * @CONFIG_MANAGER 统一配置管理器
 * 🏷️ 标签: @CONFIG_MANAGER
 * 📝 说明: 统一管理所有OTA平台的配置文件和热更新功能，提供配置验证、缓存和动态更新
 * 🎯 功能: 配置加载、热更新、验证、缓存、版本管理
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.core = window.OTA.gemini.core || {};

(function() {
    'use strict';

    /**
     * 统一配置管理器类
     * 提供配置的加载、验证、缓存和热更新功能
     */
    class ConfigManager {
        constructor() {
            this.logger = window.getLogger?.() || console;
            
            // 配置缓存
            this.configCache = new Map();
            
            // 配置元数据
            this.configMetadata = new Map();
            
            // 配置监听器
            this.configListeners = new Map();
            
            // 配置验证器
            this.validators = new Map();
            
            // 配置管理器配置
            this.managerConfig = {
                // 缓存配置 - 优化性能
                cache: {
                    enabled: true,
                    maxAge: 30 * 60 * 1000,    // 30分钟缓存 (延长缓存时间)
                    maxSize: 200,              // 最大缓存条目数 (增加缓存容量)
                    cleanupInterval: 10 * 60 * 1000,  // 10分钟清理间隔 (减少清理频率)
                    enableLRU: true,           // 启用LRU淘汰策略
                    preloadConfigs: ['ota-reference-patterns', 'field-mapping'] // 预加载关键配置
                },

                // 热更新配置 - 优化性能
                hotReload: {
                    enabled: false,            // 默认关闭热更新以提高性能
                    checkInterval: 60 * 1000,  // 60秒检查间隔 (降低检查频率)
                    maxRetries: 2,             // 最大重试次数 (减少重试)
                    retryDelay: 2000,          // 重试延迟 (增加延迟)
                    enableInProduction: false  // 生产环境禁用热更新
                },
                
                // 验证配置
                validation: {
                    enabled: true,
                    strictMode: false,         // 严格模式
                    logValidationErrors: true  // 记录验证错误
                },
                
                // 性能配置
                performance: {
                    enableMetrics: true,       // 启用性能指标
                    enableProfiling: false     // 启用性能分析
                }
            };
            
            // 性能统计
            this.stats = {
                configLoads: 0,
                cacheHits: 0,
                cacheMisses: 0,
                validationErrors: 0,
                hotReloadEvents: 0,
                averageLoadTime: 0,
                lastCleanup: Date.now()
            };
            
            // 配置类型定义
            this.configTypes = {
                OTA_REFERENCE_PATTERNS: 'ota-reference-patterns',
                FIELD_MAPPING: 'field-mapping',
                PRESET_VALUES: 'preset-values',
                FALLBACK_CONFIG: 'fallback-config',
                PROCESSOR_CONFIG: 'processor-config',
                LANGUAGE_CONFIG: 'language-config',
                PERFORMANCE_CONFIG: 'performance-config'
            };
            
            // 默认配置
            this.defaultConfigs = this.createDefaultConfigs();
            
            // 初始化配置管理器
            this.initialize();
        }

        /**
         * 初始化配置管理器
         */
        initialize() {
            this.logger.log('统一配置管理器初始化开始', 'info');
            
            // 注册默认验证器
            this.registerDefaultValidators();
            
            // 启动缓存清理
            if (this.managerConfig.cache.enabled) {
                this.startCacheCleanup();
            }
            
            // 启动热更新检查
            if (this.managerConfig.hotReload.enabled) {
                this.startHotReloadCheck();
            }
            
            this.logger.log('统一配置管理器初始化完成', 'info');
        }

        /**
         * 创建默认配置
         * @returns {Object} 默认配置集合
         */
        createDefaultConfigs() {
            return {
                [this.configTypes.OTA_REFERENCE_PATTERNS]: {
                    version: '1.0.0',
                    platformRules: {
                        'generic': {
                            patterns: [
                                /[A-Z]{2,4}\d{6,12}/g,
                                /\d{8,15}/g,
                                /[A-Z0-9]{8,20}/g
                            ],
                            priority: 1,
                            confidenceBonus: 0.1
                        }
                    },
                    genericRules: {
                        patterns: [
                            /[A-Z0-9]{6,20}/g,
                            /\d{6,15}/g
                        ],
                        minLength: 6,
                        maxLength: 20
                    }
                },
                
                [this.configTypes.FIELD_MAPPING]: {
                    version: '1.0.0',
                    commonMappings: {
                        'pickup_location': ['pickup', 'from', 'departure', '接机地点', '出发地'],
                        'dropoff_location': ['dropoff', 'to', 'destination', '送机地点', '目的地'],
                        'passenger_name': ['passenger', 'name', 'guest', '乘客', '姓名'],
                        'contact_number': ['phone', 'mobile', 'contact', '电话', '联系方式']
                    }
                },
                
                [this.configTypes.PRESET_VALUES]: {
                    version: '1.0.0',
                    defaults: {
                        car_type_id: 1,
                        languages_id_array: {"0": "2"},
                        service_type_id: 2,
                        luggage_number: 2,
                        is_return: false,
                        pickup_sign: false
                    }
                },
                
                [this.configTypes.FALLBACK_CONFIG]: {
                    version: '1.0.0',
                    strategies: {
                        channelIdentification: 'generic',
                        processorSelection: 'generic',
                        errorHandling: 'graceful',
                        dataExtraction: 'best-effort'
                    },
                    retryConfig: {
                        maxRetries: 3,
                        retryDelay: 1000,
                        backoffMultiplier: 2
                    }
                }
            };
        }

        /**
         * 加载配置
         * @param {string} configType - 配置类型
         * @param {Object} options - 加载选项
         * @returns {Promise<Object>} 配置对象
         */
        async loadConfig(configType, options = {}) {
            const startTime = Date.now();
            this.stats.configLoads++;
            
            try {
                // 检查缓存
                if (this.managerConfig.cache.enabled && !options.forceReload) {
                    const cachedConfig = this.getCachedConfig(configType);
                    if (cachedConfig) {
                        this.stats.cacheHits++;
                        return cachedConfig;
                    }
                }
                
                this.stats.cacheMisses++;
                
                // 加载配置
                let config = await this.loadConfigFromSource(configType, options);
                
                // 应用默认值
                config = this.applyDefaults(configType, config);
                
                // 验证配置
                if (this.managerConfig.validation.enabled) {
                    const validationResult = await this.validateConfig(configType, config);
                    if (!validationResult.isValid) {
                        if (this.managerConfig.validation.strictMode) {
                            throw new Error(`配置验证失败: ${validationResult.errors.join(', ')}`);
                        } else {
                            this.logger.logWarning(`配置验证警告 [${configType}]`, validationResult.errors);
                            this.stats.validationErrors++;
                        }
                    }
                }
                
                // 缓存配置
                if (this.managerConfig.cache.enabled) {
                    this.cacheConfig(configType, config);
                }
                
                // 更新元数据
                this.updateConfigMetadata(configType, {
                    loadTime: Date.now(),
                    version: config.version || '1.0.0',
                    source: options.source || 'default',
                    size: JSON.stringify(config).length
                });
                
                // 更新性能统计
                const loadTime = Date.now() - startTime;
                this.updateLoadTimeStats(loadTime);
                
                this.logger.log(`配置加载成功 [${configType}] (${loadTime}ms)`, 'info');
                
                return config;
                
            } catch (error) {
                this.logger.logError(`配置加载失败 [${configType}]`, error);
                
                // 返回默认配置
                return this.getDefaultConfig(configType);
            }
        }

        /**
         * 从源加载配置
         * @param {string} configType - 配置类型
         * @param {Object} options - 选项
         * @returns {Promise<Object>} 配置对象
         */
        async loadConfigFromSource(configType, options = {}) {
            // 尝试从全局配置对象加载
            const globalConfigPath = `window.OTA.gemini.configs.${configType.replace(/-/g, '')}`;
            const globalConfig = this.getNestedProperty(window, globalConfigPath);
            
            if (globalConfig) {
                return typeof globalConfig === 'function' ? globalConfig() : globalConfig;
            }
            
            // 尝试从注册中心加载
            const registry = window.OTA?.Registry;
            if (registry) {
                const configService = registry.getService(`${configType}Config`);
                if (configService) {
                    return typeof configService.getConfig === 'function' ? 
                        configService.getConfig() : configService;
                }
            }
            
            // 返回默认配置
            return this.getDefaultConfig(configType);
        }

        /**
         * 获取嵌套属性
         * @param {Object} obj - 对象
         * @param {string} path - 属性路径
         * @returns {*} 属性值
         */
        getNestedProperty(obj, path) {
            return path.split('.').reduce((current, key) => {
                return current && current[key] !== undefined ? current[key] : null;
            }, obj);
        }

        /**
         * 应用默认值
         * @param {string} configType - 配置类型
         * @param {Object} config - 配置对象
         * @returns {Object} 应用默认值后的配置
         */
        applyDefaults(configType, config) {
            const defaultConfig = this.getDefaultConfig(configType);
            return this.deepMerge(defaultConfig, config);
        }

        /**
         * 深度合并对象
         * @param {Object} target - 目标对象
         * @param {Object} source - 源对象
         * @returns {Object} 合并后的对象
         */
        deepMerge(target, source) {
            const result = { ...target };
            
            for (const key in source) {
                if (source.hasOwnProperty(key)) {
                    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                        result[key] = this.deepMerge(result[key] || {}, source[key]);
                    } else {
                        result[key] = source[key];
                    }
                }
            }
            
            return result;
        }

        /**
         * 验证配置
         * @param {string} configType - 配置类型
         * @param {Object} config - 配置对象
         * @returns {Promise<Object>} 验证结果
         */
        async validateConfig(configType, config) {
            const validator = this.validators.get(configType);
            
            if (!validator) {
                return { isValid: true, errors: [] };
            }
            
            try {
                const result = await validator(config);
                return result;
            } catch (error) {
                return {
                    isValid: false,
                    errors: [`验证器执行失败: ${error.message}`]
                };
            }
        }

        /**
         * 注册配置验证器
         * @param {string} configType - 配置类型
         * @param {Function} validator - 验证器函数
         */
        registerValidator(configType, validator) {
            if (typeof validator !== 'function') {
                throw new Error('验证器必须是函数');
            }
            
            this.validators.set(configType, validator);
            this.logger.log(`配置验证器注册成功 [${configType}]`, 'info');
        }

        /**
         * 注册默认验证器
         */
        registerDefaultValidators() {
            // OTA参考号模式验证器
            this.registerValidator(this.configTypes.OTA_REFERENCE_PATTERNS, (config) => {
                const errors = [];
                
                if (!config.platformRules || typeof config.platformRules !== 'object') {
                    errors.push('platformRules必须是对象');
                }
                
                if (!config.version) {
                    errors.push('缺少版本信息');
                }
                
                return { isValid: errors.length === 0, errors };
            });
            
            // 字段映射验证器
            this.registerValidator(this.configTypes.FIELD_MAPPING, (config) => {
                const errors = [];
                
                if (!config.commonMappings || typeof config.commonMappings !== 'object') {
                    errors.push('commonMappings必须是对象');
                }
                
                return { isValid: errors.length === 0, errors };
            });
            
            // 预设值验证器
            this.registerValidator(this.configTypes.PRESET_VALUES, (config) => {
                const errors = [];
                
                if (!config.defaults || typeof config.defaults !== 'object') {
                    errors.push('defaults必须是对象');
                }
                
                return { isValid: errors.length === 0, errors };
            });
        }

        /**
         * 获取缓存的配置
         * @param {string} configType - 配置类型
         * @returns {Object|null} 缓存的配置
         */
        getCachedConfig(configType) {
            const cacheEntry = this.configCache.get(configType);
            
            if (!cacheEntry) {
                return null;
            }
            
            // 检查缓存是否过期
            if (Date.now() - cacheEntry.timestamp > this.managerConfig.cache.maxAge) {
                this.configCache.delete(configType);
                return null;
            }
            
            return cacheEntry.config;
        }

        /**
         * 缓存配置
         * @param {string} configType - 配置类型
         * @param {Object} config - 配置对象
         */
        cacheConfig(configType, config) {
            // 检查缓存大小限制
            if (this.configCache.size >= this.managerConfig.cache.maxSize) {
                this.cleanupOldestCache();
            }
            
            this.configCache.set(configType, {
                config: JSON.parse(JSON.stringify(config)), // 深拷贝
                timestamp: Date.now()
            });
        }

        /**
         * 清理最旧的缓存
         */
        cleanupOldestCache() {
            let oldestKey = null;
            let oldestTime = Date.now();
            
            for (const [key, entry] of this.configCache.entries()) {
                if (entry.timestamp < oldestTime) {
                    oldestTime = entry.timestamp;
                    oldestKey = key;
                }
            }
            
            if (oldestKey) {
                this.configCache.delete(oldestKey);
            }
        }

        /**
         * 更新配置元数据
         * @param {string} configType - 配置类型
         * @param {Object} metadata - 元数据
         */
        updateConfigMetadata(configType, metadata) {
            this.configMetadata.set(configType, {
                ...this.configMetadata.get(configType),
                ...metadata
            });
        }

        /**
         * 更新加载时间统计
         * @param {number} loadTime - 加载时间
         */
        updateLoadTimeStats(loadTime) {
            const currentAverage = this.stats.averageLoadTime;
            const totalLoads = this.stats.configLoads;
            
            this.stats.averageLoadTime = ((currentAverage * (totalLoads - 1)) + loadTime) / totalLoads;
        }

        /**
         * 获取默认配置
         * @param {string} configType - 配置类型
         * @returns {Object} 默认配置
         */
        getDefaultConfig(configType) {
            return JSON.parse(JSON.stringify(this.defaultConfigs[configType] || {}));
        }

        /**
         * 启动缓存清理
         */
        startCacheCleanup() {
            setInterval(() => {
                this.cleanupExpiredCache();
            }, this.managerConfig.cache.cleanupInterval);
        }

        /**
         * 清理过期缓存
         */
        cleanupExpiredCache() {
            const now = Date.now();
            const maxAge = this.managerConfig.cache.maxAge;
            
            for (const [key, entry] of this.configCache.entries()) {
                if (now - entry.timestamp > maxAge) {
                    this.configCache.delete(key);
                }
            }
            
            this.stats.lastCleanup = now;
        }

        /**
         * 启动热更新检查
         */
        startHotReloadCheck() {
            setInterval(() => {
                this.checkForConfigUpdates();
            }, this.managerConfig.hotReload.checkInterval);
        }

        /**
         * 检查配置更新
         */
        async checkForConfigUpdates() {
            for (const configType of Object.values(this.configTypes)) {
                try {
                    const currentMetadata = this.configMetadata.get(configType);
                    if (!currentMetadata) continue;
                    
                    // 这里可以实现更复杂的更新检查逻辑
                    // 例如检查文件修改时间、版本号等
                    
                } catch (error) {
                    this.logger.logError(`配置更新检查失败 [${configType}]`, error);
                }
            }
        }

        /**
         * 重新加载配置
         * @param {string} configType - 配置类型
         * @returns {Promise<Object>} 重新加载的配置
         */
        async reloadConfig(configType) {
            // 清除缓存
            this.configCache.delete(configType);
            
            // 重新加载
            const config = await this.loadConfig(configType, { forceReload: true });
            
            // 通知监听器
            this.notifyConfigListeners(configType, config);
            
            this.stats.hotReloadEvents++;
            this.logger.log(`配置热重载完成 [${configType}]`, 'info');
            
            return config;
        }

        /**
         * 注册配置监听器
         * @param {string} configType - 配置类型
         * @param {Function} listener - 监听器函数
         */
        addConfigListener(configType, listener) {
            if (!this.configListeners.has(configType)) {
                this.configListeners.set(configType, new Set());
            }
            
            this.configListeners.get(configType).add(listener);
        }

        /**
         * 移除配置监听器
         * @param {string} configType - 配置类型
         * @param {Function} listener - 监听器函数
         */
        removeConfigListener(configType, listener) {
            const listeners = this.configListeners.get(configType);
            if (listeners) {
                listeners.delete(listener);
            }
        }

        /**
         * 通知配置监听器
         * @param {string} configType - 配置类型
         * @param {Object} config - 新配置
         */
        notifyConfigListeners(configType, config) {
            const listeners = this.configListeners.get(configType);
            if (listeners) {
                for (const listener of listeners) {
                    try {
                        listener(config, configType);
                    } catch (error) {
                        this.logger.logError(`配置监听器执行失败 [${configType}]`, error);
                    }
                }
            }
        }

        /**
         * 获取配置统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                ...this.stats,
                cacheSize: this.configCache.size,
                metadataSize: this.configMetadata.size,
                listenersCount: Array.from(this.configListeners.values())
                    .reduce((total, listeners) => total + listeners.size, 0),
                cacheHitRate: this.stats.configLoads > 0 ? 
                    ((this.stats.cacheHits / this.stats.configLoads) * 100).toFixed(2) + '%' : '0%'
            };
        }

        /**
         * 清理所有缓存
         */
        clearAllCache() {
            this.configCache.clear();
            this.configMetadata.clear();
            this.logger.log('所有配置缓存已清理', 'info');
        }

        /**
         * 获取所有已加载的配置类型
         * @returns {Array} 配置类型列表
         */
        getLoadedConfigTypes() {
            return Array.from(this.configCache.keys());
        }
    }

    // 创建全局单例实例
    function getConfigManager() {
        if (!window.OTA.gemini.core.configManager) {
            window.OTA.gemini.core.configManager = new ConfigManager();
        }
        return window.OTA.gemini.core.configManager;
    }

    // 暴露到全局命名空间
    window.OTA.gemini.core.ConfigManager = ConfigManager;
    window.OTA.gemini.core.getConfigManager = getConfigManager;

    // 向后兼容
    window.getConfigManager = getConfigManager;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('configManager', getConfigManager(), '@CONFIG_MANAGER');
        window.OTA.Registry.registerFactory('getConfigManager', getConfigManager, '@CONFIG_MANAGER_FACTORY');
    }

    console.log('✅ 统一配置管理器已加载');

})();
