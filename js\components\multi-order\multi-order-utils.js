/**
 * @fileoverview 多订单管理器工具函数库
 * @description 提供格式化、映射和通用工具函数，支持多订单处理系统
 * <AUTHOR>
 * @created 2025-01-25
 * @version 1.0.0
 */

// 防重复加载检查
if (typeof window.OTA === 'undefined') {
    window.OTA = {};
}

if (window.OTA.MultiOrderUtils) {
    console.log('⚠️ MultiOrderUtils已加载，跳过重复加载');
} else {

(function() {
    'use strict';

    /**
     * @UTIL 多订单工具函数类
     * 🏷️ 标签: @OTA_MULTI_ORDER_UTILS
     * 📝 说明: 提供格式化、映射和通用工具函数
     * ⚠️ 警告: 已注册，请勿重复开发
     */
    class MultiOrderUtils {
        
        constructor(dependencies = {}) {
            this.logger = dependencies.logger || this.getLogger();
            this.config = { ...this.getDefaultConfig(), ...dependencies.config };
        }

        /**
         * 获取默认配置
         * @returns {Object} 默认配置对象
         */
        getDefaultConfig() {
            return {
                defaultCurrency: 'MYR',
                phoneDisplayLength: 6,
                priceDecimalPlaces: 2
            };
        }

        /**
         * 获取日志记录器
         * @returns {Object} 日志记录器实例
         */
        getLogger() {
            return window.getLogger?.() || console;
        }

        // ==================== 格式化函数 ====================

        /**
         * 格式化价格显示
         * @param {number|string} price - 价格数值
         * @param {string} currency - 货币类型
         * @returns {string} 格式化的价格字符串
         */
        formatPrice(price, currency = 'MYR') {
            // 使用统一的工具函数
            if (window.OTA?.utils?.formatPrice) {
                return window.OTA.utils.formatPrice(price, currency, this.config.priceDecimalPlaces);
            }

            // 降级方案
            if (!price || price === '' || price === null || price === undefined) {
                return `${currency} 0.00`;
            }

            const numPrice = parseFloat(price);
            if (isNaN(numPrice)) {
                return `${currency} 0.00`;
            }

            return `${currency} ${numPrice.toFixed(this.config.priceDecimalPlaces)}`;
        }

        /**
         * 格式化电话号码显示（隐私保护）
         * @param {string} phone - 电话号码
         * @returns {string} 格式化的电话号码
         */
        formatPhone(phone) {
            // 使用统一的工具函数
            if (window.OTA?.utils?.formatPhoneDisplay) {
                return window.OTA.utils.formatPhoneDisplay(phone, this.config.phoneDisplayLength);
            }

            // 降级方案
            if (!phone) return '未提供';

            if (phone.startsWith('+60')) {
                return phone.substr(0, 5) + '***';
            }

            const displayLength = this.config.phoneDisplayLength;
            return phone.length > displayLength ?
                phone.substr(0, displayLength) + '***' : phone;
        }

        // ==================== 映射函数 ====================

        /**
         * 获取服务类型名称
         * @param {number|string} serviceTypeId - 服务类型ID
         * @param {Object} order - 完整订单对象（用于备用字段查找）
         * @returns {string} 服务类型名称
         */
        getServiceTypeName(serviceTypeId, order = {}) {
            const serviceMap = {
                2: '接机',
                3: '送机', 
                4: '包车',
                5: '举牌',
                '2': '接机',
                '3': '送机',
                '4': '包车',
                '5': '举牌'
            };

            // 尝试从多个可能的字段获取服务类型ID
            let actualServiceTypeId = serviceTypeId || 
                                    order.serviceTypeId || 
                                    order.service_type_id ||
                                    order.subCategoryId ||
                                    order.sub_category_id;

            return serviceMap[actualServiceTypeId] || '未知服务';
        }

        /**
         * 获取车型名称
         * @param {number|string} carTypeId - 车型ID
         * @returns {string} 车型名称
         */
        getCarTypeName(carTypeId) {
            try {
                // 使用统一的服务获取方法
                const apiService = this.getApiService();
                if (apiService && apiService.staticData && apiService.staticData.carTypes) {
                    const carType = apiService.staticData.carTypes.find(type => type.id === carTypeId);
                    if (carType) {
                        // 简化显示，只显示主要信息
                        const name = carType.name;
                        const simplified = name.split('(')[0].trim(); // 取括号前的部分
                        return simplified;
                    }
                }
                
                // 备用映射
                const fallbackMap = {
                    1: 'Comfort 5 Seater',
                    2: 'Premium 6 Seater', 
                    3: 'Luxury 7 Seater'
                };
                
                return fallbackMap[carTypeId] || `车型${carTypeId}`;
            } catch (error) {
                this.logger.logError?.('获取车型名称失败', error);
                return `车型${carTypeId}`;
            }
        }

        /**
         * 获取API服务实例
         * @returns {Object|null} API服务实例
         */
        getApiService() {
            const attempts = [
                () => window.getApiService?.(),
                () => window.getService?.('apiService'),
                () => window.OTA?.apiService
            ];

            for (const attempt of attempts) {
                try {
                    const service = attempt();
                    if (service) return service;
                } catch (error) {
                    // 继续尝试下一种方式
                }
            }
            return null;
        }

        /**
         * 获取语言名称
         * @param {Array|number} languagesIdArray - 语言ID数组或单个ID
         * @param {Object} order - 完整订单对象（用于备用字段查找）
         * @returns {string} 语言名称
         */
        getLanguageName(languagesIdArray, order = {}) {
            const languageMap = {
                2: '英文',
                3: '马来文', 
                4: '中文',
                5: '举牌'
            };

            // 尝试从多个可能的字段获取语言数据
            let actualLanguages = languagesIdArray || 
                                order.languagesIdArray ||
                                order.languages_id_array ||
                                order.languageIds ||
                                order.language_ids;

            // 处理单个ID的情况
            if (typeof actualLanguages === 'number') {
                return languageMap[actualLanguages] || '英文';
            }

            // 处理数组的情况
            if (Array.isArray(actualLanguages) && actualLanguages.length > 0) {
                const names = actualLanguages.map(id => languageMap[id] || `语言${id}`);
                return names.join(', ');
            }

            return '英文'; // 默认值
        }

        /**
         * 获取语言名称数组
         * @param {Array} languagesIdArray - 语言ID数组
         * @returns {string} 语言名称字符串
         */
        getLanguageNames(languagesIdArray) {
            if (!Array.isArray(languagesIdArray) || languagesIdArray.length === 0) {
                return '英文';
            }
            
            try {
                // 从API服务中获取语言映射
                const apiService = this.getApiService();
                if (apiService && apiService.staticData && apiService.staticData.languages) {
                    const languageNames = languagesIdArray.map(id => {
                        const language = apiService.staticData.languages.find(l => l.id === id);
                        return language ? language.name : `语言${id}`;
                    });
                    return languageNames.join(', ');
                }
                
                // 备用映射
                return this.getLanguageName(languagesIdArray);
            } catch (error) {
                this.logger.logError?.('获取语言名称失败', error);
                return this.getLanguageName(languagesIdArray);
            }
        }

        /**
         * 获取选中的语言名称数组
         * @param {number[]} languageIds - 语言ID数组
         * @returns {string[]} 语言名称数组
         */
        getSelectedLanguageNames(languageIds) {
            try {
                const languageManager = window.getLanguageManager?.();
                if (languageManager) {
                    const languages = languageManager.getLanguagesSync();
                    return languageIds.map(id => {
                        const lang = languages.find(l => l.id === id);
                        return lang ? lang.name : `语言${id}`;
                    });
                }
                
                // 备用处理
                const languageMap = {
                    2: '英文',
                    3: '马来文',
                    4: '中文', 
                    5: '举牌'
                };
                
                return languageIds.map(id => languageMap[id] || `语言${id}`);
            } catch (error) {
                this.logger.logError?.('获取语言名称失败', error);
                return languageIds.map(id => `语言${id}`);
            }
        }

        /**
         * 获取OTA渠道名称
         * @param {string} otaCode - OTA代码
         * @param {Object} order - 完整订单对象（用于备用字段查找）
         * @returns {string} OTA渠道名称
         */
        getOtaChannelName(otaCode, order = {}) {
            const otaMap = {
                'agoda': 'Agoda',
                'booking': 'Booking.com',
                'expedia': 'Expedia',
                'ctrip': '携程',
                'klook': 'Klook',
                'viator': 'Viator',
                'chong-dealer': 'Chong Dealer',
                'fallback': '通用模板'
            };

            // 尝试从多个可能的字段获取OTA代码
            let actualOtaCode = otaCode || 
                              order.ota ||
                              order.otaChannel ||
                              order.ota_channel ||
                              order.platform;

            return otaMap[actualOtaCode] || actualOtaCode || '未知渠道';
        }

        /**
         * 获取OTA参考号
         * @param {Object} order - 订单对象
         * @returns {string} OTA参考号
         */
        getOtaReferenceNumber(order) {
            // 尝试从多个可能的字段获取参考号
            const referenceNumber = order.otaReferenceNumber ||
                                  order.ota_reference_number ||
                                  order.referenceNumber ||
                                  order.reference_number ||
                                  order.bookingReference ||
                                  order.booking_reference ||
                                  order.confirmationNumber ||
                                  order.confirmation_number;

            return referenceNumber || '无参考号';
        }

        // ==================== 工具函数 ====================

        /**
         * 简化的时间点和航班特征分析
         * @param {string} text - 输入文本
         * @returns {Object} 分析结果
         */
        analyzeTimePointsAndFlights(text) {
            const analysis = {
                timePoints: [],
                hasFlightFeatures: false,
                isMultiTimePoint: false
            };

            try {
                // 简单时间点提取
                const timeMatches = text.match(/\d{1,2}:\d{2}|\d{4}/g) || [];
                analysis.timePoints = [...new Set(timeMatches)];

                // 简单航班特征检测
                const flightKeywords = ['航班', 'flight', 'AK', 'MH', 'OD'];
                analysis.hasFlightFeatures = flightKeywords.some(keyword =>
                    text.toLowerCase().includes(keyword.toLowerCase()));

                // 多时间点判断
                analysis.isMultiTimePoint = analysis.timePoints.length > 1;

                this.logger.log?.('🕐 简化时间点分析完成', 'info', analysis);
                return analysis;

            } catch (error) {
                this.logger.logError?.('时间点分析失败', error);
                return analysis;
            }
        }


    }

    // 导出到全局作用域
    window.OTA.MultiOrderUtils = MultiOrderUtils;

    // 创建单例实例
    let utilsInstance = null;

    /**
     * @OTA_FACTORY 获取多订单工具函数实例
     * 🏷️ 标签: @OTA_MULTI_ORDER_UTILS_FACTORY
     * 📝 说明: 单例工厂函数，获取工具函数实例
     * ⚠️ 警告: 已注册，请勿重复开发
     * @returns {MultiOrderUtils} 工具函数实例
     */
    function getMultiOrderUtils() {
        if (!utilsInstance) {
            utilsInstance = new MultiOrderUtils();
        }
        return utilsInstance;
    }

    // 导出工厂函数
    window.OTA.getMultiOrderUtils = getMultiOrderUtils;
    window.getMultiOrderUtils = getMultiOrderUtils;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        const instance = getMultiOrderUtils();
        window.OTA.Registry.registerManager('multiOrderUtils', instance, '@OTA_MULTI_ORDER_UTILS');
        window.OTA.Registry.registerFactory('getMultiOrderUtils', getMultiOrderUtils, '@OTA_MULTI_ORDER_UTILS_FACTORY');
    }

    console.log('✅ 多订单工具函数库已加载', {
        version: '1.0.0',
        functions: ['formatPrice', 'formatPhone', 'getServiceTypeName', 'getCarTypeName', 'getLanguageName', 'getOtaChannelName', 'getOtaReferenceNumber', 'analyzeTimePointsAndFlights']
    });

})();

// 结束防重复加载检查
}
