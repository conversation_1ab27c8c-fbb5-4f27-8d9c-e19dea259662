/**
 * @OTA_CORE 性能监控面板
 * 🏷️ 标签: @PERFORMANCE_DASHBOARD @MONITORING @REAL_TIME_DISPLAY
 * 📝 说明: 创建可视化的性能监控面板，实时显示系统性能指标和健康状态
 * ⚠️ 警告: 核心监控基础设施，请勿重复开发
 */

(function() {
    'use strict';

    // 延迟获取依赖，确保加载顺序
    function getConfigCenter() {
        return window.OTA?.configCenter || window.getConfigCenter?.();
    }

    function getLazyLoader() {
        return window.OTA?.lazyLoader || window.getLazyLoader?.();
    }

    function getDependencyResolver() {
        return window.OTA?.dependencyResolver || window.getDependencyResolver?.();
    }

    /**
     * @OTA_CORE 性能监控面板类
     * 提供实时性能监控、数据可视化和系统健康状态显示
     */
    class PerformanceMonitoringDashboard {
        constructor() {
            this.logger = window.OTA.getService('logger');
            this.configCenter = getConfigCenter();
            this.lazyLoader = getLazyLoader();
            this.dependencyResolver = getDependencyResolver();
            
            // 监控配置
            this.monitoringConfig = this.loadMonitoringConfig();
            
            // 性能数据收集器
            this.performanceCollectors = new Map();
            
            // 实时数据存储
            this.realtimeData = {
                system: {
                    memory: { used: 0, total: 0, percentage: 0 },
                    cpu: { usage: 0, cores: navigator.hardwareConcurrency || 4 },
                    network: { latency: 0, bandwidth: 0, status: 'unknown' }
                },
                application: {
                    loadTime: 0,
                    renderTime: 0,
                    apiCalls: { total: 0, success: 0, failed: 0, avgTime: 0 },
                    errors: { count: 0, rate: 0 },
                    cacheHits: { total: 0, hits: 0, rate: 0 }
                },
                modules: {
                    loaded: 0,
                    loading: 0,
                    failed: 0,
                    lazyLoaded: 0
                },
                dependencies: {
                    resolved: 0,
                    cached: 0,
                    failed: 0,
                    circularDeps: 0
                }
            };
            
            // 历史数据存储（最近100个数据点）
            this.historicalData = {
                timestamps: [],
                memory: [],
                cpu: [],
                apiLatency: [],
                errorRate: [],
                cacheHitRate: []
            };
            
            // 面板状态
            this.dashboardState = {
                isVisible: false,
                isMinimized: false,
                activeTab: 'overview',
                updateInterval: null,
                refreshRate: 2000 // 2秒更新一次
            };
            
            // 警报系统
            this.alertSystem = {
                thresholds: {
                    memoryUsage: 80, // 80%
                    errorRate: 5, // 5%
                    apiLatency: 3000, // 3秒
                    cacheHitRate: 50 // 50%
                },
                activeAlerts: new Set(),
                alertHistory: []
            };
            
            // 图表实例
            this.charts = new Map();
            
            this.initialize();
            
            this.logger.log('✅ 性能监控面板已初始化', 'info', {
                refreshRate: this.dashboardState.refreshRate,
                collectorsCount: this.performanceCollectors.size,
                alertThresholds: this.alertSystem.thresholds
            });
        }

        /**
         * 加载监控配置
         * @returns {Object} 监控配置
         */
        loadMonitoringConfig() {
            const defaultConfig = {
                // 基础配置
                enabled: true,
                autoStart: false,
                refreshRate: 2000,
                maxHistoryPoints: 100,
                
                // 数据收集配置
                collectors: {
                    system: true,
                    application: true,
                    modules: true,
                    dependencies: true,
                    network: true
                },
                
                // 显示配置
                display: {
                    position: 'bottom-right',
                    width: 400,
                    height: 300,
                    opacity: 0.95,
                    theme: 'dark'
                },
                
                // 警报配置
                alerts: {
                    enabled: true,
                    sound: false,
                    desktop: false,
                    thresholds: {
                        memoryUsage: 80,
                        errorRate: 5,
                        apiLatency: 3000,
                        cacheHitRate: 50
                    }
                },
                
                // 导出配置
                export: {
                    enabled: true,
                    format: 'json',
                    interval: 300000 // 5分钟
                }
            };

            if (this.configCenter) {
                const monitoringConfig = this.configCenter.getConfig('performanceMonitoring') || {};
                return {
                    ...defaultConfig,
                    ...monitoringConfig
                };
            }

            return defaultConfig;
        }

        /**
         * 初始化监控面板
         */
        initialize() {
            // 注册性能数据收集器
            this.registerPerformanceCollectors();
            
            // 创建面板UI
            this.createDashboardUI();
            
            // 设置键盘快捷键
            this.setupKeyboardShortcuts();
            
            // 设置警报系统
            this.setupAlertSystem();
            
            // 如果配置为自动启动，则开始监控
            if (this.monitoringConfig.autoStart) {
                this.startMonitoring();
            }
        }

        /**
         * 注册性能数据收集器
         */
        registerPerformanceCollectors() {
            // 系统性能收集器
            this.performanceCollectors.set('system', {
                collect: () => this.collectSystemMetrics(),
                enabled: this.monitoringConfig.collectors.system
            });
            
            // 应用性能收集器
            this.performanceCollectors.set('application', {
                collect: () => this.collectApplicationMetrics(),
                enabled: this.monitoringConfig.collectors.application
            });
            
            // 模块性能收集器
            this.performanceCollectors.set('modules', {
                collect: () => this.collectModuleMetrics(),
                enabled: this.monitoringConfig.collectors.modules
            });
            
            // 依赖性能收集器
            this.performanceCollectors.set('dependencies', {
                collect: () => this.collectDependencyMetrics(),
                enabled: this.monitoringConfig.collectors.dependencies
            });
            
            // 网络性能收集器
            this.performanceCollectors.set('network', {
                collect: () => this.collectNetworkMetrics(),
                enabled: this.monitoringConfig.collectors.network
            });
        }

        /**
         * 收集系统性能指标
         */
        collectSystemMetrics() {
            try {
                // 内存使用情况
                if (performance.memory) {
                    const memory = performance.memory;
                    this.realtimeData.system.memory = {
                        used: memory.usedJSHeapSize,
                        total: memory.totalJSHeapSize,
                        percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize * 100).toFixed(1)
                    };
                }
                
                // CPU使用情况（估算）
                const cpuStart = performance.now();
                const iterations = 10000;
                for (let i = 0; i < iterations; i++) {
                    Math.random();
                }
                const cpuEnd = performance.now();
                const cpuTime = cpuEnd - cpuStart;
                
                // 简单的CPU使用率估算
                this.realtimeData.system.cpu.usage = Math.min(100, (cpuTime / 10).toFixed(1));
                
            } catch (error) {
                this.logger.logError('系统性能指标收集失败', error);
            }
        }

        /**
         * 收集应用性能指标
         */
        collectApplicationMetrics() {
            try {
                // 页面加载时间
                if (performance.timing) {
                    const timing = performance.timing;
                    this.realtimeData.application.loadTime = timing.loadEventEnd - timing.navigationStart;
                }
                
                // 渲染时间
                if (performance.getEntriesByType) {
                    const paintEntries = performance.getEntriesByType('paint');
                    const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint');
                    if (fcp) {
                        this.realtimeData.application.renderTime = fcp.startTime;
                    }
                }
                
                // API调用统计（从全局监控获取）
                if (window.OTA && window.OTA.apiService) {
                    const apiMetrics = window.OTA.apiService.getMetrics?.() || {};
                    this.realtimeData.application.apiCalls = {
                        total: apiMetrics.totalCalls || 0,
                        success: apiMetrics.successCalls || 0,
                        failed: apiMetrics.failedCalls || 0,
                        avgTime: apiMetrics.averageTime || 0
                    };
                }
                
                // 错误统计
                const errorCount = this.logger.getErrorCount?.() || 0;
                this.realtimeData.application.errors = {
                    count: errorCount,
                    rate: this.calculateErrorRate(errorCount)
                };
                
            } catch (error) {
                this.logger.logError('应用性能指标收集失败', error);
            }
        }

        /**
         * 收集模块性能指标
         */
        collectModuleMetrics() {
            try {
                if (this.lazyLoader) {
                    const lazyMetrics = this.lazyLoader.getPerformanceMetrics();
                    const loadingStatus = this.lazyLoader.getLoadingStatus();
                    
                    this.realtimeData.modules = {
                        loaded: lazyMetrics.loadedModulesCount || 0,
                        loading: loadingStatus.loadingModules?.length || 0,
                        failed: lazyMetrics.failedLoads || 0,
                        lazyLoaded: lazyMetrics.totalRequests || 0
                    };
                    
                    // 缓存命中率
                    this.realtimeData.application.cacheHits = {
                        total: lazyMetrics.totalRequests || 0,
                        hits: lazyMetrics.cacheHits || 0,
                        rate: lazyMetrics.cacheHitRate ? (lazyMetrics.cacheHitRate * 100).toFixed(1) : 0
                    };
                }
            } catch (error) {
                this.logger.logError('模块性能指标收集失败', error);
            }
        }

        /**
         * 收集依赖性能指标
         */
        collectDependencyMetrics() {
            try {
                if (this.dependencyResolver) {
                    const depMetrics = this.dependencyResolver.getPerformanceMetrics?.() || {};
                    
                    this.realtimeData.dependencies = {
                        resolved: depMetrics.resolvedCount || 0,
                        cached: depMetrics.cachedCount || 0,
                        failed: depMetrics.failedCount || 0,
                        circularDeps: depMetrics.circularDependencies || 0
                    };
                }
            } catch (error) {
                this.logger.logError('依赖性能指标收集失败', error);
            }
        }

        /**
         * 收集网络性能指标
         */
        collectNetworkMetrics() {
            try {
                // 网络连接状态
                this.realtimeData.system.network.status = navigator.onLine ? 'online' : 'offline';
                
                // 网络延迟测试（简单ping测试）
                if (navigator.onLine) {
                    this.measureNetworkLatency();
                }
                
            } catch (error) {
                this.logger.logError('网络性能指标收集失败', error);
            }
        }

        /**
         * 测量网络延迟
         */
        async measureNetworkLatency() {
            try {
                const startTime = performance.now();
                
                // 使用一个小的图片请求来测试延迟
                const img = new Image();
                img.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7?' + Math.random();
                
                await new Promise((resolve, reject) => {
                    img.onload = resolve;
                    img.onerror = reject;
                    setTimeout(reject, 5000); // 5秒超时
                });
                
                const endTime = performance.now();
                this.realtimeData.system.network.latency = Math.round(endTime - startTime);
                
            } catch (error) {
                this.realtimeData.system.network.latency = -1; // 表示测试失败
            }
        }

        /**
         * 计算错误率
         * @param {number} errorCount - 错误数量
         * @returns {number} 错误率百分比
         */
        calculateErrorRate(errorCount) {
            const totalOperations = this.realtimeData.application.apiCalls.total + 
                                  this.realtimeData.modules.loaded + 
                                  this.realtimeData.dependencies.resolved;
            
            if (totalOperations === 0) return 0;
            
            return ((errorCount / totalOperations) * 100).toFixed(2);
        }

        /**
         * 创建面板UI
         */
        createDashboardUI() {
            // 创建面板容器
            const dashboard = document.createElement('div');
            dashboard.id = 'performance-monitoring-dashboard';
            dashboard.className = 'performance-dashboard';
            dashboard.style.cssText = `
                position: fixed;
                ${this.monitoringConfig.display.position.includes('right') ? 'right: 20px;' : 'left: 20px;'}
                ${this.monitoringConfig.display.position.includes('bottom') ? 'bottom: 20px;' : 'top: 20px;'}
                width: ${this.monitoringConfig.display.width}px;
                height: ${this.monitoringConfig.display.height}px;
                background: ${this.monitoringConfig.display.theme === 'dark' ? 'rgba(0, 0, 0, 0.9)' : 'rgba(255, 255, 255, 0.9)'};
                color: ${this.monitoringConfig.display.theme === 'dark' ? '#fff' : '#000'};
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                z-index: 10000;
                opacity: ${this.monitoringConfig.display.opacity};
                display: none;
                overflow: hidden;
                backdrop-filter: blur(10px);
            `;
            
            // 创建标题栏
            const titleBar = document.createElement('div');
            titleBar.className = 'dashboard-title-bar';
            titleBar.style.cssText = `
                padding: 8px 12px;
                background: ${this.monitoringConfig.display.theme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'};
                border-bottom: 1px solid ${this.monitoringConfig.display.theme === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)'};
                display: flex;
                justify-content: space-between;
                align-items: center;
                cursor: move;
                user-select: none;
            `;
            
            titleBar.innerHTML = `
                <span>🔍 性能监控面板</span>
                <div>
                    <button id="dashboard-minimize" style="background: none; border: none; color: inherit; cursor: pointer; margin-right: 5px;">−</button>
                    <button id="dashboard-close" style="background: none; border: none; color: inherit; cursor: pointer;">×</button>
                </div>
            `;
            
            // 创建内容区域
            const content = document.createElement('div');
            content.className = 'dashboard-content';
            content.style.cssText = `
                padding: 12px;
                height: calc(100% - 40px);
                overflow-y: auto;
            `;
            
            // 创建标签页
            const tabs = document.createElement('div');
            tabs.className = 'dashboard-tabs';
            tabs.style.cssText = `
                display: flex;
                margin-bottom: 10px;
                border-bottom: 1px solid ${this.monitoringConfig.display.theme === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)'};
            `;
            
            const tabButtons = ['概览', '系统', '应用', '模块', '网络'];
            const tabIds = ['overview', 'system', 'application', 'modules', 'network'];
            
            tabButtons.forEach((tabName, index) => {
                const tab = document.createElement('button');
                tab.textContent = tabName;
                tab.className = 'dashboard-tab';
                tab.dataset.tab = tabIds[index];
                tab.style.cssText = `
                    background: none;
                    border: none;
                    color: inherit;
                    padding: 5px 10px;
                    cursor: pointer;
                    border-bottom: 2px solid transparent;
                    ${index === 0 ? 'border-bottom-color: #007bff;' : ''}
                `;
                
                tab.addEventListener('click', () => this.switchTab(tabIds[index]));
                tabs.appendChild(tab);
            });
            
            // 创建内容面板
            const panels = document.createElement('div');
            panels.className = 'dashboard-panels';
            panels.innerHTML = this.createPanelContent();
            
            // 组装面板
            content.appendChild(tabs);
            content.appendChild(panels);
            dashboard.appendChild(titleBar);
            dashboard.appendChild(content);
            
            // 添加到页面
            document.body.appendChild(dashboard);
            
            // 设置事件监听器
            this.setupDashboardEvents(dashboard);
            
            this.dashboardElement = dashboard;
        }

        /**
         * 创建面板内容
         * @returns {string} HTML内容
         */
        createPanelContent() {
            return `
                <div id="panel-overview" class="dashboard-panel" style="display: block;">
                    <div class="metrics-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 15px;">
                        <div class="metric-card" style="background: rgba(0, 123, 255, 0.1); padding: 8px; border-radius: 4px;">
                            <div class="metric-label">内存使用</div>
                            <div class="metric-value" id="overview-memory">0%</div>
                        </div>
                        <div class="metric-card" style="background: rgba(40, 167, 69, 0.1); padding: 8px; border-radius: 4px;">
                            <div class="metric-label">缓存命中率</div>
                            <div class="metric-value" id="overview-cache">0%</div>
                        </div>
                        <div class="metric-card" style="background: rgba(255, 193, 7, 0.1); padding: 8px; border-radius: 4px;">
                            <div class="metric-label">API延迟</div>
                            <div class="metric-value" id="overview-latency">0ms</div>
                        </div>
                        <div class="metric-card" style="background: rgba(220, 53, 69, 0.1); padding: 8px; border-radius: 4px;">
                            <div class="metric-label">错误率</div>
                            <div class="metric-value" id="overview-errors">0%</div>
                        </div>
                    </div>
                    <div class="system-status">
                        <div class="status-item">
                            <span>系统状态:</span>
                            <span id="system-status" class="status-indicator">🟢 正常</span>
                        </div>
                        <div class="status-item">
                            <span>网络状态:</span>
                            <span id="network-status" class="status-indicator">🟢 在线</span>
                        </div>
                        <div class="status-item">
                            <span>已加载模块:</span>
                            <span id="loaded-modules">0</span>
                        </div>
                    </div>
                </div>
                
                <div id="panel-system" class="dashboard-panel" style="display: none;">
                    <div class="system-metrics">
                        <div class="metric-row">
                            <span>内存使用:</span>
                            <span id="system-memory-detail">0 MB / 0 MB (0%)</span>
                        </div>
                        <div class="metric-row">
                            <span>CPU使用:</span>
                            <span id="system-cpu">0%</span>
                        </div>
                        <div class="metric-row">
                            <span>CPU核心:</span>
                            <span id="system-cores">4</span>
                        </div>
                        <div class="metric-row">
                            <span>网络延迟:</span>
                            <span id="system-network-latency">0ms</span>
                        </div>
                    </div>
                </div>
                
                <div id="panel-application" class="dashboard-panel" style="display: none;">
                    <div class="app-metrics">
                        <div class="metric-row">
                            <span>页面加载时间:</span>
                            <span id="app-load-time">0ms</span>
                        </div>
                        <div class="metric-row">
                            <span>首次渲染:</span>
                            <span id="app-render-time">0ms</span>
                        </div>
                        <div class="metric-row">
                            <span>API调用总数:</span>
                            <span id="app-api-total">0</span>
                        </div>
                        <div class="metric-row">
                            <span>API成功率:</span>
                            <span id="app-api-success">0%</span>
                        </div>
                        <div class="metric-row">
                            <span>平均API时间:</span>
                            <span id="app-api-avg-time">0ms</span>
                        </div>
                        <div class="metric-row">
                            <span>错误数量:</span>
                            <span id="app-error-count">0</span>
                        </div>
                    </div>
                </div>
                
                <div id="panel-modules" class="dashboard-panel" style="display: none;">
                    <div class="module-metrics">
                        <div class="metric-row">
                            <span>已加载模块:</span>
                            <span id="modules-loaded">0</span>
                        </div>
                        <div class="metric-row">
                            <span>正在加载:</span>
                            <span id="modules-loading">0</span>
                        </div>
                        <div class="metric-row">
                            <span>加载失败:</span>
                            <span id="modules-failed">0</span>
                        </div>
                        <div class="metric-row">
                            <span>懒加载请求:</span>
                            <span id="modules-lazy-loaded">0</span>
                        </div>
                        <div class="metric-row">
                            <span>缓存命中:</span>
                            <span id="modules-cache-hits">0</span>
                        </div>
                        <div class="metric-row">
                            <span>缓存命中率:</span>
                            <span id="modules-cache-rate">0%</span>
                        </div>
                    </div>
                </div>
                
                <div id="panel-network" class="dashboard-panel" style="display: none;">
                    <div class="network-metrics">
                        <div class="metric-row">
                            <span>连接状态:</span>
                            <span id="network-connection-status">未知</span>
                        </div>
                        <div class="metric-row">
                            <span>网络延迟:</span>
                            <span id="network-latency-detail">0ms</span>
                        </div>
                        <div class="metric-row">
                            <span>连接类型:</span>
                            <span id="network-connection-type">未知</span>
                        </div>
                        <div class="metric-row">
                            <span>下行速度:</span>
                            <span id="network-downlink">未知</span>
                        </div>
                    </div>
                </div>
            `;
        }

        /**
         * 设置面板事件监听器
         * @param {HTMLElement} dashboard - 面板元素
         */
        setupDashboardEvents(dashboard) {
            // 关闭按钮
            const closeBtn = dashboard.querySelector('#dashboard-close');
            closeBtn.addEventListener('click', () => this.hideDashboard());
            
            // 最小化按钮
            const minimizeBtn = dashboard.querySelector('#dashboard-minimize');
            minimizeBtn.addEventListener('click', () => this.toggleMinimize());
            
            // 拖拽功能
            const titleBar = dashboard.querySelector('.dashboard-title-bar');
            this.makeDraggable(dashboard, titleBar);
        }

        /**
         * 使面板可拖拽
         * @param {HTMLElement} element - 要拖拽的元素
         * @param {HTMLElement} handle - 拖拽手柄
         */
        makeDraggable(element, handle) {
            let isDragging = false;
            let startX, startY, startLeft, startTop;
            
            handle.addEventListener('mousedown', (e) => {
                isDragging = true;
                startX = e.clientX;
                startY = e.clientY;
                startLeft = parseInt(window.getComputedStyle(element).left, 10);
                startTop = parseInt(window.getComputedStyle(element).top, 10);
                
                document.addEventListener('mousemove', onMouseMove);
                document.addEventListener('mouseup', onMouseUp);
                
                e.preventDefault();
            });
            
            function onMouseMove(e) {
                if (!isDragging) return;
                
                const deltaX = e.clientX - startX;
                const deltaY = e.clientY - startY;
                
                element.style.left = (startLeft + deltaX) + 'px';
                element.style.top = (startTop + deltaY) + 'px';
                element.style.right = 'auto';
                element.style.bottom = 'auto';
            }
            
            function onMouseUp() {
                isDragging = false;
                document.removeEventListener('mousemove', onMouseMove);
                document.removeEventListener('mouseup', onMouseUp);
            }
        }

        /**
         * 切换标签页
         * @param {string} tabId - 标签页ID
         */
        switchTab(tabId) {
            // 更新标签按钮状态
            const tabs = this.dashboardElement.querySelectorAll('.dashboard-tab');
            tabs.forEach(tab => {
                if (tab.dataset.tab === tabId) {
                    tab.style.borderBottomColor = '#007bff';
                } else {
                    tab.style.borderBottomColor = 'transparent';
                }
            });
            
            // 显示对应面板
            const panels = this.dashboardElement.querySelectorAll('.dashboard-panel');
            panels.forEach(panel => {
                panel.style.display = panel.id === `panel-${tabId}` ? 'block' : 'none';
            });
            
            this.dashboardState.activeTab = tabId;
        }

        /**
         * 设置键盘快捷键
         */
        setupKeyboardShortcuts() {
            document.addEventListener('keydown', (e) => {
                // Ctrl+Shift+P 显示/隐藏面板
                if (e.ctrlKey && e.shiftKey && e.key === 'P') {
                    e.preventDefault();
                    this.toggleDashboard();
                }
                
                // Ctrl+Shift+M 最小化/恢复面板
                if (e.ctrlKey && e.shiftKey && e.key === 'M') {
                    e.preventDefault();
                    if (this.dashboardState.isVisible) {
                        this.toggleMinimize();
                    }
                }
            });
        }

        /**
         * 设置警报系统
         */
        setupAlertSystem() {
            // 定期检查警报条件
            setInterval(() => {
                if (!this.monitoringConfig.alerts.enabled) return;
                
                this.checkAlertConditions();
            }, 5000); // 每5秒检查一次
        }

        /**
         * 检查警报条件
         */
        checkAlertConditions() {
            const thresholds = this.alertSystem.thresholds;
            const data = this.realtimeData;
            
            // 检查内存使用率
            if (data.system.memory.percentage > thresholds.memoryUsage) {
                this.triggerAlert('memory', `内存使用率过高: ${data.system.memory.percentage}%`);
            } else {
                this.clearAlert('memory');
            }
            
            // 检查错误率
            if (data.application.errors.rate > thresholds.errorRate) {
                this.triggerAlert('errors', `错误率过高: ${data.application.errors.rate}%`);
            } else {
                this.clearAlert('errors');
            }
            
            // 检查API延迟
            if (data.application.apiCalls.avgTime > thresholds.apiLatency) {
                this.triggerAlert('latency', `API延迟过高: ${data.application.apiCalls.avgTime}ms`);
            } else {
                this.clearAlert('latency');
            }
            
            // 检查缓存命中率
            if (data.application.cacheHits.rate < thresholds.cacheHitRate) {
                this.triggerAlert('cache', `缓存命中率过低: ${data.application.cacheHits.rate}%`);
            } else {
                this.clearAlert('cache');
            }
        }

        /**
         * 触发警报
         * @param {string} type - 警报类型
         * @param {string} message - 警报消息
         */
        triggerAlert(type, message) {
            if (this.alertSystem.activeAlerts.has(type)) return;
            
            this.alertSystem.activeAlerts.add(type);
            this.alertSystem.alertHistory.push({
                type,
                message,
                timestamp: new Date().toISOString(),
                status: 'active'
            });
            
            this.logger.log(`⚠️ 性能警报: ${message}`, 'warning');
            
            // 桌面通知
            if (this.monitoringConfig.alerts.desktop && 'Notification' in window) {
                if (Notification.permission === 'granted') {
                    new Notification('性能监控警报', {
                        body: message,
                        icon: 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="orange" d="M12 2L1 21h22L12 2zm0 3.5L19.5 19h-15L12 5.5zm-1 8.5h2v2h-2v-2zm0-6h2v4h-2V8z"/></svg>'
                    });
                }
            }
        }

        /**
         * 清除警报
         * @param {string} type - 警报类型
         */
        clearAlert(type) {
            if (!this.alertSystem.activeAlerts.has(type)) return;
            
            this.alertSystem.activeAlerts.delete(type);
            
            // 更新历史记录
            const lastAlert = this.alertSystem.alertHistory
                .filter(alert => alert.type === type && alert.status === 'active')
                .pop();
            
            if (lastAlert) {
                lastAlert.status = 'resolved';
                lastAlert.resolvedAt = new Date().toISOString();
            }
        }

        /**
         * 开始监控
         */
        startMonitoring() {
            if (this.dashboardState.updateInterval) {
                this.stopMonitoring();
            }
            
            this.dashboardState.updateInterval = setInterval(() => {
                this.collectAllMetrics();
                this.updateDashboardDisplay();
                this.updateHistoricalData();
            }, this.dashboardState.refreshRate);
            
            this.logger.log('✅ 性能监控已启动', 'success', {
                refreshRate: this.dashboardState.refreshRate
            });
        }

        /**
         * 停止监控
         */
        stopMonitoring() {
            if (this.dashboardState.updateInterval) {
                clearInterval(this.dashboardState.updateInterval);
                this.dashboardState.updateInterval = null;
            }
            
            this.logger.log('⏹️ 性能监控已停止', 'info');
        }

        /**
         * 收集所有性能指标
         */
        collectAllMetrics() {
            this.performanceCollectors.forEach((collector, name) => {
                if (collector.enabled) {
                    try {
                        collector.collect();
                    } catch (error) {
                        this.logger.logError(`性能指标收集失败: ${name}`, error);
                    }
                }
            });
        }

        /**
         * 更新面板显示
         */
        updateDashboardDisplay() {
            if (!this.dashboardState.isVisible || !this.dashboardElement) return;
            
            const data = this.realtimeData;
            
            // 更新概览面板
            this.updateElement('overview-memory', `${data.system.memory.percentage}%`);
            this.updateElement('overview-cache', `${data.application.cacheHits.rate}%`);
            this.updateElement('overview-latency', `${data.application.apiCalls.avgTime}ms`);
            this.updateElement('overview-errors', `${data.application.errors.rate}%`);
            
            // 更新状态指示器
            this.updateElement('system-status', this.getSystemStatusText());
            this.updateElement('network-status', data.system.network.status === 'online' ? '🟢 在线' : '🔴 离线');
            this.updateElement('loaded-modules', data.modules.loaded);
            
            // 更新系统面板
            this.updateElement('system-memory-detail', 
                `${(data.system.memory.used / 1024 / 1024).toFixed(1)} MB / ${(data.system.memory.total / 1024 / 1024).toFixed(1)} MB (${data.system.memory.percentage}%)`);
            this.updateElement('system-cpu', `${data.system.cpu.usage}%`);
            this.updateElement('system-cores', data.system.cpu.cores);
            this.updateElement('system-network-latency', `${data.system.network.latency}ms`);
            
            // 更新应用面板
            this.updateElement('app-load-time', `${data.application.loadTime}ms`);
            this.updateElement('app-render-time', `${data.application.renderTime.toFixed(1)}ms`);
            this.updateElement('app-api-total', data.application.apiCalls.total);
            this.updateElement('app-api-success', 
                data.application.apiCalls.total > 0 ? 
                `${((data.application.apiCalls.success / data.application.apiCalls.total) * 100).toFixed(1)}%` : '0%');
            this.updateElement('app-api-avg-time', `${data.application.apiCalls.avgTime}ms`);
            this.updateElement('app-error-count', data.application.errors.count);
            
            // 更新模块面板
            this.updateElement('modules-loaded', data.modules.loaded);
            this.updateElement('modules-loading', data.modules.loading);
            this.updateElement('modules-failed', data.modules.failed);
            this.updateElement('modules-lazy-loaded', data.modules.lazyLoaded);
            this.updateElement('modules-cache-hits', data.application.cacheHits.hits);
            this.updateElement('modules-cache-rate', `${data.application.cacheHits.rate}%`);
            
            // 更新网络面板
            this.updateElement('network-connection-status', data.system.network.status);
            this.updateElement('network-latency-detail', `${data.system.network.latency}ms`);
            
            // 更新连接信息
            if (navigator.connection) {
                this.updateElement('network-connection-type', navigator.connection.effectiveType || '未知');
                this.updateElement('network-downlink', `${navigator.connection.downlink || 0} Mbps`);
            }
        }

        /**
         * 更新元素内容
         * @param {string} id - 元素ID
         * @param {string} content - 内容
         */
        updateElement(id, content) {
            const element = this.dashboardElement.querySelector(`#${id}`);
            if (element) {
                element.textContent = content;
            }
        }

        /**
         * 获取系统状态文本
         * @returns {string} 状态文本
         */
        getSystemStatusText() {
            const alerts = this.alertSystem.activeAlerts.size;
            
            if (alerts === 0) {
                return '🟢 正常';
            } else if (alerts <= 2) {
                return '🟡 警告';
            } else {
                return '🔴 异常';
            }
        }

        /**
         * 更新历史数据
         */
        updateHistoricalData() {
            const now = new Date().toLocaleTimeString();
            const data = this.realtimeData;
            
            // 添加新数据点
            this.historicalData.timestamps.push(now);
            this.historicalData.memory.push(parseFloat(data.system.memory.percentage));
            this.historicalData.cpu.push(parseFloat(data.system.cpu.usage));
            this.historicalData.apiLatency.push(data.application.apiCalls.avgTime);
            this.historicalData.errorRate.push(parseFloat(data.application.errors.rate));
            this.historicalData.cacheHitRate.push(parseFloat(data.application.cacheHits.rate));
            
            // 保持最大数据点数量
            const maxPoints = this.monitoringConfig.maxHistoryPoints;
            Object.keys(this.historicalData).forEach(key => {
                if (this.historicalData[key].length > maxPoints) {
                    this.historicalData[key].shift();
                }
            });
        }

        /**
         * 显示面板
         */
        showDashboard() {
            if (this.dashboardElement) {
                this.dashboardElement.style.display = 'block';
                this.dashboardState.isVisible = true;
                
                if (!this.dashboardState.updateInterval) {
                    this.startMonitoring();
                }
            }
        }

        /**
         * 隐藏面板
         */
        hideDashboard() {
            if (this.dashboardElement) {
                this.dashboardElement.style.display = 'none';
                this.dashboardState.isVisible = false;
                
                this.stopMonitoring();
            }
        }

        /**
         * 切换面板显示状态
         */
        toggleDashboard() {
            if (this.dashboardState.isVisible) {
                this.hideDashboard();
            } else {
                this.showDashboard();
            }
        }

        /**
         * 切换最小化状态
         */
        toggleMinimize() {
            if (!this.dashboardElement) return;
            
            const content = this.dashboardElement.querySelector('.dashboard-content');
            
            if (this.dashboardState.isMinimized) {
                // 恢复
                content.style.display = 'block';
                this.dashboardElement.style.height = this.monitoringConfig.display.height + 'px';
                this.dashboardState.isMinimized = false;
            } else {
                // 最小化
                content.style.display = 'none';
                this.dashboardElement.style.height = '40px';
                this.dashboardState.isMinimized = true;
            }
        }

        /**
         * 导出性能数据
         * @param {string} format - 导出格式 ('json' | 'csv')
         * @returns {string} 导出的数据
         */
        exportData(format = 'json') {
            const exportData = {
                timestamp: new Date().toISOString(),
                realtimeData: this.realtimeData,
                historicalData: this.historicalData,
                alertHistory: this.alertSystem.alertHistory,
                config: this.monitoringConfig
            };
            
            if (format === 'json') {
                return JSON.stringify(exportData, null, 2);
            } else if (format === 'csv') {
                // 简化的CSV导出
                const csvData = this.historicalData.timestamps.map((timestamp, index) => {
                    return [
                        timestamp,
                        this.historicalData.memory[index],
                        this.historicalData.cpu[index],
                        this.historicalData.apiLatency[index],
                        this.historicalData.errorRate[index],
                        this.historicalData.cacheHitRate[index]
                    ].join(',');
                });
                
                csvData.unshift('时间,内存使用率,CPU使用率,API延迟,错误率,缓存命中率');
                return csvData.join('\n');
            }
            
            return JSON.stringify(exportData);
        }

        /**
         * 获取性能报告
         * @returns {Object} 性能报告
         */
        getPerformanceReport() {
            return {
                timestamp: new Date().toISOString(),
                summary: {
                    systemHealth: this.getSystemStatusText(),
                    activeAlerts: this.alertSystem.activeAlerts.size,
                    totalAlerts: this.alertSystem.alertHistory.length,
                    monitoringDuration: this.dashboardState.updateInterval ? 'Active' : 'Stopped'
                },
                currentMetrics: this.realtimeData,
                historicalTrends: {
                    memoryTrend: this.calculateTrend(this.historicalData.memory),
                    cpuTrend: this.calculateTrend(this.historicalData.cpu),
                    latencyTrend: this.calculateTrend(this.historicalData.apiLatency),
                    errorTrend: this.calculateTrend(this.historicalData.errorRate)
                },
                recommendations: this.generateRecommendations()
            };
        }

        /**
         * 计算趋势
         * @param {Array} data - 数据数组
         * @returns {string} 趋势描述
         */
        calculateTrend(data) {
            if (data.length < 2) return 'insufficient_data';
            
            const recent = data.slice(-5); // 最近5个数据点
            const older = data.slice(-10, -5); // 之前5个数据点
            
            if (recent.length === 0 || older.length === 0) return 'insufficient_data';
            
            const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
            const olderAvg = older.reduce((a, b) => a + b, 0) / older.length;
            
            const change = ((recentAvg - olderAvg) / olderAvg) * 100;
            
            if (Math.abs(change) < 5) return 'stable';
            return change > 0 ? 'increasing' : 'decreasing';
        }

        /**
         * 生成性能建议
         * @returns {Array} 建议列表
         */
        generateRecommendations() {
            const recommendations = [];
            const data = this.realtimeData;
            
            // 内存使用建议
            if (data.system.memory.percentage > 80) {
                recommendations.push({
                    type: 'memory',
                    priority: 'high',
                    message: '内存使用率过高，建议清理缓存或优化内存使用'
                });
            }
            
            // 缓存命中率建议
            if (data.application.cacheHits.rate < 60) {
                recommendations.push({
                    type: 'cache',
                    priority: 'medium',
                    message: '缓存命中率较低，建议优化缓存策略'
                });
            }
            
            // API性能建议
            if (data.application.apiCalls.avgTime > 2000) {
                recommendations.push({
                    type: 'api',
                    priority: 'medium',
                    message: 'API响应时间较慢，建议优化API调用或增加缓存'
                });
            }
            
            // 错误率建议
            if (data.application.errors.rate > 2) {
                recommendations.push({
                    type: 'errors',
                    priority: 'high',
                    message: '错误率较高，建议检查错误日志并修复问题'
                });
            }
            
            return recommendations;
        }

        /**
         * 获取面板状态
         * @returns {Object} 面板状态
         */
        getStatus() {
            return {
                isVisible: this.dashboardState.isVisible,
                isMinimized: this.dashboardState.isMinimized,
                isMonitoring: !!this.dashboardState.updateInterval,
                activeTab: this.dashboardState.activeTab,
                refreshRate: this.dashboardState.refreshRate,
                activeAlerts: this.alertSystem.activeAlerts.size,
                collectorsEnabled: Array.from(this.performanceCollectors.entries())
                    .filter(([, collector]) => collector.enabled)
                    .map(([name]) => name)
            };
        }
    }

    // 创建全局实例
    const performanceMonitoringDashboard = new PerformanceMonitoringDashboard();

    // 导出到全局作用域
    window.OTA = window.OTA || {};
    window.OTA.performanceMonitoringDashboard = performanceMonitoringDashboard;
    window.OTA.getPerformanceMonitoringDashboard = () => performanceMonitoringDashboard;

    // 向后兼容
    window.getPerformanceMonitoringDashboard = () => performanceMonitoringDashboard;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('performanceMonitoringDashboard', performanceMonitoringDashboard, '@OTA_PERFORMANCE_DASHBOARD');
        window.OTA.Registry.registerFactory('getPerformanceMonitoringDashboard', () => performanceMonitoringDashboard, '@OTA_PERFORMANCE_DASHBOARD_FACTORY');
    }

    // 请求桌面通知权限
    if ('Notification' in window && Notification.permission === 'default') {
        Notification.requestPermission();
    }

})();
