# OTA系统 API 参考文档

## 📋 概述

本文档包含OTA订单处理系统的完整API参考，涵盖GoMyHire后端API和智能学习引擎API。

### 版本信息
- **GoMyHire API版本**: v1.0
- **智能学习引擎版本**: v1.0.0
- **最后更新**: 2025-07-27
- **兼容性**: 现代浏览器 (Chrome 80+, Firefox 75+, Safari 13+)

## 🌐 GoMyHire 后端 API

### API 基础信息
- **基础 URL**: `https://gomyhire.com.my/api`
- **认证方式**: Bearer <PERSON> (JWT)
- **内容类型**: `application/json`
- **超时时间**: 30秒
- **默认编码**: UTF-8

### 🔐 认证端点

#### 登录接口
```http
POST /login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "Gomyhire@123456"
}
```

**响应格式**:
```json
{
    "status": true,
    "token": "2409|F0hye4nKqL58qhcdW8knwLwLMbQltjl35uggBwGA"
}
```

**Token 处理规则**:
- 提取 `|` 后的实际 token 部分
- 存储在 AppState 中用于后续请求
- 支持"记住我"功能

### 📦 订单创建接口

#### 创建订单端点
```http
POST /create_order
Content-Type: application/json
Authorization: 不需要认证
```

#### 必填字段 (Required Fields)

| 字段名 | 类型 | 说明 | 示例 | 验证规则 |
|--------|------|------|------|----------|
| `sub_category_id` | integer | 服务子类别ID | 1 | 必填，来自系统子类别列表 |
| `ota_reference_number` | string | OTA参考号 | "BK123456789" | 必填，唯一标识 |
| `car_type_id` | integer | 车型ID | 2 | 必填，来自系统车型列表 |
| `incharge_by_backend_user_id` | integer | 负责后台用户ID | 3 | 必填，来自后台用户列表 |

#### 重要提示
- `sub_category_id` 包含预设的 `order_type`, `ota`, `driving_region`, `languages`
- 传入 `sub_category_id` 后系统会自动填充相关预设值
- `driving_region_id` 和 `languages_id_array` 可选择性覆盖子类别预设值

#### 推荐填写字段 (Recommended Fields)

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `customer_name` | string | 客户姓名 | "John Smith" |
| `customer_contact` | string | 客户联系方式 | "+60123456789" |
| `pickup` | string | 上车地点 | "KLIA Terminal 1" |
| `destination` | string | 目的地 | "Kuala Lumpur City Center" |
| `date` | string | 接送日期 (YYYY-MM-DD) | "2025-01-20" |
| `time` | string | 接送时间 (HH:mm) | "14:30" |
| `passenger_number` | integer | 乘客人数 | 4 |

#### 可选字段 (Optional Fields)

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `ota` | string | OTA平台标识 | "booking" |
| `customer_email` | string | 客户邮箱 | "<EMAIL>" |
| `flight_info` | string | 航班信息 | "MH370 15:30" |
| `pickup_lat` | decimal | 上车地点纬度 | 2.7456 |
| `pickup_long` | decimal | 上车地点经度 | 101.7072 |
| `destination_lat` | decimal | 目的地纬度 | 3.1390 |
| `destination_long` | decimal | 目的地经度 | 101.6869 |
| `luggage_number` | integer | 行李件数 | 2 |
| `ota_price` | decimal | OTA价格 | 150.00 |
| `driver_fee` | decimal | 司机费用 | 120.00 |
| `driver_collect` | decimal | 司机收取费用 | 130.00 |
| `tour_guide` | boolean | 是否需要导游 | true |
| `baby_chair` | boolean | 是否需要婴儿椅 | false |
| `meet_and_greet` | boolean | 是否需要接机服务 | true |
| `extra_requirement` | text | 额外要求 | "需要儿童座椅" |
| `driving_region_id` | integer | 驾驶区域ID (覆盖子类别设置) | 2 |
| `languages_id_array` | array/object | 语言ID数组 (覆盖子类别设置) | [1,2,3] 或 {"0":"1","1":"2","2":"3"} |

#### 语言支持字段

系统支持多语言处理，可选择以下语言：

| Language ID | 语言名称 | 代码 |
|-------------|----------|------|
| 1 | English | en |
| 2 | 中文 | zh |
| 3 | Bahasa Malaysia | ms |
| 4 | Tamil | ta |

### 📊 系统数据端点

#### 获取后台用户列表
```http
GET /backend_users?search=
Authorization: Bearer {token}
```
**响应格式**:
```json
{
    "data": [
        {
            "id": 1,
            "name": "super admin name",
            "phone": "012345689",
            "role_id": "super admin"
        }
    ]
}
```

#### 获取服务子类别
```http
GET /sub_category?search=
Authorization: Bearer {token}
```
**说明**: 子类别包含预设的 order_type, ota, driving_region, languages

#### 获取车型列表  
```http
GET /car_types?search=
Authorization: Bearer {token}
```

#### 获取驾驶区域
```http
GET /driving_regions?search=
Authorization: Bearer {token}
```

#### 获取语言列表
```http
GET /languages?search=
Authorization: Bearer {token}
```

**搜索参数说明**:
- `search` 参数支持按ID、名称、相关字段模糊匹配
- 所有端点都支持搜索过滤功能

## 🤖 AI智能分析 API

### 核心模块

#### 1. Gemini AI服务

提供智能订单解析和数据提取功能。

##### 方法

**`parseOrder(orderText: string, isRealtime: boolean): Promise<Object>`**
解析订单文本。

```javascript
const geminiService = window.OTA.geminiService;
const result = await geminiService.parseOrder(orderText, false);
const orderData = result.data;
```

**`set(key: string, value: any): void`**
设置配置值。

```javascript
config.set('learningSystem.enabled', true);
config.set('storage.retentionDays', 30);
```

#### 2. 存储管理器 (LearningStorageManager)

管理学习数据的本地存储。

##### 方法

**`store(key: string, data: any): Promise<void>`**
存储学习数据。

```javascript
const storageManager = window.OTA.learningStorageManager;
await storageManager.store('userPattern_123', patternData);
```

**`retrieve(key: string): Promise<any>`**
检索学习数据。

```javascript
const patternData = await storageManager.retrieve('userPattern_123');
```

#### 3. 模式分析器 (PatternAnalyzer)

分析用户操作模式和数据格式。

##### 方法

**`analyzePattern(data: any): AnalysisResult`**
分析数据模式。

```javascript
const analyzer = window.OTA.patternAnalyzer;
const result = analyzer.analyzePattern(orderData);
```

**`learnFromCorrection(original: any, corrected: any): void`**
从用户修正中学习。

```javascript
analyzer.learnFromCorrection(originalData, correctedData);
```

#### 4. 预测引擎 (PredictionEngine)

基于学习结果预测和建议数据格式。

##### 方法

**`predict(input: any): PredictionResult`**
预测数据格式。

```javascript
const engine = window.OTA.predictionEngine;
const prediction = engine.predict(inputData);
```

**`applySuggestions(data: any, suggestions: Suggestion[]): any`**
应用建议修正。

```javascript
const correctedData = engine.applySuggestions(data, suggestions);
```

### 智能学习工作流程

1. **数据输入** → 用户输入订单文本
2. **模式识别** → 系统识别已知模式
3. **预测处理** → 基于学习数据预测格式
4. **建议展示** → 向用户展示建议修正
5. **学习更新** → 从用户确认中学习新模式

### 配置选项

```javascript
// 学习系统配置
const learningConfig = {
    enabled: true,                    // 启用学习功能
    minPatternOccurrence: 3,         // 最小模式出现次数
    maxStorageSize: 50 * 1024 * 1024, // 最大存储大小(50MB)
    retentionDays: 30,               // 数据保留天数
    confidenceThreshold: 0.7,        // 置信度阈值
    autoApplyThreshold: 0.9          // 自动应用阈值
};
```

## 🔗 API 集成示例

### 完整订单创建流程

```javascript
// 1. 登录获取token
const loginResponse = await fetch('https://gomyhire.com.my/api/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Gomyhire@123456'
    })
});
const { token } = await loginResponse.json();

// 2. 使用Gemini AI处理订单数据
const rawOrderText = "从KLIA到市中心，明天下午2点，4个人";
const geminiService = window.OTA.geminiService;
const parseResult = await geminiService.parseOrder(rawOrderText, false);
const processedData = parseResult.data;

// 3. 创建订单
const orderResponse = await fetch('https://gomyhire.com.my/api/create_order', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(processedData)
});

const result = await orderResponse.json();
```

## 🛠️ 错误处理

### HTTP 状态码

| 状态码 | 说明 | 处理建议 |
|--------|------|----------|
| 200 | 成功 | 正常处理响应 |
| 400 | 请求错误 | 检查请求参数 |
| 401 | 认证失败 | 重新登录获取token |
| 422 | 验证错误 | 检查必填字段 |
| 500 | 服务器错误 | 稍后重试或联系支持 |

### 错误响应格式

#### 验证错误响应
```json
{
    "status": false,
    "message": "Data need to be refined",
    "data": {
        "validation_error": {
            "sub_category_id": ["The sub category id field is required."],
            "ota_reference_number": ["The ota reference number field is required."],
            "car_type_id": ["The car type id field is required."],
            "incharge_by_backend_user_id": ["The incharge by backend user id field is required."]
        },
        "available_fields_to_fill_in": [
            "sub_category_id", "ota", "ota_reference_number", "ota_price",
            "customer_name", "customer_contact", "customer_email", "flight_info",
            "pickup", "pickup_lat", "pickup_long", "date", "time",
            "destination", "destination_lat", "destination_long", "car_type_id",
            "passenger_number", "luggage_number", "driver_fee", "driver_collect",
            "tour_guide", "baby_chair", "meet_and_greet", "extra_requirement",
            "incharge_by_backend_user_id", "driving_region_id", "languages_id_array"
        ]
    }
}
```

#### 一般错误响应
```json
{
    "status": false,
    "message": "Authentication failed",
    "errors": {
        "token": ["Invalid or expired token"]
    }
}
```

## 📞 支持联系

- **技术支持**: <EMAIL>
- **API问题**: <EMAIL>
- **文档更新**: <EMAIL>

---
*最后更新: 2025-07-27 | 版本: v1.0*