/**
 * @CONFIG_FILE 处理器字段映射配置
 * 🏷️ 标签: @PROCESSOR_FIELD_MAPPINGS_CONFIG
 * 📝 说明: 定义各OTA处理器的字段映射规则，统一管理字段别名和标准化映射
 * 🎯 功能: 字段映射、别名管理、数据标准化、多语言支持
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.configs = window.OTA.gemini.configs || {};

(function() {
    'use strict';

    /**
     * 处理器字段映射配置对象
     * 包含所有OTA处理器的字段映射规则
     */
    const processorFieldMappings = {
        // 版本信息
        version: '1.0.0',
        lastUpdated: '2024-01-15',
        
        // 通用字段映射（所有处理器共享）
        commonMappings: {
            'pickup_location': [
                '接送地点', '上车地点', '出发地', '起点', '接载地点', '集合地点', '接送点', '出发点',
                'pickup', 'from', 'departure', 'origin', 'meeting_point', 'pickup_point', 'start_point'
            ],
            'dropoff_location': [
                '目的地', '下车地点', '终点', '到达地', '送达地点', '结束地点', '送达点', '终点站',
                'dropoff', 'to', 'destination', 'arrival', 'end_point', 'drop_point', 'final_destination'
            ],
            'passenger_name': [
                '旅客姓名', '乘客姓名', '联系人', '姓名', '客户姓名', '主要联系人', '预订人', '出行人',
                'passenger', 'guest', 'name', 'traveler', 'customer', 'contact_person', 'booker', 'traveller'
            ],
            'contact_number': [
                '联系电话', '手机号码', '电话号码', '联系方式', '手机', '电话', '联系号码', '手机号',
                'phone', 'mobile', 'contact', 'tel', 'number', 'cell_phone', 'contact_number', 'mobile_phone'
            ],
            'pickup_date': [
                '服务日期', '出发日期', '用车日期', '接载日期', '日期', '出行日期', '行程日期',
                'date', 'pickup_date', 'service_date', 'departure_date', 'use_date', 'travel_date', 'trip_date'
            ],
            'pickup_time': [
                '服务时间', '出发时间', '用车时间', '接载时间', '时间', '出行时间', '行程时间',
                'time', 'pickup_time', 'service_time', 'departure_time', 'use_time', 'travel_time', 'trip_time'
            ],
            'flight_number': [
                '航班号', '班次号', '航班', '班次', '航班信息', 'flight', 'flight_number', 'flight_no', 'flight_info'
            ],
            'luggage_count': [
                '行李数量', '行李件数', '行李箱数', '行李', '箱包数', 'luggage', 'bags', 'baggage', 'suitcase', 'luggage_pieces'
            ],
            'passenger_count': [
                '人数', '乘客人数', '旅客数量', '参与人数', '成人数', '出行人数', 'passengers', 'pax', 'people', 'persons', 'adults', 'travelers'
            ],
            'special_requirements': [
                '特殊需求', '备注', '说明', '要求', '特别说明', '注意事项', '特殊要求', '备注信息',
                'requirements', 'notes', 'remarks', 'special', 'memo', 'comments', 'special_requests', 'additional_info'
            ]
        },

        // 处理器特定字段映射
        processorSpecific: {
            // Chong Dealer特定映射
            'ChongDealer': {
                'service_type': [
                    '服务类型', '用车类型', '接送类型', '服务项目', 'service_type', 'car_service', 'transfer_type'
                ],
                'charter_duration': [
                    '包车时长', '用车时长', '服务时长', '包车时间', 'charter_duration', 'service_duration', 'rental_hours'
                ],
                'pickup_sign': [
                    '举牌服务', '接机牌', '举牌', '接机标识', 'pickup_sign', 'name_board', 'welcome_sign'
                ],
                'driver_language': [
                    '司机语言', '驾驶员语言', '司机沟通语言', 'driver_language', 'chauffeur_language'
                ]
            },

            // Fliggy特定映射
            'Fliggy': {
                'product_name': [
                    '产品名称', '服务名称', '项目名称', '套餐名称', 'product', 'service', 'package', 'item'
                ],
                'order_amount': [
                    '订单金额', '总金额', '费用', '价格', 'amount', 'total', 'price', 'cost'
                ],
                'booking_status': [
                    '订单状态', '预订状态', '状态', 'status', 'booking_status', 'order_status'
                ],
                'payment_method': [
                    '支付方式', '付款方式', '结算方式', 'payment_method', 'payment_type', 'billing_method'
                ]
            },

            // JRCoach特定映射
            'JRCoach': {
                'vehicle_type': [
                    '车型', '车辆类型', '用车类型', 'vehicle_type', 'car_type', 'vehicle_category'
                ],
                'malaysian_location': [
                    '马来西亚地点', '本地地点', '国内地点', 'malaysian_location', 'local_location', 'domestic_location'
                ],
                'airport_terminal': [
                    '航站楼', '候机楼', '机场航站楼', 'terminal', 'airport_terminal', 'departure_terminal'
                ]
            },

            // KKday特定映射
            'KKday': {
                'activity_name': [
                    '活动名称', '项目名称', '体验名称', '产品名称', 'activity', 'experience', 'tour', 'attraction'
                ],
                'voucher_code': [
                    '凭证代码', '兑换码', '确认码', '凭证号', 'voucher', 'confirmation_code', 'redemption_code'
                ],
                'activity_date': [
                    '活动日期', '体验日期', '使用日期', '游玩日期', 'activity_date', 'experience_date', 'use_date'
                ],
                'participant_count': [
                    '参与人数', '体验人数', '游客人数', 'participants', 'attendees', 'guests'
                ]
            },

            // Klook特定映射
            'Klook': {
                'product_name': [
                    '产品名称', '活动名称', '体验名称', '项目名称', 'product', 'activity', 'experience', 'attraction'
                ],
                'booking_reference': [
                    '预订参考号', '确认码', '预订码', 'booking_reference', 'confirmation_code', 'booking_code'
                ],
                'region': [
                    '地区', '区域', '目的地区域', 'region', 'area', 'destination_region'
                ],
                'language_preference': [
                    '语言偏好', '服务语言', '沟通语言', 'language_preference', 'service_language', 'communication_language'
                ]
            },

            // Ctrip特定映射
            'Ctrip': {
                'product_name': [
                    '产品名称', '服务名称', '项目名称', '套餐名称', 'product', 'service', 'package', 'item'
                ],
                'order_amount': [
                    '订单金额', '总金额', '费用', '价格', 'amount', 'total', 'price', 'cost'
                ],
                'booking_status': [
                    '订单状态', '预订状态', '状态', 'status', 'booking_status', 'order_status'
                ],
                'supplier_info': [
                    '供应商信息', '服务商', '供应商', 'supplier', 'service_provider', 'vendor'
                ],
                'chinese_location': [
                    '中国地点', '国内地点', '中文地址', 'chinese_location', 'domestic_location', 'chinese_address'
                ]
            },

            // Agoda特定映射
            'Agoda': {
                'hotel_name': [
                    '酒店名称', '住宿名称', '酒店', '住宿', 'hotel', 'accommodation', 'hotel_name', 'property'
                ],
                'room_type': [
                    '房型', '房间类型', '客房类型', 'room_type', 'room', 'room_category'
                ],
                'booking_amount': [
                    '预订金额', '总金额', '费用', '价格', 'amount', 'total', 'price', 'cost', 'booking_amount'
                ],
                'check_in_date': [
                    '入住日期', '到达日期', '抵达日期', 'check_in_date', 'check_in', 'arrival_date'
                ],
                'check_out_date': [
                    '退房日期', '离店日期', '结束日期', 'check_out_date', 'check_out', 'departure_date', 'end_date'
                ],
                'guest_count': [
                    '客人数', '入住人数', '房客数', 'guests', 'occupants', 'guest_count'
                ]
            },

            // Booking.com特定映射
            'Booking': {
                'property_name': [
                    '酒店名称', '住宿名称', '酒店', '住宿', '物业名称', 'hotel', 'accommodation', 'hotel_name', 'property', 'property_name'
                ],
                'room_type': [
                    '房型', '房间类型', '客房类型', 'room_type', 'room', 'room_category'
                ],
                'booking_total': [
                    '预订总额', '总金额', '费用', '价格', 'amount', 'total', 'price', 'cost', 'booking_total', 'total_price'
                ],
                'check_in_date': [
                    '入住日期', '到达日期', '抵达日期', 'check_in_date', 'check_in', 'arrival_date'
                ],
                'check_out_date': [
                    '退房日期', '离店日期', '结束日期', 'check_out_date', 'check_out', 'departure_date', 'end_date'
                ],
                'main_guest': [
                    '主客人', '主要客人', '预订人', '主要联系人', 'main_guest', 'primary_guest', 'booker', 'primary_contact'
                ],
                'property_address': [
                    '酒店地址', '住宿地址', '物业地址', 'property_address', 'hotel_address', 'accommodation_address'
                ],
                'european_location': [
                    '欧洲地点', '欧洲地址', '欧洲目的地', 'european_location', 'european_address', 'european_destination'
                ]
            }
        },

        // 字段优先级配置
        fieldPriorities: {
            'ota_reference_number': 10,
            'pickup_location': 9,
            'dropoff_location': 9,
            'passenger_name': 8,
            'contact_number': 8,
            'pickup_date': 7,
            'pickup_time': 7,
            'flight_number': 6,
            'passenger_count': 5,
            'luggage_count': 4,
            'special_requirements': 3,
            'service_type': 6,
            'car_type': 5,
            'hotel_name': 7,
            'room_type': 4,
            'booking_amount': 6,
            'order_amount': 6,
            'booking_total': 6
        },

        // 字段验证规则
        fieldValidation: {
            'contact_number': {
                patterns: [
                    /^(\+\d{1,3})?[1-9]\d{7,14}$/,  // 国际格式
                    /^\d{3,4}-\d{7,8}$/,            // 固定电话格式
                    /^1[3-9]\d{9}$/,                // 中国手机号
                    /^01[0-9]\d{7,8}$/,             // 马来西亚手机号
                    /^[89]\d{7}$/                   // 新加坡手机号
                ],
                required: true,
                maxLength: 20
            },
            'pickup_date': {
                patterns: [
                    /^\d{4}-\d{2}-\d{2}$/,          // YYYY-MM-DD
                    /^\d{2}\/\d{2}\/\d{4}$/,        // DD/MM/YYYY
                    /^\d{2}-\d{2}-\d{4}$/           // DD-MM-YYYY
                ],
                required: true
            },
            'pickup_time': {
                patterns: [
                    /^([01]?\d|2[0-3]):[0-5]\d$/,   // HH:MM
                    /^([01]?\d|2[0-3])[0-5]\d$/     // HHMM
                ],
                required: true
            },
            'flight_number': {
                patterns: [
                    /^[A-Z]{2}\d{3,4}$/i,           // 标准航班号格式
                    /^[A-Z]\d{4,5}$/i               // 单字母航班号格式
                ],
                required: false,
                maxLength: 10
            },
            'passenger_count': {
                type: 'number',
                min: 1,
                max: 50,
                required: false
            },
            'luggage_count': {
                type: 'number',
                min: 0,
                max: 20,
                required: false
            }
        },

        // 数据转换规则
        dataTransformation: {
            'contact_number': {
                normalize: true,
                addCountryCode: true,
                removeSpaces: true
            },
            'pickup_date': {
                standardFormat: 'YYYY-MM-DD',
                validateFutureDate: true
            },
            'pickup_time': {
                standardFormat: 'HH:MM',
                validate24Hour: true
            },
            'passenger_name': {
                trimWhitespace: true,
                capitalizeWords: false,  // 保持原始大小写
                removeSpecialChars: false
            },
            'flight_number': {
                toUpperCase: true,
                removeSpaces: true
            },
            'passenger_count': {
                convertToNumber: true,
                defaultValue: 1
            },
            'luggage_count': {
                convertToNumber: true,
                defaultValue: 2
            }
        }
    };

    /**
     * 获取处理器字段映射配置
     * @param {string} processorName - 处理器名称（可选）
     * @returns {Object} 字段映射配置
     */
    function getProcessorFieldMappings(processorName = null) {
        if (processorName) {
            return {
                common: processorFieldMappings.commonMappings,
                specific: processorFieldMappings.processorSpecific[processorName] || {},
                priorities: processorFieldMappings.fieldPriorities,
                validation: processorFieldMappings.fieldValidation,
                transformation: processorFieldMappings.dataTransformation
            };
        }
        return processorFieldMappings;
    }

    /**
     * 获取字段别名列表
     * @param {string} standardField - 标准字段名
     * @param {string} processorName - 处理器名称（可选）
     * @returns {Array} 字段别名列表
     */
    function getFieldAliases(standardField, processorName = null) {
        const aliases = [];
        
        // 添加通用别名
        if (processorFieldMappings.commonMappings[standardField]) {
            aliases.push(...processorFieldMappings.commonMappings[standardField]);
        }
        
        // 添加处理器特定别名
        if (processorName && processorFieldMappings.processorSpecific[processorName]) {
            const specificMappings = processorFieldMappings.processorSpecific[processorName];
            if (specificMappings[standardField]) {
                aliases.push(...specificMappings[standardField]);
            }
        }
        
        return [...new Set(aliases)]; // 去重
    }

    /**
     * 映射字段名到标准字段名
     * @param {string} inputField - 输入字段名
     * @param {string} processorName - 处理器名称（可选）
     * @returns {string|null} 标准字段名
     */
    function mapToStandardField(inputField, processorName = null) {
        const lowerInputField = inputField.toLowerCase();
        
        // 检查通用映射
        for (const [standardField, aliases] of Object.entries(processorFieldMappings.commonMappings)) {
            if (aliases.some(alias => lowerInputField.includes(alias.toLowerCase()) || alias.toLowerCase().includes(lowerInputField))) {
                return standardField;
            }
        }
        
        // 检查处理器特定映射
        if (processorName && processorFieldMappings.processorSpecific[processorName]) {
            const specificMappings = processorFieldMappings.processorSpecific[processorName];
            for (const [standardField, aliases] of Object.entries(specificMappings)) {
                if (aliases.some(alias => lowerInputField.includes(alias.toLowerCase()) || alias.toLowerCase().includes(lowerInputField))) {
                    return standardField;
                }
            }
        }
        
        return null;
    }

    /**
     * 验证字段值
     * @param {string} fieldName - 字段名
     * @param {*} fieldValue - 字段值
     * @returns {Object} 验证结果
     */
    function validateFieldValue(fieldName, fieldValue) {
        const validation = processorFieldMappings.fieldValidation[fieldName];
        if (!validation) {
            return { isValid: true, errors: [] };
        }
        
        const errors = [];
        
        // 检查必填字段
        if (validation.required && (!fieldValue || fieldValue.toString().trim() === '')) {
            errors.push(`${fieldName} 是必填字段`);
            return { isValid: false, errors };
        }
        
        if (!fieldValue) {
            return { isValid: true, errors: [] };
        }
        
        const stringValue = fieldValue.toString();
        
        // 检查长度限制
        if (validation.maxLength && stringValue.length > validation.maxLength) {
            errors.push(`${fieldName} 长度不能超过 ${validation.maxLength} 个字符`);
        }
        
        // 检查数值范围
        if (validation.type === 'number') {
            const numValue = Number(fieldValue);
            if (isNaN(numValue)) {
                errors.push(`${fieldName} 必须是数字`);
            } else {
                if (validation.min !== undefined && numValue < validation.min) {
                    errors.push(`${fieldName} 不能小于 ${validation.min}`);
                }
                if (validation.max !== undefined && numValue > validation.max) {
                    errors.push(`${fieldName} 不能大于 ${validation.max}`);
                }
            }
        }
        
        // 检查格式模式
        if (validation.patterns && validation.patterns.length > 0) {
            const isValidPattern = validation.patterns.some(pattern => pattern.test(stringValue));
            if (!isValidPattern) {
                errors.push(`${fieldName} 格式不正确`);
            }
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 转换字段值
     * @param {string} fieldName - 字段名
     * @param {*} fieldValue - 字段值
     * @returns {*} 转换后的字段值
     */
    function transformFieldValue(fieldName, fieldValue) {
        const transformation = processorFieldMappings.dataTransformation[fieldName];
        if (!transformation || !fieldValue) {
            return fieldValue;
        }
        
        let transformedValue = fieldValue;
        
        // 字符串转换
        if (typeof transformedValue === 'string') {
            if (transformation.trimWhitespace) {
                transformedValue = transformedValue.trim();
            }
            
            if (transformation.toUpperCase) {
                transformedValue = transformedValue.toUpperCase();
            }
            
            if (transformation.removeSpaces) {
                transformedValue = transformedValue.replace(/\s+/g, '');
            }
            
            if (transformation.capitalizeWords) {
                transformedValue = transformedValue.replace(/\b\w/g, l => l.toUpperCase());
            }
        }
        
        // 数值转换
        if (transformation.convertToNumber) {
            const numValue = Number(transformedValue);
            if (!isNaN(numValue)) {
                transformedValue = numValue;
            } else if (transformation.defaultValue !== undefined) {
                transformedValue = transformation.defaultValue;
            }
        }
        
        return transformedValue;
    }

    /**
     * 更新字段映射配置
     * @param {Object} newMappings - 新的映射配置
     */
    function updateProcessorFieldMappings(newMappings) {
        Object.assign(processorFieldMappings, newMappings);
        console.log('处理器字段映射配置已更新');
    }

    // 暴露到全局命名空间
    window.OTA.gemini.configs.processorFieldMappings = processorFieldMappings;
    window.OTA.gemini.configs.getProcessorFieldMappings = getProcessorFieldMappings;
    window.OTA.gemini.configs.getFieldAliases = getFieldAliases;
    window.OTA.gemini.configs.mapToStandardField = mapToStandardField;
    window.OTA.gemini.configs.validateFieldValue = validateFieldValue;
    window.OTA.gemini.configs.transformFieldValue = transformFieldValue;
    window.OTA.gemini.configs.updateProcessorFieldMappings = updateProcessorFieldMappings;

    // 向后兼容
    window.getProcessorFieldMappings = getProcessorFieldMappings;
    window.getFieldAliases = getFieldAliases;
    window.mapToStandardField = mapToStandardField;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerConfig('processorFieldMappings', processorFieldMappings, '@PROCESSOR_FIELD_MAPPINGS_CONFIG');
        window.OTA.Registry.registerFactory('getProcessorFieldMappings', getProcessorFieldMappings, '@PROCESSOR_FIELD_MAPPINGS_FACTORY');
    }

    console.log('✅ 处理器字段映射配置已加载');

})();
