/**
 * OTA系统架构健康检查调度器
 * 
 * 功能：
 * - 定期执行架构健康检查
 * - 自动生成健康报告
 * - 设置检查提醒和警告
 * - 维护检查历史记录
 * 
 * <AUTHOR> Team
 * @since v2.0 - 阶段3优化后
 */

(function() {
    'use strict';

    /**
     * 健康检查调度器
     */
    class HealthCheckScheduler {
        constructor() {
            this.config = {
                // 检查频率配置
                dailyCheck: true,           // 每日快速检查
                weeklyCheck: true,          // 每周详细检查
                monthlyCheck: true,         // 每月全面检查
                
                // 检查时间配置 (24小时制)
                dailyCheckTime: '09:00',    // 每日9点
                weeklyCheckDay: 1,          // 每周一
                monthlyCheckDate: 1,        // 每月1日
                
                // 警告阈值
                warningThreshold: 70,       // 健康评分低于70分发出警告
                criticalThreshold: 60,      // 健康评分低于60分发出严重警告
                
                // 存储配置
                maxHistoryRecords: 100,     // 最多保存100条历史记录
                storageKey: 'ota_health_check_history'
            };

            this.timers = {
                daily: null,
                weekly: null,
                monthly: null
            };

            this.history = this.loadHistory();
            this.init();
        }

        /**
         * 初始化调度器
         */
        init() {
            this.setupSchedules();
            this.setupEventListeners();
            
            const logger = this.getLogger();
            logger.log('🗓️ 健康检查调度器已启动', 'info', {
                config: this.config,
                nextChecks: this.getNextCheckTimes()
            });
        }

        /**
         * 设置检查计划
         */
        setupSchedules() {
            if (this.config.dailyCheck) {
                this.scheduleDailyCheck();
            }
            
            if (this.config.weeklyCheck) {
                this.scheduleWeeklyCheck();
            }
            
            if (this.config.monthlyCheck) {
                this.scheduleMonthlyCheck();
            }
        }

        /**
         * 安排每日检查
         */
        scheduleDailyCheck() {
            const now = new Date();
            const [hours, minutes] = this.config.dailyCheckTime.split(':').map(Number);
            
            let nextCheck = new Date();
            nextCheck.setHours(hours, minutes, 0, 0);
            
            // 如果今天的检查时间已过，安排到明天
            if (nextCheck <= now) {
                nextCheck.setDate(nextCheck.getDate() + 1);
            }
            
            const timeToNext = nextCheck.getTime() - now.getTime();
            
            this.timers.daily = setTimeout(() => {
                this.performDailyCheck();
                this.scheduleDailyCheck(); // 安排下次检查
            }, timeToNext);
        }

        /**
         * 安排每周检查
         */
        scheduleWeeklyCheck() {
            const now = new Date();
            const currentDay = now.getDay();
            const targetDay = this.config.weeklyCheckDay;
            
            let daysUntilNext = targetDay - currentDay;
            if (daysUntilNext <= 0) {
                daysUntilNext += 7;
            }
            
            const nextCheck = new Date(now);
            nextCheck.setDate(now.getDate() + daysUntilNext);
            nextCheck.setHours(10, 0, 0, 0); // 周一10点
            
            const timeToNext = nextCheck.getTime() - now.getTime();
            
            this.timers.weekly = setTimeout(() => {
                this.performWeeklyCheck();
                this.scheduleWeeklyCheck(); // 安排下次检查
            }, timeToNext);
        }

        /**
         * 安排每月检查
         */
        scheduleMonthlyCheck() {
            const now = new Date();
            const targetDate = this.config.monthlyCheckDate;
            
            let nextCheck = new Date(now.getFullYear(), now.getMonth(), targetDate);
            
            // 如果本月的检查日期已过，安排到下个月
            if (nextCheck <= now) {
                nextCheck = new Date(now.getFullYear(), now.getMonth() + 1, targetDate);
            }
            
            nextCheck.setHours(11, 0, 0, 0); // 每月1日11点
            
            const timeToNext = nextCheck.getTime() - now.getTime();
            
            this.timers.monthly = setTimeout(() => {
                this.performMonthlyCheck();
                this.scheduleMonthlyCheck(); // 安排下次检查
            }, timeToNext);
        }

        /**
         * 执行每日检查
         */
        async performDailyCheck() {
            const logger = this.getLogger();
            logger.log('🌅 开始每日健康检查', 'info');

            try {
                const result = await this.runQuickHealthCheck();
                this.saveCheckResult('daily', result);
                this.processCheckResult(result, 'daily');
            } catch (error) {
                logger.logError('每日健康检查失败', error);
            }
        }

        /**
         * 执行每周检查
         */
        async performWeeklyCheck() {
            const logger = this.getLogger();
            logger.log('📅 开始每周健康检查', 'info');

            try {
                const result = await this.runDetailedHealthCheck();
                this.saveCheckResult('weekly', result);
                this.processCheckResult(result, 'weekly');
            } catch (error) {
                logger.logError('每周健康检查失败', error);
            }
        }

        /**
         * 执行每月检查
         */
        async performMonthlyCheck() {
            const logger = this.getLogger();
            logger.log('📊 开始每月全面健康检查', 'info');

            try {
                const result = await this.runComprehensiveHealthCheck();
                this.saveCheckResult('monthly', result);
                this.processCheckResult(result, 'monthly');
                this.generateMonthlyReport(result);
            } catch (error) {
                logger.logError('每月健康检查失败', error);
            }
        }

        /**
         * 运行快速健康检查
         */
        async runQuickHealthCheck() {
            const checks = {
                performance: this.checkPerformanceBasics(),
                errors: this.checkRecentErrors(),
                memory: this.checkMemoryUsage()
            };

            const results = await Promise.all(Object.values(checks));
            const score = this.calculateQuickScore(results);

            return {
                type: 'daily',
                timestamp: new Date().toISOString(),
                score: score,
                checks: {
                    performance: results[0],
                    errors: results[1],
                    memory: results[2]
                }
            };
        }

        /**
         * 运行详细健康检查
         */
        async runDetailedHealthCheck() {
            const quickCheck = await this.runQuickHealthCheck();
            
            // 添加更多检查项
            const additionalChecks = {
                architecture: this.checkArchitectureCompliance(),
                codeQuality: this.checkCodeQuality(),
                documentation: this.checkDocumentationSync()
            };

            const additionalResults = await Promise.all(Object.values(additionalChecks));
            const detailedScore = this.calculateDetailedScore(quickCheck, additionalResults);

            return {
                type: 'weekly',
                timestamp: new Date().toISOString(),
                score: detailedScore,
                quickCheck: quickCheck,
                additionalChecks: {
                    architecture: additionalResults[0],
                    codeQuality: additionalResults[1],
                    documentation: additionalResults[2]
                }
            };
        }

        /**
         * 运行全面健康检查
         */
        async runComprehensiveHealthCheck() {
            const detailedCheck = await this.runDetailedHealthCheck();
            
            // 执行所有可用的检查
            const comprehensiveChecks = {
                guardian: this.runArchitectureGuardianCheck(),
                prevention: this.runStage3PreventionCheck(),
                performance: this.runPerformanceAnalysis(),
                trends: this.analyzeTrends()
            };

            const comprehensiveResults = await Promise.all(Object.values(comprehensiveChecks));
            const comprehensiveScore = this.calculateComprehensiveScore(detailedCheck, comprehensiveResults);

            return {
                type: 'monthly',
                timestamp: new Date().toISOString(),
                score: comprehensiveScore,
                detailedCheck: detailedCheck,
                comprehensiveChecks: {
                    guardian: comprehensiveResults[0],
                    prevention: comprehensiveResults[1],
                    performance: comprehensiveResults[2],
                    trends: comprehensiveResults[3]
                }
            };
        }

        /**
         * 检查性能基础指标
         */
        checkPerformanceBasics() {
            return new Promise((resolve) => {
                const performance = window.performance;
                const memory = performance.memory;
                
                const result = {
                    loadTime: performance.now(),
                    memoryUsed: memory ? memory.usedJSHeapSize / 1024 / 1024 : 0,
                    timestamp: Date.now()
                };
                
                resolve(result);
            });
        }

        /**
         * 检查最近错误
         */
        checkRecentErrors() {
            return new Promise((resolve) => {
                // 从架构守护者获取错误信息
                const guardian = window.OTA?.ArchitectureGuardian;
                const errorCount = guardian ? guardian.violations.length : 0;
                
                resolve({
                    errorCount: errorCount,
                    recentErrors: errorCount > 0 ? guardian.violations.slice(-5) : []
                });
            });
        }

        /**
         * 检查内存使用
         */
        checkMemoryUsage() {
            return new Promise((resolve) => {
                if (performance.memory) {
                    const memory = performance.memory;
                    resolve({
                        used: memory.usedJSHeapSize / 1024 / 1024,
                        total: memory.totalJSHeapSize / 1024 / 1024,
                        limit: memory.jsHeapSizeLimit / 1024 / 1024
                    });
                } else {
                    resolve({ available: false });
                }
            });
        }

        /**
         * 运行架构守护者检查
         */
        runArchitectureGuardianCheck() {
            return new Promise((resolve) => {
                try {
                    const result = window.performSystemHealthCheck?.() || { healthScore: 0 };
                    resolve(result);
                } catch (error) {
                    resolve({ error: error.message, healthScore: 0 });
                }
            });
        }

        /**
         * 运行阶段3预防检查
         */
        runStage3PreventionCheck() {
            return new Promise((resolve) => {
                try {
                    const result = window.stage3PreventionReport?.() || { totalViolations: 0 };
                    resolve(result);
                } catch (error) {
                    resolve({ error: error.message, totalViolations: 0 });
                }
            });
        }

        /**
         * 处理检查结果
         */
        processCheckResult(result, type) {
            const logger = this.getLogger();
            
            if (result.score < this.config.criticalThreshold) {
                logger.logError(`🚨 ${type}健康检查严重警告`, {
                    score: result.score,
                    threshold: this.config.criticalThreshold,
                    result: result
                });
                this.triggerCriticalAlert(result, type);
            } else if (result.score < this.config.warningThreshold) {
                logger.log(`⚠️ ${type}健康检查警告`, 'warning', {
                    score: result.score,
                    threshold: this.config.warningThreshold,
                    result: result
                });
                this.triggerWarningAlert(result, type);
            } else {
                logger.log(`✅ ${type}健康检查通过`, 'info', {
                    score: result.score,
                    result: result
                });
            }
        }

        /**
         * 触发严重警告
         */
        triggerCriticalAlert(result, type) {
            // 触发严重警告事件
            window.dispatchEvent(new CustomEvent('healthCheckCritical', {
                detail: { result, type, timestamp: new Date().toISOString() }
            }));
        }

        /**
         * 触发警告
         */
        triggerWarningAlert(result, type) {
            // 触发警告事件
            window.dispatchEvent(new CustomEvent('healthCheckWarning', {
                detail: { result, type, timestamp: new Date().toISOString() }
            }));
        }

        /**
         * 保存检查结果
         */
        saveCheckResult(type, result) {
            this.history.push({
                type: type,
                result: result,
                timestamp: new Date().toISOString()
            });

            // 限制历史记录数量
            if (this.history.length > this.config.maxHistoryRecords) {
                this.history = this.history.slice(-this.config.maxHistoryRecords);
            }

            this.saveHistory();
        }

        /**
         * 加载历史记录
         */
        loadHistory() {
            try {
                const stored = localStorage.getItem(this.config.storageKey);
                return stored ? JSON.parse(stored) : [];
            } catch (error) {
                console.warn('无法加载健康检查历史记录:', error);
                return [];
            }
        }

        /**
         * 保存历史记录
         */
        saveHistory() {
            try {
                localStorage.setItem(this.config.storageKey, JSON.stringify(this.history));
            } catch (error) {
                console.warn('无法保存健康检查历史记录:', error);
            }
        }

        /**
         * 获取下次检查时间
         */
        getNextCheckTimes() {
            const now = new Date();
            return {
                daily: this.getNextDailyCheck(now),
                weekly: this.getNextWeeklyCheck(now),
                monthly: this.getNextMonthlyCheck(now)
            };
        }

        /**
         * 获取日志服务
         */
        getLogger() {
            if (window.OTA?.Registry?.get) {
                return window.OTA.Registry.get('logger') || console;
            }
            return window.getLogger?.() || console;
        }

        /**
         * 停止所有定时器
         */
        stop() {
            Object.values(this.timers).forEach(timer => {
                if (timer) clearTimeout(timer);
            });
            
            const logger = this.getLogger();
            logger.log('⏹️ 健康检查调度器已停止', 'info');
        }

        /**
         * 获取健康检查历史
         */
        getHistory() {
            return this.history;
        }

        /**
         * 手动执行健康检查
         */
        async runManualCheck(type = 'comprehensive') {
            switch (type) {
                case 'quick':
                    return await this.runQuickHealthCheck();
                case 'detailed':
                    return await this.runDetailedHealthCheck();
                case 'comprehensive':
                default:
                    return await this.runComprehensiveHealthCheck();
            }
        }
    }

    // 创建全局健康检查调度器实例
    const healthCheckScheduler = new HealthCheckScheduler();

    // 暴露到全局命名空间
    window.OTA = window.OTA || {};
    window.OTA.HealthCheckScheduler = healthCheckScheduler;

    // 向后兼容和便捷访问
    window.healthCheckScheduler = healthCheckScheduler;
    
    // 全局命令
    window.runHealthCheck = (type) => healthCheckScheduler.runManualCheck(type);
    window.getHealthHistory = () => healthCheckScheduler.getHistory();

    // 模块加载完成日志
    const logger = healthCheckScheduler.getLogger();
    logger.log('🗓️ 健康检查调度器模块已加载', 'info', {
        version: 'v2.0',
        nextChecks: healthCheckScheduler.getNextCheckTimes()
    });

})();
