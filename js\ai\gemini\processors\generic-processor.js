/**
 * @PROCESSOR 通用OTA订单处理器
 * 🏷️ 标签: @GENERIC_PROCESSOR
 * 📝 说明: 作为所有未识别或降级情况的默认处理器，提供基础的订单解析功能
 * 🎯 功能: 通用订单解析、智能字段提取、降级处理、错误恢复
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.processors = window.OTA.gemini.processors || {};

(function() {
    'use strict';

    /**
     * 通用OTA订单处理器类
     * 继承自BaseProcessor，提供通用的订单处理逻辑
     */
    class GenericProcessor {
        constructor() {
            this.logger = window.getLogger?.() || console;
            this.processorName = 'Generic';
            this.version = '1.0.0';
            
            // 延迟获取基础处理器
            this.baseProcessor = null;
            
            // 通用处理配置
            this.config = {
                // 通用字段映射规则
                fieldMappings: {
                    // 基础信息字段
                    'pickup_location': ['pickup', 'pick up', 'from', 'departure', '接机地点', '出发地', '上车地点'],
                    'dropoff_location': ['dropoff', 'drop off', 'to', 'destination', '送机地点', '目的地', '下车地点'],
                    'pickup_date': ['date', 'pickup date', 'departure date', '日期', '接机日期', '出发日期'],
                    'pickup_time': ['time', 'pickup time', 'departure time', '时间', '接机时间', '出发时间'],
                    
                    // 乘客信息字段
                    'passenger_name': ['passenger', 'name', 'guest', 'customer', '乘客', '姓名', '客人', '旅客'],
                    'passenger_count': ['pax', 'passengers', 'people', 'persons', '人数', '乘客数', '旅客数'],
                    'contact_number': ['phone', 'mobile', 'contact', 'tel', '电话', '手机', '联系方式'],
                    
                    // 航班信息字段
                    'flight_number': ['flight', 'flight no', 'flight number', '航班', '航班号'],
                    'airline': ['airline', 'carrier', '航空公司', '承运商'],
                    
                    // 服务信息字段
                    'service_type': ['service', 'type', '服务类型', '服务'],
                    'car_type': ['car', 'vehicle', 'car type', '车型', '车辆类型'],
                    'luggage_count': ['luggage', 'bags', 'baggage', '行李', '行李数量'],
                    
                    // 价格信息字段
                    'price': ['price', 'cost', 'amount', 'total', '价格', '费用', '金额', '总价'],
                    'currency': ['currency', '货币', '币种'],
                    
                    // 特殊要求字段
                    'special_requirements': ['requirements', 'notes', 'remarks', 'special', '特殊要求', '备注', '说明']
                },
                
                // 通用预设值
                defaultValues: {
                    car_type_id: 1,           // Comfort 5 Seater
                    languages_id_array: {"0": "2"}, // English
                    responsible_person_id: null,     // 将根据用户动态设置
                    service_type_id: null,          // 将根据内容智能判断
                    luggage_number: 2,              // 默认行李数量
                    is_return: false,               // 默认单程
                    pickup_sign: false,             // 默认不举牌
                    special_requirements: ''        // 默认无特殊要求
                },
                
                // 智能识别规则
                intelligentRules: {
                    // 服务类型识别规则
                    serviceTypeRules: {
                        'pickup': {
                            keywords: ['pickup', 'airport pickup', 'arrival', 'pick up', '接机', '到达'],
                            serviceTypeId: 2
                        },
                        'dropoff': {
                            keywords: ['dropoff', 'airport dropoff', 'departure', 'drop off', '送机', '出发'],
                            serviceTypeId: 3
                        },
                        'charter': {
                            keywords: ['charter', 'hourly', 'day tour', '包车', '小时', '日游'],
                            serviceTypeId: 4
                        }
                    },
                    
                    // 车型选择规则（基于人数）
                    carTypeRules: {
                        1: { carTypeId: 1, name: 'Comfort 5 Seater' },
                        2: { carTypeId: 1, name: 'Comfort 5 Seater' },
                        3: { carTypeId: 1, name: 'Comfort 5 Seater' },
                        4: { carTypeId: 1, name: 'Comfort 5 Seater' },
                        5: { carTypeId: 1, name: 'Comfort 5 Seater' },
                        6: { carTypeId: 2, name: 'Premium 7 Seater' },
                        7: { carTypeId: 2, name: 'Premium 7 Seater' },
                        8: { carTypeId: 3, name: 'Luxury Van' },
                        9: { carTypeId: 3, name: 'Luxury Van' },
                        10: { carTypeId: 3, name: 'Luxury Van' }
                    },
                    
                    // 语言识别规则
                    languageRules: {
                        chinese: {
                            pattern: /[\u4e00-\u9fff]/g,
                            languageId: 4,
                            minRatio: 0.1
                        },
                        english: {
                            pattern: /[a-zA-Z]/g,
                            languageId: 2,
                            minRatio: 0.3
                        }
                    }
                }
            };

            // 处理统计
            this.stats = {
                totalProcessed: 0,
                successfulExtractions: 0,
                fallbackUsed: 0,
                intelligentMatches: 0,
                averageConfidence: 0
            };

            // 初始化依赖
            this.initializeDependencies();
        }

        /**
         * 延迟初始化依赖服务
         */
        initializeDependencies() {
            setTimeout(() => {
                // 获取基础处理器
                if (window.OTA?.gemini?.core?.BaseProcessor) {
                    this.baseProcessor = new window.OTA.gemini.core.BaseProcessor();
                }
                
                this.logger.log('通用处理器依赖初始化完成', 'info');
            }, 100);
        }

        /**
         * 处理订单 - 主要入口方法
         * @param {string} orderText - 订单文本
         * @param {Object} options - 处理选项
         * @returns {Promise<Object>} 处理结果
         */
        async processOrder(orderText, options = {}) {
            this.stats.totalProcessed++;
            
            try {
                this.logger.log(`通用处理器开始处理订单`, 'info');
                
                // 1. 预处理订单文本
                const preprocessedText = this.preprocessOrderText(orderText);
                
                // 2. 智能字段提取
                const extractedFields = await this.extractFields(preprocessedText, options);
                
                // 3. 应用智能规则
                const intelligentResult = this.applyIntelligentRules(extractedFields, preprocessedText);
                
                // 4. 应用预设值
                const finalResult = this.applyDefaultValues(intelligentResult, options);
                
                // 5. 数据验证和清理
                const validatedResult = this.validateAndCleanData(finalResult);
                
                // 6. 生成处理元数据
                const processingMetadata = this.generateProcessingMetadata(validatedResult, options);
                
                this.stats.successfulExtractions++;
                
                return {
                    success: true,
                    processor: this.processorName,
                    version: this.version,
                    data: validatedResult,
                    metadata: processingMetadata,
                    confidence: processingMetadata.overallConfidence,
                    processingTime: Date.now() - (options.startTime || Date.now())
                };

            } catch (error) {
                this.logger.logError('通用处理器处理失败', error);
                this.stats.fallbackUsed++;
                
                return this.generateFallbackResult(orderText, error, options);
            }
        }

        /**
         * 预处理订单文本
         * @param {string} orderText - 原始订单文本
         * @returns {string} 预处理后的文本
         */
        preprocessOrderText(orderText) {
            if (!orderText || typeof orderText !== 'string') {
                return '';
            }

            // 1. 清理多余空白字符
            let cleaned = orderText.replace(/\s+/g, ' ').trim();
            
            // 2. 标准化换行符
            cleaned = cleaned.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
            
            // 3. 移除特殊字符（保留基本标点）
            cleaned = cleaned.replace(/[^\w\s\u4e00-\u9fff.,;:!?()[\]{}"'-]/g, ' ');
            
            // 4. 标准化常见缩写
            const abbreviations = {
                'pax': 'passengers',
                'tel': 'telephone',
                'mob': 'mobile',
                'addr': 'address',
                'dept': 'departure',
                'arr': 'arrival'
            };
            
            for (const [abbr, full] of Object.entries(abbreviations)) {
                const regex = new RegExp(`\\b${abbr}\\b`, 'gi');
                cleaned = cleaned.replace(regex, full);
            }
            
            return cleaned;
        }

        /**
         * 智能字段提取
         * @param {string} text - 预处理后的文本
         * @param {Object} options - 选项
         * @returns {Promise<Object>} 提取的字段
         */
        async extractFields(text, options = {}) {
            const extractedFields = {};
            const fieldMappings = this.config.fieldMappings;
            
            // 遍历所有字段映射规则
            for (const [fieldName, keywords] of Object.entries(fieldMappings)) {
                const extractedValue = this.extractFieldValue(text, keywords, fieldName);
                if (extractedValue) {
                    extractedFields[fieldName] = extractedValue;
                }
            }
            
            // 特殊字段的智能提取
            await this.extractSpecialFields(text, extractedFields, options);
            
            return extractedFields;
        }

        /**
         * 提取单个字段值
         * @param {string} text - 文本
         * @param {Array} keywords - 关键词列表
         * @param {string} fieldName - 字段名
         * @returns {string|null} 提取的值
         */
        extractFieldValue(text, keywords, fieldName) {
            for (const keyword of keywords) {
                // 创建匹配模式
                const patterns = [
                    new RegExp(`${keyword}[:\\s]*([^\\n,;]{1,100})`, 'gi'),
                    new RegExp(`([^\\n,;]{1,100})[\\s]*${keyword}`, 'gi'),
                    new RegExp(`${keyword}[：\\s]*([^\\n，；]{1,100})`, 'gi')
                ];
                
                for (const pattern of patterns) {
                    const matches = text.match(pattern);
                    if (matches && matches.length > 0) {
                        // 清理提取的值
                        let value = matches[0].replace(new RegExp(keyword, 'gi'), '').trim();
                        value = value.replace(/^[:\s：]+|[:\s：]+$/g, '').trim();
                        
                        if (value && value.length > 0 && value.length < 200) {
                            return this.cleanExtractedValue(value, fieldName);
                        }
                    }
                }
            }
            
            return null;
        }

        /**
         * 清理提取的值
         * @param {string} value - 原始值
         * @param {string} fieldName - 字段名
         * @returns {string} 清理后的值
         */
        cleanExtractedValue(value, fieldName) {
            // 移除常见的无用前缀和后缀
            const cleanPatterns = [
                /^[:\-\s]+/,
                /[:\-\s]+$/,
                /^[：\-\s]+/,
                /[：\-\s]+$/
            ];
            
            let cleaned = value;
            for (const pattern of cleanPatterns) {
                cleaned = cleaned.replace(pattern, '');
            }
            
            // 字段特定的清理规则
            switch (fieldName) {
                case 'passenger_count':
                    // 提取数字
                    const numberMatch = cleaned.match(/\d+/);
                    return numberMatch ? parseInt(numberMatch[0]) : cleaned;
                    
                case 'pickup_date':
                case 'dropoff_date':
                    // 日期格式标准化
                    return this.standardizeDate(cleaned);
                    
                case 'pickup_time':
                case 'dropoff_time':
                    // 时间格式标准化
                    return this.standardizeTime(cleaned);
                    
                case 'contact_number':
                    // 电话号码清理
                    return cleaned.replace(/[^\d+\-\s()]/g, '').trim();
                    
                case 'price':
                    // 价格数字提取
                    const priceMatch = cleaned.match(/[\d.,]+/);
                    return priceMatch ? priceMatch[0] : cleaned;
                    
                default:
                    return cleaned.trim();
            }
        }

        /**
         * 提取特殊字段
         * @param {string} text - 文本
         * @param {Object} extractedFields - 已提取的字段
         * @param {Object} options - 选项
         */
        async extractSpecialFields(text, extractedFields, options = {}) {
            // 1. 智能提取参考号
            if (!extractedFields.ota_reference_number) {
                extractedFields.ota_reference_number = this.extractReferenceNumber(text);
            }
            
            // 2. 智能提取邮箱
            const emailMatch = text.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/);
            if (emailMatch) {
                extractedFields.customer_email = emailMatch[0];
            }
            
            // 3. 智能提取URL
            const urlMatch = text.match(/https?:\/\/[^\s]+/);
            if (urlMatch) {
                extractedFields.booking_url = urlMatch[0];
            }
            
            // 4. 智能提取货币信息
            const currencyInfo = this.extractCurrencyInfo(text);
            if (currencyInfo.currency) {
                extractedFields.currency = currencyInfo.currency;
            }
            if (currencyInfo.amount) {
                extractedFields.price = currencyInfo.amount;
            }
        }

        /**
         * 提取参考号
         * @param {string} text - 文本
         * @returns {string|null} 参考号
         */
        extractReferenceNumber(text) {
            // 通用参考号模式
            const patterns = [
                /[A-Z]{2,4}\d{6,12}/g,           // 字母+数字组合
                /\d{8,15}/g,                     // 纯数字（8-15位）
                /[A-Z0-9]{8,20}/g,               // 字母数字混合（8-20位）
                /[A-Z]{3,6}-\d{4,8}/g,           // 字母-数字格式
                /\d{4}-\d{4}-\d{4}/g             // 数字-数字-数字格式
            ];
            
            for (const pattern of patterns) {
                const matches = text.match(pattern);
                if (matches && matches.length > 0) {
                    // 选择最可能的参考号（通常是最长的）
                    return matches.reduce((longest, current) => 
                        current.length > longest.length ? current : longest
                    );
                }
            }
            
            return null;
        }

        /**
         * 提取货币信息
         * @param {string} text - 文本
         * @returns {Object} 货币信息
         */
        extractCurrencyInfo(text) {
            const currencyPatterns = {
                'MYR': [/RM\s*(\d+(?:\.\d{2})?)/gi, /MYR\s*(\d+(?:\.\d{2})?)/gi, /马币\s*(\d+(?:\.\d{2})?)/gi],
                'SGD': [/S\$\s*(\d+(?:\.\d{2})?)/gi, /SGD\s*(\d+(?:\.\d{2})?)/gi, /新币\s*(\d+(?:\.\d{2})?)/gi],
                'USD': [/\$\s*(\d+(?:\.\d{2})?)/gi, /USD\s*(\d+(?:\.\d{2})?)/gi, /美金\s*(\d+(?:\.\d{2})?)/gi],
                'CNY': [/¥\s*(\d+(?:\.\d{2})?)/gi, /CNY\s*(\d+(?:\.\d{2})?)/gi, /人民币\s*(\d+(?:\.\d{2})?)/gi]
            };
            
            for (const [currency, patterns] of Object.entries(currencyPatterns)) {
                for (const pattern of patterns) {
                    const match = text.match(pattern);
                    if (match) {
                        return {
                            currency: currency,
                            amount: parseFloat(match[1])
                        };
                    }
                }
            }
            
            return {};
        }

        /**
         * 应用智能规则
         * @param {Object} extractedFields - 提取的字段
         * @param {string} originalText - 原始文本
         * @returns {Object} 应用规则后的结果
         */
        applyIntelligentRules(extractedFields, originalText) {
            const result = { ...extractedFields };
            
            // 1. 智能判断服务类型
            if (!result.service_type_id) {
                result.service_type_id = this.determineServiceType(originalText);
            }
            
            // 2. 智能选择车型
            if (!result.car_type_id && result.passenger_count) {
                const passengerCount = parseInt(result.passenger_count) || 1;
                const carTypeRule = this.config.intelligentRules.carTypeRules[passengerCount] || 
                                  this.config.intelligentRules.carTypeRules[1];
                result.car_type_id = carTypeRule.carTypeId;
            }
            
            // 3. 智能选择语言
            if (!result.languages_id_array) {
                result.languages_id_array = this.determineLanguage(originalText);
            }
            
            // 4. 智能判断举牌服务
            if (result.pickup_sign === undefined) {
                result.pickup_sign = this.shouldUsePickupSign(originalText, result);
            }
            
            this.stats.intelligentMatches++;
            
            return result;
        }

        /**
         * 确定服务类型
         * @param {string} text - 文本
         * @returns {number} 服务类型ID
         */
        determineServiceType(text) {
            const serviceRules = this.config.intelligentRules.serviceTypeRules;
            
            for (const [serviceType, rule] of Object.entries(serviceRules)) {
                for (const keyword of rule.keywords) {
                    if (text.toLowerCase().includes(keyword.toLowerCase())) {
                        return rule.serviceTypeId;
                    }
                }
            }
            
            // 默认返回接机服务
            return 2;
        }

        /**
         * 确定语言
         * @param {string} text - 文本
         * @returns {Object} 语言数组
         */
        determineLanguage(text) {
            const languageRules = this.config.intelligentRules.languageRules;
            
            // 检查中文内容比例
            const chineseMatches = text.match(languageRules.chinese.pattern) || [];
            const chineseRatio = chineseMatches.length / text.length;
            
            if (chineseRatio >= languageRules.chinese.minRatio) {
                return {"0": languageRules.chinese.languageId.toString()};
            }
            
            // 默认英文
            return {"0": languageRules.english.languageId.toString()};
        }

        /**
         * 判断是否需要举牌服务
         * @param {string} text - 文本
         * @param {Object} extractedData - 提取的数据
         * @returns {boolean} 是否举牌
         */
        shouldUsePickupSign(text, extractedData) {
            const signKeywords = ['sign', 'placard', 'name board', '举牌', '接机牌', '姓名牌'];
            
            for (const keyword of signKeywords) {
                if (text.toLowerCase().includes(keyword.toLowerCase())) {
                    return true;
                }
            }
            
            // 如果是接机服务且有乘客姓名，建议举牌
            if (extractedData.service_type_id === 2 && extractedData.passenger_name) {
                return true;
            }
            
            return false;
        }

        /**
         * 应用预设值
         * @param {Object} data - 数据
         * @param {Object} options - 选项
         * @returns {Object} 应用预设值后的数据
         */
        applyDefaultValues(data, options = {}) {
            const result = { ...data };
            const defaults = this.config.defaultValues;
            
            // 应用默认值（仅当字段不存在时）
            for (const [key, defaultValue] of Object.entries(defaults)) {
                if (result[key] === undefined || result[key] === null || result[key] === '') {
                    result[key] = defaultValue;
                }
            }
            
            // 特殊处理：负责人ID
            if (!result.responsible_person_id && options.userId) {
                result.responsible_person_id = this.mapUserToResponsiblePerson(options.userId);
            }
            
            return result;
        }

        /**
         * 映射用户到负责人
         * @param {string} userId - 用户ID或邮箱
         * @returns {number} 负责人ID
         */
        mapUserToResponsiblePerson(userId) {
            const userMapping = {
                '<EMAIL>': 37,
                '<EMAIL>': 310
            };
            
            return userMapping[userId] || 1; // 默认负责人ID
        }

        /**
         * 数据验证和清理
         * @param {Object} data - 数据
         * @returns {Object} 验证后的数据
         */
        validateAndCleanData(data) {
            const cleaned = { ...data };
            
            // 1. 移除空值和无效值
            Object.keys(cleaned).forEach(key => {
                if (cleaned[key] === null || cleaned[key] === undefined || cleaned[key] === '') {
                    delete cleaned[key];
                }
            });
            
            // 2. 数据类型转换
            if (cleaned.passenger_count) {
                cleaned.passenger_count = parseInt(cleaned.passenger_count) || 1;
            }
            
            if (cleaned.luggage_number) {
                cleaned.luggage_number = parseInt(cleaned.luggage_number) || 2;
            }
            
            if (cleaned.price) {
                cleaned.price = parseFloat(cleaned.price) || 0;
            }
            
            // 3. 布尔值处理
            if (cleaned.pickup_sign !== undefined) {
                cleaned.pickup_sign = Boolean(cleaned.pickup_sign);
            }
            
            if (cleaned.is_return !== undefined) {
                cleaned.is_return = Boolean(cleaned.is_return);
            }
            
            return cleaned;
        }

        /**
         * 生成处理元数据
         * @param {Object} data - 处理后的数据
         * @param {Object} options - 选项
         * @returns {Object} 元数据
         */
        generateProcessingMetadata(data, options = {}) {
            const fieldCount = Object.keys(data).length;
            const requiredFields = ['pickup_location', 'dropoff_location', 'pickup_date', 'pickup_time'];
            const foundRequiredFields = requiredFields.filter(field => data[field]).length;
            
            const confidence = foundRequiredFields / requiredFields.length;
            
            return {
                processor: this.processorName,
                version: this.version,
                processingTime: Date.now() - (options.startTime || Date.now()),
                extractedFieldCount: fieldCount,
                requiredFieldsFound: foundRequiredFields,
                totalRequiredFields: requiredFields.length,
                overallConfidence: confidence,
                usedIntelligentRules: true,
                usedFallback: false,
                processingDate: new Date().toISOString()
            };
        }

        /**
         * 生成降级结果
         * @param {string} orderText - 原始订单文本
         * @param {Error} error - 错误对象
         * @param {Object} options - 选项
         * @returns {Object} 降级结果
         */
        generateFallbackResult(orderText, error, options = {}) {
            return {
                success: false,
                processor: this.processorName,
                version: this.version,
                error: error.message,
                fallbackData: {
                    original_text: orderText,
                    ota_reference_number: this.extractReferenceNumber(orderText) || 'UNKNOWN',
                    service_type_id: 2, // 默认接机
                    car_type_id: 1,     // 默认车型
                    languages_id_array: {"0": "2"}, // 默认英文
                    responsible_person_id: 1,
                    special_requirements: `原始订单文本: ${orderText.substring(0, 500)}...`
                },
                metadata: {
                    processor: this.processorName,
                    processingTime: Date.now() - (options.startTime || Date.now()),
                    overallConfidence: 0.1,
                    usedFallback: true,
                    errorType: error.name,
                    processingDate: new Date().toISOString()
                }
            };
        }

        /**
         * 标准化日期格式
         * @param {string} dateStr - 日期字符串
         * @returns {string} 标准化后的日期
         */
        standardizeDate(dateStr) {
            // 尝试解析各种日期格式
            const datePatterns = [
                /(\d{1,2})[\/\-.](\d{1,2})[\/\-.](\d{4})/,  // DD/MM/YYYY
                /(\d{4})[\/\-.](\d{1,2})[\/\-.](\d{1,2})/,  // YYYY/MM/DD
                /(\d{1,2})\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+(\d{4})/i
            ];
            
            for (const pattern of datePatterns) {
                const match = dateStr.match(pattern);
                if (match) {
                    // 返回DD-MM-YYYY格式
                    if (pattern.source.includes('\\d{4}')[0] === '(') {
                        // YYYY/MM/DD格式
                        return `${match[3].padStart(2, '0')}-${match[2].padStart(2, '0')}-${match[1]}`;
                    } else {
                        // DD/MM/YYYY格式
                        return `${match[1].padStart(2, '0')}-${match[2].padStart(2, '0')}-${match[3]}`;
                    }
                }
            }
            
            return dateStr; // 无法解析时返回原始值
        }

        /**
         * 标准化时间格式
         * @param {string} timeStr - 时间字符串
         * @returns {string} 标准化后的时间
         */
        standardizeTime(timeStr) {
            // 提取时间信息
            const timePattern = /(\d{1,2})[:\.](\d{2})\s*(AM|PM|am|pm)?/;
            const match = timeStr.match(timePattern);
            
            if (match) {
                let hours = parseInt(match[1]);
                const minutes = match[2];
                const ampm = match[3]?.toUpperCase();
                
                // 处理12小时制
                if (ampm === 'PM' && hours !== 12) {
                    hours += 12;
                } else if (ampm === 'AM' && hours === 12) {
                    hours = 0;
                }
                
                return `${hours.toString().padStart(2, '0')}:${minutes}`;
            }
            
            return timeStr; // 无法解析时返回原始值
        }

        /**
         * 获取处理器统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                ...this.stats,
                successRate: this.stats.totalProcessed > 0 ? 
                    ((this.stats.successfulExtractions / this.stats.totalProcessed) * 100).toFixed(2) + '%' : '0%',
                fallbackRate: this.stats.totalProcessed > 0 ? 
                    ((this.stats.fallbackUsed / this.stats.totalProcessed) * 100).toFixed(2) + '%' : '0%'
            };
        }

        /**
         * 重置统计信息
         */
        resetStats() {
            this.stats = {
                totalProcessed: 0,
                successfulExtractions: 0,
                fallbackUsed: 0,
                intelligentMatches: 0,
                averageConfidence: 0
            };
        }
    }

    // 创建全局实例
    function getGenericProcessor() {
        if (!window.OTA.gemini.processors.genericProcessor) {
            window.OTA.gemini.processors.genericProcessor = new GenericProcessor();
        }
        return window.OTA.gemini.processors.genericProcessor;
    }

    // 暴露到全局命名空间
    window.OTA.gemini.processors.GenericProcessor = GenericProcessor;
    window.OTA.gemini.processors.getGenericProcessor = getGenericProcessor;

    // 向后兼容
    window.getGenericProcessor = getGenericProcessor;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('genericProcessor', getGenericProcessor(), '@GENERIC_PROCESSOR');
        window.OTA.Registry.registerFactory('getGenericProcessor', getGenericProcessor, '@GENERIC_PROCESSOR_FACTORY');
    }

    console.log('✅ 通用OTA订单处理器已加载');

})();
