/**
 * 🌐 多订单Chrome MCP集成器
 * 负责处理Chrome Model Context Protocol集成、浏览器自动化和测试功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-20
 */

(function() {
    'use strict';

    /**
     * 多订单Chrome MCP集成器类
     * 专门处理Chrome MCP集成、浏览器自动化和测试功能
     */
    class MultiOrderChromeMCP {
        /**
         * 构造函数
         * @param {Object} dependencies - 依赖注入对象
         * @param {Object} dependencies.logger - 日志服务
         * @param {Object} dependencies.config - 配置对象
         * @param {Object} dependencies.stateManager - 状态管理器
         * @param {Object} dependencies.validationManager - 验证管理器
         */
        constructor(dependencies = {}) {
            this.logger = dependencies.logger || this.getLogger();
            this.config = {
                // Chrome MCP配置
                mcpTimeout: 30000,
                retryAttempts: 3,
                retryDelay: 1000,
                testMode: false,
                enableDebug: true,
                ...dependencies.config
            };

            // 依赖注入
            this.stateManager = dependencies.stateManager;
            this.validationManager = dependencies.validationManager;

            // Chrome MCP接口对象
            this.chromeMCP = null;

            // 初始化
            this.init();
        }

        /**
         * 初始化Chrome MCP集成器
         */
        init() {
            this.logger?.log('🌐 Chrome MCP集成器初始化开始', 'info');
            
            try {
                this.initChromeMCPIntegration();
                this.logger?.log('✅ Chrome MCP集成器初始化完成', 'success');
            } catch (error) {
                this.logger?.logError('Chrome MCP集成器初始化失败', error);
                throw error;
            }
        }

        /**
         * 初始化Chrome MCP集成接口
         */
        initChromeMCPIntegration() {
            this.logger?.log('🌐 初始化Chrome MCP集成接口...', 'info');

            // Chrome MCP 接口对象
            this.chromeMCP = {
                // 🔧 基础操作接口
                navigate: async (url) => {
                    this.logger?.log(`🌐 Chrome MCP: 导航到 ${url}`, 'info');
                    if (window.chrome_navigate_chrome_mcp_stdio) {
                        return await window.chrome_navigate_chrome_mcp_stdio({ url });
                    }
                    throw new Error('Chrome MCP navigate 接口不可用');
                },

                // 📸 截图接口
                screenshot: async (options = {}) => {
                    this.logger?.log('📸 Chrome MCP: 截图', 'info');
                    if (window.chrome_screenshot_chrome_mcp_stdio) {
                        return await window.chrome_screenshot_chrome_mcp_stdio({
                            fullPage: true,
                            storeBase64: true,
                            savePng: false,
                            ...options
                        });
                    }
                    throw new Error('Chrome MCP screenshot 接口不可用');
                },

                // 🔍 获取页面内容
                getContent: async (options = {}) => {
                    this.logger?.log('🔍 Chrome MCP: 获取页面内容', 'info');
                    if (window.chrome_get_web_content_chrome_mcp_stdio) {
                        return await window.chrome_get_web_content_chrome_mcp_stdio({
                            textContent: true,
                            htmlContent: false,
                            ...options
                        });
                    }
                    throw new Error('Chrome MCP getContent 接口不可用');
                },

                // 🖱️ 点击元素
                click: async (selector) => {
                    this.logger?.log(`🖱️ Chrome MCP: 点击元素 ${selector}`, 'info');
                    if (window.chrome_click_element_chrome_mcp_stdio) {
                        return await window.chrome_click_element_chrome_mcp_stdio({
                            selector,
                            timeout: 5000,
                            waitForNavigation: false
                        });
                    }
                    throw new Error('Chrome MCP click 接口不可用');
                },

                // ⌨️ 填充表单
                fill: async (selector, value) => {
                    this.logger?.log(`⌨️ Chrome MCP: 填充 ${selector} = ${value}`, 'info');
                    if (window.chrome_fill_or_select_chrome_mcp_stdio) {
                        return await window.chrome_fill_or_select_chrome_mcp_stdio({
                            selector,
                            value
                        });
                    }
                    throw new Error('Chrome MCP fill 接口不可用');
                },

                // 🔍 获取交互元素
                getInteractiveElements: async (options = {}) => {
                    this.logger?.log('🔍 Chrome MCP: 获取交互元素', 'info');
                    if (window.chrome_get_interactive_elements_chrome_mcp_stdio) {
                        return await window.chrome_get_interactive_elements_chrome_mcp_stdio({
                            includeCoordinates: true,
                            ...options
                        });
                    }
                    throw new Error('Chrome MCP getInteractiveElements 接口不可用');
                },

                // 💉 注入脚本
                injectScript: async (script, type = 'ISOLATED') => {
                    this.logger?.log('💉 Chrome MCP: 注入脚本', 'info');
                    if (window.chrome_inject_script_chrome_mcp_stdio) {
                        return await window.chrome_inject_script_chrome_mcp_stdio({
                            jsScript: script,
                            type
                        });
                    }
                    throw new Error('Chrome MCP injectScript 接口不可用');
                },

                // 📡 发送命令到注入脚本
                sendCommand: async (eventName, payload = {}) => {
                    this.logger?.log(`📡 Chrome MCP: 发送命令 ${eventName}`, 'info');
                    if (window.chrome_send_command_to_inject_script_chrome_mcp_stdio) {
                        return await window.chrome_send_command_to_inject_script_chrome_mcp_stdio({
                            eventName,
                            payload: JSON.stringify(payload)
                        });
                    }
                    throw new Error('Chrome MCP sendCommand 接口不可用');
                },

                // 🌐 网络请求
                networkRequest: async (url, options = {}) => {
                    this.logger?.log(`🌐 Chrome MCP: 网络请求 ${url}`, 'info');
                    if (window.chrome_network_request_chrome_mcp_stdio) {
                        return await window.chrome_network_request_chrome_mcp_stdio({
                            url,
                            method: 'GET',
                            timeout: 30000,
                            ...options
                        });
                    }
                    throw new Error('Chrome MCP networkRequest 接口不可用');
                }
            };

            // 🧪 测试接口可用性
            this.testChromeMCPAvailability();
        }

        /**
         * 测试Chrome MCP接口可用性
         */
        testChromeMCPAvailability() {
            const mcpFunctions = [
                'chrome_navigate_chrome_mcp_stdio',
                'chrome_screenshot_chrome_mcp_stdio',
                'chrome_get_web_content_chrome_mcp_stdio',
                'chrome_click_element_chrome_mcp_stdio',
                'chrome_fill_or_select_chrome_mcp_stdio',
                'chrome_get_interactive_elements_chrome_mcp_stdio',
                'chrome_inject_script_chrome_mcp_stdio',
                'chrome_send_command_to_inject_script_chrome_mcp_stdio',
                'chrome_network_request_chrome_mcp_stdio'
            ];

            const availableFunctions = [];
            const unavailableFunctions = [];

            mcpFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    availableFunctions.push(funcName);
                } else {
                    unavailableFunctions.push(funcName);
                }
            });

            this.logger?.log(`Chrome MCP 可用接口: ${availableFunctions.length}/${mcpFunctions.length}`, 'info');
            
            if (unavailableFunctions.length > 0) {
                this.logger?.log(`Chrome MCP 不可用接口: ${unavailableFunctions.join(', ')}`, 'warn');
            }

            return {
                available: availableFunctions,
                unavailable: unavailableFunctions,
                isFullyAvailable: unavailableFunctions.length === 0
            };
        }

        /**
         * 测试Chrome MCP集成功能
         * @returns {Promise<Object>} 测试结果
         */
        async testChromeMCPIntegration() {
            this.logger?.log('🧪 开始测试Chrome MCP集成功能', 'info');

            const testResults = {
                passed: [],
                failed: [],
                startTime: Date.now(),
                endTime: null
            };

            try {
                // 测试1: 获取页面内容
                try {
                    const content = await this.chromeMCP.getContent();
                    testResults.passed.push('getContent');
                    this.logger?.log('✅ Chrome MCP getContent 测试通过', 'success');
                } catch (error) {
                    testResults.failed.push({ test: 'getContent', error: error.message });
                    this.logger?.logError('❌ Chrome MCP getContent 测试失败', error);
                }

                // 测试2: 获取交互元素
                try {
                    const elements = await this.chromeMCP.getInteractiveElements();
                    testResults.passed.push('getInteractiveElements');
                    this.logger?.log('✅ Chrome MCP getInteractiveElements 测试通过', 'success');
                } catch (error) {
                    testResults.failed.push({ test: 'getInteractiveElements', error: error.message });
                    this.logger?.logError('❌ Chrome MCP getInteractiveElements 测试失败', error);
                }

                // 测试3: 截图功能
                try {
                    const screenshot = await this.chromeMCP.screenshot({
                        width: 800,
                        height: 600,
                        storeBase64: true,
                        savePng: false
                    });
                    testResults.passed.push('screenshot');
                    this.logger?.log('✅ Chrome MCP screenshot 测试通过', 'success');
                } catch (error) {
                    testResults.failed.push({ test: 'screenshot', error: error.message });
                    this.logger?.logError('❌ Chrome MCP screenshot 测试失败', error);
                }

                // 测试4: 脚本注入
                try {
                    const testScript = `
                        console.log('Chrome MCP 脚本注入测试');
                        window.chromeMCPTestResult = 'success';
                    `;
                    await this.chromeMCP.injectScript(testScript);
                    testResults.passed.push('injectScript');
                    this.logger?.log('✅ Chrome MCP injectScript 测试通过', 'success');
                } catch (error) {
                    testResults.failed.push({ test: 'injectScript', error: error.message });
                    this.logger?.logError('❌ Chrome MCP injectScript 测试失败', error);
                }

            } catch (error) {
                this.logger?.logError('Chrome MCP 集成测试过程中发生错误', error);
            }

            testResults.endTime = Date.now();
            testResults.duration = testResults.endTime - testResults.startTime;
            testResults.successRate = testResults.passed.length / (testResults.passed.length + testResults.failed.length);

            this.logger?.log(`Chrome MCP 集成测试完成`, 'info', {
                passed: testResults.passed.length,
                failed: testResults.failed.length,
                successRate: Math.round(testResults.successRate * 100) + '%',
                duration: testResults.duration + 'ms'
            });

            return testResults;
        }

        /**
         * 获取Chrome MCP接口
         * @returns {Object} Chrome MCP接口对象
         */
        getChromeMCP() {
            return this.chromeMCP;
        }

        /**
         * 检查Chrome MCP是否可用
         * @returns {boolean} 是否可用
         */
        isChromeMCPAvailable() {
            return this.chromeMCP !== null && typeof this.chromeMCP === 'object';
        }

        /**
         * 获取日志服务
         * @returns {Object} 日志服务对象
         */
        getLogger() {
            return window.getLogger?.() || {
                log: console.log.bind(console),
                logError: console.error.bind(console)
            };
        }

        /**
         * 清理Chrome MCP集成器
         */
        cleanup() {
            this.chromeMCP = null;
            this.logger?.log('🌐 Chrome MCP集成器已清理', 'info');
        }

        /**
         * 销毁Chrome MCP集成器
         */
        destroy() {
            this.cleanup();
            this.logger?.log('🌐 Chrome MCP集成器已销毁', 'info');
        }
    }

    /**
     * 创建Chrome MCP集成器实例的工厂函数
     * @param {Object} dependencies - 依赖注入对象
     * @returns {MultiOrderChromeMCP} Chrome MCP集成器实例
     */
    function createMultiOrderChromeMCP(dependencies = {}) {
        return new MultiOrderChromeMCP(dependencies);
    }

    // 导出到全局作用域
    window.getMultiOrderChromeMCP = createMultiOrderChromeMCP;
    window.MultiOrderChromeMCP = MultiOrderChromeMCP;

    // 确保OTA命名空间存在
    if (typeof window.OTA === 'undefined') {
        window.OTA = {};
    }
    if (typeof window.OTA.multiOrder === 'undefined') {
        window.OTA.multiOrder = {};
    }

    // 注册到OTA命名空间
    window.OTA.multiOrder.ChromeMCP = MultiOrderChromeMCP;
    window.OTA.multiOrder.getChromeMCP = createMultiOrderChromeMCP;

})();
