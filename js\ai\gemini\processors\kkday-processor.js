/**
 * @PROCESSOR KKday专用处理器
 * 🏷️ 标签: @KKDAY_PROCESSOR
 * 📝 说明: 专门处理KKday订单的智能处理器，包含KKday特定的参考号识别、字段映射和预设值应用
 * 🎯 功能: KKday订单解析、参考号识别、字段映射、预设值应用、数据验证
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.processors = window.OTA.gemini.processors || {};

(function() {
    'use strict';

    /**
     * KKday专用处理器类
     * 继承自BaseProcessor，实现KKday订单的专业化处理
     */
    class KKdayProcessor {
        constructor() {
            this.logger = window.getLogger?.() || console;
            this.processorName = 'KKdayProcessor';
            this.version = '1.0.0';
            this.platform = 'KKday';
            this.platformDisplayName = 'KKday';
            
            // KKday特定配置
            this.kkdayConfig = {
                // 参考号模式
                referencePatterns: [
                    /KK[A-Z0-9]{8,15}/gi,            // KK开头的订单号
                    /KKDAY[A-Z0-9]{6,12}/gi,         // KKDAY开头的订单号
                    /\d{10,16}/g,                    // 长数字订单号
                    /[A-Z]{2,3}\d{8,12}/gi,          // 字母+数字组合
                    /KKday订单[：:]\s*([A-Z0-9]{8,20})/gi, // KKday标识的订单号
                    /预订编号[：:]\s*([A-Z0-9]{8,20})/gi,   // 预订编号标识
                    /确认号[：:]\s*([A-Z0-9]{8,20})/gi      // 确认号标识
                ],
                
                // 字段映射规则
                fieldMappings: {
                    'pickup_location': [
                        '接送地点', '上车地点', '出发地', '起点', '接载地点', '集合地点',
                        'pickup', 'from', 'departure', 'origin', 'meeting_point'
                    ],
                    'dropoff_location': [
                        '目的地', '下车地点', '终点', '到达地', '送达地点', '结束地点',
                        'dropoff', 'to', 'destination', 'arrival', 'end_point'
                    ],
                    'passenger_name': [
                        '旅客姓名', '乘客姓名', '联系人', '姓名', '客户姓名', '主要联系人',
                        'passenger', 'guest', 'name', 'traveler', 'customer', 'contact_person'
                    ],
                    'contact_number': [
                        '联系电话', '手机号码', '电话号码', '联系方式', '手机', '电话',
                        'phone', 'mobile', 'contact', 'tel', 'number', 'cell_phone'
                    ],
                    'pickup_date': [
                        '使用日期', '服务日期', '出发日期', '用车日期', '接载日期', '日期',
                        'date', 'pickup_date', 'service_date', 'departure_date', 'use_date'
                    ],
                    'pickup_time': [
                        '使用时间', '服务时间', '出发时间', '用车时间', '接载时间', '时间',
                        'time', 'pickup_time', 'service_time', 'departure_time', 'use_time'
                    ],
                    'flight_number': [
                        '航班号', '班次号', '航班', '班次', 'flight', 'flight_number', 'flight_no'
                    ],
                    'luggage_count': [
                        '行李数量', '行李件数', '行李箱数', '行李', 'luggage', 'bags', 'baggage', 'suitcase'
                    ],
                    'passenger_count': [
                        '人数', '乘客人数', '旅客数量', '参与人数', 'passengers', 'pax', 'people', 'persons', 'participants'
                    ],
                    'special_requirements': [
                        '特殊需求', '备注', '说明', '要求', '特别说明', '注意事项',
                        'requirements', 'notes', 'remarks', 'special', 'memo', 'comments'
                    ],
                    'activity_name': [
                        '活动名称', '产品名称', '服务名称', '项目名称', 'activity', 'product', 'service'
                    ],
                    'voucher_number': [
                        '凭证号', '券号', '票券号', 'voucher', 'ticket', 'coupon'
                    ]
                },
                
                // 预设值配置
                presetValues: {
                    car_type_id: 1,                    // 默认舒适型5座
                    service_type_id: 2,                // 默认接机服务
                    languages_id_array: {"0": "2"},    // 默认英文
                    luggage_number: 2,                 // 默认2件行李
                    is_return: false,                  // 默认单程
                    pickup_sign: true,                 // KKday通常需要举牌
                    currency: 'USD',                   // 默认美元
                    payment_method: 'prepaid'          // 默认预付费
                },
                
                // 数据验证规则
                validationRules: {
                    required_fields: [
                        'ota_reference_number', 'pickup_location', 'dropoff_location',
                        'passenger_name', 'contact_number', 'pickup_date', 'pickup_time'
                    ],
                    field_formats: {
                        contact_number: /^(\+\d{1,3})?[1-9]\d{7,14}$|^\d{3,4}-\d{7,8}$/,
                        pickup_date: /^\d{4}-\d{2}-\d{2}$|^\d{2}\/\d{2}\/\d{4}$|^\d{2}-\d{2}-\d{4}$/,
                        pickup_time: /^([01]?\d|2[0-3]):[0-5]\d$|^([01]?\d|2[0-3])[0-5]\d$/,
                        flight_number: /^[A-Z]{2}\d{3,4}$|^[A-Z]\d{4,5}$/i,
                        voucher_number: /^[A-Z0-9]{6,20}$/i
                    }
                },
                
                // 智能识别关键词
                identificationKeywords: [
                    'kkday', 'kk day', 'kk-day', 'kkd',
                    'kkday.com', 'kkday旅游', 'kkday体验'
                ],
                
                // 服务类型识别
                serviceTypeKeywords: {
                    2: ['接机', '机场接送', '到达接送', 'pickup', 'arrival', 'airport pickup', 'transfer from'],
                    3: ['送机', '机场送达', '出发送机', 'dropoff', 'departure', 'airport dropoff', 'transfer to'],
                    4: ['包车', '租车', '专车', '一日游', 'charter', 'rental', 'private', 'day tour', 'sightseeing']
                },
                
                // 车型识别关键词
                carTypeKeywords: {
                    1: ['轿车', '小车', '5座', '舒适型', 'sedan', 'comfort', 'economy', 'standard', 'car'],
                    2: ['MPV', '7座', '8座', '商务车', 'mpv', 'business', 'van', 'minivan'],
                    3: ['豪华车', '高级轿车', '奔驰', '宝马', 'luxury', 'premium', 'mercedes', 'bmw', 'audi'],
                    4: ['巴士', '大巴', '12座', '15座', 'bus', 'coach', 'minibus', 'group']
                },
                
                // 活动类型识别
                activityTypes: {
                    'transfer': ['接送', '机场', '酒店', 'transfer', 'airport', 'hotel'],
                    'tour': ['一日游', '观光', '旅游', 'tour', 'sightseeing', 'excursion'],
                    'experience': ['体验', '活动', '门票', 'experience', 'activity', 'ticket'],
                    'rental': ['租车', '包车', '专车', 'rental', 'charter', 'private']
                },
                
                // 货币识别
                currencyPatterns: {
                    'USD': [/\$(\d+(?:\.\d{2})?)/g, /USD\s*(\d+(?:\.\d{2})?)/gi, /美元\s*(\d+(?:\.\d{2})?)/g],
                    'MYR': [/RM\s*(\d+(?:\.\d{2})?)/gi, /MYR\s*(\d+(?:\.\d{2})?)/gi, /马币\s*(\d+(?:\.\d{2})?)/g],
                    'SGD': [/S\$(\d+(?:\.\d{2})?)/g, /SGD\s*(\d+(?:\.\d{2})?)/gi, /新币\s*(\d+(?:\.\d{2})?)/g],
                    'CNY': [/¥(\d+(?:\.\d{2})?)/g, /CNY\s*(\d+(?:\.\d{2})?)/gi, /人民币\s*(\d+(?:\.\d{2})?)/g]
                }
            };
            
            // 处理统计
            this.stats = {
                totalProcessed: 0,
                successfulProcessed: 0,
                failedProcessed: 0,
                averageProcessingTime: 0,
                referenceNumberMatches: 0,
                fieldExtractionSuccess: 0,
                validationErrors: 0,
                activityTypeDetected: 0,
                currencyDetected: 0
            };
            
            // 初始化处理器
            this.initialize();
        }

        /**
         * 初始化处理器
         */
        initialize() {
            this.logger.log(`${this.platformDisplayName}处理器初始化开始`, 'info');
            
            // 获取基础处理器
            this.baseProcessor = window.OTA?.gemini?.core?.getBaseProcessor?.();
            
            // 获取配置管理器
            this.configManager = window.OTA?.gemini?.core?.getConfigManager?.();
            
            this.logger.log(`${this.platformDisplayName}处理器初始化完成`, 'info');
        }

        /**
         * 处理订单 - 主要入口方法
         * @param {string} orderText - 订单文本
         * @param {Object} options - 处理选项
         * @returns {Promise<Object>} 处理结果
         */
        async processOrder(orderText, options = {}) {
            const startTime = Date.now();
            this.stats.totalProcessed++;
            
            try {
                this.logger.log(`开始处理${this.platformDisplayName}订单`, 'info');
                
                // 1. 预处理订单文本
                const preprocessedText = this.preprocessOrderText(orderText);
                
                // 2. 识别参考号
                const referenceNumber = this.extractReferenceNumber(preprocessedText);
                
                // 3. 提取基础字段
                const extractedFields = this.extractFields(preprocessedText);
                
                // 4. 智能字段映射
                const mappedFields = this.mapFields(extractedFields);
                
                // 5. KKday特定处理
                const kkdaySpecificFields = this.applyKKdaySpecificProcessing(mappedFields, preprocessedText);
                
                // 6. 应用预设值
                const fieldsWithPresets = this.applyPresetValues(kkdaySpecificFields);
                
                // 7. 数据验证和清理
                const validatedFields = this.validateAndCleanFields(fieldsWithPresets);
                
                // 8. 生成最终结果
                const result = this.generateResult(validatedFields, referenceNumber, options);
                
                // 9. 更新统计信息
                this.updateStats(startTime, true);
                
                this.logger.log(`${this.platformDisplayName}订单处理完成`, 'info');
                
                return result;
                
            } catch (error) {
                this.stats.failedProcessed++;
                this.logger.logError(`${this.platformDisplayName}订单处理失败`, error);
                
                // 返回降级结果
                return this.generateFallbackResult(orderText, options, error);
            }
        }

        /**
         * 预处理订单文本
         * @param {string} orderText - 原始订单文本
         * @returns {string} 预处理后的文本
         */
        preprocessOrderText(orderText) {
            if (!orderText || typeof orderText !== 'string') {
                throw new Error('订单文本必须是非空字符串');
            }
            
            // 统一换行符
            let processed = orderText.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
            
            // 清理多余空白
            processed = processed.replace(/\s+/g, ' ').trim();
            
            // 标准化标点符号
            processed = processed.replace(/：/g, ':').replace(/，/g, ',');
            
            // 处理KKday特有的格式
            processed = this.normalizeKKdayFormat(processed);
            
            return processed;
        }

        /**
         * 标准化KKday格式
         * @param {string} text - 文本
         * @returns {string} 标准化后的文本
         */
        normalizeKKdayFormat(text) {
            // 标准化常见的KKday字段标识
            const replacements = {
                '預訂編號': '预订编号',
                '確認號': '确认号',
                '旅客姓名': '乘客姓名',
                '聯絡電話': '联系电话',
                '使用日期': '服务日期',
                '使用時間': '服务时间',
                '集合地點': '集合地点',
                '目的地': '目的地'
            };
            
            let normalized = text;
            for (const [traditional, simplified] of Object.entries(replacements)) {
                const regex = new RegExp(traditional, 'g');
                normalized = normalized.replace(regex, simplified);
            }
            
            return normalized;
        }

        /**
         * 提取参考号
         * @param {string} orderText - 订单文本
         * @returns {string|null} 参考号
         */
        extractReferenceNumber(orderText) {
            for (const pattern of this.kkdayConfig.referencePatterns) {
                const matches = orderText.match(pattern);
                if (matches && matches.length > 0) {
                    this.stats.referenceNumberMatches++;
                    
                    // 如果是带标识的模式，提取括号内容
                    if (pattern.source.includes('订单') || pattern.source.includes('编号') || pattern.source.includes('确认')) {
                        const match = pattern.exec(orderText);
                        return match ? match[1] : matches[0];
                    }
                    
                    return matches[0];
                }
            }
            
            // 使用通用模式作为后备
            const genericPatterns = [
                /[A-Z0-9]{8,20}/g,
                /\d{8,15}/g
            ];
            
            for (const pattern of genericPatterns) {
                const matches = orderText.match(pattern);
                if (matches && matches.length > 0) {
                    return matches[0];
                }
            }
            
            return null;
        }

        /**
         * 提取字段
         * @param {string} orderText - 订单文本
         * @returns {Object} 提取的字段
         */
        extractFields(orderText) {
            const fields = {};
            const lines = orderText.split('\n');
            
            for (const line of lines) {
                const trimmedLine = line.trim();
                if (!trimmedLine) continue;
                
                // 尝试匹配键值对模式
                const keyValueMatch = trimmedLine.match(/^(.+?)[：:]\s*(.+)$/);
                if (keyValueMatch) {
                    const key = keyValueMatch[1].trim();
                    const value = keyValueMatch[2].trim();
                    
                    if (value && value !== '-' && value !== 'N/A' && value !== 'TBC' && value !== '待确认') {
                        fields[key] = value;
                    }
                }
                
                // 尝试匹配特定模式
                this.extractSpecificPatterns(trimmedLine, fields);
            }
            
            return fields;
        }

        /**
         * 提取特定模式
         * @param {string} line - 文本行
         * @param {Object} fields - 字段对象
         */
        extractSpecificPatterns(line, fields) {
            // 提取航班号
            const flightMatch = line.match(/([A-Z]{2}\d{3,4}|[A-Z]\d{4,5})/i);
            if (flightMatch && !fields.flight_number) {
                fields.flight_number = flightMatch[1].toUpperCase();
            }
            
            // 提取电话号码（支持国际格式）
            const phoneMatch = line.match(/(\+\d{1,3}[1-9]\d{7,14}|1[3-9]\d{9}|\d{3,4}-\d{7,8})/);
            if (phoneMatch && !fields.contact_number) {
                fields.contact_number = phoneMatch[1];
            }
            
            // 提取日期
            const dateMatch = line.match(/(\d{4}[-\/]\d{1,2}[-\/]\d{1,2}|\d{1,2}[-\/]\d{1,2}[-\/]\d{4})/);
            if (dateMatch && !fields.pickup_date) {
                fields.pickup_date = this.standardizeDate(dateMatch[1]);
            }
            
            // 提取时间
            const timeMatch = line.match(/([01]?\d|2[0-3])[:\s]?([0-5]\d)/);
            if (timeMatch && !fields.pickup_time) {
                fields.pickup_time = `${timeMatch[1].padStart(2, '0')}:${timeMatch[2]}`;
            }
            
            // 提取人数
            const passengerMatch = line.match(/(\d+)\s*(?:人|位|名|pax|passengers?|persons?|adults?)/i);
            if (passengerMatch && !fields.passenger_count) {
                fields.passenger_count = parseInt(passengerMatch[1]);
            }
            
            // 提取价格信息
            this.extractPriceInformation(line, fields);
            
            // 提取凭证号
            const voucherMatch = line.match(/(?:凭证|券号|票券)[：:]?\s*([A-Z0-9]{6,20})/i);
            if (voucherMatch && !fields.voucher_number) {
                fields.voucher_number = voucherMatch[1];
            }
        }

        /**
         * 提取价格信息
         * @param {string} line - 文本行
         * @param {Object} fields - 字段对象
         */
        extractPriceInformation(line, fields) {
            for (const [currency, patterns] of Object.entries(this.kkdayConfig.currencyPatterns)) {
                for (const pattern of patterns) {
                    const matches = line.match(pattern);
                    if (matches && matches.length > 0 && !fields.price) {
                        fields.price = parseFloat(matches[1]);
                        fields.currency = currency;
                        this.stats.currencyDetected++;
                        return;
                    }
                }
            }
        }

        /**
         * 映射字段
         * @param {Object} extractedFields - 提取的字段
         * @returns {Object} 映射后的字段
         */
        mapFields(extractedFields) {
            const mappedFields = {};
            
            for (const [standardField, aliases] of Object.entries(this.kkdayConfig.fieldMappings)) {
                for (const [extractedKey, extractedValue] of Object.entries(extractedFields)) {
                    // 检查是否匹配任何别名
                    const isMatch = aliases.some(alias => 
                        extractedKey.toLowerCase().includes(alias.toLowerCase()) ||
                        alias.toLowerCase().includes(extractedKey.toLowerCase())
                    );
                    
                    if (isMatch && !mappedFields[standardField]) {
                        mappedFields[standardField] = extractedValue;
                        break;
                    }
                }
            }
            
            // 智能服务类型识别
            mappedFields.service_type_id = this.identifyServiceType(extractedFields);
            
            // 智能车型识别
            mappedFields.car_type_id = this.identifyCarType(extractedFields);
            
            this.stats.fieldExtractionSuccess++;
            return mappedFields;
        }

        /**
         * 应用KKday特定处理
         * @param {Object} fields - 字段对象
         * @param {string} originalText - 原始文本
         * @returns {Object} KKday特定处理后的字段
         */
        applyKKdaySpecificProcessing(fields, originalText) {
            const processedFields = { ...fields };
            
            // 识别活动类型
            processedFields.activity_type = this.identifyActivityType(originalText);
            
            // 根据活动类型调整服务设置
            this.adjustServiceByActivityType(processedFields);
            
            // 处理多语言联系信息
            this.processMultilingualContact(processedFields, originalText);
            
            // 智能货币转换建议
            this.suggestCurrencyConversion(processedFields);
            
            return processedFields;
        }

        /**
         * 识别活动类型
         * @param {string} text - 文本
         * @returns {string} 活动类型
         */
        identifyActivityType(text) {
            const lowerText = text.toLowerCase();
            
            for (const [type, keywords] of Object.entries(this.kkdayConfig.activityTypes)) {
                if (keywords.some(keyword => lowerText.includes(keyword.toLowerCase()))) {
                    this.stats.activityTypeDetected++;
                    return type;
                }
            }
            
            return 'transfer'; // 默认为接送服务
        }

        /**
         * 根据活动类型调整服务设置
         * @param {Object} fields - 字段对象
         */
        adjustServiceByActivityType(fields) {
            switch (fields.activity_type) {
                case 'tour':
                    fields.service_type_id = 4; // 包车
                    fields.pickup_sign = true;
                    fields.is_return = true;
                    break;
                case 'experience':
                    fields.service_type_id = 2; // 接机
                    fields.pickup_sign = true;
                    break;
                case 'rental':
                    fields.service_type_id = 4; // 包车
                    fields.pickup_sign = false;
                    break;
                default:
                    // 保持默认设置
                    break;
            }
        }

        /**
         * 处理多语言联系信息
         * @param {Object} fields - 字段对象
         * @param {string} originalText - 原始文本
         */
        processMultilingualContact(fields, originalText) {
            // 检测中文姓名
            if (fields.passenger_name && /[\u4e00-\u9fff]/.test(fields.passenger_name)) {
                fields.languages_id_array = {"0": "4"}; // 中文
            }
            
            // 检测繁体中文
            if (originalText.includes('預訂') || originalText.includes('確認') || originalText.includes('聯絡')) {
                fields.languages_id_array = {"0": "4"}; // 中文
                fields.region_preference = 'traditional_chinese';
            }
        }

        /**
         * 智能货币转换建议
         * @param {Object} fields - 字段对象
         */
        suggestCurrencyConversion(fields) {
            if (fields.currency && fields.price) {
                // 根据货币类型设置转换建议
                const conversionRates = {
                    'USD': 4.3,  // 美元转马币
                    'SGD': 3.4,  // 新币转马币
                    'CNY': 0.615 // 人民币转马币
                };
                
                if (conversionRates[fields.currency]) {
                    fields.suggested_myr_price = (fields.price * conversionRates[fields.currency]).toFixed(2);
                    fields.conversion_rate = conversionRates[fields.currency];
                }
            }
        }

        /**
         * 识别服务类型
         * @param {Object} fields - 字段对象
         * @returns {number} 服务类型ID
         */
        identifyServiceType(fields) {
            const fullText = Object.values(fields).join(' ').toLowerCase();
            
            for (const [typeId, keywords] of Object.entries(this.kkdayConfig.serviceTypeKeywords)) {
                if (keywords.some(keyword => fullText.includes(keyword.toLowerCase()))) {
                    return parseInt(typeId);
                }
            }
            
            return this.kkdayConfig.presetValues.service_type_id; // 默认接机
        }

        /**
         * 识别车型
         * @param {Object} fields - 字段对象
         * @returns {number} 车型ID
         */
        identifyCarType(fields) {
            const fullText = Object.values(fields).join(' ').toLowerCase();
            const passengerCount = fields.passenger_count || 0;
            
            // 根据人数判断
            if (passengerCount > 8) {
                return 4; // 巴士
            } else if (passengerCount > 5) {
                return 2; // MPV
            }
            
            // 根据关键词判断
            for (const [typeId, keywords] of Object.entries(this.kkdayConfig.carTypeKeywords)) {
                if (keywords.some(keyword => fullText.includes(keyword.toLowerCase()))) {
                    return parseInt(typeId);
                }
            }
            
            return this.kkdayConfig.presetValues.car_type_id; // 默认轿车
        }

        /**
         * 应用预设值
         * @param {Object} mappedFields - 映射后的字段
         * @returns {Object} 应用预设值后的字段
         */
        applyPresetValues(mappedFields) {
            const fieldsWithPresets = { ...this.kkdayConfig.presetValues, ...mappedFields };
            
            // 特殊处理逻辑
            if (!fieldsWithPresets.ota_reference_number) {
                fieldsWithPresets.ota_reference_number = 'KKDAY_' + Date.now();
            }
            
            // KKday特定调整
            fieldsWithPresets.pickup_sign = true; // KKday通常需要举牌
            fieldsWithPresets.payment_method = 'prepaid'; // 预付费
            
            // 根据活动类型调整
            if (fieldsWithPresets.activity_type === 'tour') {
                fieldsWithPresets.is_return = true;
            }
            
            return fieldsWithPresets;
        }

        /**
         * 验证和清理字段
         * @param {Object} fields - 字段对象
         * @returns {Object} 验证后的字段
         */
        validateAndCleanFields(fields) {
            const validatedFields = { ...fields };
            const errors = [];
            
            // 验证必填字段
            for (const requiredField of this.kkdayConfig.validationRules.required_fields) {
                if (!validatedFields[requiredField] || validatedFields[requiredField].toString().trim() === '') {
                    errors.push(`缺少必填字段: ${requiredField}`);
                }
            }
            
            // 验证字段格式
            for (const [field, pattern] of Object.entries(this.kkdayConfig.validationRules.field_formats)) {
                if (validatedFields[field] && !pattern.test(validatedFields[field])) {
                    errors.push(`字段格式错误: ${field} = ${validatedFields[field]}`);
                }
            }
            
            // 数据清理
            if (validatedFields.pickup_date) {
                validatedFields.pickup_date = this.standardizeDate(validatedFields.pickup_date);
            }
            
            if (validatedFields.pickup_time) {
                validatedFields.pickup_time = this.standardizeTime(validatedFields.pickup_time);
            }
            
            if (validatedFields.contact_number) {
                validatedFields.contact_number = this.standardizePhoneNumber(validatedFields.contact_number);
            }
            
            if (errors.length > 0) {
                this.stats.validationErrors++;
                this.logger.logWarning(`${this.platformDisplayName}订单验证警告`, errors);
            }
            
            return validatedFields;
        }

        /**
         * 标准化日期格式
         * @param {string} dateStr - 日期字符串
         * @returns {string} 标准化后的日期
         */
        standardizeDate(dateStr) {
            if (!dateStr) return '';
            
            // 尝试解析各种日期格式
            const formats = [
                /^(\d{4})[-\/](\d{1,2})[-\/](\d{1,2})$/,  // YYYY-MM-DD 或 YYYY/MM/DD
                /^(\d{1,2})[-\/](\d{1,2})[-\/](\d{4})$/   // DD-MM-YYYY 或 DD/MM/YYYY
            ];
            
            for (const format of formats) {
                const match = dateStr.match(format);
                if (match) {
                    if (match[1].length === 4) {
                        // YYYY-MM-DD 格式
                        return `${match[1]}-${match[2].padStart(2, '0')}-${match[3].padStart(2, '0')}`;
                    } else {
                        // DD-MM-YYYY 格式
                        return `${match[3]}-${match[2].padStart(2, '0')}-${match[1].padStart(2, '0')}`;
                    }
                }
            }
            
            return dateStr; // 无法解析时返回原值
        }

        /**
         * 标准化时间格式
         * @param {string} timeStr - 时间字符串
         * @returns {string} 标准化后的时间
         */
        standardizeTime(timeStr) {
            if (!timeStr) return '';
            
            const timeMatch = timeStr.match(/([01]?\d|2[0-3])[:\s]?([0-5]\d)/);
            if (timeMatch) {
                return `${timeMatch[1].padStart(2, '0')}:${timeMatch[2]}`;
            }
            
            return timeStr;
        }

        /**
         * 标准化电话号码
         * @param {string} phoneStr - 电话号码字符串
         * @returns {string} 标准化后的电话号码
         */
        standardizePhoneNumber(phoneStr) {
            if (!phoneStr) return '';
            
            // 移除所有非数字和+号
            let cleaned = phoneStr.replace(/[^\d+]/g, '');
            
            // 处理国际格式
            if (cleaned.startsWith('+')) {
                return cleaned;
            }
            
            // 处理中国手机号
            if (cleaned.match(/^1[3-9]\d{9}$/)) {
                return '+86' + cleaned;
            }
            
            // 处理马来西亚手机号
            if (cleaned.match(/^01[0-9]\d{7,8}$/)) {
                return '+60' + cleaned.substring(1);
            }
            
            return phoneStr; // 无法标准化时返回原值
        }

        /**
         * 生成处理结果
         * @param {Object} fields - 处理后的字段
         * @param {string} referenceNumber - 参考号
         * @param {Object} options - 选项
         * @returns {Object} 处理结果
         */
        generateResult(fields, referenceNumber, options) {
            const result = {
                success: true,
                processor: this.processorName,
                platform: this.platform,
                version: this.version,
                confidence: this.calculateConfidence(fields, referenceNumber),
                data: {
                    ...fields,
                    ota_reference_number: referenceNumber || fields.ota_reference_number,
                    ota: this.platform.toLowerCase(),
                    original_platform: this.platformDisplayName
                },
                metadata: {
                    processingTime: Date.now(),
                    extractedFields: Object.keys(fields).length,
                    validationPassed: true,
                    processorSpecific: {
                        referencePatternMatched: !!referenceNumber,
                        serviceTypeDetected: !!fields.service_type_id,
                        carTypeDetected: !!fields.car_type_id,
                        activityTypeDetected: !!fields.activity_type,
                        currencyDetected: !!fields.currency,
                        multilingualProcessing: !!fields.region_preference
                    }
                }
            };
            
            // 添加测试模式标记
            if (options.test) {
                result.metadata.testMode = true;
            }
            
            return result;
        }

        /**
         * 计算置信度
         * @param {Object} fields - 字段对象
         * @param {string} referenceNumber - 参考号
         * @returns {number} 置信度分数 (0-1)
         */
        calculateConfidence(fields, referenceNumber) {
            let confidence = 0.5; // 基础置信度
            
            // 参考号匹配加分
            if (referenceNumber) {
                confidence += 0.2;
            }
            
            // 必填字段完整性加分
            const requiredFields = this.kkdayConfig.validationRules.required_fields;
            const presentFields = requiredFields.filter(field => fields[field]);
            confidence += (presentFields.length / requiredFields.length) * 0.2;
            
            // KKday特定字段识别加分
            if (fields.voucher_number) confidence += 0.05;
            if (fields.activity_type) confidence += 0.05;
            if (fields.currency) confidence += 0.05;
            if (fields.passenger_count) confidence += 0.05;
            
            return Math.min(confidence, 1.0);
        }

        /**
         * 生成降级结果
         * @param {string} orderText - 原始订单文本
         * @param {Object} options - 选项
         * @param {Error} error - 错误对象
         * @returns {Object} 降级结果
         */
        generateFallbackResult(orderText, options, error) {
            return {
                success: false,
                processor: this.processorName,
                platform: this.platform,
                version: this.version,
                confidence: 0.1,
                error: error.message,
                data: {
                    ...this.kkdayConfig.presetValues,
                    ota_reference_number: 'KKDAY_ERROR_' + Date.now(),
                    ota: this.platform.toLowerCase(),
                    original_text: orderText.substring(0, 200) // 限制长度
                },
                metadata: {
                    processingTime: Date.now(),
                    fallback: true,
                    error: {
                        message: error.message,
                        type: error.name
                    }
                }
            };
        }

        /**
         * 更新统计信息
         * @param {number} startTime - 开始时间
         * @param {boolean} success - 是否成功
         */
        updateStats(startTime, success) {
            const processingTime = Date.now() - startTime;
            
            if (success) {
                this.stats.successfulProcessed++;
            }
            
            // 更新平均处理时间
            const totalProcessed = this.stats.totalProcessed;
            this.stats.averageProcessingTime = 
                ((this.stats.averageProcessingTime * (totalProcessed - 1)) + processingTime) / totalProcessed;
        }

        /**
         * 获取处理器统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                ...this.stats,
                successRate: this.stats.totalProcessed > 0 ? 
                    (this.stats.successfulProcessed / this.stats.totalProcessed) : 0,
                platform: this.platform,
                processorName: this.processorName
            };
        }

        /**
         * 重置统计信息
         */
        resetStats() {
            this.stats = {
                totalProcessed: 0,
                successfulProcessed: 0,
                failedProcessed: 0,
                averageProcessingTime: 0,
                referenceNumberMatches: 0,
                fieldExtractionSuccess: 0,
                validationErrors: 0,
                activityTypeDetected: 0,
                currencyDetected: 0
            };
        }

        /**
         * 获取处理器信息
         * @returns {Object} 处理器信息
         */
        getProcessorInfo() {
            return {
                name: this.processorName,
                platform: this.platform,
                platformDisplayName: this.platformDisplayName,
                version: this.version,
                supportedPatterns: this.kkdayConfig.referencePatterns.length,
                supportedFields: Object.keys(this.kkdayConfig.fieldMappings).length,
                identificationKeywords: this.kkdayConfig.identificationKeywords,
                supportedCurrencies: Object.keys(this.kkdayConfig.currencyPatterns),
                activityTypes: Object.keys(this.kkdayConfig.activityTypes)
            };
        }
    }

    // 创建全局单例实例
    function getKKdayProcessor() {
        if (!window.OTA.gemini.processors.kkdayProcessor) {
            window.OTA.gemini.processors.kkdayProcessor = new KKdayProcessor();
        }
        return window.OTA.gemini.processors.kkdayProcessor;
    }

    // 暴露到全局命名空间
    window.OTA.gemini.processors.KKdayProcessor = KKdayProcessor;
    window.OTA.gemini.processors.getKKdayProcessor = getKKdayProcessor;

    // 向后兼容
    window.getKKdayProcessor = getKKdayProcessor;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('kkdayProcessor', getKKdayProcessor(), '@KKDAY_PROCESSOR');
        window.OTA.Registry.registerFactory('getKKdayProcessor', getKKdayProcessor, '@KKDAY_PROCESSOR_FACTORY');
    }

    console.log('✅ KKday专用处理器已加载');

})();
