/**
 * 配置缓存优化器
 * 优化配置系统的缓存性能，减少重复加载和内存占用
 * 
 * 功能特性:
 * - LRU缓存策略
 * - 配置预加载
 * - 缓存压缩
 * - 性能监控
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.core = window.OTA.core || {};

(function() {
    'use strict';

    /**
     * 配置缓存优化器类
     */
    class ConfigCacheOptimizer {
        constructor() {
            // LRU缓存实现
            this.cache = new Map();
            this.accessOrder = new Map(); // 记录访问顺序
            
            // 缓存配置
            this.config = {
                maxSize: 500,           // 最大缓存条目数
                maxMemory: 50 * 1024 * 1024, // 50MB内存限制
                ttl: 30 * 60 * 1000,    // 30分钟TTL
                compressionEnabled: true, // 启用压缩
                preloadEnabled: true,    // 启用预加载
                metricsEnabled: true     // 启用性能指标
            };
            
            // 性能指标
            this.metrics = {
                hits: 0,
                misses: 0,
                evictions: 0,
                compressionRatio: 0,
                averageAccessTime: 0,
                memoryUsage: 0,
                preloadCount: 0
            };
            
            // 预加载配置列表
            this.preloadConfigs = [
                'api',
                'ui',
                'performance',
                'logging',
                'features',
                'ota-reference-patterns',
                'field-mapping'
            ];
            
            // 压缩器
            this.compressor = this.createCompressor();
            
            this.warningManager = null;
            this.initialize();
        }

        /**
         * 初始化优化器
         */
        initialize() {
            // 延迟获取依赖
            setTimeout(() => {
                this.warningManager = window.OTA?.core?.warningManager;
                
                // 启动预加载
                if (this.config.preloadEnabled) {
                    this.startPreloading();
                }
                
                // 启动缓存清理
                this.startCacheCleanup();
                
                // 启动性能监控
                if (this.config.metricsEnabled) {
                    this.startMetricsCollection();
                }
            }, 100);
            
            console.log('✅ 配置缓存优化器已初始化');
        }

        /**
         * 获取配置（带缓存优化）
         * @param {string} key - 配置键
         * @param {Function} loader - 配置加载函数
         * @returns {Promise<any>} 配置值
         */
        async getConfig(key, loader) {
            const startTime = performance.now();
            
            try {
                // 检查缓存
                const cached = this.getCachedConfig(key);
                if (cached !== null) {
                    this.metrics.hits++;
                    this.updateAccessOrder(key);
                    this.updateMetrics(startTime);
                    return cached;
                }
                
                // 缓存未命中，加载配置
                this.metrics.misses++;
                const config = await loader();
                
                // 存储到缓存
                await this.setCachedConfig(key, config);
                
                this.updateMetrics(startTime);
                return config;
                
            } catch (error) {
                console.error(`配置加载失败 [${key}]:`, error);
                throw error;
            }
        }

        /**
         * 获取缓存的配置
         * @param {string} key - 配置键
         * @returns {any|null} 配置值或null
         */
        getCachedConfig(key) {
            const entry = this.cache.get(key);
            if (!entry) {
                return null;
            }
            
            // 检查TTL
            if (Date.now() - entry.timestamp > this.config.ttl) {
                this.cache.delete(key);
                this.accessOrder.delete(key);
                return null;
            }
            
            // 解压缩配置
            return this.decompressConfig(entry.data, entry.compressed);
        }

        /**
         * 设置缓存的配置
         * @param {string} key - 配置键
         * @param {any} config - 配置值
         */
        async setCachedConfig(key, config) {
            // 检查缓存大小限制
            if (this.cache.size >= this.config.maxSize) {
                this.evictLRU();
            }
            
            // 压缩配置
            const { data, compressed } = await this.compressConfig(config);
            
            // 存储配置
            const entry = {
                data,
                compressed,
                timestamp: Date.now(),
                size: this.calculateSize(data),
                accessCount: 1
            };
            
            this.cache.set(key, entry);
            this.updateAccessOrder(key);
            
            // 检查内存使用
            this.checkMemoryUsage();
        }

        /**
         * 更新访问顺序
         * @param {string} key - 配置键
         */
        updateAccessOrder(key) {
            // 删除旧的访问记录
            this.accessOrder.delete(key);
            // 添加新的访问记录
            this.accessOrder.set(key, Date.now());
            
            // 更新访问计数
            const entry = this.cache.get(key);
            if (entry) {
                entry.accessCount++;
            }
        }

        /**
         * 淘汰最少使用的配置
         */
        evictLRU() {
            if (this.accessOrder.size === 0) {
                return;
            }
            
            // 找到最少使用的配置
            const oldestKey = this.accessOrder.keys().next().value;
            
            // 删除配置
            this.cache.delete(oldestKey);
            this.accessOrder.delete(oldestKey);
            this.metrics.evictions++;
            
            console.log(`🗑️ 淘汰LRU配置: ${oldestKey}`);
        }

        /**
         * 压缩配置
         * @param {any} config - 配置对象
         * @returns {Object} 压缩结果
         */
        async compressConfig(config) {
            if (!this.config.compressionEnabled) {
                return { data: config, compressed: false };
            }
            
            try {
                const jsonString = JSON.stringify(config);
                const compressed = await this.compressor.compress(jsonString);
                
                // 计算压缩比
                const originalSize = new Blob([jsonString]).size;
                const compressedSize = compressed.length;
                const ratio = compressedSize / originalSize;
                
                // 只有压缩效果好时才使用压缩
                if (ratio < 0.8) {
                    this.metrics.compressionRatio = (this.metrics.compressionRatio + ratio) / 2;
                    return { data: compressed, compressed: true };
                } else {
                    return { data: config, compressed: false };
                }
            } catch (error) {
                console.warn('配置压缩失败，使用原始数据:', error);
                return { data: config, compressed: false };
            }
        }

        /**
         * 解压缩配置
         * @param {any} data - 数据
         * @param {boolean} compressed - 是否压缩
         * @returns {any} 解压缩后的配置
         */
        decompressConfig(data, compressed) {
            if (!compressed) {
                return data;
            }
            
            try {
                const decompressed = this.compressor.decompress(data);
                return JSON.parse(decompressed);
            } catch (error) {
                console.error('配置解压缩失败:', error);
                return null;
            }
        }

        /**
         * 创建压缩器
         * @returns {Object} 压缩器对象
         */
        createCompressor() {
            // 简单的字符串压缩实现
            return {
                compress: async (str) => {
                    // 使用简单的重复字符压缩
                    return str.replace(/(.)\1{2,}/g, (match, char) => {
                        return `${char}*${match.length}`;
                    });
                },
                
                decompress: (compressed) => {
                    // 解压缩
                    return compressed.replace(/(.)\*(\d+)/g, (match, char, count) => {
                        return char.repeat(parseInt(count));
                    });
                }
            };
        }

        /**
         * 启动预加载
         */
        async startPreloading() {
            console.log('🚀 开始配置预加载...');
            
            for (const configKey of this.preloadConfigs) {
                try {
                    // 检查配置是否已存在
                    if (this.cache.has(configKey)) {
                        continue;
                    }
                    
                    // 尝试从配置系统加载
                    const config = await this.loadConfigFromSystem(configKey);
                    if (config) {
                        await this.setCachedConfig(configKey, config);
                        this.metrics.preloadCount++;
                        console.log(`✅ 预加载配置: ${configKey}`);
                    }
                } catch (error) {
                    console.warn(`⚠️ 预加载配置失败 [${configKey}]:`, error);
                }
            }
            
            console.log(`✅ 配置预加载完成，共预加载 ${this.metrics.preloadCount} 个配置`);
        }

        /**
         * 从配置系统加载配置
         * @param {string} key - 配置键
         * @returns {Promise<any>} 配置值
         */
        async loadConfigFromSystem(key) {
            // 尝试从统一配置中心加载
            const configCenter = window.OTA?.configCenter;
            if (configCenter && typeof configCenter.getConfig === 'function') {
                const config = configCenter.getConfig(key);
                if (config !== null) {
                    return config;
                }
            }
            
            // 尝试从Gemini配置管理器加载
            const geminiConfigManager = window.OTA?.gemini?.core?.configManager;
            if (geminiConfigManager && typeof geminiConfigManager.loadConfig === 'function') {
                try {
                    return await geminiConfigManager.loadConfig(key);
                } catch (error) {
                    // 忽略加载错误
                }
            }
            
            return null;
        }

        /**
         * 启动缓存清理
         */
        startCacheCleanup() {
            setInterval(() => {
                this.cleanupExpiredConfigs();
                this.optimizeCache();
            }, 5 * 60 * 1000); // 每5分钟清理一次
        }

        /**
         * 清理过期配置
         */
        cleanupExpiredConfigs() {
            const now = Date.now();
            const expiredKeys = [];
            
            for (const [key, entry] of this.cache.entries()) {
                if (now - entry.timestamp > this.config.ttl) {
                    expiredKeys.push(key);
                }
            }
            
            expiredKeys.forEach(key => {
                this.cache.delete(key);
                this.accessOrder.delete(key);
            });
            
            if (expiredKeys.length > 0) {
                console.log(`🧹 清理了 ${expiredKeys.length} 个过期配置`);
            }
        }

        /**
         * 优化缓存
         */
        optimizeCache() {
            // 检查内存使用
            this.checkMemoryUsage();
            
            // 如果缓存过大，进行优化
            if (this.cache.size > this.config.maxSize * 0.8) {
                this.optimizeCacheSize();
            }
        }

        /**
         * 检查内存使用
         */
        checkMemoryUsage() {
            let totalSize = 0;
            for (const entry of this.cache.values()) {
                totalSize += entry.size || 0;
            }
            
            this.metrics.memoryUsage = totalSize;
            
            if (totalSize > this.config.maxMemory) {
                console.warn(`⚠️ 配置缓存内存使用过高: ${(totalSize / 1024 / 1024).toFixed(2)}MB`);
                this.evictLargestConfigs();
            }
        }

        /**
         * 淘汰最大的配置
         */
        evictLargestConfigs() {
            const entries = Array.from(this.cache.entries())
                .sort((a, b) => (b[1].size || 0) - (a[1].size || 0));
            
            // 淘汰最大的25%配置
            const evictCount = Math.ceil(entries.length * 0.25);
            for (let i = 0; i < evictCount; i++) {
                const [key] = entries[i];
                this.cache.delete(key);
                this.accessOrder.delete(key);
                this.metrics.evictions++;
            }
            
            console.log(`🗑️ 淘汰了 ${evictCount} 个大型配置`);
        }

        /**
         * 优化缓存大小
         */
        optimizeCacheSize() {
            // 基于访问频率优化
            const entries = Array.from(this.cache.entries())
                .sort((a, b) => (a[1].accessCount || 0) - (b[1].accessCount || 0));
            
            // 淘汰访问频率最低的20%配置
            const evictCount = Math.ceil(entries.length * 0.2);
            for (let i = 0; i < evictCount; i++) {
                const [key] = entries[i];
                this.cache.delete(key);
                this.accessOrder.delete(key);
                this.metrics.evictions++;
            }
            
            console.log(`🎯 优化缓存，淘汰了 ${evictCount} 个低频配置`);
        }

        /**
         * 启动性能指标收集
         */
        startMetricsCollection() {
            setInterval(() => {
                this.collectMetrics();
            }, 60 * 1000); // 每分钟收集一次
        }

        /**
         * 收集性能指标
         */
        collectMetrics() {
            const hitRate = this.metrics.hits / (this.metrics.hits + this.metrics.misses) * 100;
            
            console.log('📊 配置缓存性能指标:', {
                缓存命中率: `${hitRate.toFixed(2)}%`,
                缓存大小: this.cache.size,
                内存使用: `${(this.metrics.memoryUsage / 1024 / 1024).toFixed(2)}MB`,
                淘汰次数: this.metrics.evictions,
                压缩比: `${(this.metrics.compressionRatio * 100).toFixed(2)}%`,
                预加载数量: this.metrics.preloadCount
            });
        }

        /**
         * 更新性能指标
         * @param {number} startTime - 开始时间
         */
        updateMetrics(startTime) {
            const duration = performance.now() - startTime;
            this.metrics.averageAccessTime = 
                (this.metrics.averageAccessTime + duration) / 2;
        }

        /**
         * 计算数据大小
         * @param {any} data - 数据
         * @returns {number} 大小（字节）
         */
        calculateSize(data) {
            try {
                return new Blob([JSON.stringify(data)]).size;
            } catch (error) {
                return 0;
            }
        }

        /**
         * 获取缓存统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                ...this.metrics,
                缓存大小: this.cache.size,
                最大缓存大小: this.config.maxSize,
                缓存命中率: `${(this.metrics.hits / (this.metrics.hits + this.metrics.misses) * 100).toFixed(2)}%`,
                内存使用: `${(this.metrics.memoryUsage / 1024 / 1024).toFixed(2)}MB`,
                最大内存限制: `${(this.config.maxMemory / 1024 / 1024).toFixed(2)}MB`
            };
        }
    }

    // 创建全局唯一的配置缓存优化器实例
    const configCacheOptimizer = new ConfigCacheOptimizer();

    // 暴露到OTA命名空间
    window.OTA.core.configCacheOptimizer = configCacheOptimizer;

    console.log('✅ 配置缓存优化器模块已加载');

})();
