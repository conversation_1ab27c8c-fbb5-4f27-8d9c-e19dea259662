# OTA系统项目结构索引

## 📁 完整目录结构

```
create job/
├── 📁 css/                          # 样式文件
│   ├── main.css                    # 主样式文件
│   ├── multi-order-cards.css       # 多订单卡片样式
│   ├── base/                       # 基础样式
│   ├── components/                 # 组件样式
│   ├── layout/                     # 布局样式
│   ├── pages/                      # 页面样式
│   └── multi-order/                # 多订单样式
│
├── 📁 js/                           # JavaScript模块
│   ├── 🚀 bootstrap/               # 核心引导层
│   │   ├── app-state.js           # 应用状态管理
│   │   └── application-bootstrap.js # 启动协调器
│   │
│   ├── ⚙️ core/                    # 核心架构层
│   │   ├── dependency-container.js      # 依赖注入容器
│   │   ├── service-locator.js          # 服务定位器
│   │   ├── lazy-loader.js              # 懒加载系统
│   │   ├── module-loader-config.js     # 模块配置
│   │   ├── performance-monitor.js      # 性能监控
│   │   ├── component-lifecycle-manager.js # 组件生命周期
│   │   ├── global-event-coordinator.js # 全局事件协调
│   │   ├── ota-registry.js             # OTA注册中心
│   │   ├── architecture-guardian.js    # 架构守护器
│   │   ├── duplicate-checker.js        # 重复检查器
│   │   ├── unified-data-manager.js     # 统一数据管理
│   │   └── ...其他核心模块
│   │
│   ├── 🔧 services/                # 业务服务层
│   │   ├── api-service.js          # API服务
│   │   ├── logger.js               # 日志服务
│   │   ├── i18n.js                 # 国际化服务
│   │   ├── language-manager.js     # 语言管理
│   │   └── monitoring-wrapper.js   # 监控包装器
│   │
│   ├── 🎛️ managers/                # 管理器层
│   │   ├── ui-manager.js           # UI管理器
│   │   ├── event-manager.js        # 事件管理器
│   │   ├── form-manager.js         # 表单管理器
│   │   ├── multi-order-manager.js  # 多订单管理器
│   │   ├── price-manager.js        # 价格管理器
│   │   ├── state-manager.js        # 状态管理器
│   │   ├── currency-converter.js   # 货币转换器
│   │   ├── order-history-manager.js # 历史管理器
│   │   ├── paging-service-manager.js # 分页管理器
│   │   └── realtime-analysis-manager.js # 实时分析
│   │
│   ├── 🤖 ai/                      # AI服务层
│   │   ├── gemini-service.js       # Gemini AI服务
│   │   ├── kimi-service.js         # Kimi AI服务
│   │   └── gemini/                 # Gemini模块集
│   │       ├── core/               # 核心处理器
│   │       ├── configs/            # 配置文件
│   │       ├── processors/         # 处理器集合
│   │       ├── tests/              # 测试套件
│   │       ├── integration/        # 集成模块
│   │       └── gemini-coordinator.js # 协调器
│   │
│   ├── 🧩 components/              # UI组件层
│   │   ├── image-upload-manager.js # 图片上传组件
│   │   ├── grid-resizer.js         # 网格缩放组件
│   │   └── multi-order/            # 多订单组件集
│   │       ├── multi-order-detector.js    # 检测器
│   │       ├── multi-order-processor.js   # 处理器
│   │       ├── multi-order-renderer.js    # 渲染器
│   │       ├── multi-order-ui-manager.js  # UI管理
│   │       ├── multi-order-state-manager.js # 状态管理
│   │       ├── field-mapping-config.js    # 字段映射
│   │       └── ...其他组件
│   │
│   └── 🛠️ utils/                   # 工具函数层
│       ├── utils.js                # 通用工具
│       ├── hotel-data-inline.js    # 酒店数据
│       ├── hotel-name-database.js  # 酒店名称库
│       └── ota-channel-mapping.js  # 渠道映射
│
├── 📚 documentation/               # 项目文档
│   ├── reports/                   # 项目报告
│   │   ├── CODE_REVIEW_AND_FIXES_REPORT.md
│   │   ├── PHASE2_REORGANIZATION_REPORT.md
│   │   ├── gemini-cleanup-final-report.md
│   │   └── ...其他报告
│   ├── tests/                     # 测试文件
│   │   ├── test*.html             # 测试页面
│   │   ├── *test*.js              # 测试脚本
│   │   └── test-suites/           # 测试套件
│   ├── guides/                    # 用户指南
│   │   ├── API-Documentation.md   # API文档
│   │   ├── User-Guide.md          # 用户指南
│   │   └── Performance-Optimization-Guide.md # 性能优化指南
│   └── PROJECT_STRUCTURE_INDEX.md # 本索引文件
│
├── 🗃️ memory-bank/                # 知识库
│   ├── code_structure.md         # 代码结构文档
│   ├── productContext.md         # 产品上下文
│   ├── systemPatterns.md         # 系统模式
│   └── implementation-plans/     # 实施计划
│
├── 🧪 tests/                      # 主测试目录
│   ├── test-suites.js            # 测试套件
│   ├── performance-test-suite.js # 性能测试
│   └── README.md                 # 测试说明
│
├── 📄 配置和根文件
│   ├── index.html                # 主页面
│   ├── main.js                   # 主入口文件
│   ├── package.json              # 项目配置
│   ├── netlify.toml              # Netlify配置
│   └── README.md                 # 项目说明
│
└── 🏗️ 部署和其他
    ├── deployment/               # 部署配置
    ├── .github/                  # GitHub配置
    ├── .cursor/                  # Cursor IDE配置
    └── .augment/                 # 增强配置
```

## 🎯 模块职责划分

### 🚀 Bootstrap层 (启动引导)
- **应用状态管理**: 全局状态存储和管理
- **启动协调器**: 统一管理启动流程和时序

### ⚙️ Core层 (核心架构)
- **依赖管理**: 依赖注入和服务定位
- **性能优化**: 懒加载、监控、生命周期管理
- **架构保障**: 架构守护、重复检查、数据管理

### 🔧 Services层 (业务服务)
- **API通信**: 与后端服务交互
- **基础服务**: 日志、国际化、监控

### 🎛️ Managers层 (管理器)
- **UI管理**: 界面协调和交互
- **业务管理**: 订单、价格、表单、事件处理

### 🤖 AI层 (人工智能)
- **AI服务**: Gemini和Kimi AI集成
- **智能处理**: 订单解析、数据分析

### 🧩 Components层 (UI组件)
- **通用组件**: 图片上传、网格缩放
- **业务组件**: 多订单处理组件集

### 🛠️ Utils层 (工具函数)
- **通用工具**: 常用函数库
- **数据工具**: 酒店数据、渠道映射

## 📊 加载策略

### 立即加载 (Critical)
- 核心架构模块 (5个)
- 启动必需组件

### 按需加载 (On-Demand)
- AI服务模块 (50+个)
- 多订单组件
- 测试模块

### 预加载 (Preload)
- 管理器模块 (10个)
- 国际化模块

## 🔧 关键系统

### 懒加载系统
- **配置**: `js/core/module-loader-config.js`
- **执行**: `js/core/lazy-loader.js`
- **集成**: `js/bootstrap/application-bootstrap.js`

### 依赖管理
- **容器**: `js/core/dependency-container.js`
- **定位**: `js/core/service-locator.js`
- **注册**: `js/core/ota-registry.js`

### 性能监控
- **监控器**: `js/core/performance-monitor.js`
- **生命周期**: `js/core/component-lifecycle-manager.js`
- **内存管理**: 集成在生命周期管理器中

## 📝 文档资源

### 技术文档
- **API文档**: `documentation/guides/API-Documentation.md`
- **性能指南**: `documentation/guides/Performance-Optimization-Guide.md`
- **用户指南**: `documentation/guides/User-Guide.md`

### 项目报告
- **代码审查报告**: `documentation/reports/CODE_REVIEW_AND_FIXES_REPORT.md`
- **文件重组报告**: `documentation/reports/PHASE2_REORGANIZATION_REPORT.md`
- **Gemini清理报告**: `documentation/reports/gemini-cleanup-final-report.md`

### 测试资源
- **测试页面**: `documentation/tests/test*.html`
- **测试脚本**: `documentation/tests/*test*.js`
- **性能测试**: `tests/performance-test-suite.js`

---
*最后更新: 2025-07-27*  
*维护者: OTA开发团队*