/**
 * @INTEGRATION 语言管理器集成模块
 * 🏷️ 标签: @LANGUAGE_MANAGER_INTEGRATION
 * 📝 说明: 集成现有语言管理器与Gemini处理器系统，提供多语言支持和本地化功能
 * 🎯 功能: 语言检测、多语言支持、本地化处理、语言偏好管理
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.integrations = window.OTA.gemini.integrations || {};

(function() {
    'use strict';

    /**
     * 语言管理器集成类
     * 提供Gemini处理器与现有语言管理器的集成功能
     */
    class LanguageManagerIntegration {
        constructor() {
            this.logger = window.getLogger?.() || console;
            this.languageManager = null;
            this.i18nService = null;
            this.supportedLanguages = new Map();
            this.languageDetectionCache = new Map();
            this.processorLanguagePreferences = new Map();
            
            // 语言代码映射
            this.languageCodeMapping = {
                'zh-CN': { id: 4, name: 'Chinese Simplified', code: 'zh-CN' },
                'zh-TW': { id: 4, name: 'Chinese Traditional', code: 'zh-TW' },
                'en': { id: 2, name: 'English', code: 'en' },
                'en-US': { id: 2, name: 'English (US)', code: 'en-US' },
                'en-GB': { id: 2, name: 'English (UK)', code: 'en-GB' },
                'ms': { id: 3, name: 'Malay', code: 'ms' },
                'th': { id: 5, name: 'Thai', code: 'th' },
                'vi': { id: 6, name: 'Vietnamese', code: 'vi' },
                'ja': { id: 7, name: 'Japanese', code: 'ja' },
                'ko': { id: 8, name: 'Korean', code: 'ko' },
                'de': { id: 9, name: 'German', code: 'de' },
                'fr': { id: 10, name: 'French', code: 'fr' },
                'es': { id: 11, name: 'Spanish', code: 'es' },
                'it': { id: 12, name: 'Italian', code: 'it' },
                'pt': { id: 13, name: 'Portuguese', code: 'pt' },
                'nl': { id: 14, name: 'Dutch', code: 'nl' }
            };
            
            this.init();
        }

        /**
         * 初始化语言管理器集成
         */
        async init() {
            try {
                // 获取现有语言管理器实例
                await this.initializeLanguageManager();
                
                // 初始化国际化服务
                await this.initializeI18nService();
                
                // 加载支持的语言列表
                await this.loadSupportedLanguages();
                
                // 初始化处理器语言偏好
                this.initializeProcessorLanguagePreferences();
                
                this.logger.log('语言管理器集成初始化完成', 'info');
                
            } catch (error) {
                this.logger.logError('语言管理器集成初始化失败', error);
                throw error;
            }
        }

        /**
         * 初始化语言管理器
         */
        async initializeLanguageManager() {
            // 尝试获取现有的语言管理器
            this.languageManager = window.OTA?.managers?.getLanguageManager?.() || 
                                 window.getLanguageManager?.() ||
                                 window.languageManager;
            
            if (!this.languageManager) {
                this.logger.log('未找到现有语言管理器，创建基础语言管理器', 'warning');
                this.languageManager = this.createBasicLanguageManager();
            }
            
            this.logger.log('语言管理器初始化完成', 'info');
        }

        /**
         * 初始化国际化服务
         */
        async initializeI18nService() {
            // 尝试获取现有的国际化服务
            this.i18nService = window.OTA?.i18n || 
                             window.getI18nService?.() ||
                             window.i18n;
            
            if (!this.i18nService) {
                this.logger.log('未找到现有国际化服务，创建基础国际化服务', 'warning');
                this.i18nService = this.createBasicI18nService();
            }
            
            this.logger.log('国际化服务初始化完成', 'info');
        }

        /**
         * 加载支持的语言列表
         */
        async loadSupportedLanguages() {
            try {
                // 从API或配置加载支持的语言
                const languages = await this.fetchSupportedLanguages();
                
                for (const lang of languages) {
                    this.supportedLanguages.set(lang.code, lang);
                }
                
                this.logger.log(`已加载 ${this.supportedLanguages.size} 种支持的语言`, 'info');
                
            } catch (error) {
                this.logger.logError('加载支持语言列表失败', error);
                // 使用默认语言列表
                this.loadDefaultLanguages();
            }
        }

        /**
         * 初始化处理器语言偏好
         */
        initializeProcessorLanguagePreferences() {
            // 设置各处理器的语言偏好
            this.processorLanguagePreferences.set('ChongDealer', ['zh-CN', 'en', 'ms']);
            this.processorLanguagePreferences.set('Fliggy', ['zh-CN', 'en']);
            this.processorLanguagePreferences.set('JRCoach', ['en', 'ms', 'zh-CN']);
            this.processorLanguagePreferences.set('KKday', ['zh-TW', 'en', 'ja', 'ko']);
            this.processorLanguagePreferences.set('Klook', ['en', 'zh-CN', 'zh-TW', 'ja', 'ko']);
            this.processorLanguagePreferences.set('Ctrip', ['zh-CN', 'en']);
            this.processorLanguagePreferences.set('Agoda', ['en', 'zh-CN', 'th', 'vi', 'ms', 'ja', 'ko']);
            this.processorLanguagePreferences.set('Booking', ['en', 'zh-CN', 'de', 'fr', 'es', 'it', 'pt', 'nl']);
            
            this.logger.log('处理器语言偏好初始化完成', 'info');
        }

        /**
         * 检测文本语言
         * @param {string} text - 要检测的文本
         * @param {Object} options - 检测选项
         * @returns {Promise<Object>} 语言检测结果
         */
        async detectLanguage(text, options = {}) {
            if (!text || typeof text !== 'string') {
                return { language: 'en', confidence: 0, error: '无效的输入文本' };
            }

            // 检查缓存
            const cacheKey = this.generateCacheKey(text, options);
            if (this.languageDetectionCache.has(cacheKey)) {
                return this.languageDetectionCache.get(cacheKey);
            }

            try {
                let detectionResult;

                // 使用现有语言管理器的检测功能
                if (this.languageManager && this.languageManager.detectLanguage) {
                    detectionResult = await this.languageManager.detectLanguage(text, options);
                } else {
                    // 使用基础语言检测
                    detectionResult = this.basicLanguageDetection(text);
                }

                // 标准化检测结果
                const standardizedResult = this.standardizeLanguageResult(detectionResult);

                // 缓存结果
                this.languageDetectionCache.set(cacheKey, standardizedResult);

                return standardizedResult;

            } catch (error) {
                this.logger.logError('语言检测失败', error);
                return { language: 'en', confidence: 0, error: error.message };
            }
        }

        /**
         * 获取处理器支持的语言
         * @param {string} processorName - 处理器名称
         * @returns {Array} 支持的语言列表
         */
        getProcessorSupportedLanguages(processorName) {
            const preferences = this.processorLanguagePreferences.get(processorName) || ['en'];
            
            return preferences.map(langCode => {
                const langInfo = this.languageCodeMapping[langCode];
                return langInfo ? {
                    code: langCode,
                    id: langInfo.id,
                    name: langInfo.name
                } : {
                    code: langCode,
                    id: 2, // 默认英语
                    name: langCode
                };
            });
        }

        /**
         * 获取最佳语言匹配
         * @param {string} detectedLanguage - 检测到的语言
         * @param {string} processorName - 处理器名称
         * @param {Object} context - 上下文信息
         * @returns {Object} 最佳语言匹配
         */
        getBestLanguageMatch(detectedLanguage, processorName, context = {}) {
            const supportedLanguages = this.getProcessorSupportedLanguages(processorName);
            
            // 优先匹配检测到的语言
            let bestMatch = supportedLanguages.find(lang => lang.code === detectedLanguage);
            
            if (!bestMatch) {
                // 尝试匹配语言族（如 zh-CN 匹配 zh）
                const languageFamily = detectedLanguage.split('-')[0];
                bestMatch = supportedLanguages.find(lang => lang.code.startsWith(languageFamily));
            }
            
            if (!bestMatch) {
                // 使用处理器的默认语言
                bestMatch = supportedLanguages[0];
            }
            
            // 考虑用户偏好
            if (context.userLanguagePreference) {
                const userPreferred = supportedLanguages.find(lang => 
                    lang.code === context.userLanguagePreference
                );
                if (userPreferred) {
                    bestMatch = userPreferred;
                }
            }
            
            return bestMatch;
        }

        /**
         * 本地化文本
         * @param {string} key - 本地化键
         * @param {string} language - 目标语言
         * @param {Object} params - 参数
         * @returns {string} 本地化后的文本
         */
        localize(key, language = 'en', params = {}) {
            try {
                if (this.i18nService && this.i18nService.t) {
                    return this.i18nService.t(key, { lng: language, ...params });
                }
                
                // 基础本地化
                return this.basicLocalization(key, language, params);
                
            } catch (error) {
                this.logger.logError('文本本地化失败', error);
                return key; // 返回原始键作为后备
            }
        }

        /**
         * 获取语言特定的格式化规则
         * @param {string} language - 语言代码
         * @returns {Object} 格式化规则
         */
        getLanguageFormattingRules(language) {
            const rules = {
                'zh-CN': {
                    dateFormat: 'YYYY年MM月DD日',
                    timeFormat: 'HH:mm',
                    numberFormat: '###,###.##',
                    currency: '¥',
                    phoneFormat: '+86 ### #### ####'
                },
                'zh-TW': {
                    dateFormat: 'YYYY年MM月DD日',
                    timeFormat: 'HH:mm',
                    numberFormat: '###,###.##',
                    currency: 'NT$',
                    phoneFormat: '+886 ### ### ###'
                },
                'en': {
                    dateFormat: 'DD/MM/YYYY',
                    timeFormat: 'HH:mm',
                    numberFormat: '###,###.##',
                    currency: '$',
                    phoneFormat: '+# ### ### ####'
                },
                'ms': {
                    dateFormat: 'DD/MM/YYYY',
                    timeFormat: 'HH:mm',
                    numberFormat: '###,###.##',
                    currency: 'RM',
                    phoneFormat: '+60 ## ### ####'
                }
            };
            
            return rules[language] || rules['en'];
        }

        /**
         * 创建基础语言管理器
         * @returns {Object} 基础语言管理器
         */
        createBasicLanguageManager() {
            return {
                detectLanguage: (text) => this.basicLanguageDetection(text),
                getSupportedLanguages: () => Array.from(this.supportedLanguages.values()),
                getCurrentLanguage: () => 'en',
                setLanguage: (language) => {
                    this.logger.log(`设置语言为: ${language}`, 'info');
                }
            };
        }

        /**
         * 创建基础国际化服务
         * @returns {Object} 基础国际化服务
         */
        createBasicI18nService() {
            return {
                t: (key, options = {}) => this.basicLocalization(key, options.lng || 'en', options),
                changeLanguage: (language) => {
                    this.logger.log(`切换语言为: ${language}`, 'info');
                },
                getLanguage: () => 'en'
            };
        }

        /**
         * 基础语言检测
         * @param {string} text - 文本
         * @returns {Object} 检测结果
         */
        basicLanguageDetection(text) {
            // 简单的语言检测逻辑
            const chinesePattern = /[\u4e00-\u9fff]/;
            const englishPattern = /[a-zA-Z]/;
            const malayPattern = /\b(dan|atau|dengan|untuk|dari|ke|di|yang|adalah|akan|telah|boleh|tidak|ada|ini|itu|saya|anda|kita|mereka)\b/i;
            
            let language = 'en';
            let confidence = 0.5;
            
            if (chinesePattern.test(text)) {
                language = 'zh-CN';
                confidence = 0.8;
                
                // 检测繁体中文特征
                const traditionalPattern = /[繁體台灣]/;
                if (traditionalPattern.test(text)) {
                    language = 'zh-TW';
                    confidence = 0.9;
                }
            } else if (malayPattern.test(text)) {
                language = 'ms';
                confidence = 0.7;
            } else if (englishPattern.test(text)) {
                language = 'en';
                confidence = 0.6;
            }
            
            return { language, confidence };
        }

        /**
         * 基础本地化
         * @param {string} key - 键
         * @param {string} language - 语言
         * @param {Object} params - 参数
         * @returns {string} 本地化文本
         */
        basicLocalization(key, language, params) {
            // 基础本地化字典
            const translations = {
                'en': {
                    'processing': 'Processing...',
                    'error': 'Error',
                    'success': 'Success',
                    'invalid_data': 'Invalid data'
                },
                'zh-CN': {
                    'processing': '处理中...',
                    'error': '错误',
                    'success': '成功',
                    'invalid_data': '无效数据'
                },
                'zh-TW': {
                    'processing': '處理中...',
                    'error': '錯誤',
                    'success': '成功',
                    'invalid_data': '無效數據'
                },
                'ms': {
                    'processing': 'Memproses...',
                    'error': 'Ralat',
                    'success': 'Berjaya',
                    'invalid_data': 'Data tidak sah'
                }
            };
            
            const langDict = translations[language] || translations['en'];
            let text = langDict[key] || key;
            
            // 简单参数替换
            if (params) {
                Object.keys(params).forEach(param => {
                    text = text.replace(`{{${param}}}`, params[param]);
                });
            }
            
            return text;
        }

        /**
         * 获取支持的语言列表
         * @returns {Promise<Array>} 语言列表
         */
        async fetchSupportedLanguages() {
            // 模拟从API获取语言列表
            return Object.values(this.languageCodeMapping);
        }

        /**
         * 加载默认语言
         */
        loadDefaultLanguages() {
            Object.values(this.languageCodeMapping).forEach(lang => {
                this.supportedLanguages.set(lang.code, lang);
            });
        }

        /**
         * 标准化语言检测结果
         * @param {Object} result - 原始结果
         * @returns {Object} 标准化结果
         */
        standardizeLanguageResult(result) {
            return {
                language: result.language || result.lang || 'en',
                confidence: result.confidence || result.score || 0.5,
                alternatives: result.alternatives || [],
                error: result.error || null
            };
        }

        /**
         * 生成缓存键
         * @param {string} text - 文本
         * @param {Object} options - 选项
         * @returns {string} 缓存键
         */
        generateCacheKey(text, options) {
            const textHash = this.simpleHash(text);
            const optionsHash = this.simpleHash(JSON.stringify(options));
            return `${textHash}_${optionsHash}`;
        }

        /**
         * 简单哈希函数
         * @param {string} str - 字符串
         * @returns {string} 哈希值
         */
        simpleHash(str) {
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // 转换为32位整数
            }
            return Math.abs(hash).toString(36);
        }

        /**
         * 清理缓存
         */
        clearCache() {
            this.languageDetectionCache.clear();
            this.logger.log('语言检测缓存已清理', 'info');
        }

        /**
         * 获取统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                supportedLanguagesCount: this.supportedLanguages.size,
                cacheSize: this.languageDetectionCache.size,
                processorLanguagePreferencesCount: this.processorLanguagePreferences.size,
                hasLanguageManager: !!this.languageManager,
                hasI18nService: !!this.i18nService
            };
        }
    }

    // 创建全局单例实例
    function getLanguageManagerIntegration() {
        if (!window.OTA.gemini.integrations.languageManagerIntegration) {
            window.OTA.gemini.integrations.languageManagerIntegration = new LanguageManagerIntegration();
        }
        return window.OTA.gemini.integrations.languageManagerIntegration;
    }

    // 暴露到全局命名空间
    window.OTA.gemini.integrations.LanguageManagerIntegration = LanguageManagerIntegration;
    window.OTA.gemini.integrations.getLanguageManagerIntegration = getLanguageManagerIntegration;

    // 向后兼容
    window.getLanguageManagerIntegration = getLanguageManagerIntegration;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('languageManagerIntegration', getLanguageManagerIntegration(), '@LANGUAGE_MANAGER_INTEGRATION');
        window.OTA.Registry.registerFactory('getLanguageManagerIntegration', getLanguageManagerIntegration, '@LANGUAGE_MANAGER_INTEGRATION_FACTORY');
    }

    console.log('✅ 语言管理器集成模块已加载');

})();
