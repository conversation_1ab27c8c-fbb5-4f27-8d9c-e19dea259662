/**
 * @TEST Gemini性能测试套件
 * 🏷️ 标签: @GEMINI_PERFORMANCE_TEST_SUITE
 * 📝 说明: 专门测试重构后Gemini系统的性能表现
 * 🎯 功能: 响应时间测试、吞吐量测试、内存使用测试、并发性能测试
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保测试环境
if (typeof window === 'undefined') {
    global.window = {};
}

// Gemini性能测试套件
describe('Gemini系统性能测试', function() {
    let geminiService;
    let performanceData;

    // 测试前置设置
    beforeAll(async function() {
        geminiService = window.OTA?.geminiService;
        
        if (!geminiService) {
            throw new Error('Gemini服务未初始化');
        }

        // 初始化性能数据收集
        performanceData = {
            responseTimes: [],
            memoryUsage: [],
            throughput: [],
            errorRates: []
        };

        // 预热系统
        await this.warmupSystem();
    });

    // 系统预热
    async warmupSystem() {
        const warmupOrder = '预热测试\n客户：预热用户\n电话：+60123456789';
        
        for (let i = 0; i < 3; i++) {
            await geminiService.parseOrder(warmupOrder);
        }
        
        console.log('Gemini系统预热完成');
    }

    // 响应时间测试
    describe('响应时间性能测试', function() {
        
        it('简单订单解析响应时间', async function() {
            const testOrder = `
                客户姓名：响应时间测试
                联系电话：+60123456789
                接送地点：KLIA
                目的地：双子塔
                航班信息：MH123 15:30
            `;

            const measurements = [];
            const testRuns = 5;

            for (let i = 0; i < testRuns; i++) {
                const startTime = performance.now();
                const result = await geminiService.parseOrder(testOrder);
                const endTime = performance.now();
                
                const duration = endTime - startTime;
                measurements.push(duration);
                
                assertTrue(result, `第${i + 1}次测试应该返回结果`);
            }

            const avgTime = measurements.reduce((a, b) => a + b, 0) / measurements.length;
            const maxTime = Math.max(...measurements);
            const minTime = Math.min(...measurements);

            performanceData.responseTimes.push({
                test: 'simple_order_parsing',
                average: avgTime,
                max: maxTime,
                min: minTime,
                measurements: measurements
            });

            console.log(`简单订单解析性能 - 平均: ${avgTime.toFixed(2)}ms, 最大: ${maxTime.toFixed(2)}ms, 最小: ${minTime.toFixed(2)}ms`);
            
            // 性能断言 - 重构后应该更快
            assertTrue(avgTime < 3000, `平均响应时间应该少于3秒，实际：${avgTime.toFixed(2)}ms`);
            assertTrue(maxTime < 5000, `最大响应时间应该少于5秒，实际：${maxTime.toFixed(2)}ms`);
        });

        it('多订单解析响应时间', async function() {
            const multipleOrders = [
                '订单1\n客户：用户1\n电话：+60123456789',
                '订单2\n客户：用户2\n电话：+60198765432',
                '订单3\n客户：用户3\n电话：+60187654321'
            ];

            const startTime = performance.now();
            const result = await geminiService.parseMultipleOrders(multipleOrders);
            const endTime = performance.now();

            const duration = endTime - startTime;

            performanceData.responseTimes.push({
                test: 'multiple_orders_parsing',
                duration: duration,
                orderCount: multipleOrders.length,
                avgPerOrder: duration / multipleOrders.length
            });

            console.log(`多订单解析性能 - 总时间: ${duration.toFixed(2)}ms, 平均每订单: ${(duration / multipleOrders.length).toFixed(2)}ms`);

            assertTrue(result, '多订单解析应该返回结果');
            assertTrue(duration < 10000, `多订单解析应该在10秒内完成，实际：${duration.toFixed(2)}ms`);
        });
    });

    // 吞吐量测试
    describe('吞吐量性能测试', function() {
        
        it('批量处理吞吐量测试', async function() {
            const batchSize = 10;
            const orders = [];
            
            // 生成测试订单
            for (let i = 1; i <= batchSize; i++) {
                orders.push(`
                    批量测试订单${i}
                    客户：测试用户${i}
                    电话：+6012345${String(i).padStart(4, '0')}
                    接机：KLIA${i % 2 === 0 ? '2' : ''}
                    目的地：酒店${i}
                `);
            }

            const startTime = performance.now();
            const results = await Promise.all(
                orders.map(order => geminiService.parseOrder(order))
            );
            const endTime = performance.now();

            const totalTime = endTime - startTime;
            const throughput = (batchSize / totalTime) * 1000; // 每秒处理数量
            const successCount = results.filter(r => r && r.success).length;

            performanceData.throughput.push({
                test: 'batch_processing_throughput',
                batchSize: batchSize,
                totalTime: totalTime,
                throughput: throughput,
                successCount: successCount,
                successRate: successCount / batchSize
            });

            console.log(`批量处理吞吐量 - 总时间: ${totalTime.toFixed(2)}ms, 吞吐量: ${throughput.toFixed(2)} 订单/秒, 成功率: ${((successCount / batchSize) * 100).toFixed(2)}%`);

            assertEqual(results.length, batchSize, '应该处理所有订单');
            assertTrue(throughput > 0.5, `吞吐量应该大于0.5订单/秒，实际：${throughput.toFixed(2)}`);
            assertTrue(successCount >= batchSize * 0.9, '成功率应该大于90%');
        });

        it('持续处理能力测试', async function() {
            const testDuration = 5000; // 5秒测试
            const requestInterval = 500; // 每500ms一个请求
            const testOrder = '持续测试\n客户：持续用户\n电话：+60123456789';
            
            const results = [];
            const startTime = performance.now();
            let requestCount = 0;

            const processRequest = async () => {
                requestCount++;
                try {
                    const result = await geminiService.parseOrder(testOrder);
                    results.push({ success: true, result, timestamp: performance.now() });
                } catch (error) {
                    results.push({ success: false, error, timestamp: performance.now() });
                }
            };

            // 启动持续请求
            const intervalId = setInterval(processRequest, requestInterval);

            // 等待测试完成
            await new Promise(resolve => setTimeout(resolve, testDuration));
            clearInterval(intervalId);

            // 等待所有请求完成
            await new Promise(resolve => setTimeout(resolve, 1000));

            const endTime = performance.now();
            const actualDuration = endTime - startTime;
            const successCount = results.filter(r => r.success).length;
            const errorRate = (results.length - successCount) / results.length;

            performanceData.throughput.push({
                test: 'sustained_processing',
                duration: actualDuration,
                requestCount: requestCount,
                completedCount: results.length,
                successCount: successCount,
                errorRate: errorRate,
                avgResponseTime: results.length > 0 ? 
                    results.reduce((sum, r) => sum + (r.timestamp - startTime), 0) / results.length : 0
            });

            console.log(`持续处理能力 - 请求数: ${requestCount}, 完成数: ${results.length}, 成功数: ${successCount}, 错误率: ${(errorRate * 100).toFixed(2)}%`);

            assertTrue(errorRate < 0.1, `错误率应该低于10%，实际：${(errorRate * 100).toFixed(2)}%`);
            assertTrue(successCount > 0, '应该有成功的请求');
        });
    });

    // 内存使用测试
    describe('内存使用性能测试', function() {
        
        it('内存使用效率测试', async function() {
            if (!performance.memory) {
                console.log('浏览器不支持内存监控，跳过测试');
                return;
            }

            const initialMemory = performance.memory.usedJSHeapSize;
            const testOrder = '内存测试\n客户：内存用户\n电话：+60123456789';
            
            const memorySnapshots = [initialMemory];

            // 执行多次解析并记录内存使用
            for (let i = 0; i < 20; i++) {
                await geminiService.parseOrder(testOrder);
                
                if (i % 5 === 0) {
                    memorySnapshots.push(performance.memory.usedJSHeapSize);
                }
            }

            // 强制垃圾回收（如果支持）
            if (window.gc) {
                window.gc();
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            const finalMemory = performance.memory.usedJSHeapSize;
            const memoryIncrease = finalMemory - initialMemory;
            const peakMemory = Math.max(...memorySnapshots);

            performanceData.memoryUsage.push({
                test: 'memory_efficiency',
                initialMemory: initialMemory,
                finalMemory: finalMemory,
                peakMemory: peakMemory,
                memoryIncrease: memoryIncrease,
                memoryIncreasePerOperation: memoryIncrease / 20,
                snapshots: memorySnapshots
            });

            console.log(`内存使用效率 - 初始: ${(initialMemory / 1024 / 1024).toFixed(2)}MB, 最终: ${(finalMemory / 1024 / 1024).toFixed(2)}MB, 峰值: ${(peakMemory / 1024 / 1024).toFixed(2)}MB, 增长: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);

            // 重构后内存使用应该更高效
            assertTrue(memoryIncrease < 30 * 1024 * 1024, `内存增长应该少于30MB，实际：${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
        });
    });

    // 并发性能测试
    describe('并发性能测试', function() {
        
        it('并发处理性能测试', async function() {
            const concurrency = 5;
            const requestsPerWorker = 3;
            const testOrder = '并发测试\n客户：并发用户\n电话：+60123456789';

            const workers = [];
            for (let i = 0; i < concurrency; i++) {
                const workerPromises = [];
                for (let j = 0; j < requestsPerWorker; j++) {
                    workerPromises.push(geminiService.parseOrder(testOrder));
                }
                workers.push(Promise.all(workerPromises));
            }

            const startTime = performance.now();
            const results = await Promise.all(workers);
            const endTime = performance.now();

            const totalTime = endTime - startTime;
            const totalRequests = concurrency * requestsPerWorker;
            const flatResults = results.flat();
            const successCount = flatResults.filter(r => r && r.success).length;

            performanceData.throughput.push({
                test: 'concurrent_processing',
                concurrency: concurrency,
                totalRequests: totalRequests,
                successCount: successCount,
                totalTime: totalTime,
                avgTimePerRequest: totalTime / totalRequests,
                concurrentThroughput: (totalRequests / totalTime) * 1000
            });

            console.log(`并发处理性能 - 并发数: ${concurrency}, 总请求: ${totalRequests}, 成功: ${successCount}, 总时间: ${totalTime.toFixed(2)}ms, 并发吞吐量: ${((totalRequests / totalTime) * 1000).toFixed(2)} 请求/秒`);

            assertEqual(results.length, concurrency, '应该完成所有并发工作');
            assertTrue(successCount >= totalRequests * 0.9, `成功率应该大于90%，实际：${(successCount / totalRequests * 100).toFixed(2)}%`);
            assertTrue(totalTime < 15000, `并发处理应该在15秒内完成，实际：${totalTime.toFixed(2)}ms`);
        });
    });

    // 性能基准测试
    describe('性能基准测试', function() {
        
        it('生成性能基准报告', function() {
            const report = {
                timestamp: new Date().toISOString(),
                testEnvironment: {
                    userAgent: navigator.userAgent,
                    platform: navigator.platform,
                    memorySupport: !!performance.memory
                },
                summary: {
                    totalTests: performanceData.responseTimes.length + 
                               performanceData.throughput.length + 
                               performanceData.memoryUsage.length
                },
                responseTimes: performanceData.responseTimes,
                throughput: performanceData.throughput,
                memoryUsage: performanceData.memoryUsage,
                benchmarks: {
                    avgSimpleOrderTime: performanceData.responseTimes
                        .find(t => t.test === 'simple_order_parsing')?.average || 0,
                    maxThroughput: Math.max(...performanceData.throughput
                        .map(t => t.throughput || t.concurrentThroughput || 0)),
                    memoryEfficiency: performanceData.memoryUsage.length > 0 ? 
                        performanceData.memoryUsage[0].memoryIncreasePerOperation : 0
                }
            };

            // 输出性能基准报告
            console.log('\n' + '='.repeat(60));
            console.log('🚀 Gemini系统性能基准报告');
            console.log('='.repeat(60));
            console.log(`测试时间: ${report.timestamp}`);
            console.log(`测试环境: ${report.testEnvironment.platform}`);
            console.log(`总测试数: ${report.summary.totalTests}`);
            console.log('\n性能基准:');
            console.log(`  - 简单订单平均解析时间: ${report.benchmarks.avgSimpleOrderTime.toFixed(2)}ms`);
            console.log(`  - 最大吞吐量: ${report.benchmarks.maxThroughput.toFixed(2)} 请求/秒`);
            if (report.benchmarks.memoryEfficiency > 0) {
                console.log(`  - 内存效率: ${(report.benchmarks.memoryEfficiency / 1024).toFixed(2)}KB/操作`);
            }
            console.log('='.repeat(60));

            assertTrue(report.summary.totalTests > 0, '应该有性能测试数据');
            assertTrue(report.benchmarks.avgSimpleOrderTime > 0, '应该有响应时间基准');
            
            // 将报告保存到全局变量供外部访问
            window.OTA = window.OTA || {};
            window.OTA.geminiPerformanceReport = report;
        });
    });
});

// 日志记录
const logger = window.getLogger?.() || console;
logger.log('Gemini性能测试套件加载完成', 'info', {
    version: '1.0.0',
    testCategories: [
        'response_time_performance',
        'throughput_performance',
        'memory_usage_performance', 
        'concurrent_performance',
        'performance_benchmarks'
    ]
});
