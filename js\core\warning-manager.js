/**
 * 智能警告管理器
 * 解决控制台日志污染问题，提供智能的警告频率控制和环境检测
 * 
 * 功能特性:
 * - 警告频率控制（每个警告类型最多显示3次）
 * - 环境检测（生产环境禁用警告）
 * - 警告分级机制（CRITICAL/WARNING/INFO）
 * - 批量警告汇总报告
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.core = window.OTA.core || {};

(function() {
    'use strict';

    /**
     * 智能警告管理器类
     */
    class WarningManager {
        constructor() {
            // 警告计数器
            this.warningCounts = new Map();
            
            // 警告配置
            this.config = {
                // 每个警告类型的最大显示次数
                maxWarningsPerType: 3,
                
                // 环境检测
                environment: this.detectEnvironment(),
                
                // 警告级别配置
                levels: {
                    CRITICAL: { color: '#ff4444', prefix: '🚨', enabled: true },
                    WARNING: { color: '#ffaa00', prefix: '⚠️', enabled: true },
                    INFO: { color: '#4488ff', prefix: 'ℹ️', enabled: true },
                    DEBUG: { color: '#888888', prefix: '🔧', enabled: false }
                },
                
                // 生产环境禁用所有警告
                disableInProduction: true,
                
                // 静默模式
                silentMode: false
            };
            
            // 警告统计
            this.stats = {
                totalWarnings: 0,
                suppressedWarnings: 0,
                warningsByLevel: new Map(),
                warningsByType: new Map(),
                startTime: Date.now()
            };
            
            // 批量警告缓存
            this.batchWarnings = [];
            this.batchTimer = null;
            this.batchInterval = 5000; // 5秒批量输出
            
            this.initialize();
        }

        /**
         * 初始化警告管理器
         */
        initialize() {
            // 检测环境并调整配置
            if (this.config.environment === 'production' && this.config.disableInProduction) {
                this.config.silentMode = true;
            }
            
            // 启动批量警告处理
            this.startBatchProcessing();
            
            console.log('✅ 智能警告管理器已初始化', {
                environment: this.config.environment,
                silentMode: this.config.silentMode,
                maxWarningsPerType: this.config.maxWarningsPerType
            });
        }

        /**
         * 检测运行环境
         * @returns {string} 环境类型
         */
        detectEnvironment() {
            if (typeof window !== 'undefined') {
                const hostname = window.location?.hostname;
                const protocol = window.location?.protocol;
                
                if (hostname === 'localhost' || 
                    hostname === '127.0.0.1' || 
                    protocol === 'file:') {
                    return 'development';
                }
                
                if (hostname && (hostname.includes('test') || hostname.includes('staging'))) {
                    return 'testing';
                }
            }
            
            return 'production';
        }

        /**
         * 发出智能警告
         * @param {string} type - 警告类型
         * @param {string} message - 警告消息
         * @param {string} level - 警告级别
         * @param {Object} data - 附加数据
         */
        warn(type, message, level = 'WARNING', data = {}) {
            // 静默模式检查
            if (this.config.silentMode) {
                this.stats.suppressedWarnings++;
                return;
            }
            
            // 级别检查
            if (!this.config.levels[level] || !this.config.levels[level].enabled) {
                return;
            }
            
            // 频率控制检查
            const warningKey = `${type}:${level}`;
            const currentCount = this.warningCounts.get(warningKey) || 0;
            
            if (currentCount >= this.config.maxWarningsPerType) {
                this.stats.suppressedWarnings++;
                return;
            }
            
            // 更新计数器
            this.warningCounts.set(warningKey, currentCount + 1);
            
            // 创建警告对象
            const warning = {
                type,
                message,
                level,
                data,
                timestamp: Date.now(),
                count: currentCount + 1,
                maxCount: this.config.maxWarningsPerType
            };
            
            // 更新统计
            this.updateStats(warning);
            
            // 输出警告
            this.outputWarning(warning);
        }

        /**
         * 输出警告到控制台
         * @param {Object} warning - 警告对象
         */
        outputWarning(warning) {
            const levelConfig = this.config.levels[warning.level];
            const prefix = levelConfig.prefix;
            const color = levelConfig.color;
            
            // 构建警告消息
            let fullMessage = `${prefix} [${warning.type}] ${warning.message}`;
            
            // 添加频率信息
            if (warning.count === this.config.maxWarningsPerType) {
                fullMessage += ` (最后一次警告，后续将被抑制)`;
            } else if (warning.count > 1) {
                fullMessage += ` (${warning.count}/${warning.maxCount})`;
            }
            
            // 输出到控制台
            if (warning.level === 'CRITICAL') {
                console.error(`%c${fullMessage}`, `color: ${color}; font-weight: bold;`, warning.data);
            } else if (warning.level === 'WARNING') {
                console.warn(`%c${fullMessage}`, `color: ${color};`, warning.data);
            } else {
                console.log(`%c${fullMessage}`, `color: ${color};`, warning.data);
            }
        }

        /**
         * 更新警告统计
         * @param {Object} warning - 警告对象
         */
        updateStats(warning) {
            this.stats.totalWarnings++;
            
            // 按级别统计
            const levelCount = this.stats.warningsByLevel.get(warning.level) || 0;
            this.stats.warningsByLevel.set(warning.level, levelCount + 1);
            
            // 按类型统计
            const typeCount = this.stats.warningsByType.get(warning.type) || 0;
            this.stats.warningsByType.set(warning.type, typeCount + 1);
        }

        /**
         * 启动批量警告处理
         */
        startBatchProcessing() {
            if (this.batchTimer) {
                clearInterval(this.batchTimer);
            }
            
            this.batchTimer = setInterval(() => {
                this.processBatchWarnings();
            }, this.batchInterval);
        }

        /**
         * 处理批量警告
         */
        processBatchWarnings() {
            if (this.batchWarnings.length === 0) {
                return;
            }
            
            // 生成批量报告
            const report = this.generateBatchReport();
            if (report) {
                console.groupCollapsed(`📊 警告汇总报告 (${this.batchWarnings.length}个警告)`);
                console.table(report);
                console.groupEnd();
            }
            
            // 清空批量缓存
            this.batchWarnings = [];
        }

        /**
         * 生成批量报告
         * @returns {Array} 报告数据
         */
        generateBatchReport() {
            if (this.batchWarnings.length === 0) {
                return null;
            }
            
            const reportMap = new Map();
            
            this.batchWarnings.forEach(warning => {
                const key = `${warning.type}:${warning.level}`;
                if (!reportMap.has(key)) {
                    reportMap.set(key, {
                        类型: warning.type,
                        级别: warning.level,
                        数量: 0,
                        最新消息: warning.message
                    });
                }
                reportMap.get(key).数量++;
                reportMap.get(key).最新消息 = warning.message;
            });
            
            return Array.from(reportMap.values());
        }

        /**
         * 设置静默模式
         * @param {boolean} silent - 是否启用静默模式
         */
        setSilentMode(silent) {
            this.config.silentMode = silent;
            console.log(`${silent ? '🔇' : '🔊'} 警告管理器静默模式: ${silent ? '已启用' : '已禁用'}`);
        }

        /**
         * 获取警告统计报告
         * @returns {Object} 统计报告
         */
        getStatsReport() {
            const runtime = Date.now() - this.stats.startTime;
            
            return {
                运行时间: `${Math.round(runtime / 1000)}秒`,
                总警告数: this.stats.totalWarnings,
                被抑制警告数: this.stats.suppressedWarnings,
                抑制率: `${Math.round((this.stats.suppressedWarnings / (this.stats.totalWarnings + this.stats.suppressedWarnings)) * 100)}%`,
                按级别统计: Object.fromEntries(this.stats.warningsByLevel),
                按类型统计: Object.fromEntries(this.stats.warningsByType),
                环境: this.config.environment,
                静默模式: this.config.silentMode
            };
        }

        /**
         * 重置警告计数器
         */
        resetCounters() {
            this.warningCounts.clear();
            this.stats = {
                totalWarnings: 0,
                suppressedWarnings: 0,
                warningsByLevel: new Map(),
                warningsByType: new Map(),
                startTime: Date.now()
            };
            console.log('🔄 警告计数器已重置');
        }

        /**
         * 销毁警告管理器
         */
        destroy() {
            if (this.batchTimer) {
                clearInterval(this.batchTimer);
                this.batchTimer = null;
            }
            
            // 处理剩余的批量警告
            this.processBatchWarnings();
            
            console.log('🗑️ 智能警告管理器已销毁');
        }
    }

    // 创建全局唯一的警告管理器实例
    const warningManager = new WarningManager();

    // 暴露到OTA命名空间
    window.OTA.core.warningManager = warningManager;

    // 提供便捷的全局函数
    window.OTA.warn = function(type, message, level, data) {
        return warningManager.warn(type, message, level, data);
    };

    // 向后兼容：暴露到全局（带废弃警告）
    window.getWarningManager = function() {
        warningManager.warn('DEPRECATED_API', 'window.getWarningManager() 已废弃，请使用 window.OTA.core.warningManager', 'WARNING');
        return warningManager;
    };

    console.log('✅ 智能警告管理器模块已加载');

})();
