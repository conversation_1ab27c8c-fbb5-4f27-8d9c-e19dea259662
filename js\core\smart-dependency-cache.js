/**
 * @CORE 智能依赖缓存系统
 * 🏷️ 标签: @SMART_DEPENDENCY_CACHE
 * 📝 说明: 高性能的依赖缓存系统，支持智能失效、预加载、内存管理和性能监控
 * 🎯 功能: 智能缓存、自动失效、内存优化、性能分析、缓存预热
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.core = window.OTA.core || {};

(function() {
    'use strict';

    /**
     * 智能依赖缓存类
     * 提供高性能的依赖缓存机制，支持智能失效和内存管理
     */
    class SmartDependencyCache {
        constructor(options = {}) {
            // 配置选项
            this.config = {
                maxSize: options.maxSize || 100,           // 最大缓存条目数
                maxMemoryMB: options.maxMemoryMB || 50,    // 最大内存使用量(MB)
                ttl: options.ttl || 30 * 60 * 1000,       // 默认TTL: 30分钟
                cleanupInterval: options.cleanupInterval || 5 * 60 * 1000, // 清理间隔: 5分钟
                enablePreload: options.enablePreload !== false, // 是否启用预加载
                enableCompression: options.enableCompression !== false // 是否启用压缩
            };

            // 缓存存储
            this.cache = new Map();
            this.metadata = new Map(); // 存储缓存元数据
            
            // 性能统计
            this.stats = {
                hits: 0,
                misses: 0,
                evictions: 0,
                compressionSaved: 0,
                totalRequests: 0,
                averageAccessTime: 0,
                memoryUsage: 0
            };

            // 访问频率统计（用于LFU算法）
            this.accessFrequency = new Map();
            this.accessTimes = new Map();

            // 预加载队列
            this.preloadQueue = new Set();
            this.preloadInProgress = new Set();

            // 内存监控
            this.memoryMonitor = {
                lastCheck: Date.now(),
                checkInterval: 30000, // 30秒检查一次
                warningThreshold: 0.8 // 80%内存使用率警告
            };

            // 启动定期清理
            this.startCleanupTimer();
            
            // 启动内存监控
            this.startMemoryMonitoring();

            this.logger = this.getLogger();
            this.logger.log('✅ 智能依赖缓存系统已初始化', 'info', this.config);
        }

        /**
         * 获取日志器
         */
        getLogger() {
            try {
                return window.OTA.getService('logger');
            } catch (error) {
                return console;
            }
        }

        /**
         * 设置缓存项
         * @param {string} key - 缓存键
         * @param {any} value - 缓存值
         * @param {Object} options - 选项
         */
        set(key, value, options = {}) {
            const startTime = Date.now();
            
            try {
                // 检查内存限制
                if (this.shouldEvictForMemory()) {
                    this.evictLeastUsed();
                }

                // 检查大小限制
                if (this.cache.size >= this.config.maxSize) {
                    this.evictLeastUsed();
                }

                // 计算值的大小
                const valueSize = this.calculateSize(value);
                
                // 压缩大对象
                let storedValue = value;
                let compressed = false;
                if (this.config.enableCompression && valueSize > 1024) { // 1KB以上压缩
                    const compressionResult = this.compressValue(value);
                    if (compressionResult.success) {
                        storedValue = compressionResult.compressed;
                        compressed = true;
                        this.stats.compressionSaved += valueSize - compressionResult.size;
                    }
                }

                // 存储缓存项
                this.cache.set(key, storedValue);
                
                // 存储元数据
                const metadata = {
                    createdAt: Date.now(),
                    lastAccessed: Date.now(),
                    accessCount: 0,
                    ttl: options.ttl || this.config.ttl,
                    size: valueSize,
                    compressed: compressed,
                    tags: options.tags || [],
                    priority: options.priority || 'normal'
                };
                
                this.metadata.set(key, metadata);
                this.accessFrequency.set(key, 0);
                this.accessTimes.set(key, []);

                // 更新内存使用统计
                this.updateMemoryUsage();

                // 触发预加载相关依赖
                if (this.config.enablePreload && options.preloadRelated) {
                    this.schedulePreload(options.preloadRelated);
                }

                this.logger.log(`✅ 缓存项已设置: ${key} (${valueSize} bytes, 压缩: ${compressed})`, 'debug');
                
            } catch (error) {
                this.logger.logError(`设置缓存项失败: ${key}`, error);
            }
        }

        /**
         * 获取缓存项
         * @param {string} key - 缓存键
         * @returns {any} 缓存值
         */
        get(key) {
            const startTime = Date.now();
            this.stats.totalRequests++;

            try {
                if (!this.cache.has(key)) {
                    this.stats.misses++;
                    return null;
                }

                const metadata = this.metadata.get(key);
                
                // 检查TTL
                if (this.isExpired(metadata)) {
                    this.delete(key);
                    this.stats.misses++;
                    return null;
                }

                // 获取值
                let value = this.cache.get(key);
                
                // 解压缩
                if (metadata.compressed) {
                    const decompressionResult = this.decompressValue(value);
                    if (decompressionResult.success) {
                        value = decompressionResult.decompressed;
                    } else {
                        this.logger.log(`⚠️ 解压缩失败: ${key}`, 'warn');
                        this.delete(key);
                        this.stats.misses++;
                        return null;
                    }
                }

                // 更新访问统计
                this.updateAccessStats(key, Date.now() - startTime);
                this.stats.hits++;

                return value;

            } catch (error) {
                this.logger.logError(`获取缓存项失败: ${key}`, error);
                this.stats.misses++;
                return null;
            }
        }

        /**
         * 检查缓存项是否存在且未过期
         * @param {string} key - 缓存键
         * @returns {boolean} 是否存在
         */
        has(key) {
            if (!this.cache.has(key)) {
                return false;
            }

            const metadata = this.metadata.get(key);
            if (this.isExpired(metadata)) {
                this.delete(key);
                return false;
            }

            return true;
        }

        /**
         * 删除缓存项
         * @param {string} key - 缓存键
         */
        delete(key) {
            if (this.cache.has(key)) {
                this.cache.delete(key);
                this.metadata.delete(key);
                this.accessFrequency.delete(key);
                this.accessTimes.delete(key);
                this.updateMemoryUsage();
                this.logger.log(`🗑️ 缓存项已删除: ${key}`, 'debug');
            }
        }

        /**
         * 按标签删除缓存项
         * @param {string} tag - 标签
         */
        deleteByTag(tag) {
            let deletedCount = 0;
            
            for (const [key, metadata] of this.metadata.entries()) {
                if (metadata.tags.includes(tag)) {
                    this.delete(key);
                    deletedCount++;
                }
            }

            this.logger.log(`🗑️ 按标签删除缓存项: ${tag} (${deletedCount}个)`, 'info');
            return deletedCount;
        }

        /**
         * 清空所有缓存
         */
        clear() {
            const size = this.cache.size;
            this.cache.clear();
            this.metadata.clear();
            this.accessFrequency.clear();
            this.accessTimes.clear();
            this.updateMemoryUsage();
            this.logger.log(`🗑️ 已清空所有缓存 (${size}个)`, 'info');
        }

        /**
         * 预加载依赖
         * @param {Array<string>} keys - 要预加载的键
         */
        async preload(keys) {
            if (!this.config.enablePreload) {
                return;
            }

            for (const key of keys) {
                if (!this.preloadInProgress.has(key) && !this.has(key)) {
                    this.preloadQueue.add(key);
                }
            }

            this.processPreloadQueue();
        }

        /**
         * 处理预加载队列
         */
        async processPreloadQueue() {
            const batchSize = 3; // 并发预加载数量
            const batch = Array.from(this.preloadQueue).slice(0, batchSize);
            
            for (const key of batch) {
                this.preloadQueue.delete(key);
                this.preloadInProgress.add(key);
                
                try {
                    // 这里应该调用实际的依赖获取逻辑
                    // 由于这是缓存层，具体的获取逻辑由上层提供
                    this.logger.log(`🔄 预加载依赖: ${key}`, 'debug');
                    
                    // 模拟异步加载
                    setTimeout(() => {
                        this.preloadInProgress.delete(key);
                    }, 100);
                    
                } catch (error) {
                    this.logger.logError(`预加载失败: ${key}`, error);
                    this.preloadInProgress.delete(key);
                }
            }
        }

        /**
         * 计算值的大小（字节）
         * @param {any} value - 值
         * @returns {number} 大小
         */
        calculateSize(value) {
            try {
                const jsonString = JSON.stringify(value);
                return new Blob([jsonString]).size;
            } catch (error) {
                // 对于无法序列化的对象，使用估算
                return this.estimateObjectSize(value);
            }
        }

        /**
         * 估算对象大小
         * @param {any} obj - 对象
         * @returns {number} 估算大小
         */
        estimateObjectSize(obj) {
            if (obj === null || obj === undefined) return 0;
            if (typeof obj === 'string') return obj.length * 2; // Unicode字符
            if (typeof obj === 'number') return 8;
            if (typeof obj === 'boolean') return 4;
            if (typeof obj === 'function') return obj.toString().length * 2;
            
            if (typeof obj === 'object') {
                let size = 0;
                for (const key in obj) {
                    if (obj.hasOwnProperty(key)) {
                        size += key.length * 2; // 键的大小
                        size += this.estimateObjectSize(obj[key]); // 值的大小
                    }
                }
                return size;
            }
            
            return 0;
        }

        /**
         * 压缩值
         * @param {any} value - 要压缩的值
         * @returns {Object} 压缩结果
         */
        compressValue(value) {
            try {
                const jsonString = JSON.stringify(value);
                // 简单的压缩：移除空白字符
                const compressed = jsonString.replace(/\s+/g, '');
                
                return {
                    success: true,
                    compressed: compressed,
                    size: new Blob([compressed]).size,
                    originalSize: new Blob([jsonString]).size
                };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        /**
         * 解压缩值
         * @param {string} compressed - 压缩的值
         * @returns {Object} 解压缩结果
         */
        decompressValue(compressed) {
            try {
                const decompressed = JSON.parse(compressed);
                return { success: true, decompressed };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        /**
         * 检查是否过期
         * @param {Object} metadata - 元数据
         * @returns {boolean} 是否过期
         */
        isExpired(metadata) {
            if (!metadata || !metadata.ttl) return false;
            return Date.now() - metadata.createdAt > metadata.ttl;
        }

        /**
         * 更新访问统计
         * @param {string} key - 缓存键
         * @param {number} accessTime - 访问时间
         */
        updateAccessStats(key, accessTime) {
            const metadata = this.metadata.get(key);
            if (metadata) {
                metadata.lastAccessed = Date.now();
                metadata.accessCount++;
            }

            // 更新访问频率
            const frequency = this.accessFrequency.get(key) || 0;
            this.accessFrequency.set(key, frequency + 1);

            // 记录访问时间（保留最近10次）
            const times = this.accessTimes.get(key) || [];
            times.push(accessTime);
            if (times.length > 10) {
                times.shift();
            }
            this.accessTimes.set(key, times);

            // 更新平均访问时间
            this.updateAverageAccessTime();
        }

        /**
         * 更新平均访问时间
         */
        updateAverageAccessTime() {
            let totalTime = 0;
            let totalCount = 0;

            for (const times of this.accessTimes.values()) {
                totalTime += times.reduce((sum, time) => sum + time, 0);
                totalCount += times.length;
            }

            this.stats.averageAccessTime = totalCount > 0 ? totalTime / totalCount : 0;
        }

        /**
         * 检查是否需要为内存而驱逐
         * @returns {boolean} 是否需要驱逐
         */
        shouldEvictForMemory() {
            const currentMemoryMB = this.stats.memoryUsage / (1024 * 1024);
            return currentMemoryMB > this.config.maxMemoryMB * this.memoryMonitor.warningThreshold;
        }

        /**
         * 驱逐最少使用的项目（LFU算法）
         */
        evictLeastUsed() {
            if (this.cache.size === 0) return;

            // 找到访问频率最低的项目
            let leastUsedKey = null;
            let minFrequency = Infinity;
            let oldestTime = Infinity;

            for (const [key, frequency] of this.accessFrequency.entries()) {
                const metadata = this.metadata.get(key);
                
                // 优先驱逐频率最低的，如果频率相同则驱逐最旧的
                if (frequency < minFrequency || 
                    (frequency === minFrequency && metadata.lastAccessed < oldestTime)) {
                    minFrequency = frequency;
                    oldestTime = metadata.lastAccessed;
                    leastUsedKey = key;
                }
            }

            if (leastUsedKey) {
                this.delete(leastUsedKey);
                this.stats.evictions++;
                this.logger.log(`🗑️ 驱逐最少使用的缓存项: ${leastUsedKey}`, 'debug');
            }
        }

        /**
         * 更新内存使用统计
         */
        updateMemoryUsage() {
            let totalSize = 0;
            for (const metadata of this.metadata.values()) {
                totalSize += metadata.size;
            }
            this.stats.memoryUsage = totalSize;
        }

        /**
         * 启动清理定时器
         */
        startCleanupTimer() {
            setInterval(() => {
                this.cleanup();
            }, this.config.cleanupInterval);
        }

        /**
         * 启动内存监控
         */
        startMemoryMonitoring() {
            setInterval(() => {
                this.monitorMemory();
            }, this.memoryMonitor.checkInterval);
        }

        /**
         * 清理过期项目
         */
        cleanup() {
            let cleanedCount = 0;
            const now = Date.now();

            for (const [key, metadata] of this.metadata.entries()) {
                if (this.isExpired(metadata)) {
                    this.delete(key);
                    cleanedCount++;
                }
            }

            if (cleanedCount > 0) {
                this.logger.log(`🧹 清理过期缓存项: ${cleanedCount}个`, 'debug');
            }
        }

        /**
         * 监控内存使用
         */
        monitorMemory() {
            this.updateMemoryUsage();
            const memoryMB = this.stats.memoryUsage / (1024 * 1024);
            
            if (memoryMB > this.config.maxMemoryMB * this.memoryMonitor.warningThreshold) {
                this.logger.log(`⚠️ 缓存内存使用过高: ${memoryMB.toFixed(2)}MB / ${this.config.maxMemoryMB}MB`, 'warn');
                
                // 主动清理
                while (this.shouldEvictForMemory() && this.cache.size > 0) {
                    this.evictLeastUsed();
                }
            }
        }

        /**
         * 调度预加载
         * @param {Array<string>} keys - 要预加载的键
         */
        schedulePreload(keys) {
            setTimeout(() => {
                this.preload(keys);
            }, 100); // 延迟100ms执行预加载
        }

        /**
         * 获取缓存统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            const hitRate = this.stats.totalRequests > 0 ? 
                (this.stats.hits / this.stats.totalRequests * 100).toFixed(2) + '%' : '0%';
            
            return {
                ...this.stats,
                hitRate,
                size: this.cache.size,
                memoryUsageMB: (this.stats.memoryUsage / (1024 * 1024)).toFixed(2) + 'MB',
                preloadQueueSize: this.preloadQueue.size,
                preloadInProgress: this.preloadInProgress.size
            };
        }

        /**
         * 重置统计信息
         */
        resetStats() {
            this.stats = {
                hits: 0,
                misses: 0,
                evictions: 0,
                compressionSaved: 0,
                totalRequests: 0,
                averageAccessTime: 0,
                memoryUsage: 0
            };
            
            this.logger.log('📊 缓存统计信息已重置', 'info');
        }
    }

    // 暴露到OTA命名空间
    window.OTA.core.SmartDependencyCache = SmartDependencyCache;

    console.log('✅ 智能依赖缓存系统已加载');

})();
