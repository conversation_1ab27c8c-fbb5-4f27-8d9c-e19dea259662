/**
 * 循环依赖解决器
 * 专门处理UIManager↔EventManager等循环依赖问题
 * 
 * 功能特性:
 * - 延迟初始化管理
 * - 代理模式实现
 * - 依赖注入协调
 * - 循环检测和警告
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.core = window.OTA.core || {};

(function() {
    'use strict';

    /**
     * 循环依赖解决器类
     */
    class CircularDependencyResolver {
        constructor() {
            // 依赖关系图
            this.dependencyGraph = new Map();
            
            // 延迟初始化队列
            this.lazyInitQueue = new Map();
            
            // 代理对象缓存
            this.proxyCache = new Map();
            
            // 初始化状态跟踪
            this.initializationState = new Map();
            
            // 配置
            this.config = {
                // 启用循环检测
                enableCircularDetection: true,
                
                // 最大初始化重试次数
                maxRetries: 3,
                
                // 初始化超时时间（毫秒）
                initTimeout: 5000,
                
                // 启用代理模式
                enableProxyMode: true
            };
            
            this.warningManager = null;
            this.initialize();
        }

        /**
         * 初始化解决器
         */
        initialize() {
            // 延迟获取警告管理器
            setTimeout(() => {
                this.warningManager = window.OTA?.core?.warningManager;
            }, 100);
            
            // 注册已知的循环依赖关系
            this.registerKnownCircularDependencies();
            
            console.log('✅ 循环依赖解决器已初始化');
        }

        /**
         * 注册已知的循环依赖关系
         */
        registerKnownCircularDependencies() {
            // UIManager ↔ EventManager
            this.registerCircularDependency('uiManager', 'eventManager');
            
            // MultiOrderManager ↔ UIManager
            this.registerCircularDependency('multiOrderManager', 'uiManager');
            
            // EventManager ↔ MultiOrderManager (通过UIManager)
            this.registerCircularDependency('eventManager', 'multiOrderManager');
        }

        /**
         * 注册循环依赖关系
         * @param {string} serviceA - 服务A名称
         * @param {string} serviceB - 服务B名称
         */
        registerCircularDependency(serviceA, serviceB) {
            if (!this.dependencyGraph.has(serviceA)) {
                this.dependencyGraph.set(serviceA, new Set());
            }
            if (!this.dependencyGraph.has(serviceB)) {
                this.dependencyGraph.set(serviceB, new Set());
            }
            
            this.dependencyGraph.get(serviceA).add(serviceB);
            this.dependencyGraph.get(serviceB).add(serviceA);
        }

        /**
         * 创建延迟初始化的服务代理
         * @param {string} serviceName - 服务名称
         * @param {Function} factory - 服务工厂函数
         * @returns {Proxy} 服务代理对象
         */
        createLazyProxy(serviceName, factory) {
            if (this.proxyCache.has(serviceName)) {
                return this.proxyCache.get(serviceName);
            }

            let realService = null;
            let isInitializing = false;

            const proxy = new Proxy({}, {
                get: (target, prop) => {
                    // 如果服务还未初始化，尝试初始化
                    if (!realService && !isInitializing) {
                        isInitializing = true;
                        try {
                            realService = this.initializeService(serviceName, factory);
                        } catch (error) {
                            if (this.warningManager) {
                                this.warningManager.warn(
                                    'LAZY_INIT_FAILED',
                                    `延迟初始化服务 ${serviceName} 失败: ${error.message}`,
                                    'WARNING',
                                    { serviceName, error: error.message }
                                );
                            }
                            // 返回一个空函数或默认值，避免崩溃
                            return typeof prop === 'string' && prop.startsWith('on') ? () => {} : undefined;
                        } finally {
                            isInitializing = false;
                        }
                    }

                    // 如果服务已初始化，返回真实属性
                    if (realService) {
                        const value = realService[prop];
                        return typeof value === 'function' ? value.bind(realService) : value;
                    }

                    // 服务未初始化时的降级处理
                    if (typeof prop === 'string') {
                        if (prop.startsWith('on') || prop.includes('Event')) {
                            // 事件相关方法返回空函数
                            return () => {};
                        }
                        if (prop === 'init' || prop === 'initialize') {
                            // 初始化方法
                            return () => {
                                if (!realService) {
                                    realService = this.initializeService(serviceName, factory);
                                }
                            };
                        }
                    }

                    return undefined;
                },

                set: (target, prop, value) => {
                    if (realService) {
                        realService[prop] = value;
                        return true;
                    }
                    // 如果服务未初始化，暂存属性
                    target[prop] = value;
                    return true;
                },

                has: (target, prop) => {
                    if (realService) {
                        return prop in realService;
                    }
                    return prop in target;
                }
            });

            this.proxyCache.set(serviceName, proxy);
            return proxy;
        }

        /**
         * 初始化服务
         * @param {string} serviceName - 服务名称
         * @param {Function} factory - 服务工厂函数
         * @returns {any} 服务实例
         */
        initializeService(serviceName, factory) {
            // 检查是否正在初始化（防止无限递归）
            if (this.initializationState.get(serviceName) === 'initializing') {
                if (this.warningManager) {
                    this.warningManager.warn(
                        'CIRCULAR_INIT_DETECTED',
                        `检测到服务 ${serviceName} 的循环初始化`,
                        'WARNING',
                        { serviceName }
                    );
                }
                return null;
            }

            this.initializationState.set(serviceName, 'initializing');

            try {
                const service = factory();
                this.initializationState.set(serviceName, 'initialized');
                return service;
            } catch (error) {
                this.initializationState.set(serviceName, 'failed');
                throw error;
            }
        }

        /**
         * 解决UIManager和EventManager的循环依赖
         * @returns {Object} 包含两个管理器的对象
         */
        resolveUIManagerEventManagerCircular() {
            const elements = this.cacheElements();
            
            // 创建事件总线用于通信
            const eventBus = this.createEventBus();
            
            // 先创建UIManager（不初始化EventManager）
            const uiManager = new window.OTA.managers.UIManager();
            uiManager.elements = elements;
            uiManager.eventBus = eventBus;
            
            // 创建EventManager（使用事件总线而不是直接引用UIManager）
            const eventManager = new window.OTA.managers.EventManager(elements);
            eventManager.eventBus = eventBus;
            
            // 建立双向引用
            uiManager.managers = uiManager.managers || {};
            uiManager.managers.event = eventManager;
            eventManager._uiManager = uiManager;
            
            // 设置事件总线监听器
            this.setupEventBusListeners(eventBus, uiManager, eventManager);
            
            return { uiManager, eventManager };
        }

        /**
         * 缓存DOM元素
         * @returns {Object} 元素缓存对象
         */
        cacheElements() {
            return {
                // 登录相关
                loginPanel: document.getElementById('loginPanel'),
                workspace: document.getElementById('workspace'),
                loginBtn: document.getElementById('loginBtn'),
                logoutBtn: document.getElementById('logoutBtn'),
                
                // 表单元素
                orderInput: document.getElementById('orderInput'),
                parseBtn: document.getElementById('parseBtn'),
                createOrderBtn: document.getElementById('createOrderBtn'),
                
                // 其他关键元素
                otaChannel: document.getElementById('ota'),
                priceDisplay: document.getElementById('priceDisplay')
            };
        }

        /**
         * 创建事件总线
         * @returns {Object} 事件总线对象
         */
        createEventBus() {
            const listeners = new Map();
            
            return {
                on: (event, callback) => {
                    if (!listeners.has(event)) {
                        listeners.set(event, []);
                    }
                    listeners.get(event).push(callback);
                },
                
                emit: (event, data) => {
                    if (listeners.has(event)) {
                        listeners.get(event).forEach(callback => {
                            try {
                                callback(data);
                            } catch (error) {
                                console.error(`Event bus callback error for ${event}:`, error);
                            }
                        });
                    }
                },
                
                off: (event, callback) => {
                    if (listeners.has(event)) {
                        const eventListeners = listeners.get(event);
                        const index = eventListeners.indexOf(callback);
                        if (index > -1) {
                            eventListeners.splice(index, 1);
                        }
                    }
                }
            };
        }

        /**
         * 设置事件总线监听器
         * @param {Object} eventBus - 事件总线
         * @param {Object} uiManager - UI管理器
         * @param {Object} eventManager - 事件管理器
         */
        setupEventBusListeners(eventBus, uiManager, eventManager) {
            // UIManager监听EventManager的事件
            eventBus.on('ui:update', (data) => {
                if (uiManager.updateUI) {
                    uiManager.updateUI(data);
                }
            });
            
            eventBus.on('ui:showModal', (data) => {
                if (uiManager.showModal) {
                    uiManager.showModal(data);
                }
            });
            
            // EventManager监听UIManager的事件
            eventBus.on('event:bind', (data) => {
                if (eventManager.bindEvents) {
                    eventManager.bindEvents(data);
                }
            });
            
            eventBus.on('event:trigger', (data) => {
                if (eventManager.handleEvent) {
                    eventManager.handleEvent(data);
                }
            });
        }

        /**
         * 检测循环依赖
         * @param {string} serviceName - 起始服务名称
         * @returns {Array} 循环路径
         */
        detectCircularDependency(serviceName) {
            const visited = new Set();
            const recursionStack = new Set();
            
            const dfs = (current, path) => {
                if (recursionStack.has(current)) {
                    // 找到循环
                    const cycleStart = path.indexOf(current);
                    return path.slice(cycleStart).concat([current]);
                }
                
                if (visited.has(current)) {
                    return null;
                }
                
                visited.add(current);
                recursionStack.add(current);
                path.push(current);
                
                const dependencies = this.dependencyGraph.get(current) || new Set();
                for (const dep of dependencies) {
                    const cycle = dfs(dep, [...path]);
                    if (cycle) {
                        return cycle;
                    }
                }
                
                recursionStack.delete(current);
                return null;
            };
            
            return dfs(serviceName, []);
        }

        /**
         * 获取解决器统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                已注册循环依赖数: this.dependencyGraph.size,
                代理缓存数: this.proxyCache.size,
                延迟初始化队列数: this.lazyInitQueue.size,
                初始化状态: Object.fromEntries(this.initializationState)
            };
        }
    }

    // 创建全局唯一的循环依赖解决器实例
    const circularDependencyResolver = new CircularDependencyResolver();

    // 暴露到OTA命名空间
    window.OTA.core.circularDependencyResolver = circularDependencyResolver;

    // 提供便捷的解决接口
    window.OTA.resolveCircularDependency = function(serviceA, serviceB) {
        return circularDependencyResolver.registerCircularDependency(serviceA, serviceB);
    };

    console.log('✅ 循环依赖解决器模块已加载');

})();
