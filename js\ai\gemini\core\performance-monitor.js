/**
 * @PERFORMANCE_MONITOR 性能监控系统
 * 🏷️ 标签: @PERFORMANCE @MONITORING @METRICS
 * 📝 说明: 监控整个系统的性能指标和资源使用情况
 * 🎯 目标: 提供实时性能监控、性能分析和优化建议
 */

(function() {
    'use strict';
    
    // 确保OTA命名空间存在
    window.OTA = window.OTA || {};
    window.OTA.gemini = window.OTA.gemini || {};
    window.OTA.gemini.core = window.OTA.gemini.core || {};
    
    /**
     * 性能监控器类
     * 监控系统性能指标和资源使用情况
     */
    class PerformanceMonitor {
        constructor() {
            this.logger = window.getLogger?.() || console;
            
            // 性能指标存储
            this.metrics = {
                // 响应时间指标
                responseTimes: new Map(),
                
                // 内存使用指标
                memoryUsage: [],
                
                // API调用指标
                apiCalls: new Map(),
                
                // 错误率指标
                errorRates: new Map(),
                
                // 组件性能指标
                componentPerformance: new Map(),
                
                // 用户交互指标
                userInteractions: [],
                
                // 系统资源指标
                systemResources: []
            };
            
            // 性能阈值配置
            this.thresholds = {
                responseTime: {
                    excellent: 100,    // 100ms以下为优秀
                    good: 500,         // 500ms以下为良好
                    acceptable: 1000,  // 1s以下为可接受
                    poor: 3000         // 3s以上为差
                },
                
                memoryUsage: {
                    low: 50,           // 50MB以下为低
                    medium: 100,       // 100MB以下为中等
                    high: 200,         // 200MB以下为高
                    critical: 500      // 500MB以上为临界
                },
                
                errorRate: {
                    excellent: 0.01,   // 1%以下为优秀
                    good: 0.05,        // 5%以下为良好
                    acceptable: 0.1,   // 10%以下为可接受
                    poor: 0.2          // 20%以上为差
                },
                
                cpuUsage: {
                    low: 30,           // 30%以下为低
                    medium: 60,        // 60%以下为中等
                    high: 80,          // 80%以下为高
                    critical: 95       // 95%以上为临界
                }
            };
            
            // 监控状态
            this.monitoringState = {
                isActive: false,
                startTime: null,
                lastReportTime: null,
                reportInterval: 60000,  // 1分钟报告间隔
                sampleInterval: 5000,   // 5秒采样间隔
                maxSamples: 1000        // 最大样本数
            };
            
            // 性能观察器
            this.observers = {
                performanceObserver: null,
                mutationObserver: null,
                intersectionObserver: null
            };
            
            // 自动初始化
            this.initialize();
            
            this.logger.log('📊 性能监控系统初始化完成', 'info');
        }
        
        /**
         * 初始化性能监控
         */
        initialize() {
            try {
                // 初始化Performance Observer
                this.initializePerformanceObserver();
                
                // 初始化内存监控
                this.initializeMemoryMonitoring();
                
                // 初始化错误监控
                this.initializeErrorMonitoring();
                
                // 设置定期采样
                this.startPeriodicSampling();
                
            } catch (error) {
                this.logger.logError('性能监控初始化失败', error);
            }
        }
        
        /**
         * 初始化Performance Observer
         * @private
         */
        initializePerformanceObserver() {
            if (typeof PerformanceObserver !== 'undefined') {
                try {
                    this.observers.performanceObserver = new PerformanceObserver((list) => {
                        const entries = list.getEntries();
                        entries.forEach(entry => {
                            this.recordPerformanceEntry(entry);
                        });
                    });
                    
                    // 观察不同类型的性能条目
                    this.observers.performanceObserver.observe({
                        entryTypes: ['measure', 'navigation', 'resource', 'paint']
                    });
                    
                } catch (error) {
                    this.logger.logWarning('Performance Observer初始化失败', error);
                }
            }
        }
        
        /**
         * 初始化内存监控
         * @private
         */
        initializeMemoryMonitoring() {
            // 检查是否支持内存API
            if (performance.memory) {
                this.memoryMonitoringSupported = true;
            } else {
                this.logger.logWarning('浏览器不支持内存监控API');
                this.memoryMonitoringSupported = false;
            }
        }
        
        /**
         * 初始化错误监控
         * @private
         */
        initializeErrorMonitoring() {
            // 监听全局错误
            window.addEventListener('error', (event) => {
                this.recordError('javascript_error', {
                    message: event.message,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    stack: event.error?.stack
                });
            });
            
            // 监听Promise拒绝
            window.addEventListener('unhandledrejection', (event) => {
                this.recordError('promise_rejection', {
                    reason: event.reason,
                    promise: event.promise
                });
            });
        }
        
        /**
         * 开始定期采样
         * @private
         */
        startPeriodicSampling() {
            setInterval(() => {
                if (this.monitoringState.isActive) {
                    this.collectSystemMetrics();
                }
            }, this.monitoringState.sampleInterval);
            
            // 定期生成报告
            setInterval(() => {
                if (this.monitoringState.isActive) {
                    this.generatePerformanceReport();
                }
            }, this.monitoringState.reportInterval);
        }
        
        /**
         * 开始监控
         */
        startMonitoring() {
            this.monitoringState.isActive = true;
            this.monitoringState.startTime = Date.now();
            this.logger.log('🚀 性能监控已启动', 'info');
        }
        
        /**
         * 停止监控
         */
        stopMonitoring() {
            this.monitoringState.isActive = false;
            this.logger.log('⏹️ 性能监控已停止', 'info');
        }
        
        /**
         * 记录操作开始时间
         * @param {string} operationName - 操作名称
         * @param {Object} context - 上下文信息
         * @returns {string} 操作ID
         */
        startOperation(operationName, context = {}) {
            const operationId = `${operationName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const startTime = performance.now();
            
            if (!this.metrics.responseTimes.has(operationName)) {
                this.metrics.responseTimes.set(operationName, []);
            }
            
            // 存储操作开始信息
            this.activeOperations = this.activeOperations || new Map();
            this.activeOperations.set(operationId, {
                name: operationName,
                startTime,
                context
            });
            
            return operationId;
        }
        
        /**
         * 记录操作结束时间
         * @param {string} operationId - 操作ID
         * @param {Object} result - 操作结果
         */
        endOperation(operationId, result = {}) {
            if (!this.activeOperations || !this.activeOperations.has(operationId)) {
                this.logger.logWarning(`未找到操作: ${operationId}`);
                return;
            }
            
            const operation = this.activeOperations.get(operationId);
            const endTime = performance.now();
            const duration = endTime - operation.startTime;
            
            // 记录响应时间
            const responseTimes = this.metrics.responseTimes.get(operation.name);
            responseTimes.push({
                duration,
                timestamp: Date.now(),
                context: operation.context,
                result
            });
            
            // 保持数组大小在合理范围内
            if (responseTimes.length > this.monitoringState.maxSamples) {
                responseTimes.splice(0, responseTimes.length - this.monitoringState.maxSamples);
            }
            
            // 清理活跃操作
            this.activeOperations.delete(operationId);
            
            // 检查性能阈值
            this.checkPerformanceThresholds(operation.name, duration);
        }
        
        /**
         * 记录API调用
         * @param {string} apiName - API名称
         * @param {number} duration - 调用时长
         * @param {boolean} success - 是否成功
         * @param {Object} details - 详细信息
         */
        recordAPICall(apiName, duration, success, details = {}) {
            if (!this.metrics.apiCalls.has(apiName)) {
                this.metrics.apiCalls.set(apiName, {
                    totalCalls: 0,
                    successfulCalls: 0,
                    failedCalls: 0,
                    totalDuration: 0,
                    averageDuration: 0,
                    recentCalls: []
                });
            }
            
            const apiMetrics = this.metrics.apiCalls.get(apiName);
            apiMetrics.totalCalls++;
            apiMetrics.totalDuration += duration;
            apiMetrics.averageDuration = apiMetrics.totalDuration / apiMetrics.totalCalls;
            
            if (success) {
                apiMetrics.successfulCalls++;
            } else {
                apiMetrics.failedCalls++;
            }
            
            // 记录最近的调用
            apiMetrics.recentCalls.push({
                timestamp: Date.now(),
                duration,
                success,
                details
            });
            
            // 保持最近调用记录在合理范围内
            if (apiMetrics.recentCalls.length > 100) {
                apiMetrics.recentCalls.splice(0, apiMetrics.recentCalls.length - 100);
            }
        }
        
        /**
         * 记录错误
         * @param {string} errorType - 错误类型
         * @param {Object} errorDetails - 错误详情
         */
        recordError(errorType, errorDetails) {
            if (!this.metrics.errorRates.has(errorType)) {
                this.metrics.errorRates.set(errorType, {
                    count: 0,
                    recentErrors: []
                });
            }
            
            const errorMetrics = this.metrics.errorRates.get(errorType);
            errorMetrics.count++;
            errorMetrics.recentErrors.push({
                timestamp: Date.now(),
                details: errorDetails
            });
            
            // 保持错误记录在合理范围内
            if (errorMetrics.recentErrors.length > 50) {
                errorMetrics.recentErrors.splice(0, errorMetrics.recentErrors.length - 50);
            }
            
            this.logger.logError(`性能监控记录错误: ${errorType}`, errorDetails);
        }
        
        /**
         * 记录组件性能
         * @param {string} componentName - 组件名称
         * @param {string} operation - 操作类型
         * @param {number} duration - 执行时长
         * @param {Object} metadata - 元数据
         */
        recordComponentPerformance(componentName, operation, duration, metadata = {}) {
            const key = `${componentName}.${operation}`;
            
            if (!this.metrics.componentPerformance.has(key)) {
                this.metrics.componentPerformance.set(key, {
                    totalExecutions: 0,
                    totalDuration: 0,
                    averageDuration: 0,
                    minDuration: Infinity,
                    maxDuration: 0,
                    recentExecutions: []
                });
            }
            
            const componentMetrics = this.metrics.componentPerformance.get(key);
            componentMetrics.totalExecutions++;
            componentMetrics.totalDuration += duration;
            componentMetrics.averageDuration = componentMetrics.totalDuration / componentMetrics.totalExecutions;
            componentMetrics.minDuration = Math.min(componentMetrics.minDuration, duration);
            componentMetrics.maxDuration = Math.max(componentMetrics.maxDuration, duration);
            
            componentMetrics.recentExecutions.push({
                timestamp: Date.now(),
                duration,
                metadata
            });
            
            // 保持最近执行记录在合理范围内
            if (componentMetrics.recentExecutions.length > 100) {
                componentMetrics.recentExecutions.splice(0, componentMetrics.recentExecutions.length - 100);
            }
        }
        
        /**
         * 收集系统指标
         * @private
         */
        collectSystemMetrics() {
            const metrics = {
                timestamp: Date.now(),
                memory: this.collectMemoryMetrics(),
                timing: this.collectTimingMetrics(),
                resources: this.collectResourceMetrics()
            };
            
            this.metrics.systemResources.push(metrics);
            
            // 保持系统资源记录在合理范围内
            if (this.metrics.systemResources.length > this.monitoringState.maxSamples) {
                this.metrics.systemResources.splice(0, this.metrics.systemResources.length - this.monitoringState.maxSamples);
            }
        }
        
        /**
         * 收集内存指标
         * @returns {Object} 内存指标
         * @private
         */
        collectMemoryMetrics() {
            if (!this.memoryMonitoringSupported) {
                return null;
            }
            
            const memory = performance.memory;
            return {
                usedJSHeapSize: memory.usedJSHeapSize,
                totalJSHeapSize: memory.totalJSHeapSize,
                jsHeapSizeLimit: memory.jsHeapSizeLimit,
                usagePercentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
            };
        }
        
        /**
         * 收集时间指标
         * @returns {Object} 时间指标
         * @private
         */
        collectTimingMetrics() {
            const navigation = performance.getEntriesByType('navigation')[0];
            if (!navigation) return null;
            
            return {
                domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                domInteractive: navigation.domInteractive - navigation.navigationStart,
                firstPaint: this.getFirstPaintTime(),
                firstContentfulPaint: this.getFirstContentfulPaintTime()
            };
        }
        
        /**
         * 收集资源指标
         * @returns {Object} 资源指标
         * @private
         */
        collectResourceMetrics() {
            const resources = performance.getEntriesByType('resource');
            
            return {
                totalResources: resources.length,
                totalTransferSize: resources.reduce((sum, resource) => sum + (resource.transferSize || 0), 0),
                averageLoadTime: resources.length > 0 ? 
                    resources.reduce((sum, resource) => sum + resource.duration, 0) / resources.length : 0
            };
        }
        
        /**
         * 获取首次绘制时间
         * @returns {number} 首次绘制时间
         * @private
         */
        getFirstPaintTime() {
            const paintEntries = performance.getEntriesByType('paint');
            const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
            return firstPaint ? firstPaint.startTime : null;
        }
        
        /**
         * 获取首次内容绘制时间
         * @returns {number} 首次内容绘制时间
         * @private
         */
        getFirstContentfulPaintTime() {
            const paintEntries = performance.getEntriesByType('paint');
            const firstContentfulPaint = paintEntries.find(entry => entry.name === 'first-contentful-paint');
            return firstContentfulPaint ? firstContentfulPaint.startTime : null;
        }
        
        /**
         * 记录性能条目
         * @param {PerformanceEntry} entry - 性能条目
         * @private
         */
        recordPerformanceEntry(entry) {
            switch (entry.entryType) {
                case 'measure':
                    this.recordComponentPerformance('custom', entry.name, entry.duration);
                    break;
                case 'navigation':
                    this.recordNavigationTiming(entry);
                    break;
                case 'resource':
                    this.recordResourceTiming(entry);
                    break;
                case 'paint':
                    this.recordPaintTiming(entry);
                    break;
            }
        }
        
        /**
         * 记录导航时间
         * @param {PerformanceNavigationTiming} entry - 导航时间条目
         * @private
         */
        recordNavigationTiming(entry) {
            this.recordComponentPerformance('navigation', 'page_load', entry.loadEventEnd - entry.navigationStart);
        }
        
        /**
         * 记录资源时间
         * @param {PerformanceResourceTiming} entry - 资源时间条目
         * @private
         */
        recordResourceTiming(entry) {
            this.recordComponentPerformance('resource', entry.name, entry.duration, {
                transferSize: entry.transferSize,
                encodedBodySize: entry.encodedBodySize
            });
        }
        
        /**
         * 记录绘制时间
         * @param {PerformancePaintTiming} entry - 绘制时间条目
         * @private
         */
        recordPaintTiming(entry) {
            this.recordComponentPerformance('paint', entry.name, entry.startTime);
        }
        
        /**
         * 检查性能阈值
         * @param {string} operationName - 操作名称
         * @param {number} duration - 持续时间
         * @private
         */
        checkPerformanceThresholds(operationName, duration) {
            const thresholds = this.thresholds.responseTime;
            
            if (duration > thresholds.poor) {
                this.logger.logWarning(`性能警告: ${operationName} 响应时间过长 (${duration.toFixed(2)}ms)`);
            } else if (duration > thresholds.acceptable) {
                this.logger.log(`性能提示: ${operationName} 响应时间较长 (${duration.toFixed(2)}ms)`, 'warning');
            }
        }
        
        /**
         * 生成性能报告
         * @returns {Object} 性能报告
         */
        generatePerformanceReport() {
            const report = {
                timestamp: Date.now(),
                summary: this.generateSummary(),
                responseTimes: this.analyzeResponseTimes(),
                apiCalls: this.analyzeAPICalls(),
                errors: this.analyzeErrors(),
                components: this.analyzeComponentPerformance(),
                system: this.analyzeSystemMetrics(),
                recommendations: this.generateRecommendations()
            };
            
            this.monitoringState.lastReportTime = Date.now();
            this.logger.log('📊 性能报告生成完成', 'info');
            
            return report;
        }
        
        /**
         * 生成摘要
         * @returns {Object} 摘要信息
         * @private
         */
        generateSummary() {
            const totalOperations = Array.from(this.metrics.responseTimes.values())
                .reduce((sum, operations) => sum + operations.length, 0);
            
            const totalAPIcalls = Array.from(this.metrics.apiCalls.values())
                .reduce((sum, api) => sum + api.totalCalls, 0);
            
            const totalErrors = Array.from(this.metrics.errorRates.values())
                .reduce((sum, error) => sum + error.count, 0);
            
            return {
                monitoringDuration: this.monitoringState.startTime ? 
                    Date.now() - this.monitoringState.startTime : 0,
                totalOperations,
                totalAPIcalls,
                totalErrors,
                errorRate: totalOperations > 0 ? (totalErrors / totalOperations) * 100 : 0,
                isHealthy: this.assessSystemHealth()
            };
        }
        
        /**
         * 分析响应时间
         * @returns {Object} 响应时间分析
         * @private
         */
        analyzeResponseTimes() {
            const analysis = {};
            
            for (const [operationName, times] of this.metrics.responseTimes) {
                if (times.length === 0) continue;
                
                const durations = times.map(t => t.duration);
                const sorted = durations.sort((a, b) => a - b);
                
                analysis[operationName] = {
                    count: times.length,
                    average: durations.reduce((sum, d) => sum + d, 0) / durations.length,
                    median: sorted[Math.floor(sorted.length / 2)],
                    p95: sorted[Math.floor(sorted.length * 0.95)],
                    min: Math.min(...durations),
                    max: Math.max(...durations),
                    rating: this.ratePerformance(durations)
                };
            }
            
            return analysis;
        }
        
        /**
         * 分析API调用
         * @returns {Object} API调用分析
         * @private
         */
        analyzeAPICalls() {
            const analysis = {};
            
            for (const [apiName, metrics] of this.metrics.apiCalls) {
                analysis[apiName] = {
                    ...metrics,
                    successRate: metrics.totalCalls > 0 ? 
                        (metrics.successfulCalls / metrics.totalCalls) * 100 : 0,
                    errorRate: metrics.totalCalls > 0 ? 
                        (metrics.failedCalls / metrics.totalCalls) * 100 : 0
                };
            }
            
            return analysis;
        }
        
        /**
         * 分析错误
         * @returns {Object} 错误分析
         * @private
         */
        analyzeErrors() {
            const analysis = {};
            
            for (const [errorType, metrics] of this.metrics.errorRates) {
                analysis[errorType] = {
                    count: metrics.count,
                    recentCount: metrics.recentErrors.filter(
                        error => Date.now() - error.timestamp < 3600000 // 最近1小时
                    ).length
                };
            }
            
            return analysis;
        }
        
        /**
         * 分析组件性能
         * @returns {Object} 组件性能分析
         * @private
         */
        analyzeComponentPerformance() {
            const analysis = {};
            
            for (const [componentKey, metrics] of this.metrics.componentPerformance) {
                analysis[componentKey] = {
                    executions: metrics.totalExecutions,
                    averageDuration: metrics.averageDuration,
                    minDuration: metrics.minDuration,
                    maxDuration: metrics.maxDuration,
                    rating: this.rateComponentPerformance(metrics.averageDuration)
                };
            }
            
            return analysis;
        }
        
        /**
         * 分析系统指标
         * @returns {Object} 系统指标分析
         * @private
         */
        analyzeSystemMetrics() {
            if (this.metrics.systemResources.length === 0) {
                return null;
            }
            
            const latest = this.metrics.systemResources[this.metrics.systemResources.length - 1];
            
            return {
                memory: latest.memory,
                timing: latest.timing,
                resources: latest.resources,
                trend: this.analyzeMetricsTrend()
            };
        }
        
        /**
         * 分析指标趋势
         * @returns {Object} 趋势分析
         * @private
         */
        analyzeMetricsTrend() {
            if (this.metrics.systemResources.length < 2) {
                return null;
            }
            
            const recent = this.metrics.systemResources.slice(-10);
            const memoryTrend = this.calculateTrend(recent.map(r => r.memory?.usagePercentage || 0));
            
            return {
                memory: memoryTrend,
                direction: memoryTrend > 5 ? 'increasing' : memoryTrend < -5 ? 'decreasing' : 'stable'
            };
        }
        
        /**
         * 计算趋势
         * @param {Array} values - 数值数组
         * @returns {number} 趋势值
         * @private
         */
        calculateTrend(values) {
            if (values.length < 2) return 0;
            
            const first = values[0];
            const last = values[values.length - 1];
            
            return ((last - first) / first) * 100;
        }
        
        /**
         * 评估系统健康状况
         * @returns {boolean} 是否健康
         * @private
         */
        assessSystemHealth() {
            // 检查错误率
            const totalErrors = Array.from(this.metrics.errorRates.values())
                .reduce((sum, error) => sum + error.count, 0);
            const totalOperations = Array.from(this.metrics.responseTimes.values())
                .reduce((sum, operations) => sum + operations.length, 0);
            
            const errorRate = totalOperations > 0 ? totalErrors / totalOperations : 0;
            
            if (errorRate > this.thresholds.errorRate.poor) {
                return false;
            }
            
            // 检查响应时间
            for (const [, times] of this.metrics.responseTimes) {
                if (times.length > 0) {
                    const avgDuration = times.reduce((sum, t) => sum + t.duration, 0) / times.length;
                    if (avgDuration > this.thresholds.responseTime.poor) {
                        return false;
                    }
                }
            }
            
            return true;
        }
        
        /**
         * 评估性能等级
         * @param {Array} durations - 持续时间数组
         * @returns {string} 性能等级
         * @private
         */
        ratePerformance(durations) {
            const avgDuration = durations.reduce((sum, d) => sum + d, 0) / durations.length;
            const thresholds = this.thresholds.responseTime;
            
            if (avgDuration <= thresholds.excellent) return 'excellent';
            if (avgDuration <= thresholds.good) return 'good';
            if (avgDuration <= thresholds.acceptable) return 'acceptable';
            return 'poor';
        }
        
        /**
         * 评估组件性能等级
         * @param {number} avgDuration - 平均持续时间
         * @returns {string} 性能等级
         * @private
         */
        rateComponentPerformance(avgDuration) {
            const thresholds = this.thresholds.responseTime;
            
            if (avgDuration <= thresholds.excellent) return 'excellent';
            if (avgDuration <= thresholds.good) return 'good';
            if (avgDuration <= thresholds.acceptable) return 'acceptable';
            return 'poor';
        }
        
        /**
         * 生成优化建议
         * @returns {Array} 建议列表
         * @private
         */
        generateRecommendations() {
            const recommendations = [];
            
            // 检查响应时间
            for (const [operationName, times] of this.metrics.responseTimes) {
                if (times.length > 0) {
                    const avgDuration = times.reduce((sum, t) => sum + t.duration, 0) / times.length;
                    if (avgDuration > this.thresholds.responseTime.acceptable) {
                        recommendations.push(`优化 ${operationName} 操作的性能，当前平均响应时间: ${avgDuration.toFixed(2)}ms`);
                    }
                }
            }
            
            // 检查错误率
            for (const [errorType, metrics] of this.metrics.errorRates) {
                if (metrics.count > 10) {
                    recommendations.push(`关注 ${errorType} 错误，发生次数: ${metrics.count}`);
                }
            }
            
            // 检查内存使用
            if (this.metrics.systemResources.length > 0) {
                const latest = this.metrics.systemResources[this.metrics.systemResources.length - 1];
                if (latest.memory && latest.memory.usagePercentage > 80) {
                    recommendations.push(`内存使用率过高: ${latest.memory.usagePercentage.toFixed(2)}%，建议优化内存使用`);
                }
            }
            
            return recommendations;
        }
        
        /**
         * 获取性能统计
         * @returns {Object} 性能统计
         */
        getStats() {
            return {
                isActive: this.monitoringState.isActive,
                startTime: this.monitoringState.startTime,
                lastReportTime: this.monitoringState.lastReportTime,
                totalOperations: Array.from(this.metrics.responseTimes.values())
                    .reduce((sum, operations) => sum + operations.length, 0),
                totalAPIcalls: Array.from(this.metrics.apiCalls.values())
                    .reduce((sum, api) => sum + api.totalCalls, 0),
                totalErrors: Array.from(this.metrics.errorRates.values())
                    .reduce((sum, error) => sum + error.count, 0),
                memorySupported: this.memoryMonitoringSupported,
                version: '1.0.0'
            };
        }
        
        /**
         * 清理旧数据
         */
        cleanup() {
            const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24小时前
            
            // 清理响应时间数据
            for (const [operationName, times] of this.metrics.responseTimes) {
                const filtered = times.filter(t => t.timestamp > cutoffTime);
                this.metrics.responseTimes.set(operationName, filtered);
            }
            
            // 清理系统资源数据
            this.metrics.systemResources = this.metrics.systemResources.filter(
                r => r.timestamp > cutoffTime
            );
            
            this.logger.log('🧹 性能监控数据清理完成', 'info');
        }
    }
    
    // 创建全局实例
    const performanceMonitor = new PerformanceMonitor();
    
    // 注册到全局命名空间
    window.OTA.gemini.core.PerformanceMonitor = PerformanceMonitor;
    window.OTA.gemini.core.performanceMonitor = performanceMonitor;
    
    // 便捷访问函数
    window.OTA.gemini.core.startOperation = function(operationName, context) {
        return performanceMonitor.startOperation(operationName, context);
    };
    
    window.OTA.gemini.core.endOperation = function(operationId, result) {
        return performanceMonitor.endOperation(operationId, result);
    };
    
    window.OTA.gemini.core.recordAPICall = function(apiName, duration, success, details) {
        return performanceMonitor.recordAPICall(apiName, duration, success, details);
    };
    
    // 注册到服务注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('performance-monitor', performanceMonitor, {
            dependencies: ['logger'],
            description: '性能监控系统，监控整个系统的性能指标和资源使用情况'
        });
    }
    
    // 自动启动监控
    performanceMonitor.startMonitoring();
    
    console.log('✅ 性能监控系统已加载');
    
})();
