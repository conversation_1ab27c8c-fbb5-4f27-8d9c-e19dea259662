/**
 * 性能监控系统
 * 实时监控应用性能指标，提供性能分析和优化建议
 */

(function() {
    'use strict';

    /**
     * 性能监控器类
     */
    class PerformanceMonitor {
        constructor() {
            this.metrics = {
                startup: {
                    startTime: null,
                    endTime: null,
                    duration: null,
                    phases: []
                },
                runtime: {
                    fps: [],
                    memoryUsage: [],
                    domQueries: 0,
                    apiCalls: 0,
                    rerenders: 0
                },
                loading: {
                    moduleLoads: [],
                    totalModules: 0,
                    failedLoads: 0,
                    averageLoadTime: 0
                },
                user: {
                    interactions: [],
                    pageViews: 0,
                    sessionStart: Date.now()
                }
            };

            this.observers = {
                performance: null,
                mutation: null,
                intersection: null,
                resize: null
            };

            // 阶段3优化后的性能基线 (2025-01-28)
            this.baseline = {
                loadTime: {
                    first: { min: 450, max: 730, target: 500 },
                    cached: { min: 150, max: 250, target: 200 }
                },
                memory: {
                    initial: { min: 8, max: 12, target: 10 }, // MB
                    peak: { min: 15, max: 20, target: 18 },   // MB
                    average: { min: 10, max: 15, target: 12 } // MB
                },
                response: {
                    interaction: { target: 50 },    // ms
                    aiParsing: { target: 3000 },    // ms
                    apiCall: { target: 1500 }       // ms
                },
                codeMetrics: {
                    totalFiles: 120,        // 优化后文件数量
                    reducedFiles: 13,       // 减少的文件数量
                    totalLines: 26500,      // 优化后代码行数
                    reducedLines: 450       // 减少的代码行数
                }
            };

            this.thresholds = {
                fps: 30, // 最低FPS
                memory: 100 * 1024 * 1024, // 100MB内存阈值 (优化后更合理的阈值)
                loadTime: 3000, // 3秒加载时间阈值
                domQueries: 200, // DOM查询次数阈值 (提高到200次)
                // 新增阈值
                cpuUsage: 80, // CPU使用率阈值 (%)
                networkLatency: 1000, // 网络延迟阈值 (ms)
                renderTime: 16, // 渲染时间阈值 (ms, 60fps = 16.67ms)
                scriptExecutionTime: 50 // 脚本执行时间阈值 (ms)
            };

            this.intervalId = null;
            this.initialized = false;
            this.logger = null;
        }

        /**
         * 初始化性能监控
         */
        init() {
            if (this.initialized) {
                console.warn('性能监控已经初始化');
                return;
            }

            this.logger = this.getLogger();
            this.setupPerformanceObserver();
            this.setupMutationObserver();
            this.setupIntersectionObserver();
            this.setupResizeObserver();
            this.startRuntimeMonitoring();
            this.hookDOMAPIs();
            this.hookFetchAPI();

            this.initialized = true;
            this.log('性能监控系统已初始化', 'info');
        }

        /**
         * 记录启动性能
         */
        recordStartup(phase, duration, details = {}) {
            this.metrics.startup.phases.push({
                phase,
                duration,
                details,
                timestamp: Date.now()
            });

            if (phase === 'complete') {
                this.metrics.startup.endTime = Date.now();
                this.metrics.startup.duration = 
                    this.metrics.startup.endTime - this.metrics.startup.startTime;
                
                this.analyzeStartupPerformance();
            }
        }

        /**
         * 设置启动开始时间
         */
        setStartupStart() {
            this.metrics.startup.startTime = Date.now();
        }

        /**
         * 设置性能观察器
         */
        setupPerformanceObserver() {
            if (!window.PerformanceObserver) return;

            try {
                this.observers.performance = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        this.processPerformanceEntry(entry);
                    }
                });

                this.observers.performance.observe({
                    entryTypes: ['navigation', 'resource', 'measure', 'mark', 'paint']
                });
            } catch (error) {
                this.log('设置性能观察器失败', 'error', error);
            }
        }

        /**
         * 处理性能条目
         */
        processPerformanceEntry(entry) {
            switch (entry.entryType) {
                case 'navigation':
                    this.processNavigationEntry(entry);
                    break;
                case 'resource':
                    this.processResourceEntry(entry);
                    break;
                case 'paint':
                    this.processPaintEntry(entry);
                    break;
                case 'measure':
                    this.processMeasureEntry(entry);
                    break;
            }
        }

        /**
         * 处理导航性能条目
         */
        processNavigationEntry(entry) {
            const metrics = {
                dns: entry.domainLookupEnd - entry.domainLookupStart,
                tcp: entry.connectEnd - entry.connectStart,
                request: entry.responseStart - entry.requestStart,
                response: entry.responseEnd - entry.responseStart,
                dom: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
                load: entry.loadEventEnd - entry.loadEventStart,
                total: entry.loadEventEnd - entry.navigationStart
            };

            this.log('页面导航性能', 'info', metrics);
        }

        /**
         * 处理资源加载条目
         */
        processResourceEntry(entry) {
            if (entry.name.includes('.js')) {
                this.metrics.loading.moduleLoads.push({
                    name: entry.name,
                    duration: entry.duration,
                    size: entry.transferSize || 0,
                    timestamp: Date.now()
                });

                this.metrics.loading.totalModules++;
                this.updateAverageLoadTime();
            }
        }

        /**
         * 处理绘制条目
         */
        processPaintEntry(entry) {
            this.log(`绘制性能 ${entry.name}`, 'debug', {
                startTime: entry.startTime,
                duration: entry.duration
            });
        }

        /**
         * 处理测量条目
         */
        processMeasureEntry(entry) {
            this.log(`性能测量 ${entry.name}`, 'debug', {
                startTime: entry.startTime,
                duration: entry.duration
            });
        }

        /**
         * 设置DOM变化观察器
         */
        setupMutationObserver() {
            this.observers.mutation = new MutationObserver((mutations) => {
                this.metrics.runtime.rerenders += mutations.length;
            });

            this.observers.mutation.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true
            });
        }

        /**
         * 设置交叉观察器
         */
        setupIntersectionObserver() {
            this.observers.intersection = new IntersectionObserver((entries) => {
                // 监控元素可见性变化
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.log('元素进入视口', 'debug', {
                            target: entry.target.id || entry.target.className
                        });
                    }
                });
            });
        }

        /**
         * 设置大小变化观察器
         */
        setupResizeObserver() {
            if (!window.ResizeObserver) return;

            this.observers.resize = new ResizeObserver((entries) => {
                this.log('元素大小变化', 'debug', {
                    count: entries.length
                });
            });
        }

        /**
         * 开始运行时监控
         */
        startRuntimeMonitoring() {
            this.intervalId = setInterval(() => {
                this.collectRuntimeMetrics();
            }, 5000); // 每5秒收集一次
        }

        /**
         * 收集运行时指标
         */
        collectRuntimeMetrics() {
            // FPS监控
            const fps = this.calculateFPS();
            if (fps > 0) {
                this.metrics.runtime.fps.push({
                    value: fps,
                    timestamp: Date.now()
                });

                // 只保留最近100个FPS记录
                if (this.metrics.runtime.fps.length > 100) {
                    this.metrics.runtime.fps.shift();
                }
            }

            // 内存使用监控
            if (performance.memory) {
                this.metrics.runtime.memoryUsage.push({
                    used: performance.memory.usedJSHeapSize,
                    total: performance.memory.totalJSHeapSize,
                    limit: performance.memory.jsHeapSizeLimit,
                    timestamp: Date.now()
                });

                // 只保留最近50个内存记录
                if (this.metrics.runtime.memoryUsage.length > 50) {
                    this.metrics.runtime.memoryUsage.shift();
                }

                // 智能内存阈值检查
                this.checkMemoryThreshold(performance.memory);
            }

            // 智能FPS阈值检查
            if (fps > 0) {
                this.checkFPSThreshold(fps);
            }

            // 检查DOM查询次数
            this.checkDOMQueryThreshold();

            // 检查脚本执行时间
            this.checkScriptExecutionTime();
        }

        /**
         * 计算FPS
         */
        calculateFPS() {
            if (!this.lastFrameTime) {
                this.lastFrameTime = performance.now();
                this.frameCount = 0;
                return 0;
            }

            this.frameCount++;
            const currentTime = performance.now();
            const elapsed = currentTime - this.lastFrameTime;

            if (elapsed >= 1000) { // 每秒计算一次
                const fps = Math.round((this.frameCount * 1000) / elapsed);
                this.lastFrameTime = currentTime;
                this.frameCount = 0;
                return fps;
            }

            return 0;
        }

        /**
         * 钩子DOM API
         */
        hookDOMAPIs() {
            const originalQuerySelector = document.querySelector;
            const originalQuerySelectorAll = document.querySelectorAll;
            const self = this;

            document.querySelector = function(...args) {
                self.metrics.runtime.domQueries++;
                return originalQuerySelector.apply(this, args);
            };

            document.querySelectorAll = function(...args) {
                self.metrics.runtime.domQueries++;
                return originalQuerySelectorAll.apply(this, args);
            };
        }

        /**
         * 钩子Fetch API
         */
        hookFetchAPI() {
            const originalFetch = window.fetch;
            const self = this;

            window.fetch = function(...args) {
                self.metrics.runtime.apiCalls++;
                const startTime = performance.now();

                return originalFetch.apply(this, args).then(response => {
                    const endTime = performance.now();
                    const duration = endTime - startTime;

                    self.log('API调用完成', 'debug', {
                        url: args[0],
                        duration,
                        status: response.status
                    });

                    return response;
                }).catch(error => {
                    self.log('API调用失败', 'error', {
                        url: args[0],
                        error: error.message
                    });
                    throw error;
                });
            };
        }

        /**
         * 更新平均加载时间
         */
        updateAverageLoadTime() {
            const loads = this.metrics.loading.moduleLoads;
            if (loads.length > 0) {
                const total = loads.reduce((sum, load) => sum + load.duration, 0);
                this.metrics.loading.averageLoadTime = total / loads.length;
            }
        }

        /**
         * 分析启动性能
         */
        analyzeStartupPerformance() {
            const phases = this.metrics.startup.phases;
            const totalDuration = this.metrics.startup.duration;

            // 找出最慢的阶段
            const slowestPhase = phases.reduce((slowest, current) => 
                current.duration > slowest.duration ? current : slowest
            );

            // 性能建议
            const suggestions = [];
            if (totalDuration > 3000) {
                suggestions.push('启动时间过长，建议使用懒加载优化');
            }
            if (slowestPhase.duration > 1000) {
                suggestions.push(`${slowestPhase.phase}阶段耗时过长，需要优化`);
            }

            this.log('启动性能分析完成', 'info', {
                totalDuration,
                slowestPhase: slowestPhase.phase,
                suggestions
            });
        }

        /**
         * 触发性能警告
         */
        triggerAlert(type, data) {
            const alert = {
                type,
                data,
                timestamp: Date.now(),
                severity: this.calculateSeverity(type, data)
            };

            this.log(`性能警告: ${type}`, 'warning', alert);

            // 触发自定义事件
            window.dispatchEvent(new CustomEvent('performance-alert', {
                detail: alert
            }));
        }

        /**
         * 计算警告严重程度
         */
        calculateSeverity(type, data) {
            switch (type) {
                case 'memory':
                    const memoryRatio = data.current / data.threshold;
                    if (memoryRatio > 2) return 'critical';
                    if (memoryRatio > 1.5) return 'high';
                    return 'medium';
                
                case 'fps':
                    if (data.current < 15) return 'critical';
                    if (data.current < 24) return 'high';
                    return 'medium';
                
                default:
                    return 'medium';
            }
        }

        /**
         * 获取性能报告
         */
        getPerformanceReport() {
            const report = {
                timestamp: Date.now(),
                startup: this.metrics.startup,
                runtime: {
                    averageFPS: this.getAverageFPS(),
                    currentMemory: this.getCurrentMemoryUsage(),
                    domQueries: this.metrics.runtime.domQueries,
                    apiCalls: this.metrics.runtime.apiCalls,
                    rerenders: this.metrics.runtime.rerenders
                },
                loading: {
                    totalModules: this.metrics.loading.totalModules,
                    averageLoadTime: this.metrics.loading.averageLoadTime,
                    failedLoads: this.metrics.loading.failedLoads
                },
                recommendations: this.generateRecommendations()
            };

            return report;
        }

        /**
         * 获取平均FPS
         */
        getAverageFPS() {
            const recentFPS = this.metrics.runtime.fps.slice(-10);
            if (recentFPS.length === 0) return 0;
            
            const sum = recentFPS.reduce((total, fps) => total + fps.value, 0);
            return Math.round(sum / recentFPS.length);
        }

        /**
         * 获取当前内存使用
         */
        getCurrentMemoryUsage() {
            if (!performance.memory) return null;
            
            return {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                usagePercent: Math.round(
                    (performance.memory.usedJSHeapSize / performance.memory.totalJSHeapSize) * 100
                )
            };
        }

        /**
         * 生成性能优化建议
         */
        generateRecommendations() {
            const recommendations = [];
            const avgFPS = this.getAverageFPS();
            const memoryUsage = this.getCurrentMemoryUsage();

            if (avgFPS > 0 && avgFPS < 30) {
                recommendations.push({
                    type: 'fps',
                    severity: 'high',
                    message: 'FPS过低，建议减少DOM操作和动画复杂度'
                });
            }

            if (memoryUsage && memoryUsage.usagePercent > 80) {
                recommendations.push({
                    type: 'memory',
                    severity: 'high',
                    message: '内存使用率过高，建议启用垃圾回收和组件复用'
                });
            }

            if (this.metrics.runtime.domQueries > this.thresholds.domQueries) {
                recommendations.push({
                    type: 'dom',
                    severity: 'medium',
                    message: 'DOM查询次数过多，建议缓存DOM元素'
                });
            }

            if (this.metrics.loading.averageLoadTime > 2000) {
                recommendations.push({
                    type: 'loading',
                    severity: 'medium',
                    message: '模块加载时间过长，建议启用代码分割和预加载'
                });
            }

            return recommendations;
        }

        /**
         * 获取Logger实例
         */
        getLogger() {
            try {
                return window.OTA?.getService('logger') || window.getLogger?.() || console;
            } catch {
                return console;
            }
        }

        /**
         * 日志输出
         */
        log(message, level = 'info', data = null) {
            if (this.logger && this.logger.log) {
                this.logger.log(`[PerformanceMonitor] ${message}`, level, data);
            } else if (level === 'error' || level === 'warning' || this.debugMode) {
                // 仅在错误、警告或调试模式时输出到控制台
                const consoleMethod = level === 'error' ? console.error :
                                    level === 'warning' ? console.warn : console.log;
                consoleMethod(`[PerformanceMonitor] ${message}`, data);
            }
        }

        /**
         * 销毁监控器
         */
        destroy() {
            if (this.intervalId) {
                clearInterval(this.intervalId);
            }

            Object.values(this.observers).forEach(observer => {
                if (observer && observer.disconnect) {
                    observer.disconnect();
                }
            });

            this.initialized = false;
            this.log('性能监控器已销毁', 'info');
        }
    }

        /**
         * 智能内存阈值检查
         * @param {Object} memory - 内存信息对象
         */
        checkMemoryThreshold(memory) {
            const currentUsage = memory.usedJSHeapSize;
            const memoryHistory = this.metrics.runtime.memoryUsage;

            // 基础阈值检查
            if (currentUsage > this.thresholds.memory) {
                // 检查是否是持续增长趋势
                const isGrowingTrend = this.isMemoryGrowingTrend(memoryHistory);
                const severity = isGrowingTrend ? 'CRITICAL' : 'WARNING';

                this.triggerAlert('memory', {
                    current: currentUsage,
                    threshold: this.thresholds.memory,
                    trend: isGrowingTrend ? 'growing' : 'stable',
                    severity
                });
            }
        }

        /**
         * 智能FPS阈值检查
         * @param {number} fps - 当前FPS
         */
        checkFPSThreshold(fps) {
            const fpsHistory = this.metrics.runtime.fps;

            if (fps < this.thresholds.fps) {
                // 检查FPS下降趋势
                const isDropping = this.isFPSDropping(fpsHistory);
                const severity = fps < 15 ? 'CRITICAL' : 'WARNING';

                this.triggerAlert('fps', {
                    current: fps,
                    threshold: this.thresholds.fps,
                    trend: isDropping ? 'dropping' : 'stable',
                    severity
                });
            }
        }

        /**
         * 检查DOM查询次数阈值
         */
        checkDOMQueryThreshold() {
            const currentQueries = this.metrics.runtime.domQueries;
            if (currentQueries > this.thresholds.domQueries) {
                this.triggerAlert('dom_queries', {
                    current: currentQueries,
                    threshold: this.thresholds.domQueries,
                    severity: currentQueries > this.thresholds.domQueries * 2 ? 'CRITICAL' : 'WARNING'
                });
            }
        }

        /**
         * 检查脚本执行时间
         */
        checkScriptExecutionTime() {
            // 使用Performance API检查长任务
            if (window.PerformanceLongTaskTiming) {
                const longTasks = performance.getEntriesByType('longtask');
                longTasks.forEach(task => {
                    if (task.duration > this.thresholds.scriptExecutionTime) {
                        this.triggerAlert('long_task', {
                            duration: task.duration,
                            threshold: this.thresholds.scriptExecutionTime,
                            severity: task.duration > 100 ? 'CRITICAL' : 'WARNING'
                        });
                    }
                });
            }
        }

        /**
         * 检查内存是否呈增长趋势
         * @param {Array} memoryHistory - 内存历史记录
         * @returns {boolean} 是否呈增长趋势
         */
        isMemoryGrowingTrend(memoryHistory) {
            if (memoryHistory.length < 5) return false;

            const recent = memoryHistory.slice(-5);
            let increasingCount = 0;

            for (let i = 1; i < recent.length; i++) {
                if (recent[i].used > recent[i-1].used) {
                    increasingCount++;
                }
            }

            return increasingCount >= 3; // 5次中有3次增长
        }

        /**
         * 检查FPS是否呈下降趋势
         * @param {Array} fpsHistory - FPS历史记录
         * @returns {boolean} 是否呈下降趋势
         */
        isFPSDropping(fpsHistory) {
            if (fpsHistory.length < 5) return false;

            const recent = fpsHistory.slice(-5);
            let droppingCount = 0;

            for (let i = 1; i < recent.length; i++) {
                if (recent[i] < recent[i-1]) {
                    droppingCount++;
                }
            }

            return droppingCount >= 3; // 5次中有3次下降
        }
    }

    // 创建全局实例
    const performanceMonitor = new PerformanceMonitor();

    // 导出到全局作用域
    window.OTA = window.OTA || {};
    window.OTA.performanceMonitor = performanceMonitor;
    window.OTA.getPerformanceMonitor = () => performanceMonitor;

    // 向后兼容
    window.getPerformanceMonitor = () => performanceMonitor;

    console.log('✅ 性能监控系统已加载');

})();