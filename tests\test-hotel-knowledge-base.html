<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>酒店知识库功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
            border-left: 4px solid #007bff;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }
        .knowledge-base {
            background: #e7f3ff;
            border-left-color: #0066cc;
        }
        .ai-translation {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .test-cases {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        .test-case {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .test-case:hover {
            background: #e9ecef;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <h1>🏨 酒店知识库功能测试</h1>
    
    <div class="test-container">
        <h2>知识库状态</h2>
        <div id="knowledgeBaseStatus" class="result">
            <p>🔄 正在检查知识库状态...</p>
        </div>
        <div id="knowledgeBaseStats" class="stats" style="display: none;"></div>
    </div>

    <div class="test-container">
        <h2>地址翻译测试</h2>
        <div class="form-group">
            <label for="testAddress">输入地址:</label>
            <input type="text" id="testAddress" placeholder="输入中文地址或酒店名称..." value="香格里拉酒店">
        </div>
        <button onclick="testSingleTranslation()">测试翻译</button>
        <button onclick="runBatchTest()">批量测试</button>
        <button onclick="loadKnowledgeBase()">重新加载知识库</button>
        
        <div class="test-cases">
            <div class="test-case" onclick="setTestAddress('香格里拉酒店')">香格里拉酒店</div>
            <div class="test-case" onclick="setTestAddress('希尔顿酒店')">希尔顿酒店</div>
            <div class="test-case" onclick="setTestAddress('万豪酒店')">万豪酒店</div>
            <div class="test-case" onclick="setTestAddress('吉隆坡国际机场')">吉隆坡国际机场</div>
            <div class="test-case" onclick="setTestAddress('亚庇机场')">亚庇机场</div>
            <div class="test-case" onclick="setTestAddress('新山机场')">新山机场</div>
            <div class="test-case" onclick="setTestAddress('槟城机场')">槟城机场</div>
            <div class="test-case" onclick="setTestAddress('斗湖机场')">斗湖机场</div>
            <div class="test-case" onclick="setTestAddress('新加坡樟宜机场')">新加坡樟宜机场</div>
            <div class="test-case" onclick="setTestAddress('1 City酒店')">1 City酒店</div>
            <div class="test-case" onclick="setTestAddress('白金酒店')">白金酒店</div>
            <div class="test-case" onclick="setTestAddress('不存在的地址')">不存在的地址</div>
        </div>
        
        <div id="translationResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-container">
        <h2>批量测试结果</h2>
        <div id="batchTestResult" class="result" style="display: none;"></div>
    </div>

    <script>
        // 模拟酒店知识库数据
        const mockHotelData = {
            metadata: {
                total_hotels: 4264,
                total_regions: 8
            },
            hotels_by_region: {
                "吉隆坡": [
                    { chinese_name: "香格里拉酒店", english_name: "Shangri-La Hotel", region: "吉隆坡" },
                    { chinese_name: "希尔顿酒店", english_name: "Hilton Hotel", region: "吉隆坡" },
                    { chinese_name: "万豪酒店", english_name: "Marriott Hotel", region: "吉隆坡" }
                ],
                "其他": [
                    { chinese_name: "1 City酒店", english_name: "1 City Hotel", region: "其他" },
                    { chinese_name: "白金酒店", english_name: "Platinum Hotel", region: "其他" }
                ]
            }
        };

        // 机场翻译数据库
        const mockAirportData = new Map([
            // 吉隆坡机场
            ['吉隆坡国际机场', { english: 'Kuala Lumpur International Airport', malay: 'Lapangan Terbang Antarabangsa Kuala Lumpur', code: 'KLIA', region: '吉隆坡' }],
            ['吉隆坡机场', { english: 'Kuala Lumpur International Airport', malay: 'Lapangan Terbang Antarabangsa Kuala Lumpur', code: 'KLIA', region: '吉隆坡' }],
            ['KLIA', { english: 'Kuala Lumpur International Airport', malay: 'Lapangan Terbang Antarabangsa Kuala Lumpur', code: 'KLIA', region: '吉隆坡' }],
            ['KLIA2', { english: 'Kuala Lumpur International Airport 2', malay: 'Lapangan Terbang Antarabangsa Kuala Lumpur 2', code: 'KLIA2', region: '吉隆坡' }],

            // 亚庇机场
            ['亚庇国际机场', { english: 'Kota Kinabalu International Airport', malay: 'Lapangan Terbang Antarabangsa Kota Kinabalu', code: 'BKI', region: '亚庇' }],
            ['亚庇机场', { english: 'Kota Kinabalu International Airport', malay: 'Lapangan Terbang Antarabangsa Kota Kinabalu', code: 'BKI', region: '亚庇' }],

            // 新山机场
            ['新山机场', { english: 'Senai International Airport', malay: 'Lapangan Terbang Antarabangsa Senai', code: 'JHB', region: '新山' }],
            ['士乃机场', { english: 'Senai International Airport', malay: 'Lapangan Terbang Antarabangsa Senai', code: 'JHB', region: '新山' }],

            // 槟城机场
            ['槟城机场', { english: 'Penang International Airport', malay: 'Lapangan Terbang Antarabangsa Pulau Pinang', code: 'PEN', region: '槟城' }],
            ['槟城国际机场', { english: 'Penang International Airport', malay: 'Lapangan Terbang Antarabangsa Pulau Pinang', code: 'PEN', region: '槟城' }],

            // 斗湖机场
            ['斗湖机场', { english: 'Tawau Airport', malay: 'Lapangan Terbang Tawau', code: 'TWU', region: '斗湖' }],

            // 新加坡机场
            ['新加坡樟宜机场', { english: 'Singapore Changi Airport', malay: 'Lapangan Terbang Changi Singapura', code: 'SIN', region: '新加坡' }],
            ['新加坡机场', { english: 'Singapore Changi Airport', malay: 'Lapangan Terbang Changi Singapura', code: 'SIN', region: '新加坡' }],
            ['樟宜机场', { english: 'Singapore Changi Airport', malay: 'Lapangan Terbang Changi Singapura', code: 'SIN', region: '新加坡' }]
        ]);

        // 模拟GeminiService的酒店知识库功能
        class MockHotelKnowledgeBase {
            constructor() {
                this.loaded = false;
                this.chineseToEnglishMap = new Map();
                this.fuzzySearchIndex = new Map();
                this.totalHotels = 0;
                this.loadError = null;
                this.airportTranslations = mockAirportData;
            }

            async initialize() {
                try {
                    console.log('🏨 开始加载酒店知识库...');
                    
                    // 模拟网络延迟
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    this.buildSearchIndex(mockHotelData);
                    this.loaded = true;
                    this.totalHotels = mockHotelData.metadata.total_hotels;
                    
                    console.log(`✅ 酒店知识库加载完成，共 ${this.totalHotels} 家酒店`);
                    return true;
                    
                } catch (error) {
                    this.loadError = error.message;
                    console.error(`❌ 酒店知识库加载失败: ${error.message}`);
                    return false;
                }
            }

            buildSearchIndex(hotelData) {
                Object.values(hotelData.hotels_by_region || {}).forEach(hotels => {
                    hotels.forEach(hotel => {
                        const chineseName = hotel.chinese_name;
                        const englishName = hotel.english_name;
                        const region = hotel.region;
                        
                        if (chineseName && englishName) {
                            // 精确匹配索引
                            this.chineseToEnglishMap.set(chineseName, {
                                english: englishName,
                                malay: this.generateMalayName(englishName),
                                region: region,
                                source: 'knowledge_base'
                            });
                            
                            // 模糊匹配索引
                            const keywords = this.extractKeywords(chineseName);
                            keywords.forEach(keyword => {
                                if (!this.fuzzySearchIndex.has(keyword)) {
                                    this.fuzzySearchIndex.set(keyword, []);
                                }
                                this.fuzzySearchIndex.get(keyword).push({
                                    chinese: chineseName,
                                    english: englishName,
                                    malay: this.generateMalayName(englishName),
                                    region: region,
                                    source: 'knowledge_base'
                                });
                            });
                        }
                    });
                });
            }

            generateMalayName(englishName) {
                const translations = {
                    'Hotel': 'Hotel',
                    'Shangri-La': 'Shangri-La',
                    'Hilton': 'Hilton',
                    'Marriott': 'Marriott',
                    'City': 'Bandar',
                    'Platinum': 'Platinum'
                };
                
                let malayName = englishName;
                Object.entries(translations).forEach(([english, malay]) => {
                    malayName = malayName.replace(new RegExp(`\\b${english}\\b`, 'gi'), malay);
                });
                
                return malayName;
            }

            extractKeywords(hotelName) {
                const suffixes = ['酒店', '宾馆', '旅馆', '度假村'];
                let cleanName = hotelName;
                
                suffixes.forEach(suffix => {
                    cleanName = cleanName.replace(new RegExp(suffix + '$'), '');
                });
                
                const keywords = [cleanName];
                const brands = ['香格里拉', '希尔顿', '万豪', '白金'];
                brands.forEach(brand => {
                    if (hotelName.includes(brand)) {
                        keywords.push(brand);
                    }
                });
                
                return keywords.filter(k => k.length > 0);
            }

            queryAirport(address) {
                // 精确匹配
                if (this.airportTranslations.has(address)) {
                    const result = this.airportTranslations.get(address);
                    console.log(`✈️ 机场精确匹配: ${address} → ${result.english}`);
                    return {
                        english: result.english,
                        malay: result.malay,
                        region: result.region,
                        code: result.code,
                        source: 'airport_database'
                    };
                }

                // 模糊匹配 - 检查是否包含机场关键词
                const airportKeywords = ['机场', '国际机场', 'airport'];
                const hasAirportKeyword = airportKeywords.some(keyword =>
                    address.toLowerCase().includes(keyword.toLowerCase())
                );

                if (hasAirportKeyword) {
                    // 尝试通过城市名匹配
                    for (const [key, value] of this.airportTranslations.entries()) {
                        if (address.includes(value.region) || key.includes(address.replace(/机场|国际机场/g, ''))) {
                            console.log(`✈️ 机场模糊匹配: ${address} → ${value.english}`);
                            return {
                                english: value.english,
                                malay: value.malay,
                                region: value.region,
                                code: value.code,
                                source: 'airport_database'
                            };
                        }
                    }
                }

                return null;
            }

            queryHotel(address) {
                if (!this.loaded) {
                    return null;
                }

                // 精确匹配
                if (this.chineseToEnglishMap.has(address)) {
                    const result = this.chineseToEnglishMap.get(address);
                    console.log(`🎯 酒店精确匹配: ${address} → ${result.english}`);
                    return result;
                }

                // 模糊匹配
                const keywords = this.extractKeywords(address);
                for (const keyword of keywords) {
                    if (this.fuzzySearchIndex.has(keyword)) {
                        const candidates = this.fuzzySearchIndex.get(keyword);
                        if (candidates.length > 0) {
                            const result = candidates[0];
                            console.log(`🔍 酒店模糊匹配: ${address} → ${result.english}`);
                            return result;
                        }
                    }
                }

                return null;
            }

            async translateAddress(address) {
                // 优先查询机场数据库
                const airportResult = this.queryAirport(address);
                if (airportResult) {
                    return {
                        original: address,
                        malay: airportResult.malay,
                        english: airportResult.english,
                        source: 'airport_database',
                        region: airportResult.region,
                        code: airportResult.code
                    };
                }

                // 其次查询酒店知识库
                const knowledgeResult = this.queryHotel(address);
                if (knowledgeResult) {
                    return {
                        original: address,
                        malay: knowledgeResult.malay,
                        english: knowledgeResult.english,
                        source: 'knowledge_base',
                        region: knowledgeResult.region
                    };
                }

                // 模拟AI翻译
                console.log(`🤖 本地数据库无匹配，使用AI翻译: ${address}`);
                await new Promise(resolve => setTimeout(resolve, 500));

                return {
                    original: address,
                    malay: `${address} (Malay Translation)`,
                    english: `${address} (English Translation)`,
                    source: 'ai_translation'
                };
            }
        }

        // 创建知识库实例
        const hotelKB = new MockHotelKnowledgeBase();

        // 页面加载时初始化
        window.addEventListener('load', async () => {
            await loadKnowledgeBase();
        });

        async function loadKnowledgeBase() {
            const statusDiv = document.getElementById('knowledgeBaseStatus');
            const statsDiv = document.getElementById('knowledgeBaseStats');
            
            statusDiv.innerHTML = '<p>🔄 正在加载酒店知识库...</p>';
            statusDiv.className = 'result';
            
            const success = await hotelKB.initialize();
            
            if (success) {
                statusDiv.innerHTML = `
                    <h4>✅ 酒店知识库加载成功</h4>
                    <p>总酒店数: ${hotelKB.totalHotels}</p>
                    <p>精确匹配索引: ${hotelKB.chineseToEnglishMap.size} 条</p>
                    <p>模糊匹配索引: ${hotelKB.fuzzySearchIndex.size} 条</p>
                `;
                statusDiv.className = 'result success';
                
                // 显示统计信息
                statsDiv.innerHTML = `
                    <div class="stat-card">
                        <div class="stat-number">${hotelKB.totalHotels}</div>
                        <div class="stat-label">总酒店数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${hotelKB.chineseToEnglishMap.size}</div>
                        <div class="stat-label">精确匹配</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${hotelKB.fuzzySearchIndex.size}</div>
                        <div class="stat-label">模糊匹配</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">8</div>
                        <div class="stat-label">覆盖区域</div>
                    </div>
                `;
                statsDiv.style.display = 'grid';
                
            } else {
                statusDiv.innerHTML = `
                    <h4>❌ 酒店知识库加载失败</h4>
                    <p>错误: ${hotelKB.loadError}</p>
                    <p>将回退到AI翻译模式</p>
                `;
                statusDiv.className = 'result error';
                statsDiv.style.display = 'none';
            }
        }

        function setTestAddress(address) {
            document.getElementById('testAddress').value = address;
        }

        async function testSingleTranslation() {
            const address = document.getElementById('testAddress').value.trim();
            const resultDiv = document.getElementById('translationResult');
            
            if (!address) {
                resultDiv.innerHTML = '<p>❌ 请输入地址</p>';
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
                return;
            }
            
            resultDiv.innerHTML = '<p>🔄 正在翻译地址...</p>';
            resultDiv.className = 'result';
            resultDiv.style.display = 'block';
            
            try {
                const result = await hotelKB.translateAddress(address);
                
                let sourceClass, sourceIcon, sourceName;
                if (result.source === 'airport_database') {
                    sourceClass = 'knowledge-base';
                    sourceIcon = '✈️';
                    sourceName = '机场数据库';
                } else if (result.source === 'knowledge_base') {
                    sourceClass = 'knowledge-base';
                    sourceIcon = '🏨';
                    sourceName = '酒店知识库';
                } else {
                    sourceClass = 'ai-translation';
                    sourceIcon = '🤖';
                    sourceName = 'AI翻译';
                }
                
                resultDiv.innerHTML = `
                    <h4>${sourceIcon} 翻译结果 (${sourceName})</h4>
                    <p><strong>原始地址:</strong> ${result.original}</p>
                    <p><strong>英文:</strong> ${result.english}</p>
                    <p><strong>马来文:</strong> ${result.malay}</p>
                    ${result.region ? `<p><strong>区域:</strong> ${result.region}</p>` : ''}
                    ${result.code ? `<p><strong>机场代码:</strong> ${result.code}</p>` : ''}
                    <p><strong>翻译来源:</strong> ${result.source}</p>
                `;
                resultDiv.className = `result ${sourceClass}`;
                
            } catch (error) {
                resultDiv.innerHTML = `<p>❌ 翻译失败: ${error.message}</p>`;
                resultDiv.className = 'result error';
            }
        }

        async function runBatchTest() {
            const testCases = [
                // 酒店测试
                '香格里拉酒店',
                '希尔顿酒店',
                '万豪酒店',
                '1 City酒店',
                '白金酒店',
                // 机场测试
                '吉隆坡国际机场',
                '亚庇机场',
                '新山机场',
                '槟城机场',
                '斗湖机场',
                '新加坡樟宜机场',
                'KLIA',
                'KLIA2',
                // 其他地址
                '双子塔',
                '不存在的地址'
            ];
            
            const resultDiv = document.getElementById('batchTestResult');
            resultDiv.innerHTML = '<p>🔄 正在执行批量测试...</p>';
            resultDiv.className = 'result';
            resultDiv.style.display = 'block';
            
            const results = [];
            let knowledgeBaseHits = 0;
            let airportHits = 0;
            let aiTranslations = 0;

            for (const testCase of testCases) {
                try {
                    const result = await hotelKB.translateAddress(testCase);
                    results.push({
                        input: testCase,
                        result: result,
                        success: true
                    });

                    if (result.source === 'airport_database') {
                        airportHits++;
                    } else if (result.source === 'knowledge_base') {
                        knowledgeBaseHits++;
                    } else {
                        aiTranslations++;
                    }

                } catch (error) {
                    results.push({
                        input: testCase,
                        error: error.message,
                        success: false
                    });
                }
            }
            
            const successRate = (results.filter(r => r.success).length / results.length * 100).toFixed(1);
            const localDbHitRate = ((knowledgeBaseHits + airportHits) / results.length * 100).toFixed(1);

            let html = `
                <h4>✅ 批量测试完成</h4>
                <p><strong>测试用例:</strong> ${testCases.length} 个</p>
                <p><strong>成功率:</strong> ${successRate}%</p>
                <p><strong>本地数据库命中率:</strong> ${localDbHitRate}%</p>
                <p><strong>机场数据库:</strong> ${airportHits} 个</p>
                <p><strong>酒店知识库:</strong> ${knowledgeBaseHits} 个</p>
                <p><strong>AI翻译:</strong> ${aiTranslations} 个</p>
                <hr>
                <h5>详细结果:</h5>
            `;
            
            results.forEach((item, index) => {
                if (item.success) {
                    let sourceIcon;
                    if (item.result.source === 'airport_database') {
                        sourceIcon = '✈️';
                    } else if (item.result.source === 'knowledge_base') {
                        sourceIcon = '🏨';
                    } else {
                        sourceIcon = '🤖';
                    }

                    html += `
                        <div style="margin-bottom: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                            <strong>${index + 1}. ${item.input}</strong> ${sourceIcon}<br>
                            <small>英文: ${item.result.english}</small><br>
                            <small>马来文: ${item.result.malay}</small><br>
                            ${item.result.code ? `<small>机场代码: ${item.result.code}</small><br>` : ''}
                            <small>来源: ${item.result.source}</small>
                        </div>
                    `;
                } else {
                    html += `
                        <div style="margin-bottom: 10px; padding: 10px; background: #f8d7da; border-radius: 4px;">
                            <strong>${index + 1}. ${item.input}</strong> ❌<br>
                            <small>错误: ${item.error}</small>
                        </div>
                    `;
                }
            });
            
            resultDiv.innerHTML = html;
            resultDiv.className = 'result success';
        }
    </script>
</body>
</html>
