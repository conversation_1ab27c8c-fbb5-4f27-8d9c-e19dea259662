/**
 * @INTEGRATION_FIX 系统集成修复
 * 🏷️ 标签: @SYSTEM_INTEGRATION @COMPATIBILITY_FIX
 * 📝 说明: 确保新的模块化Gemini架构与现有系统完全兼容
 * 🎯 目标: 修复集成问题，确保向后兼容性
 */

(function() {
    'use strict';
    
    // 确保OTA命名空间存在
    window.OTA = window.OTA || {};
    window.OTA.gemini = window.OTA.gemini || {};
    window.OTA.gemini.integration = window.OTA.gemini.integration || {};
    
    /**
     * 系统集成修复器
     * 确保所有组件正确集成和兼容
     */
    class SystemIntegrationFix {
        constructor() {
            this.logger = window.getLogger?.() || console;
            this.fixApplied = false;
            
            // 修复配置
            this.config = {
                // 需要修复的服务映射
                serviceMapping: {
                    'geminiService': 'window.OTA.geminiService',
                    'geminiCoordinator': 'window.OTA.gemini.coordinator'
                },
                
                // 需要确保的全局函数
                globalFunctions: [
                    'getGeminiService',
                    'getGeminiCoordinator',
                    'parseOrderWithGemini'
                ],
                
                // 兼容性检查项
                compatibilityChecks: [
                    'geminiServiceInstance',
                    'coordinatorInstance',
                    'globalFunctions',
                    'serviceLocatorIntegration',
                    'multiOrderManagerIntegration',
                    'formManagerIntegration'
                ]
            };
            
            // 自动应用修复
            this.applyFixes();
        }
        
        /**
         * 应用所有修复
         */
        applyFixes() {
            if (this.fixApplied) {
                return;
            }
            
            try {
                this.logger.log('开始应用系统集成修复', 'info');
                
                // 1. 修复服务定位器集成
                this.fixServiceLocatorIntegration();
                
                // 2. 修复全局函数定义
                this.fixGlobalFunctions();
                
                // 3. 修复Gemini服务实例
                this.fixGeminiServiceInstance();
                
                // 4. 修复协调器集成
                this.fixCoordinatorIntegration();
                
                // 5. 验证集成
                this.validateIntegration();
                
                this.fixApplied = true;
                this.logger.log('系统集成修复完成', 'info');
                
            } catch (error) {
                this.logger.logError('系统集成修复失败', error);
            }
        }
        
        /**
         * 修复服务定位器集成
         * @private
         */
        fixServiceLocatorIntegration() {
            // 确保service-locator正确注册geminiService
            if (window.OTA && window.OTA.serviceLocator) {
                const serviceLocator = window.OTA.serviceLocator;
                
                // 重新注册geminiService
                if (serviceLocator.register) {
                    serviceLocator.register('geminiService', () => {
                        return window.OTA.geminiService || this.createFallbackGeminiService();
                    }, { singleton: true });
                }
            }
            
            // 确保全局getGeminiService函数正确工作
            if (!window.getGeminiService || typeof window.getGeminiService !== 'function') {
                window.getGeminiService = () => {
                    return window.OTA.geminiService || this.createFallbackGeminiService();
                };
            }
        }
        
        /**
         * 修复全局函数定义
         * @private
         */
        fixGlobalFunctions() {
            // 确保getGeminiService函数存在且正确
            if (!window.getGeminiService) {
                window.getGeminiService = () => {
                    return window.OTA.geminiService || this.createFallbackGeminiService();
                };
            }
            
            // 确保getGeminiCoordinator函数存在且正确
            if (!window.getGeminiCoordinator) {
                window.getGeminiCoordinator = () => {
                    return window.OTA?.gemini?.getGeminiCoordinator?.() || 
                           window.OTA?.gemini?.coordinator ||
                           null;
                };
            }
            
            // 确保parseOrderWithGemini函数存在且正确
            if (!window.parseOrderWithGemini) {
                window.parseOrderWithGemini = async (orderText, options = {}) => {
                    const geminiService = window.getGeminiService();
                    if (geminiService && geminiService.parseOrder) {
                        return await geminiService.parseOrder(orderText, options.isRealtime || false);
                    }
                    throw new Error('Gemini服务不可用');
                };
            }
        }
        
        /**
         * 修复Gemini服务实例
         * @private
         */
        fixGeminiServiceInstance() {
            // 确保window.OTA.geminiService存在
            if (!window.OTA.geminiService) {
                // 尝试从协调器获取或创建新实例
                const coordinator = window.OTA?.gemini?.getGeminiCoordinator?.();
                if (coordinator) {
                    window.OTA.geminiService = this.createCompatibleGeminiService(coordinator);
                } else {
                    window.OTA.geminiService = this.createFallbackGeminiService();
                }
            }
            
            // 确保服务具有必要的方法
            const service = window.OTA.geminiService;
            if (service) {
                // 确保parseOrder方法存在
                if (!service.parseOrder) {
                    service.parseOrder = async (orderText, isRealtime = false) => {
                        const coordinator = service.getCoordinator?.() || window.getGeminiCoordinator?.();
                        if (coordinator && coordinator.parseOrderCompatible) {
                            return await coordinator.parseOrderCompatible(orderText, isRealtime);
                        }
                        return this.fallbackParseOrder(orderText, isRealtime);
                    };
                }
                
                // 确保isAvailable方法存在
                if (!service.isAvailable) {
                    service.isAvailable = () => {
                        const coordinator = service.getCoordinator?.() || window.getGeminiCoordinator?.();
                        return !!coordinator;
                    };
                }
                
                // 确保其他必要方法存在
                if (!service.parseMultipleOrders) {
                    service.parseMultipleOrders = async (orderSegments) => {
                        const coordinator = service.getCoordinator?.() || window.getGeminiCoordinator?.();
                        if (coordinator && coordinator.parseMultipleOrdersCompatible) {
                            return await coordinator.parseMultipleOrdersCompatible(orderSegments);
                        }
                        return { success: false, error: 'Coordinator not available' };
                    };
                }
                
                if (!service.getLanguagesIdArray) {
                    service.getLanguagesIdArray = (text, customerName) => {
                        // 简单的语言检测逻辑
                        if (/[\u4e00-\u9fff]/.test(text + customerName)) {
                            return [4]; // 中文
                        }
                        return [2]; // 英文
                    };
                }
            }
        }
        
        /**
         * 修复协调器集成
         * @private
         */
        fixCoordinatorIntegration() {
            // 确保协调器实例存在
            if (!window.OTA.gemini.coordinator && window.OTA.gemini.getGeminiCoordinator) {
                try {
                    window.OTA.gemini.coordinator = window.OTA.gemini.getGeminiCoordinator();
                } catch (error) {
                    this.logger.logWarning('无法创建协调器实例', error);
                }
            }
        }
        
        /**
         * 创建兼容的Gemini服务
         * @param {Object} coordinator - 协调器实例
         * @returns {Object} 兼容的服务实例
         * @private
         */
        createCompatibleGeminiService(coordinator) {
            return {
                version: '2.0.0',
                coordinator: coordinator,
                
                getCoordinator() {
                    return this.coordinator;
                },
                
                async parseOrder(orderText, isRealtime = false) {
                    if (this.coordinator && this.coordinator.parseOrderCompatible) {
                        return await this.coordinator.parseOrderCompatible(orderText, isRealtime);
                    }
                    return this.fallbackParseOrder(orderText, isRealtime);
                },
                
                async parseMultipleOrders(orderSegments) {
                    if (this.coordinator && this.coordinator.parseMultipleOrdersCompatible) {
                        return await this.coordinator.parseMultipleOrdersCompatible(orderSegments);
                    }
                    return { success: false, error: 'Coordinator not available' };
                },
                
                isAvailable() {
                    return !!this.coordinator;
                },
                
                getLanguagesIdArray(text, customerName) {
                    // 简单的语言检测逻辑
                    if (/[\u4e00-\u9fff]/.test(text + customerName)) {
                        return [4]; // 中文
                    }
                    return [2]; // 英文
                },
                
                fallbackParseOrder(orderText, isRealtime) {
                    return {
                        success: false,
                        error: 'Gemini coordinator not available',
                        data: null
                    };
                }
            };
        }
        
        /**
         * 创建降级Gemini服务
         * @returns {Object} 降级服务实例
         * @private
         */
        createFallbackGeminiService() {
            return {
                version: '2.0.0-fallback',
                
                async parseOrder(orderText, isRealtime = false) {
                    return {
                        success: false,
                        error: 'Gemini service not properly initialized',
                        data: null
                    };
                },
                
                async parseMultipleOrders(orderSegments) {
                    return {
                        success: false,
                        error: 'Gemini service not properly initialized',
                        orders: []
                    };
                },
                
                isAvailable() {
                    return false;
                },
                
                getLanguagesIdArray(text, customerName) {
                    // 简单的语言检测逻辑
                    if (/[\u4e00-\u9fff]/.test(text + customerName)) {
                        return [4]; // 中文
                    }
                    return [2]; // 英文
                }
            };
        }
        
        /**
         * 验证集成
         * @private
         */
        validateIntegration() {
            const results = {};
            
            // 检查Gemini服务实例
            results.geminiServiceInstance = {
                exists: !!window.OTA.geminiService,
                hasParseOrder: !!(window.OTA.geminiService?.parseOrder),
                hasIsAvailable: !!(window.OTA.geminiService?.isAvailable),
                isAvailable: window.OTA.geminiService?.isAvailable?.() || false
            };
            
            // 检查协调器实例
            results.coordinatorInstance = {
                exists: !!(window.OTA?.gemini?.coordinator || window.OTA?.gemini?.getGeminiCoordinator?.()),
                accessible: !!window.getGeminiCoordinator?.()
            };
            
            // 检查全局函数
            results.globalFunctions = {
                getGeminiService: typeof window.getGeminiService === 'function',
                getGeminiCoordinator: typeof window.getGeminiCoordinator === 'function',
                parseOrderWithGemini: typeof window.parseOrderWithGemini === 'function'
            };
            
            // 检查服务定位器集成
            results.serviceLocatorIntegration = {
                serviceLocatorExists: !!window.OTA.serviceLocator,
                canGetGeminiService: !!window.getGeminiService?.()
            };
            
            // 检查multi-order-manager集成
            results.multiOrderManagerIntegration = {
                canGetGeminiService: !!(window.getGeminiService?.()),
                serviceReturnsValidInstance: !!(window.getGeminiService?.()?.parseOrder)
            };
            
            // 检查form-manager集成
            results.formManagerIntegration = {
                otaGeminiServiceExists: !!(window.OTA?.geminiService),
                hasLanguageMethod: !!(window.OTA?.geminiService?.getLanguagesIdArray)
            };
            
            // 记录验证结果
            this.logger.log('系统集成验证结果', 'info', results);
            
            // 检查是否有失败项
            const failures = this.findValidationFailures(results);
            if (failures.length > 0) {
                this.logger.logWarning('发现集成问题', failures);
            } else {
                this.logger.log('所有集成检查通过', 'success');
            }
            
            return results;
        }
        
        /**
         * 查找验证失败项
         * @param {Object} results - 验证结果
         * @returns {Array} 失败项列表
         * @private
         */
        findValidationFailures(results) {
            const failures = [];
            
            // 检查关键项
            if (!results.geminiServiceInstance.exists) {
                failures.push('Gemini服务实例不存在');
            }
            
            if (!results.globalFunctions.getGeminiService) {
                failures.push('getGeminiService全局函数不存在');
            }
            
            if (!results.serviceLocatorIntegration.canGetGeminiService) {
                failures.push('服务定位器无法获取Gemini服务');
            }
            
            if (!results.multiOrderManagerIntegration.serviceReturnsValidInstance) {
                failures.push('multi-order-manager无法获取有效的Gemini服务实例');
            }
            
            return failures;
        }
        
        /**
         * 获取集成状态
         * @returns {Object} 集成状态
         */
        getIntegrationStatus() {
            return {
                fixApplied: this.fixApplied,
                geminiServiceAvailable: !!(window.OTA?.geminiService?.isAvailable?.()),
                coordinatorAvailable: !!window.getGeminiCoordinator?.(),
                globalFunctionsReady: !!(window.getGeminiService && window.parseOrderWithGemini),
                lastValidation: this.validateIntegration()
            };
        }
    }
    
    // 创建全局实例
    const systemIntegrationFix = new SystemIntegrationFix();
    
    // 注册到全局命名空间
    window.OTA.gemini.integration.SystemIntegrationFix = SystemIntegrationFix;
    window.OTA.gemini.integration.systemIntegrationFix = systemIntegrationFix;
    
    // 便捷访问函数
    window.OTA.gemini.integration.getIntegrationStatus = function() {
        return systemIntegrationFix.getIntegrationStatus();
    };
    
    window.OTA.gemini.integration.validateIntegration = function() {
        return systemIntegrationFix.validateIntegration();
    };
    
    // 注册到服务注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('system-integration-fix', systemIntegrationFix, {
            dependencies: ['logger'],
            description: '系统集成修复器，确保新的模块化Gemini架构与现有系统完全兼容'
        });
    }
    
    console.log('✅ 系统集成修复已加载');
    
})();
