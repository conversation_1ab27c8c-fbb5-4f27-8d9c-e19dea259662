/**
 * @OTA_SERVICE OTA处理器路由管理器
 * 🏷️ 标签: @PROCESSOR_ROUTER
 * 📝 说明: 智能路由系统，根据OTA渠道动态选择和管理处理器实例
 * 🎯 功能: 动态路由、实例缓存、工厂管理、性能监控
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.core = window.OTA.gemini.core || {};

(function() {
    'use strict';

    /**
     * OTA处理器路由管理器类
     * 负责根据识别的OTA渠道，动态选择和管理对应的处理器实例
     */
    class ProcessorRouter {
        constructor() {
            this.logger = window.getLogger?.() || console;
            
            // 处理器实例缓存
            this.processorCache = new Map();
            
            // 处理器工厂映射
            this.processorFactories = new Map();
            
            // 路由配置
            this.routingConfig = this.loadRoutingConfig();
            
            // 性能统计
            this.stats = {
                totalRoutes: 0,
                cacheHits: 0,
                cacheMisses: 0,
                processorUsage: new Map(),
                averageRoutingTime: 0
            };

            // 初始化处理器工厂
            this.initializeProcessorFactories();
        }

        /**
         * 加载路由配置
         * @returns {Object} 路由配置
         */
        loadRoutingConfig() {
            return {
                // 渠道到处理器的映射
                channelMapping: {
                    'Chong Dealer': 'ChongDealerProcessor',
                    'Klook': 'KlookProcessor',
                    'Ctrip': 'CtripProcessor',
                    'KKday': 'KKdayProcessor',
                    'Agoda': 'AgodaProcessor',
                    'Booking.com': 'BookingProcessor',
                    'generic': 'GenericProcessor',
                    'unknown': 'GenericProcessor'
                },

                // 处理器优先级（用于降级处理）
                processorPriority: {
                    'ChongDealerProcessor': 10,
                    'KlookProcessor': 9,
                    'CtripProcessor': 9,
                    'KKdayProcessor': 8,
                    'AgodaProcessor': 8,
                    'BookingProcessor': 8,
                    'GenericProcessor': 1
                },

                // 缓存配置
                cacheConfig: {
                    maxCacheSize: 10,           // 最大缓存处理器数量
                    cacheTimeout: 30 * 60 * 1000, // 缓存超时时间（30分钟）
                    enableCache: true           // 是否启用缓存
                },

                // 降级策略
                fallbackStrategy: {
                    enableFallback: true,
                    fallbackProcessor: 'GenericProcessor',
                    maxRetries: 2
                }
            };
        }

        /**
         * 初始化处理器工厂
         */
        initializeProcessorFactories() {
            // 注册已知的处理器工厂
            this.registerProcessorFactory('ChongDealerProcessor', () => {
                const ChongDealerProcessor = window.OTA?.gemini?.processors?.ChongDealerProcessor;
                return ChongDealerProcessor ? new ChongDealerProcessor() : null;
            });

            this.registerProcessorFactory('KlookProcessor', () => {
                const KlookProcessor = window.OTA?.gemini?.processors?.KlookProcessor;
                return KlookProcessor ? new KlookProcessor() : null;
            });

            this.registerProcessorFactory('CtripProcessor', () => {
                const CtripProcessor = window.OTA?.gemini?.processors?.CtripProcessor;
                return CtripProcessor ? new CtripProcessor() : null;
            });

            this.registerProcessorFactory('GenericProcessor', () => {
                const GenericProcessor = window.OTA?.gemini?.processors?.GenericProcessor;
                return GenericProcessor ? new GenericProcessor() : null;
            });

            this.logger.log('处理器工厂初始化完成', 'info');
        }

        /**
         * 注册处理器工厂
         * @param {string} processorName - 处理器名称
         * @param {Function} factory - 工厂函数
         */
        registerProcessorFactory(processorName, factory) {
            this.processorFactories.set(processorName, factory);
            this.logger.log(`注册处理器工厂: ${processorName}`, 'info');
        }

        /**
         * 路由到对应的处理器 - 主要入口方法
         * @param {string} channelName - OTA渠道名称
         * @param {Object} options - 路由选项
         * @returns {Promise<Object>} 处理器实例
         */
        async routeToProcessor(channelName, options = {}) {
            const startTime = Date.now();
            this.stats.totalRoutes++;

            try {
                // 1. 获取处理器名称
                const processorName = this.getProcessorName(channelName);
                
                // 2. 尝试从缓存获取
                let processor = this.getFromCache(processorName);
                
                if (processor) {
                    this.stats.cacheHits++;
                    this.updateRoutingStats(processorName, Date.now() - startTime);
                    return processor;
                }

                // 3. 创建新的处理器实例
                processor = await this.createProcessor(processorName, options);
                
                if (!processor) {
                    // 4. 降级处理
                    processor = await this.handleFallback(processorName, options);
                }

                // 5. 添加到缓存
                if (processor && this.routingConfig.cacheConfig.enableCache) {
                    this.addToCache(processorName, processor);
                }

                this.stats.cacheMisses++;
                this.updateRoutingStats(processorName, Date.now() - startTime);
                
                return processor;

            } catch (error) {
                this.logger.logError(`处理器路由失败: ${channelName}`, error);
                
                // 错误降级
                return await this.handleFallback('GenericProcessor', options);
            }
        }

        /**
         * 获取处理器名称
         * @param {string} channelName - 渠道名称
         * @returns {string} 处理器名称
         */
        getProcessorName(channelName) {
            const mapping = this.routingConfig.channelMapping;
            return mapping[channelName] || mapping['generic'];
        }

        /**
         * 从缓存获取处理器
         * @param {string} processorName - 处理器名称
         * @returns {Object|null} 处理器实例
         */
        getFromCache(processorName) {
            if (!this.routingConfig.cacheConfig.enableCache) {
                return null;
            }

            const cacheEntry = this.processorCache.get(processorName);
            
            if (!cacheEntry) {
                return null;
            }

            // 检查缓存是否过期
            const now = Date.now();
            const cacheTimeout = this.routingConfig.cacheConfig.cacheTimeout;
            
            if (now - cacheEntry.timestamp > cacheTimeout) {
                this.processorCache.delete(processorName);
                this.logger.log(`处理器缓存过期: ${processorName}`, 'info');
                return null;
            }

            this.logger.log(`从缓存获取处理器: ${processorName}`, 'info');
            return cacheEntry.processor;
        }

        /**
         * 创建处理器实例
         * @param {string} processorName - 处理器名称
         * @param {Object} options - 创建选项
         * @returns {Promise<Object|null>} 处理器实例
         */
        async createProcessor(processorName, options = {}) {
            const factory = this.processorFactories.get(processorName);
            
            if (!factory) {
                this.logger.logError(`未找到处理器工厂: ${processorName}`);
                return null;
            }

            try {
                const processor = factory(options);
                
                if (!processor) {
                    this.logger.logError(`处理器工厂返回null: ${processorName}`);
                    return null;
                }

                // 验证处理器接口
                if (!this.validateProcessorInterface(processor)) {
                    this.logger.logError(`处理器接口验证失败: ${processorName}`);
                    return null;
                }

                this.logger.log(`创建处理器实例: ${processorName}`, 'info');
                return processor;

            } catch (error) {
                this.logger.logError(`创建处理器失败: ${processorName}`, error);
                return null;
            }
        }

        /**
         * 验证处理器接口
         * @param {Object} processor - 处理器实例
         * @returns {boolean} 是否有效
         */
        validateProcessorInterface(processor) {
            // 检查必需的方法
            const requiredMethods = ['processOrder', 'getStats', 'getVersion'];
            
            for (const method of requiredMethods) {
                if (typeof processor[method] !== 'function') {
                    this.logger.logError(`处理器缺少必需方法: ${method}`);
                    return false;
                }
            }

            return true;
        }

        /**
         * 添加到缓存
         * @param {string} processorName - 处理器名称
         * @param {Object} processor - 处理器实例
         */
        addToCache(processorName, processor) {
            const cacheConfig = this.routingConfig.cacheConfig;
            
            // 检查缓存大小限制
            if (this.processorCache.size >= cacheConfig.maxCacheSize) {
                // 移除最旧的缓存项
                const oldestKey = this.processorCache.keys().next().value;
                this.processorCache.delete(oldestKey);
                this.logger.log(`移除最旧的缓存项: ${oldestKey}`, 'info');
            }

            // 添加到缓存
            this.processorCache.set(processorName, {
                processor,
                timestamp: Date.now()
            });

            this.logger.log(`添加处理器到缓存: ${processorName}`, 'info');
        }

        /**
         * 处理降级
         * @param {string} failedProcessorName - 失败的处理器名称
         * @param {Object} options - 选项
         * @returns {Promise<Object>} 降级处理器
         */
        async handleFallback(failedProcessorName, options = {}) {
            const fallbackConfig = this.routingConfig.fallbackStrategy;
            
            if (!fallbackConfig.enableFallback) {
                throw new Error(`处理器不可用且降级被禁用: ${failedProcessorName}`);
            }

            this.logger.log(`启动降级处理: ${failedProcessorName} -> ${fallbackConfig.fallbackProcessor}`, 'warning');

            // 尝试创建降级处理器
            const fallbackProcessor = await this.createProcessor(fallbackConfig.fallbackProcessor, options);
            
            if (!fallbackProcessor) {
                throw new Error(`降级处理器也不可用: ${fallbackConfig.fallbackProcessor}`);
            }

            return fallbackProcessor;
        }

        /**
         * 批量路由处理
         * @param {Array} requests - 请求数组 [{channelName, options}]
         * @returns {Promise<Array>} 处理器数组
         */
        async batchRoute(requests) {
            const results = [];
            
            for (const request of requests) {
                try {
                    const processor = await this.routeToProcessor(request.channelName, request.options);
                    results.push({
                        success: true,
                        processor,
                        channelName: request.channelName
                    });
                } catch (error) {
                    results.push({
                        success: false,
                        error: error.message,
                        channelName: request.channelName
                    });
                }
            }

            return results;
        }

        /**
         * 获取可用的处理器列表
         * @returns {Array} 可用处理器列表
         */
        getAvailableProcessors() {
            const available = [];
            
            for (const [processorName, factory] of this.processorFactories) {
                try {
                    const testInstance = factory();
                    if (testInstance) {
                        available.push({
                            name: processorName,
                            version: testInstance.getVersion?.() || 'unknown',
                            cached: this.processorCache.has(processorName)
                        });
                    }
                } catch (error) {
                    // 忽略测试失败的处理器
                }
            }

            return available;
        }

        /**
         * 清理缓存
         * @param {string} [processorName] - 特定处理器名称，不提供则清理所有
         */
        clearCache(processorName = null) {
            if (processorName) {
                this.processorCache.delete(processorName);
                this.logger.log(`清理处理器缓存: ${processorName}`, 'info');
            } else {
                this.processorCache.clear();
                this.logger.log('清理所有处理器缓存', 'info');
            }
        }

        /**
         * 更新路由统计
         * @param {string} processorName - 处理器名称
         * @param {number} routingTime - 路由时间
         */
        updateRoutingStats(processorName, routingTime) {
            // 更新处理器使用统计
            const usage = this.stats.processorUsage.get(processorName) || 0;
            this.stats.processorUsage.set(processorName, usage + 1);

            // 更新平均路由时间
            const totalTime = this.stats.averageRoutingTime * (this.stats.totalRoutes - 1) + routingTime;
            this.stats.averageRoutingTime = Math.round(totalTime / this.stats.totalRoutes);
        }

        /**
         * 获取路由统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                ...this.stats,
                cacheHitRate: this.stats.totalRoutes > 0 ? 
                    ((this.stats.cacheHits / this.stats.totalRoutes) * 100).toFixed(2) + '%' : '0%',
                processorUsage: Object.fromEntries(this.stats.processorUsage),
                cacheSize: this.processorCache.size,
                availableProcessors: this.getAvailableProcessors().length
            };
        }

        /**
         * 重置统计信息
         */
        resetStats() {
            this.stats = {
                totalRoutes: 0,
                cacheHits: 0,
                cacheMisses: 0,
                processorUsage: new Map(),
                averageRoutingTime: 0
            };
        }

        /**
         * 获取路由配置
         * @returns {Object} 路由配置
         */
        getRoutingConfig() {
            return { ...this.routingConfig };
        }

        /**
         * 更新路由配置
         * @param {Object} newConfig - 新配置
         */
        updateRoutingConfig(newConfig) {
            this.routingConfig = { ...this.routingConfig, ...newConfig };
            this.logger.log('路由配置已更新', 'info');
        }
    }

    // 创建全局实例
    function getProcessorRouter() {
        if (!window.OTA.gemini.core.processorRouter) {
            window.OTA.gemini.core.processorRouter = new ProcessorRouter();
        }
        return window.OTA.gemini.core.processorRouter;
    }

    // 暴露到全局命名空间
    window.OTA.gemini.core.ProcessorRouter = ProcessorRouter;
    window.OTA.gemini.core.getProcessorRouter = getProcessorRouter;

    // 向后兼容
    window.getProcessorRouter = getProcessorRouter;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('processorRouter', getProcessorRouter(), '@PROCESSOR_ROUTER');
        window.OTA.Registry.registerFactory('getProcessorRouter', getProcessorRouter, '@PROCESSOR_ROUTER_FACTORY');
    }

    console.log('✅ OTA处理器路由管理器已加载');

})();
