# Gemini AI系统性能监控文档

## 📋 文档概述

本文档详细说明了Gemini AI系统的性能监控体系，包括关键指标定义、监控方法、性能调优策略和告警机制。

**适用版本**: 2.0.0 (重构版本)  
**目标用户**: 系统管理员、运维工程师、开发者  
**更新时间**: 2024-01-01

## 📊 关键性能指标 (KPIs)

### 1. 响应时间指标

#### 处理时间 (Processing Time)
- **定义**: 从接收请求到返回结果的总时间
- **单位**: 毫秒 (ms)
- **目标值**: < 8,000ms (8秒)
- **告警阈值**: > 15,000ms (15秒)

```javascript
// 获取处理时间指标
const coordinator = window.OTA.gemini.getGeminiCoordinator();
const metrics = coordinator.getMetrics();
console.log('平均处理时间:', metrics.processingStats.averageProcessingTime + 'ms');
```

#### API响应时间 (API Response Time)
- **定义**: Gemini API调用的响应时间
- **单位**: 毫秒 (ms)
- **目标值**: < 5,000ms (5秒)
- **告警阈值**: > 10,000ms (10秒)

### 2. 吞吐量指标

#### 请求处理率 (Request Processing Rate)
- **定义**: 每分钟处理的请求数量
- **单位**: 请求/分钟 (RPM)
- **目标值**: > 60 RPM
- **告警阈值**: < 30 RPM

#### 并发处理能力 (Concurrent Processing)
- **定义**: 同时处理的请求数量
- **单位**: 个
- **目标值**: 15-20个
- **告警阈值**: > 25个

```javascript
// 监控并发处理
function monitorConcurrency() {
    const metrics = coordinator.getMetrics();
    console.log('当前并发数:', metrics.concurrencyStats.current);
    console.log('峰值并发数:', metrics.concurrencyStats.peak);
    console.log('队列长度:', metrics.concurrencyStats.queueLength);
}
```

### 3. 成功率指标

#### 处理成功率 (Success Rate)
- **定义**: 成功处理的请求占总请求的比例
- **单位**: 百分比 (%)
- **目标值**: > 95%
- **告警阈值**: < 90%

#### 缓存命中率 (Cache Hit Rate)
- **定义**: 缓存命中的请求占总请求的比例
- **单位**: 百分比 (%)
- **目标值**: > 70%
- **告警阈值**: < 50%

```javascript
// 监控成功率和缓存
function monitorSuccessRates() {
    const metrics = coordinator.getMetrics();
    
    const successRate = metrics.processingStats.successfulRequests / 
                       metrics.processingStats.totalRequests * 100;
    const cacheHitRate = metrics.cacheStats.hitRate * 100;
    
    console.log('处理成功率:', successRate.toFixed(2) + '%');
    console.log('缓存命中率:', cacheHitRate.toFixed(2) + '%');
}
```

### 4. 资源使用指标

#### 内存使用量 (Memory Usage)
- **定义**: 系统占用的内存大小
- **单位**: MB
- **目标值**: < 150MB
- **告警阈值**: > 200MB

#### 缓存大小 (Cache Size)
- **定义**: 缓存中存储的条目数量
- **单位**: 个
- **目标值**: 1000-3000个
- **告警阈值**: > 5000个

```javascript
// 监控资源使用
function monitorResourceUsage() {
    // 内存使用
    if (performance.memory) {
        const memoryMB = performance.memory.usedJSHeapSize / 1024 / 1024;
        console.log('内存使用:', memoryMB.toFixed(2) + 'MB');
    }
    
    // 缓存大小
    const cacheSize = coordinator.cache?.size || 0;
    console.log('缓存条目数:', cacheSize);
}
```

## 🔍 监控实现

### 1. 实时监控仪表板

#### 基础监控面板
```javascript
/**
 * 创建实时监控仪表板
 */
function createMonitoringDashboard() {
    const coordinator = window.OTA.gemini.getGeminiCoordinator();
    const metrics = coordinator.getMetrics();
    
    console.clear();
    console.log('='.repeat(60));
    console.log('🚀 Gemini AI系统实时监控仪表板');
    console.log('='.repeat(60));
    
    // 基础统计
    console.log('📊 基础统计:');
    console.log(`   总请求数: ${metrics.processingStats.totalRequests}`);
    console.log(`   成功请求: ${metrics.processingStats.successfulRequests}`);
    console.log(`   失败请求: ${metrics.processingStats.failedRequests}`);
    
    // 性能指标
    console.log('\n⚡ 性能指标:');
    console.log(`   平均响应时间: ${metrics.processingStats.averageProcessingTime}ms`);
    console.log(`   当前并发数: ${metrics.concurrencyStats.current}`);
    console.log(`   队列长度: ${metrics.concurrencyStats.queueLength}`);
    
    // 缓存统计
    console.log('\n💾 缓存统计:');
    console.log(`   命中次数: ${metrics.cacheStats.hits}`);
    console.log(`   未命中次数: ${metrics.cacheStats.misses}`);
    console.log(`   命中率: ${(metrics.cacheStats.hitRate * 100).toFixed(2)}%`);
    
    // 健康状态
    const healthStatus = assessSystemHealth(metrics);
    console.log(`\n🏥 系统健康: ${healthStatus.status}`);
    if (healthStatus.warnings.length > 0) {
        console.log('⚠️  警告:');
        healthStatus.warnings.forEach(warning => {
            console.log(`   - ${warning}`);
        });
    }
    
    console.log('='.repeat(60));
    console.log(`📅 更新时间: ${new Date().toLocaleString()}`);
}

// 每30秒更新一次仪表板
setInterval(createMonitoringDashboard, 30000);
```

#### 系统健康评估
```javascript
/**
 * 评估系统健康状态
 * @param {Object} metrics - 性能指标
 * @returns {Object} 健康状态
 */
function assessSystemHealth(metrics) {
    const warnings = [];
    let status = '🟢 健康';
    
    // 检查响应时间
    if (metrics.processingStats.averageProcessingTime > 15000) {
        warnings.push('响应时间过长 (>15秒)');
        status = '🟡 警告';
    }
    
    // 检查成功率
    const successRate = metrics.processingStats.successfulRequests / 
                       metrics.processingStats.totalRequests;
    if (successRate < 0.9) {
        warnings.push(`成功率过低 (${(successRate * 100).toFixed(2)}%)`);
        status = '🔴 异常';
    }
    
    // 检查缓存命中率
    if (metrics.cacheStats.hitRate < 0.5) {
        warnings.push(`缓存命中率过低 (${(metrics.cacheStats.hitRate * 100).toFixed(2)}%)`);
        if (status === '🟢 健康') status = '🟡 警告';
    }
    
    // 检查并发数
    if (metrics.concurrencyStats.current > 25) {
        warnings.push(`并发数过高 (${metrics.concurrencyStats.current})`);
        status = '🔴 异常';
    }
    
    return { status, warnings };
}
```

### 2. 性能告警系统

#### 告警配置
```javascript
/**
 * 性能告警配置
 */
const ALERT_THRESHOLDS = {
    processingTime: 15000,          // 处理时间超过15秒
    successRate: 0.9,               // 成功率低于90%
    cacheHitRate: 0.5,              // 缓存命中率低于50%
    concurrentRequests: 25,         // 并发请求超过25个
    memoryUsage: 200 * 1024 * 1024, // 内存使用超过200MB
    queueLength: 50                 // 队列长度超过50
};

/**
 * 检查告警条件
 */
function checkAlerts() {
    const coordinator = window.OTA.gemini.getGeminiCoordinator();
    const metrics = coordinator.getMetrics();
    const alerts = [];
    
    // 处理时间告警
    if (metrics.processingStats.averageProcessingTime > ALERT_THRESHOLDS.processingTime) {
        alerts.push({
            type: 'PERFORMANCE',
            level: 'WARNING',
            message: `处理时间过长: ${metrics.processingStats.averageProcessingTime}ms`,
            threshold: ALERT_THRESHOLDS.processingTime,
            current: metrics.processingStats.averageProcessingTime
        });
    }
    
    // 成功率告警
    const successRate = metrics.processingStats.successfulRequests / 
                       metrics.processingStats.totalRequests;
    if (successRate < ALERT_THRESHOLDS.successRate) {
        alerts.push({
            type: 'RELIABILITY',
            level: 'CRITICAL',
            message: `成功率过低: ${(successRate * 100).toFixed(2)}%`,
            threshold: ALERT_THRESHOLDS.successRate * 100,
            current: successRate * 100
        });
    }
    
    // 缓存命中率告警
    if (metrics.cacheStats.hitRate < ALERT_THRESHOLDS.cacheHitRate) {
        alerts.push({
            type: 'PERFORMANCE',
            level: 'WARNING',
            message: `缓存命中率过低: ${(metrics.cacheStats.hitRate * 100).toFixed(2)}%`,
            threshold: ALERT_THRESHOLDS.cacheHitRate * 100,
            current: metrics.cacheStats.hitRate * 100
        });
    }
    
    // 并发数告警
    if (metrics.concurrencyStats.current > ALERT_THRESHOLDS.concurrentRequests) {
        alerts.push({
            type: 'CAPACITY',
            level: 'CRITICAL',
            message: `并发数过高: ${metrics.concurrencyStats.current}`,
            threshold: ALERT_THRESHOLDS.concurrentRequests,
            current: metrics.concurrencyStats.current
        });
    }
    
    // 处理告警
    if (alerts.length > 0) {
        handleAlerts(alerts);
    }
    
    return alerts;
}

/**
 * 处理告警
 * @param {Array} alerts - 告警列表
 */
function handleAlerts(alerts) {
    alerts.forEach(alert => {
        console.warn(`🚨 ${alert.level} 告警: ${alert.message}`);
        
        // 根据告警类型采取相应措施
        switch (alert.type) {
            case 'PERFORMANCE':
                suggestPerformanceOptimization(alert);
                break;
            case 'RELIABILITY':
                suggestReliabilityImprovement(alert);
                break;
            case 'CAPACITY':
                suggestCapacityAdjustment(alert);
                break;
        }
    });
}
```

### 3. 性能数据收集

#### 历史数据记录
```javascript
/**
 * 性能数据收集器
 */
class PerformanceDataCollector {
    constructor() {
        this.data = [];
        this.maxRecords = 1000; // 最多保存1000条记录
        this.collectInterval = 60000; // 每分钟收集一次
        
        this.startCollection();
    }
    
    /**
     * 开始数据收集
     */
    startCollection() {
        setInterval(() => {
            this.collectData();
        }, this.collectInterval);
    }
    
    /**
     * 收集性能数据
     */
    collectData() {
        const coordinator = window.OTA.gemini.getGeminiCoordinator();
        const metrics = coordinator.getMetrics();
        
        const dataPoint = {
            timestamp: Date.now(),
            processingTime: metrics.processingStats.averageProcessingTime,
            successRate: metrics.processingStats.successfulRequests / 
                        metrics.processingStats.totalRequests,
            cacheHitRate: metrics.cacheStats.hitRate,
            concurrentRequests: metrics.concurrencyStats.current,
            queueLength: metrics.concurrencyStats.queueLength,
            totalRequests: metrics.processingStats.totalRequests
        };
        
        this.data.push(dataPoint);
        
        // 保持数据量在限制范围内
        if (this.data.length > this.maxRecords) {
            this.data.shift();
        }
    }
    
    /**
     * 获取性能趋势
     * @param {number} minutes - 分析的时间范围（分钟）
     * @returns {Object} 趋势分析结果
     */
    getTrend(minutes = 60) {
        const cutoffTime = Date.now() - (minutes * 60 * 1000);
        const recentData = this.data.filter(d => d.timestamp > cutoffTime);
        
        if (recentData.length < 2) {
            return { trend: 'insufficient_data' };
        }
        
        const first = recentData[0];
        const last = recentData[recentData.length - 1];
        
        return {
            trend: 'available',
            processingTime: {
                change: last.processingTime - first.processingTime,
                trend: last.processingTime > first.processingTime ? 'increasing' : 'decreasing'
            },
            successRate: {
                change: last.successRate - first.successRate,
                trend: last.successRate > first.successRate ? 'improving' : 'declining'
            },
            cacheHitRate: {
                change: last.cacheHitRate - first.cacheHitRate,
                trend: last.cacheHitRate > first.cacheHitRate ? 'improving' : 'declining'
            }
        };
    }
    
    /**
     * 生成性能报告
     * @returns {Object} 性能报告
     */
    generateReport() {
        if (this.data.length === 0) {
            return { error: '无性能数据' };
        }
        
        const latest = this.data[this.data.length - 1];
        const trend = this.getTrend(60);
        
        return {
            timestamp: new Date().toISOString(),
            current: latest,
            trend: trend,
            summary: {
                dataPoints: this.data.length,
                timeRange: `${Math.round((latest.timestamp - this.data[0].timestamp) / 60000)} 分钟`,
                averageProcessingTime: this.data.reduce((sum, d) => sum + d.processingTime, 0) / this.data.length,
                averageSuccessRate: this.data.reduce((sum, d) => sum + d.successRate, 0) / this.data.length,
                averageCacheHitRate: this.data.reduce((sum, d) => sum + d.cacheHitRate, 0) / this.data.length
            }
        };
    }
}

// 创建全局性能数据收集器
window.OTA = window.OTA || {};
window.OTA.performanceCollector = new PerformanceDataCollector();
```

## 🔧 性能调优策略

### 1. 缓存优化
```javascript
// 优化缓存配置
function optimizeCache() {
    const coordinator = window.OTA.gemini.getGeminiCoordinator();
    
    // 根据使用情况调整缓存大小
    const metrics = coordinator.getMetrics();
    if (metrics.cacheStats.hitRate > 0.8) {
        // 命中率高，可以增加缓存大小
        coordinator.config.cache.maxSize = Math.min(coordinator.config.cache.maxSize * 1.5, 10000);
    } else if (metrics.cacheStats.hitRate < 0.3) {
        // 命中率低，检查缓存策略
        coordinator.config.cache.maxAge = Math.max(coordinator.config.cache.maxAge * 0.8, 5 * 60 * 1000);
    }
}
```

### 2. 并发控制优化
```javascript
// 动态调整并发数
function optimizeConcurrency() {
    const coordinator = window.OTA.gemini.getGeminiCoordinator();
    const metrics = coordinator.getMetrics();
    
    // 根据队列长度和响应时间调整并发数
    if (metrics.concurrencyStats.queueLength > 20 && 
        metrics.processingStats.averageProcessingTime < 8000) {
        // 队列长但响应快，可以增加并发
        coordinator.config.processing.maxConcurrent = Math.min(
            coordinator.config.processing.maxConcurrent + 2, 30
        );
    } else if (metrics.processingStats.averageProcessingTime > 12000) {
        // 响应慢，减少并发
        coordinator.config.processing.maxConcurrent = Math.max(
            coordinator.config.processing.maxConcurrent - 2, 5
        );
    }
}
```

### 3. 自动调优
```javascript
// 自动性能调优
function autoTunePerformance() {
    console.log('🔧 开始自动性能调优...');
    
    optimizeCache();
    optimizeConcurrency();
    
    // 清理过期缓存
    const coordinator = window.OTA.gemini.getGeminiCoordinator();
    if (coordinator.cache && typeof coordinator.cache.cleanup === 'function') {
        coordinator.cache.cleanup();
    }
    
    console.log('✅ 自动调优完成');
}

// 每10分钟执行一次自动调优
setInterval(autoTunePerformance, 10 * 60 * 1000);
```

## 📋 监控检查清单

### 日常监控
- [ ] 检查系统健康状态
- [ ] 查看响应时间趋势
- [ ] 监控成功率变化
- [ ] 检查缓存命中率
- [ ] 观察并发处理情况

### 周期性检查
- [ ] 分析性能趋势报告
- [ ] 检查告警历史
- [ ] 评估资源使用情况
- [ ] 优化配置参数
- [ ] 清理历史数据

### 问题排查
- [ ] 收集详细性能数据
- [ ] 分析错误日志
- [ ] 检查网络连接状态
- [ ] 验证API配置
- [ ] 测试降级机制

---

**文档版本**: 2.0.0  
**最后更新**: 2024-01-01  
**维护团队**: OTA系统开发组
