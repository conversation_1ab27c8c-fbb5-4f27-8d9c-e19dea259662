/**
 * 代码质量监控器
 * 监控代码复杂度、重复度、依赖健康度等质量指标
 * 
 * 功能特性:
 * - 代码复杂度分析
 * - 重复代码检测
 * - 依赖健康度评估
 * - 代码异味检测
 * - 质量趋势分析
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.core = window.OTA.core || {};

(function() {
    'use strict';

    /**
     * 代码质量监控器类
     */
    class CodeQualityMonitor {
        constructor() {
            // 质量指标配置
            this.qualityConfig = {
                complexity: {
                    maxCyclomaticComplexity: 10,
                    maxFunctionLength: 50,
                    maxParameterCount: 5,
                    maxNestingDepth: 4
                },
                duplication: {
                    maxDuplicateLines: 10,
                    similarityThreshold: 0.8,
                    minBlockSize: 5
                },
                dependencies: {
                    maxDependencies: 15,
                    maxCircularDependencies: 0,
                    maxDepthLevel: 5
                },
                maintainability: {
                    minMaintainabilityIndex: 60,
                    maxTechnicalDebt: 30,
                    maxCodeSmells: 5
                }
            };
            
            // 质量指标存储
            this.qualityMetrics = {
                complexity: new Map(),
                duplication: new Map(),
                dependencies: new Map(),
                maintainability: new Map(),
                trends: []
            };
            
            // 代码异味检测规则
            this.codeSmellRules = {
                longMethod: {
                    threshold: 50,
                    severity: 'WARNING'
                },
                longParameterList: {
                    threshold: 5,
                    severity: 'WARNING'
                },
                duplicateCode: {
                    threshold: 10,
                    severity: 'CRITICAL'
                },
                largeClass: {
                    threshold: 500,
                    severity: 'WARNING'
                },
                deadCode: {
                    enabled: true,
                    severity: 'INFO'
                },
                magicNumbers: {
                    enabled: true,
                    severity: 'INFO'
                }
            };
            
            // 监控状态
            this.monitoringState = {
                isActive: false,
                lastAnalysis: null,
                analysisCount: 0,
                qualityScore: 0,
                trendDirection: 'stable'
            };
            
            this.warningManager = null;
            this.initialize();
        }

        /**
         * 初始化监控器
         */
        initialize() {
            // 延迟获取依赖
            setTimeout(() => {
                this.warningManager = window.OTA?.core?.warningManager;
                
                // 启动定期质量检查
                this.startQualityMonitoring();
                
                console.log('✅ 代码质量监控器已初始化');
            }, 100);
        }

        /**
         * 启动质量监控
         */
        startQualityMonitoring() {
            this.monitoringState.isActive = true;
            
            // 立即执行一次分析
            this.performQualityAnalysis();
            
            // 定期执行质量分析
            setInterval(() => {
                this.performQualityAnalysis();
            }, 5 * 60 * 1000); // 每5分钟分析一次
            
            console.log('🔍 代码质量监控已启动');
        }

        /**
         * 执行质量分析
         */
        async performQualityAnalysis() {
            console.log('📊 开始代码质量分析...');
            
            try {
                // 分析代码复杂度
                const complexityResults = await this.analyzeComplexity();
                
                // 检测重复代码
                const duplicationResults = await this.detectDuplication();
                
                // 评估依赖健康度
                const dependencyResults = await this.assessDependencyHealth();
                
                // 检测代码异味
                const codeSmellResults = await this.detectCodeSmells();
                
                // 计算质量分数
                const qualityScore = this.calculateQualityScore({
                    complexity: complexityResults,
                    duplication: duplicationResults,
                    dependencies: dependencyResults,
                    codeSmells: codeSmellResults
                });
                
                // 更新监控状态
                this.updateMonitoringState(qualityScore);
                
                // 生成质量报告
                const report = this.generateQualityReport({
                    complexity: complexityResults,
                    duplication: duplicationResults,
                    dependencies: dependencyResults,
                    codeSmells: codeSmellResults,
                    qualityScore
                });
                
                console.log('✅ 代码质量分析完成，质量分数:', qualityScore);
                
                // 如果质量分数过低，发出警告
                if (qualityScore < 60) {
                    this.reportQualityIssue('low_quality_score', {
                        score: qualityScore,
                        threshold: 60,
                        report: report
                    });
                }
                
                return report;
                
            } catch (error) {
                console.error('代码质量分析失败:', error);
                throw error;
            }
        }

        /**
         * 分析代码复杂度
         */
        async analyzeComplexity() {
            const results = {
                functions: [],
                averageComplexity: 0,
                maxComplexity: 0,
                violationCount: 0
            };
            
            // 分析全局函数
            const globalFunctions = this.getGlobalFunctions();
            
            for (const funcName of globalFunctions) {
                try {
                    const func = window[funcName];
                    const funcStr = func.toString();
                    
                    const complexity = this.calculateCyclomaticComplexity(funcStr);
                    const lineCount = funcStr.split('\n').length;
                    const paramCount = this.getParameterCount(funcStr);
                    const nestingDepth = this.calculateNestingDepth(funcStr);
                    
                    const funcAnalysis = {
                        name: funcName,
                        complexity,
                        lineCount,
                        paramCount,
                        nestingDepth,
                        violations: []
                    };
                    
                    // 检查违规
                    if (complexity > this.qualityConfig.complexity.maxCyclomaticComplexity) {
                        funcAnalysis.violations.push('high_complexity');
                        results.violationCount++;
                    }
                    
                    if (lineCount > this.qualityConfig.complexity.maxFunctionLength) {
                        funcAnalysis.violations.push('long_function');
                        results.violationCount++;
                    }
                    
                    if (paramCount > this.qualityConfig.complexity.maxParameterCount) {
                        funcAnalysis.violations.push('too_many_parameters');
                        results.violationCount++;
                    }
                    
                    if (nestingDepth > this.qualityConfig.complexity.maxNestingDepth) {
                        funcAnalysis.violations.push('deep_nesting');
                        results.violationCount++;
                    }
                    
                    results.functions.push(funcAnalysis);
                    results.maxComplexity = Math.max(results.maxComplexity, complexity);
                    
                } catch (error) {
                    console.warn(`分析函数 ${funcName} 失败:`, error);
                }
            }
            
            // 计算平均复杂度
            if (results.functions.length > 0) {
                results.averageComplexity = results.functions.reduce((sum, f) => sum + f.complexity, 0) / results.functions.length;
            }
            
            // 存储结果
            this.qualityMetrics.complexity.set(Date.now(), results);
            
            return results;
        }

        /**
         * 计算圈复杂度
         */
        calculateCyclomaticComplexity(funcStr) {
            let complexity = 1; // 基础复杂度
            
            // 计算决策点
            const decisionPoints = [
                /\bif\b/g,
                /\belse\s+if\b/g,
                /\bwhile\b/g,
                /\bfor\b/g,
                /\bdo\b/g,
                /\bswitch\b/g,
                /\bcase\b/g,
                /\bcatch\b/g,
                /\?\s*.*\s*:/g, // 三元操作符
                /&&/g,
                /\|\|/g
            ];
            
            decisionPoints.forEach(pattern => {
                const matches = funcStr.match(pattern);
                if (matches) {
                    complexity += matches.length;
                }
            });
            
            return complexity;
        }

        /**
         * 获取参数数量
         */
        getParameterCount(funcStr) {
            const match = funcStr.match(/function[^(]*\(([^)]*)\)/);
            if (!match || !match[1].trim()) {
                return 0;
            }
            
            return match[1].split(',').filter(param => param.trim()).length;
        }

        /**
         * 计算嵌套深度
         */
        calculateNestingDepth(funcStr) {
            let maxDepth = 0;
            let currentDepth = 0;
            
            for (let i = 0; i < funcStr.length; i++) {
                const char = funcStr[i];
                if (char === '{') {
                    currentDepth++;
                    maxDepth = Math.max(maxDepth, currentDepth);
                } else if (char === '}') {
                    currentDepth--;
                }
            }
            
            return maxDepth;
        }

        /**
         * 检测重复代码
         */
        async detectDuplication() {
            const results = {
                duplicateBlocks: [],
                totalDuplicateLines: 0,
                duplicationRatio: 0,
                violationCount: 0
            };
            
            // 获取所有函数的代码块
            const codeBlocks = this.extractCodeBlocks();
            
            // 比较代码块相似性
            for (let i = 0; i < codeBlocks.length; i++) {
                for (let j = i + 1; j < codeBlocks.length; j++) {
                    const similarity = this.calculateSimilarity(codeBlocks[i].code, codeBlocks[j].code);
                    
                    if (similarity > this.qualityConfig.duplication.similarityThreshold) {
                        const duplicateBlock = {
                            block1: codeBlocks[i],
                            block2: codeBlocks[j],
                            similarity,
                            lineCount: Math.min(codeBlocks[i].lineCount, codeBlocks[j].lineCount)
                        };
                        
                        results.duplicateBlocks.push(duplicateBlock);
                        results.totalDuplicateLines += duplicateBlock.lineCount;
                        
                        if (duplicateBlock.lineCount > this.qualityConfig.duplication.maxDuplicateLines) {
                            results.violationCount++;
                        }
                    }
                }
            }
            
            // 计算重复率
            const totalLines = codeBlocks.reduce((sum, block) => sum + block.lineCount, 0);
            if (totalLines > 0) {
                results.duplicationRatio = results.totalDuplicateLines / totalLines;
            }
            
            // 存储结果
            this.qualityMetrics.duplication.set(Date.now(), results);
            
            return results;
        }

        /**
         * 提取代码块
         */
        extractCodeBlocks() {
            const blocks = [];
            const globalFunctions = this.getGlobalFunctions();
            
            globalFunctions.forEach(funcName => {
                try {
                    const func = window[funcName];
                    const funcStr = func.toString();
                    const lines = funcStr.split('\n');
                    
                    blocks.push({
                        name: funcName,
                        code: funcStr,
                        lineCount: lines.length,
                        type: 'function'
                    });
                } catch (error) {
                    // 忽略错误
                }
            });
            
            return blocks;
        }

        /**
         * 计算代码相似性
         */
        calculateSimilarity(code1, code2) {
            // 简化的相似性计算
            const normalize = (code) => {
                return code
                    .replace(/\s+/g, ' ')
                    .replace(/\/\*[\s\S]*?\*\//g, '')
                    .replace(/\/\/.*$/gm, '')
                    .toLowerCase()
                    .trim();
            };
            
            const norm1 = normalize(code1);
            const norm2 = normalize(code2);
            
            if (norm1.length === 0 || norm2.length === 0) {
                return 0;
            }
            
            // 使用Levenshtein距离计算相似性
            const distance = this.levenshteinDistance(norm1, norm2);
            const maxLength = Math.max(norm1.length, norm2.length);
            
            return 1 - (distance / maxLength);
        }

        /**
         * 计算Levenshtein距离
         */
        levenshteinDistance(str1, str2) {
            const matrix = [];
            
            for (let i = 0; i <= str2.length; i++) {
                matrix[i] = [i];
            }
            
            for (let j = 0; j <= str1.length; j++) {
                matrix[0][j] = j;
            }
            
            for (let i = 1; i <= str2.length; i++) {
                for (let j = 1; j <= str1.length; j++) {
                    if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                        matrix[i][j] = matrix[i - 1][j - 1];
                    } else {
                        matrix[i][j] = Math.min(
                            matrix[i - 1][j - 1] + 1,
                            matrix[i][j - 1] + 1,
                            matrix[i - 1][j] + 1
                        );
                    }
                }
            }
            
            return matrix[str2.length][str1.length];
        }

        /**
         * 评估依赖健康度
         */
        async assessDependencyHealth() {
            const results = {
                totalDependencies: 0,
                circularDependencies: [],
                unusedDependencies: [],
                missingDependencies: [],
                dependencyDepth: 0,
                healthScore: 100,
                violationCount: 0
            };
            
            try {
                // 分析OTA服务依赖
                const otaServices = window.OTA || {};
                const dependencyGraph = this.buildDependencyGraph(otaServices);
                
                results.totalDependencies = dependencyGraph.size;
                
                // 检测循环依赖
                results.circularDependencies = this.detectCircularDependencies(dependencyGraph);
                
                // 计算依赖深度
                results.dependencyDepth = this.calculateDependencyDepth(dependencyGraph);
                
                // 检查违规
                if (results.totalDependencies > this.qualityConfig.dependencies.maxDependencies) {
                    results.violationCount++;
                    results.healthScore -= 20;
                }
                
                if (results.circularDependencies.length > this.qualityConfig.dependencies.maxCircularDependencies) {
                    results.violationCount++;
                    results.healthScore -= 30;
                }
                
                if (results.dependencyDepth > this.qualityConfig.dependencies.maxDepthLevel) {
                    results.violationCount++;
                    results.healthScore -= 15;
                }
                
                // 存储结果
                this.qualityMetrics.dependencies.set(Date.now(), results);
                
            } catch (error) {
                console.error('依赖健康度评估失败:', error);
                results.healthScore = 0;
            }
            
            return results;
        }

        /**
         * 构建依赖图
         */
        buildDependencyGraph(services) {
            const graph = new Map();
            
            Object.keys(services).forEach(serviceName => {
                const service = services[serviceName];
                if (service && typeof service === 'object') {
                    const dependencies = this.extractServiceDependencies(service);
                    graph.set(serviceName, dependencies);
                }
            });
            
            return graph;
        }

        /**
         * 提取服务依赖
         */
        extractServiceDependencies(service) {
            const dependencies = [];
            const serviceStr = service.toString();
            
            // 查找OTA服务引用
            const otaMatches = serviceStr.match(/window\.OTA\.(\w+)/g);
            if (otaMatches) {
                otaMatches.forEach(match => {
                    const serviceName = match.replace('window.OTA.', '');
                    if (serviceName !== 'getService' && !dependencies.includes(serviceName)) {
                        dependencies.push(serviceName);
                    }
                });
            }
            
            // 查找getService调用
            const getServiceMatches = serviceStr.match(/getService\(['"`](\w+)['"`]\)/g);
            if (getServiceMatches) {
                getServiceMatches.forEach(match => {
                    const serviceName = match.match(/['"`](\w+)['"`]/)[1];
                    if (!dependencies.includes(serviceName)) {
                        dependencies.push(serviceName);
                    }
                });
            }
            
            return dependencies;
        }

        /**
         * 检测循环依赖
         */
        detectCircularDependencies(graph) {
            const cycles = [];
            const visited = new Set();
            const recursionStack = new Set();
            
            const dfs = (node, path) => {
                if (recursionStack.has(node)) {
                    const cycleStart = path.indexOf(node);
                    cycles.push(path.slice(cycleStart).concat(node));
                    return;
                }
                
                if (visited.has(node)) return;
                
                visited.add(node);
                recursionStack.add(node);
                
                const dependencies = graph.get(node) || [];
                dependencies.forEach(dep => {
                    if (graph.has(dep)) {
                        dfs(dep, path.concat(node));
                    }
                });
                
                recursionStack.delete(node);
            };
            
            graph.forEach((_, node) => {
                if (!visited.has(node)) {
                    dfs(node, []);
                }
            });
            
            return cycles;
        }

        /**
         * 计算依赖深度
         */
        calculateDependencyDepth(graph) {
            let maxDepth = 0;
            
            const calculateDepth = (node, visited = new Set()) => {
                if (visited.has(node)) return 0;
                
                visited.add(node);
                const dependencies = graph.get(node) || [];
                
                let depth = 0;
                dependencies.forEach(dep => {
                    if (graph.has(dep)) {
                        depth = Math.max(depth, calculateDepth(dep, new Set(visited)));
                    }
                });
                
                return depth + 1;
            };
            
            graph.forEach((_, node) => {
                maxDepth = Math.max(maxDepth, calculateDepth(node));
            });
            
            return maxDepth;
        }

        /**
         * 检测代码异味
         */
        async detectCodeSmells() {
            const results = {
                smells: [],
                totalSmells: 0,
                smellsByType: {},
                severity: 'INFO'
            };
            
            const globalFunctions = this.getGlobalFunctions();
            
            for (const funcName of globalFunctions) {
                try {
                    const func = window[funcName];
                    const funcStr = func.toString();
                    const smells = this.analyzeCodeSmells(funcName, funcStr);
                    
                    results.smells.push(...smells);
                    
                } catch (error) {
                    // 忽略错误
                }
            }
            
            // 统计异味类型
            results.smells.forEach(smell => {
                results.smellsByType[smell.type] = (results.smellsByType[smell.type] || 0) + 1;
            });
            
            results.totalSmells = results.smells.length;
            
            // 确定严重程度
            if (results.totalSmells > this.qualityConfig.maintainability.maxCodeSmells) {
                results.severity = results.totalSmells > 15 ? 'CRITICAL' : 'WARNING';
            }
            
            return results;
        }

        /**
         * 分析代码异味
         */
        analyzeCodeSmells(funcName, funcStr) {
            const smells = [];
            const lines = funcStr.split('\n');
            
            // 长方法
            if (lines.length > this.codeSmellRules.longMethod.threshold) {
                smells.push({
                    type: 'long_method',
                    function: funcName,
                    severity: this.codeSmellRules.longMethod.severity,
                    description: `方法过长 (${lines.length} 行)`,
                    suggestion: '考虑将方法拆分为更小的函数'
                });
            }
            
            // 长参数列表
            const paramCount = this.getParameterCount(funcStr);
            if (paramCount > this.codeSmellRules.longParameterList.threshold) {
                smells.push({
                    type: 'long_parameter_list',
                    function: funcName,
                    severity: this.codeSmellRules.longParameterList.severity,
                    description: `参数过多 (${paramCount} 个)`,
                    suggestion: '考虑使用对象参数或参数对象模式'
                });
            }
            
            // 魔法数字
            if (this.codeSmellRules.magicNumbers.enabled) {
                const magicNumbers = funcStr.match(/\b\d{2,}\b/g);
                if (magicNumbers && magicNumbers.length > 3) {
                    smells.push({
                        type: 'magic_numbers',
                        function: funcName,
                        severity: this.codeSmellRules.magicNumbers.severity,
                        description: `包含魔法数字: ${magicNumbers.slice(0, 3).join(', ')}...`,
                        suggestion: '使用命名常量替代魔法数字'
                    });
                }
            }
            
            // 重复代码（简化检测）
            const duplicatePatterns = funcStr.match(/(.{20,})\1/g);
            if (duplicatePatterns && duplicatePatterns.length > 0) {
                smells.push({
                    type: 'duplicate_code',
                    function: funcName,
                    severity: this.codeSmellRules.duplicateCode.severity,
                    description: '函数内存在重复代码',
                    suggestion: '提取重复代码为独立函数'
                });
            }
            
            return smells;
        }

        /**
         * 获取全局函数列表
         */
        getGlobalFunctions() {
            return Object.keys(window).filter(key => 
                typeof window[key] === 'function' && 
                !key.startsWith('webkit') && 
                !key.startsWith('chrome') &&
                !key.startsWith('moz') &&
                !key.startsWith('ms') &&
                key.length > 2
            );
        }

        /**
         * 计算质量分数
         */
        calculateQualityScore(analysisResults) {
            let score = 100;
            
            // 复杂度扣分
            const complexityViolations = analysisResults.complexity.violationCount;
            score -= complexityViolations * 5;
            
            // 重复代码扣分
            const duplicationRatio = analysisResults.duplication.duplicationRatio;
            score -= duplicationRatio * 30;
            
            // 依赖健康度扣分
            score -= (100 - analysisResults.dependencies.healthScore) * 0.3;
            
            // 代码异味扣分
            const codeSmellCount = analysisResults.codeSmells.totalSmells;
            score -= codeSmellCount * 2;
            
            return Math.max(0, Math.round(score));
        }

        /**
         * 更新监控状态
         */
        updateMonitoringState(qualityScore) {
            const previousScore = this.monitoringState.qualityScore;
            
            this.monitoringState.lastAnalysis = new Date().toISOString();
            this.monitoringState.analysisCount++;
            this.monitoringState.qualityScore = qualityScore;
            
            // 确定趋势方向
            if (qualityScore > previousScore + 5) {
                this.monitoringState.trendDirection = 'improving';
            } else if (qualityScore < previousScore - 5) {
                this.monitoringState.trendDirection = 'declining';
            } else {
                this.monitoringState.trendDirection = 'stable';
            }
            
            // 记录趋势
            this.qualityMetrics.trends.push({
                timestamp: Date.now(),
                score: qualityScore,
                trend: this.monitoringState.trendDirection
            });
            
            // 保持趋势记录大小
            if (this.qualityMetrics.trends.length > 100) {
                this.qualityMetrics.trends = this.qualityMetrics.trends.slice(-50);
            }
        }

        /**
         * 生成质量报告
         */
        generateQualityReport(analysisResults) {
            return {
                timestamp: new Date().toISOString(),
                qualityScore: analysisResults.qualityScore,
                trend: this.monitoringState.trendDirection,
                summary: {
                    complexity: {
                        averageComplexity: analysisResults.complexity.averageComplexity.toFixed(2),
                        maxComplexity: analysisResults.complexity.maxComplexity,
                        violations: analysisResults.complexity.violationCount
                    },
                    duplication: {
                        duplicateBlocks: analysisResults.duplication.duplicateBlocks.length,
                        duplicationRatio: `${(analysisResults.duplication.duplicationRatio * 100).toFixed(2)}%`,
                        violations: analysisResults.duplication.violationCount
                    },
                    dependencies: {
                        totalDependencies: analysisResults.dependencies.totalDependencies,
                        circularDependencies: analysisResults.dependencies.circularDependencies.length,
                        healthScore: analysisResults.dependencies.healthScore,
                        violations: analysisResults.dependencies.violationCount
                    },
                    codeSmells: {
                        totalSmells: analysisResults.codeSmells.totalSmells,
                        severity: analysisResults.codeSmells.severity,
                        smellsByType: analysisResults.codeSmells.smellsByType
                    }
                },
                recommendations: this.generateRecommendations(analysisResults),
                detailedResults: analysisResults
            };
        }

        /**
         * 生成改进建议
         */
        generateRecommendations(analysisResults) {
            const recommendations = [];
            
            // 复杂度建议
            if (analysisResults.complexity.violationCount > 0) {
                recommendations.push('降低函数复杂度：拆分复杂函数，减少嵌套层级');
                recommendations.push('重构长函数：将大函数分解为多个小函数');
            }
            
            // 重复代码建议
            if (analysisResults.duplication.violationCount > 0) {
                recommendations.push('消除重复代码：提取公共函数，使用继承或组合');
                recommendations.push('建立代码复用机制：创建工具函数库');
            }
            
            // 依赖建议
            if (analysisResults.dependencies.violationCount > 0) {
                recommendations.push('优化依赖结构：减少不必要的依赖，解决循环依赖');
                recommendations.push('使用依赖注入：降低模块间的耦合度');
            }
            
            // 代码异味建议
            if (analysisResults.codeSmells.totalSmells > 5) {
                recommendations.push('清理代码异味：重构长方法，减少参数数量');
                recommendations.push('提高代码可读性：使用有意义的变量名，添加注释');
            }
            
            return recommendations;
        }

        /**
         * 报告质量问题
         */
        reportQualityIssue(type, details) {
            if (this.warningManager) {
                this.warningManager.warn(
                    `CODE_QUALITY_${type.toUpperCase()}`,
                    `代码质量问题: ${type}`,
                    'WARNING',
                    details
                );
            } else {
                console.warn(`⚠️ 代码质量问题 [${type}]:`, details);
            }
        }

        /**
         * 获取质量统计
         */
        getQualityStats() {
            return {
                monitoringState: this.monitoringState,
                latestMetrics: {
                    complexity: Array.from(this.qualityMetrics.complexity.values()).pop(),
                    duplication: Array.from(this.qualityMetrics.duplication.values()).pop(),
                    dependencies: Array.from(this.qualityMetrics.dependencies.values()).pop(),
                    maintainability: Array.from(this.qualityMetrics.maintainability.values()).pop()
                },
                trends: this.qualityMetrics.trends.slice(-10),
                config: this.qualityConfig
            };
        }
    }

    // 创建全局唯一的代码质量监控器实例
    const codeQualityMonitor = new CodeQualityMonitor();

    // 暴露到OTA命名空间
    window.OTA.core.codeQualityMonitor = codeQualityMonitor;

    // 提供全局命令
    window.analyzeCodeQuality = async () => {
        console.group('📊 代码质量分析');
        const report = await codeQualityMonitor.performQualityAnalysis();
        console.log('质量报告:', report);
        console.groupEnd();
        return report;
    };

    window.getQualityStats = () => {
        return codeQualityMonitor.getQualityStats();
    };

    console.log('✅ 代码质量监控器模块已加载');

})();
