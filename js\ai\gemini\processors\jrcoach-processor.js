/**
 * @PROCESSOR JRCoach专用处理器
 * 🏷️ 标签: @JRCOACH_PROCESSOR
 * 📝 说明: 专门处理JRCoach订单的智能处理器，包含JRCoach特定的参考号识别、字段映射和预设值应用
 * 🎯 功能: JRCoach订单解析、参考号识别、字段映射、预设值应用、数据验证
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.processors = window.OTA.gemini.processors || {};

(function() {
    'use strict';

    /**
     * JRCoach专用处理器类
     * 继承自BaseProcessor，实现JRCoach订单的专业化处理
     */
    class JRCoachProcessor {
        constructor() {
            this.logger = window.getLogger?.() || console;
            this.processorName = 'JRCoachProcessor';
            this.version = '1.0.0';
            this.platform = 'JRCoach';
            this.platformDisplayName = 'JRCoach';
            
            // JRCoach特定配置
            this.jrcoachConfig = {
                // 参考号模式
                referencePatterns: [
                    /JRC[A-Z0-9]{8,15}/gi,           // JRC开头的订单号
                    /JR[A-Z0-9]{10,18}/gi,           // JR开头的订单号
                    /\d{12,18}/g,                    // 长数字订单号
                    /[A-Z]{3}\d{8,12}/gi,            // 三字母+数字组合
                    /JRCoach[：:]\s*([A-Z0-9]{8,20})/gi, // JRCoach标识的订单号
                    /订单编号[：:]\s*([A-Z0-9]{8,20})/gi  // 订单编号标识
                ],
                
                // 字段映射规则
                fieldMappings: {
                    'pickup_location': [
                        '上车地点', '接载地点', '出发地', '起点', '接送地点',
                        'pickup', 'from', 'departure', 'origin', 'boarding'
                    ],
                    'dropoff_location': [
                        '下车地点', '目的地', '终点', '到达地', '送达地点',
                        'dropoff', 'to', 'destination', 'arrival', 'drop-off'
                    ],
                    'passenger_name': [
                        '乘客姓名', '旅客姓名', '联系人', '姓名', '客户姓名',
                        'passenger', 'guest', 'name', 'traveler', 'customer'
                    ],
                    'contact_number': [
                        '联系电话', '手机号码', '电话号码', '联系方式', '手机',
                        'phone', 'mobile', 'contact', 'tel', 'number', 'cell'
                    ],
                    'pickup_date': [
                        '服务日期', '出发日期', '用车日期', '接载日期', '日期',
                        'date', 'pickup_date', 'service_date', 'departure_date'
                    ],
                    'pickup_time': [
                        '服务时间', '出发时间', '用车时间', '接载时间', '时间',
                        'time', 'pickup_time', 'service_time', 'departure_time'
                    ],
                    'flight_number': [
                        '航班号', '班次号', '航班', '班次', 'flight', 'flight_number', 'flight_no'
                    ],
                    'luggage_count': [
                        '行李数量', '行李件数', '行李箱数', '行李', 'luggage', 'bags', 'baggage', 'suitcase'
                    ],
                    'passenger_count': [
                        '乘客人数', '人数', '旅客数量', '乘客数', 'passengers', 'pax', 'people', 'persons'
                    ],
                    'special_requirements': [
                        '特殊要求', '备注信息', '说明', '要求', '特别说明', '备注',
                        'requirements', 'notes', 'remarks', 'special', 'memo', 'comments'
                    ],
                    'vehicle_type': [
                        '车型', '车辆类型', '车种', '车款', 'vehicle', 'car_type', 'vehicle_type'
                    ],
                    'route_type': [
                        '路线类型', '服务类型', '行程类型', 'route', 'service_type', 'trip_type'
                    ]
                },
                
                // 预设值配置
                presetValues: {
                    car_type_id: 1,                    // 默认舒适型5座
                    service_type_id: 2,                // 默认接机服务
                    languages_id_array: {"0": "2"},    // 默认英文
                    luggage_number: 2,                 // 默认2件行李
                    is_return: false,                  // 默认单程
                    pickup_sign: false,                // 默认不举牌
                    currency: 'MYR',                   // 默认马币
                    payment_method: 'online'           // 默认在线支付
                },
                
                // 数据验证规则
                validationRules: {
                    required_fields: [
                        'ota_reference_number', 'pickup_location', 'dropoff_location',
                        'passenger_name', 'contact_number', 'pickup_date', 'pickup_time'
                    ],
                    field_formats: {
                        contact_number: /^(\+60|60)?[1-9]\d{7,9}$|^1[3-9]\d{9}$|^\d{3,4}-\d{7,8}$/,
                        pickup_date: /^\d{4}-\d{2}-\d{2}$|^\d{2}\/\d{2}\/\d{4}$|^\d{2}-\d{2}-\d{4}$/,
                        pickup_time: /^([01]?\d|2[0-3]):[0-5]\d$|^([01]?\d|2[0-3])[0-5]\d$/,
                        flight_number: /^[A-Z]{2}\d{3,4}$|^[A-Z]\d{4,5}$/i
                    }
                },
                
                // 智能识别关键词
                identificationKeywords: [
                    'jrcoach', 'jr coach', 'jr-coach', 'jrc',
                    'junior coach', 'journey coach'
                ],
                
                // 服务类型识别
                serviceTypeKeywords: {
                    2: ['接机', '机场接载', '到达接送', 'pickup', 'arrival', 'airport pickup'],
                    3: ['送机', '机场送达', '出发送机', 'dropoff', 'departure', 'airport dropoff'],
                    4: ['包车', '租车服务', '专车', 'charter', 'rental', 'private hire', 'hourly']
                },
                
                // 车型识别关键词
                carTypeKeywords: {
                    1: ['轿车', '小车', '5座', '舒适型', 'sedan', 'comfort', 'economy', 'standard'],
                    2: ['MPV', '7座', '8座', '商务车', 'mpv', 'business', 'van'],
                    3: ['豪华车', '高级轿车', '奔驰', '宝马', 'luxury', 'premium', 'mercedes', 'bmw'],
                    4: ['巴士', '大巴', '12座', '15座', 'bus', 'coach', 'minibus', 'maxi']
                },
                
                // 马来西亚特定地点识别
                malaysianLocations: {
                    airports: [
                        'KLIA', 'KLIA2', 'Subang Airport', 'Penang Airport', 'Langkawi Airport',
                        '吉隆坡国际机场', '梳邦机场', '槟城机场', '兰卡威机场'
                    ],
                    cities: [
                        'Kuala Lumpur', 'KL', 'Petaling Jaya', 'PJ', 'Shah Alam',
                        'Subang Jaya', 'Penang', 'Johor Bahru', 'JB', 'Malacca',
                        '吉隆坡', '八打灵再也', '莎阿南', '梳邦再也', '槟城', '新山', '马六甲'
                    ],
                    hotels: [
                        'Hotel', 'Resort', 'Inn', 'Lodge', 'Suites',
                        '酒店', '度假村', '旅馆', '套房'
                    ]
                }
            };
            
            // 处理统计
            this.stats = {
                totalProcessed: 0,
                successfulProcessed: 0,
                failedProcessed: 0,
                averageProcessingTime: 0,
                referenceNumberMatches: 0,
                fieldExtractionSuccess: 0,
                validationErrors: 0,
                locationMatches: 0
            };
            
            // 初始化处理器
            this.initialize();
        }

        /**
         * 初始化处理器
         */
        initialize() {
            this.logger.log(`${this.platformDisplayName}处理器初始化开始`, 'info');
            
            // 获取基础处理器
            this.baseProcessor = window.OTA?.gemini?.core?.getBaseProcessor?.();
            
            // 获取配置管理器
            this.configManager = window.OTA?.gemini?.core?.getConfigManager?.();
            
            this.logger.log(`${this.platformDisplayName}处理器初始化完成`, 'info');
        }

        /**
         * 处理订单 - 主要入口方法
         * @param {string} orderText - 订单文本
         * @param {Object} options - 处理选项
         * @returns {Promise<Object>} 处理结果
         */
        async processOrder(orderText, options = {}) {
            const startTime = Date.now();
            this.stats.totalProcessed++;
            
            try {
                this.logger.log(`开始处理${this.platformDisplayName}订单`, 'info');
                
                // 1. 预处理订单文本
                const preprocessedText = this.preprocessOrderText(orderText);
                
                // 2. 识别参考号
                const referenceNumber = this.extractReferenceNumber(preprocessedText);
                
                // 3. 提取基础字段
                const extractedFields = this.extractFields(preprocessedText);
                
                // 4. 智能字段映射
                const mappedFields = this.mapFields(extractedFields);
                
                // 5. 马来西亚特定处理
                const localizedFields = this.applyMalaysianLocalization(mappedFields, preprocessedText);
                
                // 6. 应用预设值
                const fieldsWithPresets = this.applyPresetValues(localizedFields);
                
                // 7. 数据验证和清理
                const validatedFields = this.validateAndCleanFields(fieldsWithPresets);
                
                // 8. 生成最终结果
                const result = this.generateResult(validatedFields, referenceNumber, options);
                
                // 9. 更新统计信息
                this.updateStats(startTime, true);
                
                this.logger.log(`${this.platformDisplayName}订单处理完成`, 'info');
                
                return result;
                
            } catch (error) {
                this.stats.failedProcessed++;
                this.logger.logError(`${this.platformDisplayName}订单处理失败`, error);
                
                // 返回降级结果
                return this.generateFallbackResult(orderText, options, error);
            }
        }

        /**
         * 预处理订单文本
         * @param {string} orderText - 原始订单文本
         * @returns {string} 预处理后的文本
         */
        preprocessOrderText(orderText) {
            if (!orderText || typeof orderText !== 'string') {
                throw new Error('订单文本必须是非空字符串');
            }
            
            // 统一换行符
            let processed = orderText.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
            
            // 清理多余空白
            processed = processed.replace(/\s+/g, ' ').trim();
            
            // 标准化标点符号
            processed = processed.replace(/：/g, ':').replace(/，/g, ',');
            
            // 处理马来西亚特有的地名缩写
            processed = this.expandMalaysianAbbreviations(processed);
            
            return processed;
        }

        /**
         * 展开马来西亚地名缩写
         * @param {string} text - 文本
         * @returns {string} 展开后的文本
         */
        expandMalaysianAbbreviations(text) {
            const abbreviations = {
                'KL': 'Kuala Lumpur',
                'PJ': 'Petaling Jaya',
                'JB': 'Johor Bahru',
                'SJ': 'Subang Jaya',
                'SA': 'Shah Alam'
            };
            
            let expanded = text;
            for (const [abbr, full] of Object.entries(abbreviations)) {
                const regex = new RegExp(`\\b${abbr}\\b`, 'gi');
                expanded = expanded.replace(regex, full);
            }
            
            return expanded;
        }

        /**
         * 提取参考号
         * @param {string} orderText - 订单文本
         * @returns {string|null} 参考号
         */
        extractReferenceNumber(orderText) {
            for (const pattern of this.jrcoachConfig.referencePatterns) {
                const matches = orderText.match(pattern);
                if (matches && matches.length > 0) {
                    this.stats.referenceNumberMatches++;
                    
                    // 如果是带标识的模式，提取括号内容
                    if (pattern.source.includes('订单') || pattern.source.includes('JRCoach')) {
                        const match = pattern.exec(orderText);
                        return match ? match[1] : matches[0];
                    }
                    
                    return matches[0];
                }
            }
            
            // 使用通用模式作为后备
            const genericPatterns = [
                /[A-Z0-9]{8,20}/g,
                /\d{8,15}/g
            ];
            
            for (const pattern of genericPatterns) {
                const matches = orderText.match(pattern);
                if (matches && matches.length > 0) {
                    return matches[0];
                }
            }
            
            return null;
        }

        /**
         * 提取字段
         * @param {string} orderText - 订单文本
         * @returns {Object} 提取的字段
         */
        extractFields(orderText) {
            const fields = {};
            const lines = orderText.split('\n');
            
            for (const line of lines) {
                const trimmedLine = line.trim();
                if (!trimmedLine) continue;
                
                // 尝试匹配键值对模式
                const keyValueMatch = trimmedLine.match(/^(.+?)[：:]\s*(.+)$/);
                if (keyValueMatch) {
                    const key = keyValueMatch[1].trim();
                    const value = keyValueMatch[2].trim();
                    
                    if (value && value !== '-' && value !== 'N/A' && value !== 'TBC') {
                        fields[key] = value;
                    }
                }
                
                // 尝试匹配特定模式
                this.extractSpecificPatterns(trimmedLine, fields);
            }
            
            return fields;
        }

        /**
         * 提取特定模式
         * @param {string} line - 文本行
         * @param {Object} fields - 字段对象
         */
        extractSpecificPatterns(line, fields) {
            // 提取航班号
            const flightMatch = line.match(/([A-Z]{2}\d{3,4}|[A-Z]\d{4,5})/i);
            if (flightMatch && !fields.flight_number) {
                fields.flight_number = flightMatch[1].toUpperCase();
            }
            
            // 提取马来西亚电话号码
            const phoneMatch = line.match(/(\+60[1-9]\d{7,9}|60[1-9]\d{7,9}|01[0-9]\d{7,8}|1[3-9]\d{9})/);
            if (phoneMatch && !fields.contact_number) {
                fields.contact_number = phoneMatch[1];
            }
            
            // 提取日期
            const dateMatch = line.match(/(\d{4}[-\/]\d{1,2}[-\/]\d{1,2}|\d{1,2}[-\/]\d{1,2}[-\/]\d{4})/);
            if (dateMatch && !fields.pickup_date) {
                fields.pickup_date = this.standardizeDate(dateMatch[1]);
            }
            
            // 提取时间
            const timeMatch = line.match(/([01]?\d|2[0-3])[:\s]?([0-5]\d)/);
            if (timeMatch && !fields.pickup_time) {
                fields.pickup_time = `${timeMatch[1].padStart(2, '0')}:${timeMatch[2]}`;
            }
            
            // 提取人数
            const passengerMatch = line.match(/(\d+)\s*(?:人|pax|passengers?|persons?)/i);
            if (passengerMatch && !fields.passenger_count) {
                fields.passenger_count = parseInt(passengerMatch[1]);
            }
            
            // 提取价格信息
            const priceMatch = line.match(/(RM|MYR)\s*(\d+(?:\.\d{2})?)/i);
            if (priceMatch && !fields.price) {
                fields.price = parseFloat(priceMatch[2]);
                fields.currency = 'MYR';
            }
        }

        /**
         * 映射字段
         * @param {Object} extractedFields - 提取的字段
         * @returns {Object} 映射后的字段
         */
        mapFields(extractedFields) {
            const mappedFields = {};
            
            for (const [standardField, aliases] of Object.entries(this.jrcoachConfig.fieldMappings)) {
                for (const [extractedKey, extractedValue] of Object.entries(extractedFields)) {
                    // 检查是否匹配任何别名
                    const isMatch = aliases.some(alias => 
                        extractedKey.toLowerCase().includes(alias.toLowerCase()) ||
                        alias.toLowerCase().includes(extractedKey.toLowerCase())
                    );
                    
                    if (isMatch && !mappedFields[standardField]) {
                        mappedFields[standardField] = extractedValue;
                        break;
                    }
                }
            }
            
            // 智能服务类型识别
            mappedFields.service_type_id = this.identifyServiceType(extractedFields);
            
            // 智能车型识别
            mappedFields.car_type_id = this.identifyCarType(extractedFields);
            
            this.stats.fieldExtractionSuccess++;
            return mappedFields;
        }

        /**
         * 应用马来西亚本地化处理
         * @param {Object} fields - 字段对象
         * @param {string} originalText - 原始文本
         * @returns {Object} 本地化处理后的字段
         */
        applyMalaysianLocalization(fields, originalText) {
            const localizedFields = { ...fields };
            
            // 识别马来西亚机场
            localizedFields.pickup_location = this.identifyMalaysianLocation(
                localizedFields.pickup_location, originalText, 'pickup'
            );
            localizedFields.dropoff_location = this.identifyMalaysianLocation(
                localizedFields.dropoff_location, originalText, 'dropoff'
            );
            
            // 标准化马来西亚电话号码
            if (localizedFields.contact_number) {
                localizedFields.contact_number = this.standardizeMalaysianPhone(localizedFields.contact_number);
            }
            
            // 设置默认货币为马币
            if (!localizedFields.currency) {
                localizedFields.currency = 'MYR';
            }
            
            return localizedFields;
        }

        /**
         * 识别马来西亚地点
         * @param {string} location - 地点字符串
         * @param {string} fullText - 完整文本
         * @param {string} type - 地点类型 (pickup/dropoff)
         * @returns {string} 识别后的地点
         */
        identifyMalaysianLocation(location, fullText, type) {
            if (!location) return location;
            
            const lowerLocation = location.toLowerCase();
            const lowerFullText = fullText.toLowerCase();
            
            // 检查是否为机场
            for (const airport of this.jrcoachConfig.malaysianLocations.airports) {
                if (lowerLocation.includes(airport.toLowerCase()) || 
                    lowerFullText.includes(airport.toLowerCase())) {
                    this.stats.locationMatches++;
                    return airport;
                }
            }
            
            // 检查是否为城市
            for (const city of this.jrcoachConfig.malaysianLocations.cities) {
                if (lowerLocation.includes(city.toLowerCase())) {
                    this.stats.locationMatches++;
                    return city;
                }
            }
            
            // 检查是否为酒店
            for (const hotelKeyword of this.jrcoachConfig.malaysianLocations.hotels) {
                if (lowerLocation.includes(hotelKeyword.toLowerCase())) {
                    return location; // 保持原始酒店名称
                }
            }
            
            return location;
        }

        /**
         * 标准化马来西亚电话号码
         * @param {string} phoneStr - 电话号码字符串
         * @returns {string} 标准化后的电话号码
         */
        standardizeMalaysianPhone(phoneStr) {
            if (!phoneStr) return '';
            
            // 移除所有非数字和+号
            let cleaned = phoneStr.replace(/[^\d+]/g, '');
            
            // 处理马来西亚手机号
            if (cleaned.match(/^01[0-9]\d{7,8}$/)) {
                return '+60' + cleaned.substring(1); // 移除前导0，添加国家代码
            }
            
            // 处理已有国家代码的号码
            if (cleaned.startsWith('+60')) {
                return cleaned;
            }
            
            if (cleaned.startsWith('60') && cleaned.length > 10) {
                return '+' + cleaned;
            }
            
            // 处理中国手机号（可能是中国游客）
            if (cleaned.match(/^1[3-9]\d{9}$/)) {
                return '+86' + cleaned;
            }
            
            return phoneStr; // 无法标准化时返回原值
        }

        /**
         * 识别服务类型
         * @param {Object} fields - 字段对象
         * @returns {number} 服务类型ID
         */
        identifyServiceType(fields) {
            const fullText = Object.values(fields).join(' ').toLowerCase();
            
            for (const [typeId, keywords] of Object.entries(this.jrcoachConfig.serviceTypeKeywords)) {
                if (keywords.some(keyword => fullText.includes(keyword.toLowerCase()))) {
                    return parseInt(typeId);
                }
            }
            
            return this.jrcoachConfig.presetValues.service_type_id; // 默认接机
        }

        /**
         * 识别车型
         * @param {Object} fields - 字段对象
         * @returns {number} 车型ID
         */
        identifyCarType(fields) {
            const fullText = Object.values(fields).join(' ').toLowerCase();
            const passengerCount = fields.passenger_count || 0;
            
            // 根据人数判断
            if (passengerCount > 8) {
                return 4; // 巴士
            } else if (passengerCount > 5) {
                return 2; // MPV
            }
            
            // 根据关键词判断
            for (const [typeId, keywords] of Object.entries(this.jrcoachConfig.carTypeKeywords)) {
                if (keywords.some(keyword => fullText.includes(keyword.toLowerCase()))) {
                    return parseInt(typeId);
                }
            }
            
            return this.jrcoachConfig.presetValues.car_type_id; // 默认轿车
        }

        /**
         * 应用预设值
         * @param {Object} mappedFields - 映射后的字段
         * @returns {Object} 应用预设值后的字段
         */
        applyPresetValues(mappedFields) {
            const fieldsWithPresets = { ...this.jrcoachConfig.presetValues, ...mappedFields };
            
            // 特殊处理逻辑
            if (!fieldsWithPresets.ota_reference_number) {
                fieldsWithPresets.ota_reference_number = 'JRC_' + Date.now();
            }
            
            // 根据服务类型调整预设值
            if (fieldsWithPresets.service_type_id === 4) { // 包车服务
                fieldsWithPresets.is_return = false;
                fieldsWithPresets.pickup_sign = false;
            }
            
            // 马来西亚特定调整
            fieldsWithPresets.currency = 'MYR';
            
            return fieldsWithPresets;
        }

        /**
         * 验证和清理字段
         * @param {Object} fields - 字段对象
         * @returns {Object} 验证后的字段
         */
        validateAndCleanFields(fields) {
            const validatedFields = { ...fields };
            const errors = [];
            
            // 验证必填字段
            for (const requiredField of this.jrcoachConfig.validationRules.required_fields) {
                if (!validatedFields[requiredField] || validatedFields[requiredField].toString().trim() === '') {
                    errors.push(`缺少必填字段: ${requiredField}`);
                }
            }
            
            // 验证字段格式
            for (const [field, pattern] of Object.entries(this.jrcoachConfig.validationRules.field_formats)) {
                if (validatedFields[field] && !pattern.test(validatedFields[field])) {
                    errors.push(`字段格式错误: ${field} = ${validatedFields[field]}`);
                }
            }
            
            // 数据清理
            if (validatedFields.pickup_date) {
                validatedFields.pickup_date = this.standardizeDate(validatedFields.pickup_date);
            }
            
            if (validatedFields.pickup_time) {
                validatedFields.pickup_time = this.standardizeTime(validatedFields.pickup_time);
            }
            
            if (errors.length > 0) {
                this.stats.validationErrors++;
                this.logger.logWarning(`${this.platformDisplayName}订单验证警告`, errors);
            }
            
            return validatedFields;
        }

        /**
         * 标准化日期格式
         * @param {string} dateStr - 日期字符串
         * @returns {string} 标准化后的日期
         */
        standardizeDate(dateStr) {
            if (!dateStr) return '';
            
            // 尝试解析各种日期格式
            const formats = [
                /^(\d{4})[-\/](\d{1,2})[-\/](\d{1,2})$/,  // YYYY-MM-DD 或 YYYY/MM/DD
                /^(\d{1,2})[-\/](\d{1,2})[-\/](\d{4})$/   // DD-MM-YYYY 或 DD/MM/YYYY
            ];
            
            for (const format of formats) {
                const match = dateStr.match(format);
                if (match) {
                    if (match[1].length === 4) {
                        // YYYY-MM-DD 格式
                        return `${match[1]}-${match[2].padStart(2, '0')}-${match[3].padStart(2, '0')}`;
                    } else {
                        // DD-MM-YYYY 格式
                        return `${match[3]}-${match[2].padStart(2, '0')}-${match[1].padStart(2, '0')}`;
                    }
                }
            }
            
            return dateStr; // 无法解析时返回原值
        }

        /**
         * 标准化时间格式
         * @param {string} timeStr - 时间字符串
         * @returns {string} 标准化后的时间
         */
        standardizeTime(timeStr) {
            if (!timeStr) return '';
            
            const timeMatch = timeStr.match(/([01]?\d|2[0-3])[:\s]?([0-5]\d)/);
            if (timeMatch) {
                return `${timeMatch[1].padStart(2, '0')}:${timeMatch[2]}`;
            }
            
            return timeStr;
        }

        /**
         * 生成处理结果
         * @param {Object} fields - 处理后的字段
         * @param {string} referenceNumber - 参考号
         * @param {Object} options - 选项
         * @returns {Object} 处理结果
         */
        generateResult(fields, referenceNumber, options) {
            const result = {
                success: true,
                processor: this.processorName,
                platform: this.platform,
                version: this.version,
                confidence: this.calculateConfidence(fields, referenceNumber),
                data: {
                    ...fields,
                    ota_reference_number: referenceNumber || fields.ota_reference_number,
                    ota: this.platform.toLowerCase(),
                    original_platform: this.platformDisplayName
                },
                metadata: {
                    processingTime: Date.now(),
                    extractedFields: Object.keys(fields).length,
                    validationPassed: true,
                    processorSpecific: {
                        referencePatternMatched: !!referenceNumber,
                        serviceTypeDetected: !!fields.service_type_id,
                        carTypeDetected: !!fields.car_type_id,
                        malaysianLocationDetected: this.stats.locationMatches > 0
                    }
                }
            };
            
            // 添加测试模式标记
            if (options.test) {
                result.metadata.testMode = true;
            }
            
            return result;
        }

        /**
         * 计算置信度
         * @param {Object} fields - 字段对象
         * @param {string} referenceNumber - 参考号
         * @returns {number} 置信度分数 (0-1)
         */
        calculateConfidence(fields, referenceNumber) {
            let confidence = 0.5; // 基础置信度
            
            // 参考号匹配加分
            if (referenceNumber) {
                confidence += 0.2;
            }
            
            // 必填字段完整性加分
            const requiredFields = this.jrcoachConfig.validationRules.required_fields;
            const presentFields = requiredFields.filter(field => fields[field]);
            confidence += (presentFields.length / requiredFields.length) * 0.2;
            
            // 马来西亚特定字段识别加分
            if (fields.flight_number) confidence += 0.05;
            if (fields.passenger_count) confidence += 0.05;
            if (this.stats.locationMatches > 0) confidence += 0.1;
            
            return Math.min(confidence, 1.0);
        }

        /**
         * 生成降级结果
         * @param {string} orderText - 原始订单文本
         * @param {Object} options - 选项
         * @param {Error} error - 错误对象
         * @returns {Object} 降级结果
         */
        generateFallbackResult(orderText, options, error) {
            return {
                success: false,
                processor: this.processorName,
                platform: this.platform,
                version: this.version,
                confidence: 0.1,
                error: error.message,
                data: {
                    ...this.jrcoachConfig.presetValues,
                    ota_reference_number: 'JRC_ERROR_' + Date.now(),
                    ota: this.platform.toLowerCase(),
                    original_text: orderText.substring(0, 200) // 限制长度
                },
                metadata: {
                    processingTime: Date.now(),
                    fallback: true,
                    error: {
                        message: error.message,
                        type: error.name
                    }
                }
            };
        }

        /**
         * 更新统计信息
         * @param {number} startTime - 开始时间
         * @param {boolean} success - 是否成功
         */
        updateStats(startTime, success) {
            const processingTime = Date.now() - startTime;
            
            if (success) {
                this.stats.successfulProcessed++;
            }
            
            // 更新平均处理时间
            const totalProcessed = this.stats.totalProcessed;
            this.stats.averageProcessingTime = 
                ((this.stats.averageProcessingTime * (totalProcessed - 1)) + processingTime) / totalProcessed;
        }

        /**
         * 获取处理器统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            return {
                ...this.stats,
                successRate: this.stats.totalProcessed > 0 ? 
                    (this.stats.successfulProcessed / this.stats.totalProcessed) : 0,
                platform: this.platform,
                processorName: this.processorName
            };
        }

        /**
         * 重置统计信息
         */
        resetStats() {
            this.stats = {
                totalProcessed: 0,
                successfulProcessed: 0,
                failedProcessed: 0,
                averageProcessingTime: 0,
                referenceNumberMatches: 0,
                fieldExtractionSuccess: 0,
                validationErrors: 0,
                locationMatches: 0
            };
        }

        /**
         * 获取处理器信息
         * @returns {Object} 处理器信息
         */
        getProcessorInfo() {
            return {
                name: this.processorName,
                platform: this.platform,
                platformDisplayName: this.platformDisplayName,
                version: this.version,
                supportedPatterns: this.jrcoachConfig.referencePatterns.length,
                supportedFields: Object.keys(this.jrcoachConfig.fieldMappings).length,
                identificationKeywords: this.jrcoachConfig.identificationKeywords,
                malaysianSpecific: true
            };
        }
    }

    // 创建全局单例实例
    function getJRCoachProcessor() {
        if (!window.OTA.gemini.processors.jrcoachProcessor) {
            window.OTA.gemini.processors.jrcoachProcessor = new JRCoachProcessor();
        }
        return window.OTA.gemini.processors.jrcoachProcessor;
    }

    // 暴露到全局命名空间
    window.OTA.gemini.processors.JRCoachProcessor = JRCoachProcessor;
    window.OTA.gemini.processors.getJRCoachProcessor = getJRCoachProcessor;

    // 向后兼容
    window.getJRCoachProcessor = getJRCoachProcessor;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('jrcoachProcessor', getJRCoachProcessor(), '@JRCOACH_PROCESSOR');
        window.OTA.Registry.registerFactory('getJRCoachProcessor', getJRCoachProcessor, '@JRCOACH_PROCESSOR_FACTORY');
    }

    console.log('✅ JRCoach专用处理器已加载');

})();
