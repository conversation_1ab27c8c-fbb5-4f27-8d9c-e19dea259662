/**
 * 开发规范守护者
 * 建立开发规范并实时监控架构违规行为
 * 防止未来开发中引入导致多选组件问题的代码模式
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 开发规范守护者类
     */
    class DevelopmentStandardsGuardian {
        constructor() {
            this.rules = new Map(); // 规则注册表
            this.violations = []; // 违规记录
            this.monitoringActive = false;
            this.logger = null;
            
            // 统计信息
            this.stats = {
                rulesCount: 0,
                checksPerformed: 0,
                violationsFound: 0,
                lastCheckTime: null
            };

            // 默认规则
            this.registerDefaultRules();
        }

        /**
         * 初始化守护者
         */
        init() {
            if (this.monitoringActive) {
                this.log('开发规范守护者已经激活', 'warning');
                return;
            }

            this.setupMonitoring();
            this.monitoringActive = true;
            this.log('开发规范守护者已激活', 'info');
        }

        /**
         * 注册默认规则
         */
        registerDefaultRules() {
            // 规则1: 禁止多重全局事件监听器
            this.registerRule('no-multiple-global-listeners', {
                name: '禁止多重全局事件监听器',
                description: '防止多个组件注册重复的全局事件监听器',
                category: 'event-management',
                severity: 'error',
                check: this.checkMultipleGlobalListeners.bind(this)
            });

            // 规则2: 强制使用依赖注入
            this.registerRule('mandatory-dependency-injection', {
                name: '强制使用依赖注入',
                description: '所有组件必须通过依赖容器或工厂创建',
                category: 'architecture',
                severity: 'warning',
                check: this.checkDependencyInjection.bind(this)
            });

            // 规则3: CSS类命名规范
            this.registerRule('css-naming-convention', {
                name: 'CSS类命名规范',
                description: '新组件应使用命名空间前缀避免冲突',
                category: 'styling',
                severity: 'warning',
                check: this.checkCSSNaming.bind(this)
            });

            // 规则4: Z-Index规范
            this.registerRule('zindex-compliance', {
                name: 'Z-Index层级规范',
                description: '必须使用预定义的Z-Index变量',
                category: 'styling',
                severity: 'error',
                check: this.checkZIndexCompliance.bind(this)
            });

            // 规则5: 组件生命周期管理
            this.registerRule('lifecycle-management', {
                name: '组件生命周期管理',
                description: '所有UI组件必须正确注册到生命周期管理器',
                category: 'architecture',
                severity: 'warning',
                check: this.checkLifecycleManagement.bind(this)
            });

            // 规则6: 数据源统一性
            this.registerRule('unified-data-source', {
                name: '统一数据源',
                description: '应使用统一数据管理器而非直接访问多个数据源',
                category: 'data-management',
                severity: 'info',
                check: this.checkUnifiedDataSource.bind(this)
            });

            this.log(`已注册 ${this.rules.size} 条默认规则`, 'info');
        }

        /**
         * 注册规则
         * @param {string} ruleId - 规则ID
         * @param {Object} ruleConfig - 规则配置
         */
        registerRule(ruleId, ruleConfig) {
            if (this.rules.has(ruleId)) {
                this.log(`规则 ${ruleId} 已存在，将被覆盖`, 'warning');
            }

            const rule = {
                id: ruleId,
                ...ruleConfig,
                registeredAt: Date.now(),
                checkCount: 0,
                violationCount: 0
            };

            this.rules.set(ruleId, rule);
            this.stats.rulesCount = this.rules.size;
        }

        /**
         * 设置监控
         */
        setupMonitoring() {
            // 监控DOM变化
            this.setupMutationObserver();
            
            // 监控函数调用
            this.setupFunctionInterception();
            
            // 定期检查
            this.setupPeriodicChecks();
        }

        /**
         * 设置DOM变化监控
         */
        setupMutationObserver() {
            if (!window.MutationObserver) return;

            this.mutationObserver = new MutationObserver((mutations) => {
                mutations.forEach(mutation => {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach(node => {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                this.checkNewElement(node);
                            }
                        });
                    }
                });
            });

            this.mutationObserver.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['class', 'style']
            });

            this.log('DOM变化监控已设置', 'debug');
        }

        /**
         * 设置函数拦截
         */
        setupFunctionInterception() {
            // 拦截addEventListener调用
            this.interceptAddEventListener();
            
            // 拦截组件构造函数
            this.interceptComponentConstruction();
        }

        /**
         * 拦截addEventListener
         */
        interceptAddEventListener() {
            const originalAddEventListener = EventTarget.prototype.addEventListener;
            const self = this;

            EventTarget.prototype.addEventListener = function(type, listener, options) {
                // 检查全局事件监听器
                if (this === document || this === window) {
                    self.checkGlobalEventListener(type, listener, this);
                }

                return originalAddEventListener.call(this, type, listener, options);
            };

            this.log('addEventListener拦截已设置', 'debug');
        }

        /**
         * 拦截组件构造
         */
        interceptComponentConstruction() {
            // 拦截MultiSelectDropdown构造
            if (window.MultiSelectDropdown) {
                const OriginalConstructor = window.MultiSelectDropdown;
                const self = this;

                function InterceptedConstructor(...args) {
                    const instance = new OriginalConstructor(...args);
                    
                    // 检查是否遵循规范
                    setTimeout(() => {
                        self.checkComponentInstance(instance, 'MultiSelectDropdown');
                    }, 100);

                    return instance;
                }

                // 保持原型链
                InterceptedConstructor.prototype = OriginalConstructor.prototype;
                
                // 替换构造函数
                window.MultiSelectDropdown = InterceptedConstructor;
                
                if (window.OTA) {
                    window.OTA.MultiSelectDropdown = InterceptedConstructor;
                }
            }
        }

        /**
         * 设置定期检查
         */
        setupPeriodicChecks() {
            // 每30秒进行一次全面检查
            setInterval(() => {
                this.performFullCheck();
            }, 30000);

            this.log('定期检查已设置 (30秒间隔)', 'debug');
        }

        /**
         * 执行全面检查
         */
        performFullCheck() {
            this.log('开始执行全面架构检查', 'debug');

            const results = {
                timestamp: Date.now(),
                rulesChecked: 0,
                violationsFound: 0,
                details: []
            };

            this.rules.forEach((rule, ruleId) => {
                try {
                    const violations = rule.check();
                    rule.checkCount++;
                    results.rulesChecked++;

                    if (violations && violations.length > 0) {
                        rule.violationCount += violations.length;
                        results.violationsFound += violations.length;
                        
                        violations.forEach(violation => {
                            this.recordViolation(ruleId, violation);
                            results.details.push({
                                rule: ruleId,
                                ...violation
                            });
                        });
                    }
                } catch (error) {
                    this.log(`规则 ${ruleId} 检查失败: ${error.message}`, 'error');
                }
            });

            this.stats.checksPerformed++;
            this.stats.violationsFound += results.violationsFound;
            this.stats.lastCheckTime = results.timestamp;

            if (results.violationsFound > 0) {
                this.log(`发现 ${results.violationsFound} 个架构违规`, 'warning');
            }

            return results;
        }

        /**
         * 记录违规
         * @param {string} ruleId - 规则ID
         * @param {Object} violation - 违规详情
         */
        recordViolation(ruleId, violation) {
            const rule = this.rules.get(ruleId);
            
            const violationRecord = {
                id: `${ruleId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                ruleId,
                ruleName: rule.name,
                category: rule.category,
                severity: rule.severity,
                timestamp: Date.now(),
                ...violation
            };

            this.violations.push(violationRecord);

            // 保持违规记录不超过1000条
            if (this.violations.length > 1000) {
                this.violations = this.violations.slice(-1000);
            }

            // 根据严重程度决定日志级别
            const logLevel = rule.severity === 'error' ? 'error' : 
                           rule.severity === 'warning' ? 'warning' : 'info';
            
            this.log(`[${rule.severity.toUpperCase()}] ${rule.name}: ${violation.message}`, logLevel);
        }

        /**
         * 检查多重全局事件监听器
         */
        checkMultipleGlobalListeners() {
            const violations = [];
            const globalListeners = this.getGlobalEventListeners();
            
            // 统计相同类型的事件监听器
            const listenerCounts = {};
            
            globalListeners.forEach(listener => {
                const key = `${listener.target}_${listener.type}`;
                listenerCounts[key] = (listenerCounts[key] || 0) + 1;
            });

            // 检查是否有重复
            Object.entries(listenerCounts).forEach(([key, count]) => {
                if (count > 1) {
                    violations.push({
                        message: `检测到${count}个相同的全局${key}监听器`,
                        details: { key, count },
                        suggestion: '使用GlobalEventCoordinator统一管理全局事件'
                    });
                }
            });

            return violations;
        }

        /**
         * 检查依赖注入
         */
        checkDependencyInjection() {
            const violations = [];
            
            // 检查是否有组件绕过了依赖容器
            const directInstances = this.findDirectInstances();
            
            directInstances.forEach(instance => {
                violations.push({
                    message: `组件${instance.type}未通过依赖容器创建`,
                    details: instance,
                    suggestion: '使用依赖容器的工厂方法创建组件实例'
                });
            });

            return violations;
        }

        /**
         * 检查CSS命名规范
         */
        checkCSSNaming() {
            const violations = [];
            const elements = document.querySelectorAll('[class*="multi-select"]');
            
            elements.forEach(element => {
                const classList = Array.from(element.classList);
                const hasNamespace = classList.some(cls => cls.startsWith('ota-'));
                
                if (!hasNamespace && classList.some(cls => cls.includes('multi-select'))) {
                    violations.push({
                        message: '发现未使用命名空间的多选组件样式',
                        details: { element: element.tagName, classes: classList },
                        suggestion: '使用ota-前缀的CSS类名'
                    });
                }
            });

            return violations;
        }

        /**
         * 检查Z-Index规范
         */
        checkZIndexCompliance() {
            const violations = [];
            const elements = document.querySelectorAll('*');
            
            elements.forEach(element => {
                const style = getComputedStyle(element);
                const zIndex = style.zIndex;
                
                if (zIndex && zIndex !== 'auto' && !isNaN(parseInt(zIndex))) {
                    const zIndexValue = parseInt(zIndex);
                    
                    // 检查是否使用了硬编码的Z-Index值
                    const inlineStyle = element.style.zIndex;
                    if (inlineStyle && !this.isValidZIndexVariable(inlineStyle)) {
                        violations.push({
                            message: `元素使用硬编码Z-Index值: ${zIndexValue}`,
                            details: { element: element.tagName, zIndex: zIndexValue },
                            suggestion: '使用CSS变量如 var(--z-dropdown)'
                        });
                    }
                }
            });

            return violations;
        }

        /**
         * 检查生命周期管理
         */
        checkLifecycleManagement() {
            const violations = [];
            
            // 检查是否有组件未注册到生命周期管理器
            if (window.OTA && window.OTA.componentLifecycleManager) {
                const registeredComponents = window.OTA.componentLifecycleManager.getAllComponents();
                const actualComponents = this.findActualComponents();
                
                actualComponents.forEach(component => {
                    const isRegistered = registeredComponents.some(reg => reg.id === component.id);
                    
                    if (!isRegistered) {
                        violations.push({
                            message: `组件${component.id}未注册到生命周期管理器`,
                            details: component,
                            suggestion: '在组件初始化时调用lifecycleManager.register()'
                        });
                    }
                });
            }

            return violations;
        }

        /**
         * 检查统一数据源
         */
        checkUnifiedDataSource() {
            const violations = [];
            
            // 检查是否直接访问AppState或ApiService
            const directAccess = this.findDirectDataAccess();
            
            directAccess.forEach(access => {
                violations.push({
                    message: `检测到直接数据源访问: ${access.source}`,
                    details: access,
                    suggestion: '使用UnifiedDataManager获取数据'
                });
            });

            return violations;
        }

        /**
         * 获取所有违规记录
         * @param {Object} filter - 过滤条件
         * @returns {Array} 违规记录
         */
        getViolations(filter = {}) {
            let violations = [...this.violations];

            if (filter.severity) {
                violations = violations.filter(v => v.severity === filter.severity);
            }

            if (filter.category) {
                violations = violations.filter(v => v.category === filter.category);
            }

            if (filter.ruleId) {
                violations = violations.filter(v => v.ruleId === filter.ruleId);
            }

            if (filter.since) {
                violations = violations.filter(v => v.timestamp >= filter.since);
            }

            return violations.sort((a, b) => b.timestamp - a.timestamp);
        }

        /**
         * 获取统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            const ruleStats = {};
            
            this.rules.forEach((rule, ruleId) => {
                ruleStats[ruleId] = {
                    name: rule.name,
                    category: rule.category,
                    checkCount: rule.checkCount,
                    violationCount: rule.violationCount
                };
            });

            return {
                ...this.stats,
                totalViolations: this.violations.length,
                ruleStats,
                lastCheck: this.stats.lastCheckTime ? new Date(this.stats.lastCheckTime).toISOString() : null
            };
        }

        /**
         * 生成合规性报告
         * @returns {Object} 合规性报告
         */
        generateComplianceReport() {
            const report = {
                timestamp: new Date().toISOString(),
                summary: {
                    rulesTotal: this.rules.size,
                    violationsTotal: this.violations.length,
                    complianceScore: this.calculateComplianceScore()
                },
                categories: {},
                recommendations: []
            };

            // 按类别统计违规
            this.violations.forEach(violation => {
                const category = violation.category;
                if (!report.categories[category]) {
                    report.categories[category] = {
                        total: 0,
                        byRule: {},
                        severity: { error: 0, warning: 0, info: 0 }
                    };
                }

                report.categories[category].total++;
                report.categories[category].byRule[violation.ruleId] = 
                    (report.categories[category].byRule[violation.ruleId] || 0) + 1;
                report.categories[category].severity[violation.severity]++;
            });

            // 生成建议
            report.recommendations = this.generateRecommendations();

            return report;
        }

        /**
         * 计算合规性分数
         * @returns {number} 合规性分数 (0-100)
         */
        calculateComplianceScore() {
            if (this.stats.checksPerformed === 0) return 100;

            const totalPossibleViolations = this.stats.checksPerformed * this.rules.size;
            const actualViolations = this.stats.violationsFound;

            return Math.max(0, Math.round(100 - (actualViolations / totalPossibleViolations * 100)));
        }

        /**
         * 生成建议
         * @returns {Array} 建议列表
         */
        generateRecommendations() {
            const recommendations = [];
            
            // 分析最常见的违规类型
            const violationCounts = {};
            this.violations.forEach(v => {
                violationCounts[v.ruleId] = (violationCounts[v.ruleId] || 0) + 1;
            });

            const sortedViolations = Object.entries(violationCounts)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 3);

            sortedViolations.forEach(([ruleId, count]) => {
                const rule = this.rules.get(ruleId);
                recommendations.push({
                    priority: rule.severity === 'error' ? 'high' : 
                             rule.severity === 'warning' ? 'medium' : 'low',
                    title: `修复 ${rule.name}`,
                    description: `发现${count}个违规，建议立即处理`,
                    action: rule.description
                });
            });

            return recommendations;
        }

        // 辅助方法
        getGlobalEventListeners() {
            // 简化实现，实际应该跟踪所有添加的监听器
            return [];
        }

        findDirectInstances() {
            return [];
        }

        findActualComponents() {
            return [];
        }

        findDirectDataAccess() {
            return [];
        }

        isValidZIndexVariable(value) {
            return value.includes('var(--z-');
        }

        checkNewElement(element) {
            // 检查新添加的元素是否符合规范
            if (element.classList && element.classList.contains('multi-select-dropdown')) {
                setTimeout(() => {
                    this.performFullCheck();
                }, 100);
            }
        }

        checkGlobalEventListener(type, listener, target) {
            // 记录全局事件监听器添加
            this.log(`检测到全局${type}事件监听器`, 'debug');
        }

        checkComponentInstance(instance, type) {
            // 检查组件实例是否符合规范
            this.log(`检查${type}实例规范`, 'debug');
        }

        /**
         * 日志输出
         * @param {string} message - 消息
         * @param {string} level - 级别
         * @param {Object} data - 数据
         */
        log(message, level = 'info', data = null) {
            if (!this.logger) {
                try {
                    this.logger = getLogger ? getLogger() : null;
                } catch (e) {
                    // Logger可能还未初始化
                }
            }

            if (this.logger) {
                this.logger.log(`[StandardsGuardian] ${message}`, level, data);
            } else if (level === 'error' || level === 'warning') {
                // 仅在错误和警告时输出到控制台
                console[level](`[StandardsGuardian] ${message}`, data);
            }
        }
    }

    // 创建全局实例
    const developmentStandardsGuardian = new DevelopmentStandardsGuardian();

    // 导出到OTA命名空间
    window.OTA.DevelopmentStandardsGuardian = DevelopmentStandardsGuardian;
    window.OTA.developmentStandardsGuardian = developmentStandardsGuardian;

    // 向后兼容
    window.DevelopmentStandardsGuardian = DevelopmentStandardsGuardian;
    window.developmentStandardsGuardian = developmentStandardsGuardian;

    // 🔧 注册到依赖容器（如果可用）
    if (window.OTA && window.OTA.container && typeof window.OTA.container.register === 'function') {
        try {
            window.OTA.container.register('standardsGuardian', () => developmentStandardsGuardian, {
                singleton: true
            });
        } catch (error) {
            console.warn('[DevelopmentStandardsGuardian] 注册到依赖容器失败:', error.message);
        }
    }

})();